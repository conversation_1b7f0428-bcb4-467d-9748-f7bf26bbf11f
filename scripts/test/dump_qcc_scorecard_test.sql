-- MySQL dump 10.13  Distrib 8.0.41, for macos13.7 (x86_64)
--
-- Host: **************    Database: qcc_scorecard_test
-- ------------------------------------------------------
-- Server version	8.0.22

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '1230c1d4-3e5c-11ef-ac90-06777fb6dc0c:1-28189806,
1a74d69e-1c6e-11ee-ae8c-5a54ac473028:1-193579725,
5e490d95-f7e7-11ef-9de7-fa163e82ac61:1-12106519,
d8bccb1c-22fa-11ef-8f3a-76169e623a0f:1-2399749,
e91b7a07-6431-11ea-a5fe-fa163e68fe5a:1-34049587';

--
-- Current Database: `qcc_scorecard_test`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `qcc_scorecard_test` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `qcc_scorecard_test`;

--
-- Table structure for table `access_control`
--

DROP TABLE IF EXISTS `access_control`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `access_control` (
  `id` int NOT NULL AUTO_INCREMENT,
  `access_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `product` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expire` int NOT NULL,
  `status` int DEFAULT '0',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `scope` int DEFAULT '1' COMMENT '1 只能能访问所有外部接口\n2 能访问 所有外部接口 + 平台接口\n',
  `secret_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ai_chat_history`
--

DROP TABLE IF EXISTS `ai_chat_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_chat_history` (
  `chat_history_id` int NOT NULL AUTO_INCREMENT,
  `chat_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `prompt_id` int DEFAULT NULL,
  `input_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `api_response` json DEFAULT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `prompt_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'prompt_id 和 prompt_content 都可以',
  PRIMARY KEY (`chat_history_id`),
  KEY `index1` (`chat_id`)
) ENGINE=InnoDB AUTO_INCREMENT=720 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ai_models`
--

DROP TABLE IF EXISTS `ai_models`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_models` (
  `id` int NOT NULL AUTO_INCREMENT,
  `model` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ai_prompts`
--

DROP TABLE IF EXISTS `ai_prompts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_prompts` (
  `prompt_id` int NOT NULL AUTO_INCREMENT,
  `business_type` int NOT NULL COMMENT '0 尽调\n1 风险巡检\n2 风险监控',
  `product_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '第三方，风险洞察',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `desc` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `org_id` int DEFAULT NULL COMMENT '如果没有值，就是系统级别的prompt，都可以用',
  `fee_level` int NOT NULL DEFAULT '0' COMMENT '0 免费版\n1 收费1\n2 收费2',
  `prompt_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '报告分析员',
  `is_default` int DEFAULT '0',
  PRIMARY KEY (`prompt_id`),
  KEY `index` (`product_code`,`business_type`,`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ai_report_chat`
--

DROP TABLE IF EXISTS `ai_report_chat`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_report_chat` (
  `chat_id` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `chat_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '截取用户输入的前几个字符',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `report_id` int DEFAULT NULL COMMENT '生成报告之后的报告ID',
  `business_type` int NOT NULL DEFAULT '0' COMMENT '0 尽调\n1 监控\n2 风险巡检',
  `business_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'diligence_id 或者其他唯一的字符串',
  `org_id` int NOT NULL,
  `user_id` int NOT NULL,
  PRIMARY KEY (`chat_id`),
  KEY `index1` (`report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ai_report_comment`
--

DROP TABLE IF EXISTS `ai_report_comment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_report_comment` (
  `comment_id` int NOT NULL AUTO_INCREMENT,
  `report_id` int NOT NULL,
  `org_id` int NOT NULL COMMENT '组织 ID',
  `comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `like_it` int DEFAULT '0' COMMENT '-1 踩,0 默认值,1 赞',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` int DEFAULT NULL,
  PRIMARY KEY (`comment_id`),
  KEY `ai_report_comment_report_id_index` (`report_id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ai_reports`
--

DROP TABLE IF EXISTS `ai_reports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_reports` (
  `report_id` int NOT NULL AUTO_INCREMENT,
  `business_type` int NOT NULL DEFAULT '0' COMMENT '0 尽调\n1 监控\n2 风险巡检',
  `business_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'diligence_id 或者其他唯一的字符串',
  `org_id` int NOT NULL,
  `user_id` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `report_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`report_id`),
  KEY `index1` (`business_id`,`business_type`,`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=721 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch`
--

DROP TABLE IF EXISTS `batch`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch` (
  `batch_id` int NOT NULL AUTO_INCREMENT,
  `file_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `org_id` int NOT NULL,
  `dep_id` int NOT NULL DEFAULT '-1' COMMENT '部门id',
  `create_by` int NOT NULL,
  `status` int DEFAULT '0' COMMENT '0 待处理 1 处理中 2 处理成功 3处理失败',
  `comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述信息，可是是处理失败的时候的错误原因等',
  `batch_type` int NOT NULL DEFAULT '0' COMMENT '0 导入\n1 导出',
  `result_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '批量（导入或者导出）结果对应的文件',
  `business_type` int DEFAULT '0' COMMENT '0 尽职调查\\n1 客商',
  `batch_info` json DEFAULT NULL COMMENT '批量排查任务：记录使用的排查模型； 年检任务： 记录年检设置规则；',
  `start_date` datetime DEFAULT NULL,
  `record_count` int NOT NULL COMMENT '该批次中包含的记录的count',
  `end_date` datetime DEFAULT NULL,
  `statistics_info` json NOT NULL,
  `origin_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '批量导入的原始文件',
  `detail_file` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '排查详情对应的文件地址',
  `preview_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预览地址',
  `can_retry` int DEFAULT '0' COMMENT '0-不可重试；1-可以重试',
  `paid_count` int DEFAULT '0',
  `error_count` int DEFAULT '0',
  `success_count` int DEFAULT '0',
  `updated_count` int DEFAULT '0',
  `duplicated_count` int DEFAULT '0',
  `withholding_count` int DEFAULT '0',
  `withholding_record_count` int DEFAULT '0',
  `product` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属产品',
  PRIMARY KEY (`batch_id`),
  KEY `batch_status` (`batch_id`,`status`),
  KEY `org_batch` (`org_id`,`dep_id`,`batch_type`,`business_type`),
  KEY `idx_tp_cr_org` (`batch_type`,`create_date`,`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=50039919 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_diligence`
--

DROP TABLE IF EXISTS `batch_diligence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_diligence` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` int NOT NULL,
  `diligence_id` int NOT NULL,
  `job_id` int DEFAULT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `changing_version` int DEFAULT NULL,
  `changing_detail` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `batch_id` (`batch_id`,`diligence_id`),
  KEY `job_id` (`job_id`),
  KEY `diligence_id` (`diligence_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1862445 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_job`
--

DROP TABLE IF EXISTS `batch_job`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_job` (
  `job_id` int NOT NULL AUTO_INCREMENT,
  `job_info` json NOT NULL COMMENT '类型根据 batch_type 不同可以分别映射',
  `status` int NOT NULL DEFAULT '0' COMMENT '0 待处理\n1 处理中\n2 处理成功\n3 处理失败',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `batch_id` int NOT NULL,
  `comment` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理失败的时候的错误原因',
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `error_date` datetime DEFAULT NULL,
  PRIMARY KEY (`job_id`),
  KEY `batch_id` (`batch_id`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=442189 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_match_company`
--

DROP TABLE IF EXISTS `batch_match_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_match_company` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `file_name` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_by` int NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `origin_file` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `statistics_info` json DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_match_company_item`
--

DROP TABLE IF EXISTS `batch_match_company_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_match_company_item` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` int NOT NULL,
  `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `flag` tinyint NOT NULL,
  `match_by` tinyint NOT NULL,
  `parsed_item` json DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_result`
--

DROP TABLE IF EXISTS `batch_result`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_result` (
  `result_id` int NOT NULL AUTO_INCREMENT,
  `result_type` int NOT NULL COMMENT '10 执行成功-付费\\n11 执行成功-未付费\\n12 执行成功-数据重复\\n20 执行失败 （代码执行过程中失败）\\n21 执行失败- 数据不合规\\n',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `batch_id` int NOT NULL,
  `info` json NOT NULL COMMENT '类型根据 batch_type 不同可以分别映射',
  `job_id` int NOT NULL,
  `comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注信息',
  `result_hashkey` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `result` json DEFAULT NULL COMMENT '招标排查job执行的结果',
  PRIMARY KEY (`result_id`),
  KEY `batch_id_type` (`batch_id`,`result_type`),
  KEY `job_id` (`job_id`),
  KEY `hashkey` (`result_hashkey`)
) ENGINE=InnoDB AUTO_INCREMENT=2070397 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_verification`
--

DROP TABLE IF EXISTS `batch_verification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_verification` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_id` int NOT NULL COMMENT '批次ID',
  `job_id` int NOT NULL COMMENT '任务ID',
  `verification_id` int NOT NULL COMMENT '验证记录ID',
  `paid_count` int DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `product_code` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'SAAS_PRO',
  PRIMARY KEY (`id`),
  KEY `batch_verif_key` (`batch_id`,`verification_id`),
  KEY `batch_job_key` (`job_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1604 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量验证记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `company`
--

DROP TABLE IF EXISTS `company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `econkind` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '企业类型code',
  `econkind_desc` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '企业类型中文描述',
  `province` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `district` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `industry1` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国民行业1级',
  `industry2` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国民行业2级',
  `industry3` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国民行业3级',
  `industry4` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国民行业4级',
  `registcapi` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注册资本',
  `status_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '登记状态',
  `start_date_code` datetime DEFAULT NULL COMMENT '成立时间',
  `registcapi_amount` int DEFAULT NULL COMMENT '注册资本数值',
  `credit_rate` int DEFAULT NULL,
  `econ_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '企业性质',
  `enterprise_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '机构类型code',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `list_status` int DEFAULT '2' COMMENT '上市状态:1-已上市,2-未上市',
  `reccap` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '实缴资本',
  `reccapamount` int DEFAULT NULL COMMENT '实缴资本金额数字(万元)',
  `scale` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '企业规模',
  PRIMARY KEY (`id`),
  UNIQUE KEY `company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=109572 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='第三方和黑名单关联工商信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `data_source`
--

DROP TABLE IF EXISTS `data_source`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `data_source` (
  `id` int NOT NULL AUTO_INCREMENT,
  `source_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `source_type` int NOT NULL COMMENT '1  es\\n2 api\\n3 mysql\\n4 postgres\\n5 mongodb',
  `comment` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `username` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `passwd` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `call_params` json DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dataset`
--

DROP TABLE IF EXISTS `dataset`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dataset` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `create_by` int NOT NULL,
  `org_id` int NOT NULL,
  `product` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属产品',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dataset_items`
--

DROP TABLE IF EXISTS `dataset_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dataset_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `dataset_id` int NOT NULL,
  `item_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dimension_definition`
--

DROP TABLE IF EXISTS `dimension_definition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dimension_definition` (
  `dimension_id` int NOT NULL AUTO_INCREMENT,
  `dimension_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  `extend_from` int DEFAULT NULL COMMENT '从哪个维度继承过来的， 快速基于某个维度创建新的维度的时候，会保留这个字段',
  `source` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '''数据维度的数据源类型''\n\n  /**\n   * 专业版\n   */\n  Pro = ''Pro'',\n  /**\n   * 信用用大数据\n   */\n  Credit = ''Credit'',\n  /**\n   * 企业库\n   */\n  EnterpriseLib = ''EnterpriseLib'',\n  /**\n   * 企业详情\n   */\n  CompanyDetail = ''CompanyDetail'',\n\n  /**\n   * Rover\n   */\n  Rover = ''Rover'',\n\n  /**\n   * 标讯\n   */\n  Tender = ''Tender'',\n  /**\n   * 司法案件\n   */\n  Case = ''Case'',\n\n  /**\n   * 负面新闻\n   */\n  NegativeNews = ''NegativeNews'',\n\n  /**\n   * 裁判文书\n   */\n  Judgement = ''Judgement'',\n\n  /**\n   * 税务公告\n   */\n  TaxAnnouncement = ''TaxAnnouncement'',\n\n  /**\n   * 股权出质\n   */\n  Pledge = ''Pledge'',\n  /**\n   * 风险ES\n   */\n  RiskChange = ''RiskChange'',\n  /**\n   * 特殊黑名单 仅用来详情搜索\n   */\n  SpecialBlacklist = ''SpecialBlacklist'',\n  OuterBlacklist = ''OuterBlacklist'',\n\n  /**\n   * 行政处罚\n   */\n  SupervisePunish = ''SupervisePunish'',',
  `indicator_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'generalItems' COMMENT ' /**\n   * 指标类型：关键项\n   */\n  keyItems = ''keyItems'',\n  /**\n   * 指标类型：一般项\n   */\n  generalItems = ''generalItems'',',
  `create_by` int NOT NULL,
  `update_by` int DEFAULT NULL,
  `modified_date` datetime DEFAULT NULL,
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deprecated_date` datetime DEFAULT NULL COMMENT '如果已经废弃，废弃的日期',
  `deprecate_start_date` datetime DEFAULT NULL COMMENT '如果已经进入到 废弃阶段(进入维护计划，不再更新) 日期',
  `detail_source` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '维度详情的数据来源',
  `source_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '维度数据接口地址',
  `detail_source_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据详情接口地址',
  `type_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '维度对应专业版维度code',
  PRIMARY KEY (`dimension_id`),
  KEY `index1` (`dimension_key`)
) ENGINE=InnoDB AUTO_INCREMENT=4580 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dimension_fields`
--

DROP TABLE IF EXISTS `dimension_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dimension_fields` (
  `field_id` int NOT NULL AUTO_INCREMENT,
  `input_type` int DEFAULT '1' COMMENT '0 文本框 1 下拉框单选 2 下拉多选 3 单选框 4 复选框',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `field_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `data_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字段类型，  Integer,Float,String,Date',
  `is_array` int NOT NULL DEFAULT '0' COMMENT '0 false, 1 true',
  `dimension_id` int NOT NULL,
  `deprecated_date` datetime DEFAULT NULL COMMENT '如果已经废弃，废弃的日期',
  `deprecate_start_date` datetime DEFAULT NULL COMMENT '如果已经进入到 废弃阶段(进入维护计划，不再更新) 日期',
  `options` json DEFAULT NULL COMMENT '如果是下拉框，下拉框中的选项',
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int NOT NULL DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  `field_order` int DEFAULT '0',
  `modified_date` datetime DEFAULT NULL,
  `default_value` json DEFAULT NULL COMMENT '维度属性默认值',
  `default_compare_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '默认比较类型',
  PRIMARY KEY (`field_id`),
  KEY `index1` (`dimension_id`,`field_key`)
) ENGINE=InnoDB AUTO_INCREMENT=10050 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dimension_hit_strategy`
--

DROP TABLE IF EXISTS `dimension_hit_strategy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dimension_hit_strategy` (
  `strategy_id` int NOT NULL AUTO_INCREMENT,
  `dimension_id` int NOT NULL,
  `strategy_name` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` int NOT NULL DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  `deprecated_date` datetime DEFAULT NULL COMMENT '如果已经废弃，废弃的日期',
  `deprecate_start_date` datetime DEFAULT NULL COMMENT '如果已经进入到 废弃阶段(进入维护计划，不再更新) 日期',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `modified_date` datetime DEFAULT NULL,
  `hit_strategy` json NOT NULL,
  `create_by` int NOT NULL,
  `update_by` int DEFAULT NULL,
  `template` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `extend_from` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` int DEFAULT '1' COMMENT '1 系统级别\n2 用户级别',
  `org_id` int DEFAULT '0' COMMENT '模型发布时候对应的组织',
  `published_date` datetime DEFAULT NULL COMMENT '如果已经正式发布，记录发布日期',
  `publish_by` int DEFAULT NULL,
  `strategy_role` int NOT NULL DEFAULT '1' COMMENT '维度策略角色 1 普通策略维度，排查正常计数和结果展示； 2 仅过滤策略维度，只参与指标命中的判定，不参与排查技术和结果展示;',
  PRIMARY KEY (`strategy_id`),
  KEY `dimension_id` (`dimension_id`)
) ENGINE=InnoDB AUTO_INCREMENT=253718 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dimension_hit_strategy_fields_relation`
--

DROP TABLE IF EXISTS `dimension_hit_strategy_fields_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dimension_hit_strategy_fields_relation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `strategy_id` int NOT NULL,
  `dimension_id` int NOT NULL,
  `dimension_field_id` int NOT NULL,
  `status` int NOT NULL DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  `deprecated_date` datetime DEFAULT NULL COMMENT '如果已经废弃，废弃的日期',
  `deprecate_start_date` datetime DEFAULT NULL COMMENT '如果已经进入到 废弃阶段(进入维护计划，不再更新) 日期',
  `field_value` json NOT NULL COMMENT '命中的value',
  `search_type` int NOT NULL DEFAULT '0' COMMENT '0 常规查询\n1 xx 关联关系查询\n2 yy 关联关系查询',
  `compare_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `modified_date` datetime DEFAULT NULL,
  `create_by` int NOT NULL,
  `update_by` int DEFAULT NULL,
  `category` int DEFAULT '1' COMMENT '1 系统级别\\n2 用户级别',
  `extend_from` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `org_id` int DEFAULT '0' COMMENT '模型发布时候对应的组织',
  `published_date` datetime DEFAULT NULL COMMENT '如果已经正式发布，记录发布日期',
  `publish_by` int DEFAULT NULL,
  `dimension_field_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `dimension_field_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `access_scope` int NOT NULL DEFAULT '0' COMMENT '数据状态：0-完整权限, 1-不可见不可修改',
  `options` json DEFAULT NULL COMMENT '这里可以配置选项、文本框的约束等',
  PRIMARY KEY (`id`),
  KEY `index1` (`dimension_id`,`strategy_id`,`dimension_field_id`),
  KEY `idx_strategy_id` (`strategy_id`)
) ENGINE=InnoDB AUTO_INCREMENT=625069 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `distributed_system_resource`
--

DROP TABLE IF EXISTS `distributed_system_resource`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `distributed_system_resource` (
  `id` int NOT NULL AUTO_INCREMENT,
  `resource_type` int NOT NULL COMMENT '1 模型\n2 指标\n3 维度命中规则',
  `resource_id` int NOT NULL,
  `distributed_by` int NOT NULL,
  `is_org_default` int DEFAULT '0' COMMENT '是否是组织的默认模型\\n1 是\\n0 否 默认值',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `org_id` int NOT NULL COMMENT '资源被发布到的组织',
  `product_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的产品',
  `distribute_status` int NOT NULL DEFAULT '1' COMMENT 'Disable, // 禁用\\n  Enable, // 启用\\n  Trial = 2, // 试用\\n  Deprecated = 3, // 用户自己废弃',
  `expire_date` datetime DEFAULT NULL,
  `branch_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型或者指标的 branchCode ，暂时只有模型有branchCode',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index1` (`org_id`,`resource_type`,`resource_id`),
  KEY `index2` (`resource_type`),
  KEY `index3` (`resource_id`)
) ENGINE=InnoDB AUTO_INCREMENT=29500 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `due_diligence`
--

DROP TABLE IF EXISTS `due_diligence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `due_diligence` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `score` int NOT NULL DEFAULT '0',
  `result` tinyint NOT NULL DEFAULT '0' COMMENT '0 通过\n1 风险较高\n2 慎重考虑',
  `operator` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `org_id` int NOT NULL,
  `details` json DEFAULT NULL,
  `snapshot_date` datetime DEFAULT NULL,
  `snapshot_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
  `snapshot_details` json DEFAULT NULL,
  `should_update` tinyint NOT NULL DEFAULT '0',
  `credit_rate` int DEFAULT NULL,
  `org_model_id` int NOT NULL COMMENT '排查使用的排查模型id',
  `product` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '归属产品',
  `is_first_to_org` int DEFAULT '0',
  `is_first_to_model` int DEFAULT '0',
  `paid` int DEFAULT '0',
  `model_branch_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `type` int NOT NULL DEFAULT '0' COMMENT '类别：0-风险洞察尽调，1-风险洞察监控',
  PRIMARY KEY (`id`),
  KEY `org_company_uniq` (`org_id`,`company_id`,`snapshot_id`) USING BTREE,
  KEY `idx_org_comp_ct` (`org_id`,`operator`,`create_date`,`company_id`) USING BTREE,
  KEY `snapshot_id` (`snapshot_id`) USING BTREE,
  KEY `dilligence_index` (`org_id`,`type`,`create_date`) USING BTREE,
  KEY `idx_code_comp_ct` (`model_branch_code`,`company_id`,`org_id`,`product`,`create_date`),
  KEY `idx_module_id` (`org_model_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=51878968 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `due_diligence_excludes`
--

DROP TABLE IF EXISTS `due_diligence_excludes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `due_diligence_excludes` (
  `id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `org_id` int NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dimension_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `record_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '指定维度记录的es id',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `operator` int NOT NULL,
  `dimension_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `comment` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `org_company_dimension` (`org_id`,`company_id`,`dimension_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `due_diligence_remark`
--

DROP TABLE IF EXISTS `due_diligence_remark`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `due_diligence_remark` (
  `id` int NOT NULL AUTO_INCREMENT,
  `diligence_id` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `operator` int NOT NULL,
  `details` json DEFAULT NULL,
  `due_diligence_remarkcol` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `group`
--

DROP TABLE IF EXISTS `group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `group` (
  `group_id` int NOT NULL AUTO_INCREMENT,
  `group_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parent_group_id` int DEFAULT NULL,
  `is_virtual` tinyint DEFAULT NULL COMMENT '是否是虚拟分组',
  `details_json` json DEFAULT NULL,
  `product_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属的产品',
  `risk_level` int DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_by` int NOT NULL,
  `update_by` int DEFAULT NULL,
  `status` int DEFAULT '2' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  `extend_from` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` int DEFAULT '1' COMMENT '1 系统级别\n2 用户级别',
  `model_id` int NOT NULL,
  `order` int DEFAULT '0',
  `org_id` int DEFAULT '0' COMMENT '模型发布时候对应的组织',
  `published_date` datetime DEFAULT NULL COMMENT '如果已经正式发布，记录发布日期',
  `publish_by` int DEFAULT NULL,
  PRIMARY KEY (`group_id`),
  KEY `idx_model_id` (`model_id`)
) ENGINE=InnoDB AUTO_INCREMENT=50015775 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `group_label_relation`
--

DROP TABLE IF EXISTS `group_label_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `group_label_relation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `group_id` int NOT NULL,
  `label_id` int NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`,`group_id`,`label_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `group_metric_relation`
--

DROP TABLE IF EXISTS `group_metric_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `group_metric_relation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `metrics_id` int NOT NULL,
  `group_id` int NOT NULL,
  `order` int DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  PRIMARY KEY (`id`,`group_id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_metrics_id` (`metrics_id`)
) ENGINE=InnoDB AUTO_INCREMENT=177139 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `label`
--

DROP TABLE IF EXISTS `label`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `label` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `product_code` int NOT NULL COMMENT '归属的产品',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `business_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'Common' COMMENT '0 通用的\\n1 维度标签\\n2 指标标签',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=50000000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `message`
--

DROP TABLE IF EXISTS `message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `message` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息标题',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  `org_id` int NOT NULL COMMENT '组织id',
  `user_id` int NOT NULL COMMENT '用户id',
  `product` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属产品',
  `msg_type` tinyint NOT NULL COMMENT '消息类型：1-任务提醒；2-下载提醒；',
  `object_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '对象id',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '消息状态：1-未读；2-已读；-1-删除；',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `url` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '消息链接',
  PRIMARY KEY (`id`),
  KEY `message_org_id_product_msg_type_index` (`org_id`,`product`,`msg_type`),
  KEY `message_user_id_product_msg_type_index` (`user_id`,`product`,`msg_type`)
) ENGINE=InnoDB AUTO_INCREMENT=932 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `metric_dimension_relation`
--

DROP TABLE IF EXISTS `metric_dimension_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `metric_dimension_relation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `metrics_id` int NOT NULL,
  `dimension_strategy_id` int NOT NULL,
  `priority` int NOT NULL DEFAULT '1' COMMENT '优先级',
  `order` int NOT NULL DEFAULT '1' COMMENT '排序',
  `is_pre_condition` int NOT NULL DEFAULT '0' COMMENT '是否是前置条件\n0 否\n1 是',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `template` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_dim_strategy_id` (`dimension_strategy_id`),
  KEY `idx_metrics_id` (`metrics_id`)
) ENGINE=InnoDB AUTO_INCREMENT=250854 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `metric_label_relation`
--

DROP TABLE IF EXISTS `metric_label_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `metric_label_relation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `label_id` int NOT NULL,
  `metrics_id` int NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `metrics`
--

DROP TABLE IF EXISTS `metrics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `metrics` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `risk_level` tinyint DEFAULT NULL,
  `is_veto` tinyint DEFAULT '0' COMMENT '是否具有一票否决权',
  `product_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属的产品',
  `metric_type` int NOT NULL DEFAULT '0' COMMENT '0 和 dimension 一对一\\n1 对应多个 dimension\\n2 compound  复合型 , 可能有若干个 dimension，metric 组成，暂时不开放',
  `score` int DEFAULT NULL,
  `status` int NOT NULL DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  `comment` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deprecated_date` datetime DEFAULT NULL COMMENT '如果已经废弃，废弃的日期',
  `deprecate_start_date` datetime DEFAULT NULL COMMENT '如果已经进入到 废弃阶段(进入维护计划，不再更新) 日期',
  `hit_strategy` json NOT NULL,
  `modified_date` datetime DEFAULT NULL,
  `details_json` json DEFAULT NULL,
  `create_by` int NOT NULL,
  `update_by` int DEFAULT NULL,
  `extend_from` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` int DEFAULT '1' COMMENT '1 系统级别\n2 用户级别',
  `org_id` int DEFAULT '0' COMMENT '模型发布时候对应的组织',
  `published_date` datetime DEFAULT NULL COMMENT '如果已经正式发布，记录发布日期',
  `publish_by` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=157834 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_company`
--

DROP TABLE IF EXISTS `monitor_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_company` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `org_id` int NOT NULL,
  `dep_id` int NOT NULL DEFAULT '-1' COMMENT '部门id',
  `create_by` int NOT NULL,
  `batch_id` int DEFAULT '-1',
  `status` int DEFAULT '2' COMMENT '0:等待， 1: 处理中， 2:  处理完成 3：执行错误， 4：队列中排队',
  `product_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的产品',
  `monitor_group_id` int NOT NULL,
  `related_dynamic_hash_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '监控企业的关联方发生变化时，记录关联方变化的动态的hashKey',
  `risk_level` int DEFAULT NULL COMMENT '最新一次执行监控批次是，监控公司动态中最高的等级',
  `primary_object` int NOT NULL DEFAULT '1' COMMENT '是否是主要的监控对象， 如果是被作为关联方加进来的， 这个字段赋值 0 ',
  `related_party_count` int DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index2` (`monitor_group_id`,`company_id`,`org_id`) USING BTREE,
  KEY `index1` (`org_id`,`product_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=159793 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='持续排查企业表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_company_realted_party`
--

DROP TABLE IF EXISTS `monitor_company_realted_party`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_company_realted_party` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id_primary` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_id_related` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `monitor_group_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `related_types` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` int NOT NULL DEFAULT '1',
  `org_id` int NOT NULL,
  `product` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49582 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_company_related_daily`
--

DROP TABLE IF EXISTS `monitor_company_related_daily`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_company_related_daily` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id_primary` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `related_ids` text COLLATE utf8mb4_unicode_ci,
  `monitor_group_id` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `status` int NOT NULL DEFAULT '1',
  `org_id` int NOT NULL,
  `product` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1934 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_dynamic_remark`
--

DROP TABLE IF EXISTS `monitor_dynamic_remark`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_dynamic_remark` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `dynamic_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'monitor_metrics_dynamic.uuid',
  `grade` tinyint NOT NULL DEFAULT '0' COMMENT '跟进等级 0一般, 1-重要, 2 非常重要',
  `way` tinyint NOT NULL DEFAULT '1' COMMENT '核实方式：1-无需核实, 2-电话/短信核实, 3-实地核实, 4-网络核实, 5-其他方式',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '处理结果',
  `attachments` json DEFAULT NULL COMMENT '附件信息',
  `update_by` int NOT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `org_dynamic_index` (`org_id`,`dynamic_id`)
) ENGINE=InnoDB AUTO_INCREMENT=149 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合作监控企业动态跟进';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_group`
--

DROP TABLE IF EXISTS `monitor_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_group` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `order` int NOT NULL DEFAULT '0',
  `org_id` int NOT NULL,
  `owner_id` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `changes_count` int DEFAULT '0',
  `monitor_status` int NOT NULL DEFAULT '0' COMMENT '0 待开启\n1 已开启\n2 已关闭',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `product_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的产品',
  `monitor_model_id` int DEFAULT NULL COMMENT '监控绑定的模型的ID，原则上不能为空',
  `company_count` int DEFAULT '0',
  `status` int NOT NULL DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  `push_enable` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启推送：0-不开启，1-开启',
  `update_by` int DEFAULT NULL COMMENT '更新人',
  `scope` tinyint NOT NULL DEFAULT '-1' COMMENT '监控分组的可见范围： -1-全部可见，1-指定人可见，2-指定人不可见',
  PRIMARY KEY (`id`),
  UNIQUE KEY `monitor_group_org_id_IDX` (`org_id`,`product_code`,`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3690 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_group_user`
--

DROP TABLE IF EXISTS `monitor_group_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_group_user` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
  `monitor_group_id` int NOT NULL COMMENT '监控分组 ID',
  `user_id` int NOT NULL COMMENT '用户 ID',
  `org_id` int NOT NULL,
  `product_code` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `group_user_index` (`monitor_group_id`,`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=88 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='监控分组的查看权限';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_metrics_dynamic`
--

DROP TABLE IF EXISTS `monitor_metrics_dynamic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_metrics_dynamic` (
  `id` int NOT NULL AUTO_INCREMENT,
  `metrics_id` int NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `metrics_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `risk_level` int NOT NULL,
  `risk_score` int NOT NULL,
  `monitor_group_id` int NOT NULL,
  `status` int NOT NULL DEFAULT '0' COMMENT '\n-1 第一次生成的数据，无需处理\n0  待处理\n1  已处理 ',
  `metrics_content` json DEFAULT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `risk_model_id` int NOT NULL,
  `risk_model_branch_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `diligence_id` int NOT NULL,
  `batch_id` int NOT NULL,
  `company_metrics_hash_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `org_id` int NOT NULL,
  `product_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的产品',
  `diligence_result` int NOT NULL,
  `diligence_score` int NOT NULL,
  `metric_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pre_batch_id` int NOT NULL DEFAULT '0' COMMENT '如果是两次尽调比对产生的动态，记录上次尽调batch_id',
  `unique_hashkey` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `index1` (`company_metrics_hash_key`),
  KEY `index2` (`org_id`,`product_code`,`monitor_group_id`,`risk_model_id`),
  KEY `index3` (`unique_hashkey`),
  KEY `idx_comp_metric` (`company_id`,`metrics_id`,`org_id`,`risk_model_branch_code`,`create_date`),
  KEY `idx_monitor_group_id` (`monitor_group_id`),
  KEY `idx_org_bat_pre` (`org_id`,`batch_id`,`pre_batch_id`)
) ENGINE=InnoDB AUTO_INCREMENT=992271 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `push_content`
--

DROP TABLE IF EXISTS `push_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `push_content` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key',
  `org_id` int NOT NULL COMMENT '组织ID',
  `push_rule_id` int NOT NULL COMMENT '关联的推送规则ID',
  `pushed_info_json` json DEFAULT NULL COMMENT '已经推送的信息',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int NOT NULL DEFAULT '0' COMMENT '数据状态：0-未推送, 1-已推送',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2354 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='推送内容表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `push_message_status`
--

DROP TABLE IF EXISTS `push_message_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `push_message_status` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key',
  `org_id` int NOT NULL COMMENT '组织ID',
  `push_content_id` int NOT NULL COMMENT '关联的推送内容Id',
  `method` tinyint(1) NOT NULL DEFAULT '0' COMMENT '消息类型：1-短信，2-邮件',
  `recipient` varchar(45) NOT NULL COMMENT '联系方式信息（电话号码，或者邮箱）',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `attempts` int NOT NULL DEFAULT '0' COMMENT '重试次数，最多3次',
  `status` int NOT NULL DEFAULT '0' COMMENT '发送状态：0-未发送, 1-发送',
  `error_message` text COMMENT '错误信息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4219 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='推送消息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `push_rule`
--

DROP TABLE IF EXISTS `push_rule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `push_rule` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key',
  `org_id` int NOT NULL COMMENT '组织ID',
  `type` int NOT NULL COMMENT '业务类型：1-风险尽调监控类消息',
  `business_id` int NOT NULL COMMENT '关联业务Id，目前支持分组Id',
  `rule_Json` json DEFAULT NULL COMMENT '推送的规则',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` int NOT NULL COMMENT '创建者',
  `update_by` int DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=693 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Push Rule Table';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qa_company`
--

DROP TABLE IF EXISTS `qa_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qa_company` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `econkind` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '企业类型code',
  `econkind_desc` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '企业类型中文描述',
  `province` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `district` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `industry1` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国民行业1级',
  `industry2` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国民行业2级',
  `industry3` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国民行业3级',
  `industry4` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国民行业4级',
  `registcapi` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注册资本',
  `status_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '登记状态',
  `start_date_code` datetime DEFAULT NULL COMMENT '成立时间',
  `registcapi_amount` int DEFAULT NULL COMMENT '注册资本数值',
  `credit_rate` int DEFAULT NULL,
  `econ_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '企业性质',
  `enterprise_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '机构类型code',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `list_status` int DEFAULT '2' COMMENT '上市状态:1-已上市,2-未上市',
  `reccap` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '实缴资本',
  `reccapamount` int DEFAULT NULL COMMENT '实缴资本金额数字(万元)',
  `scale` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '企业规模',
  `org_id` int NOT NULL,
  `product` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属产品',
  `user_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=87581 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='第三方和黑名单关联工商信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qa_company_annotated_data_test`
--

DROP TABLE IF EXISTS `qa_company_annotated_data_test`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qa_company_annotated_data_test` (
  `id` int NOT NULL AUTO_INCREMENT,
  `qa_company_item_id` int NOT NULL,
  `result_group` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Metric\nHitStrategy\nModel',
  `field_key_l1` varchar(220) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用来和 指标或者指标命中策略对应的key\n\n暂时可以考虑用 指标名字，  指标名字_策略名字 这种相对唯一的标记',
  `field_key_l2` varchar(220) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类似 field_key_l1 的冗余字段',
  `result_type` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '以公司为主体， 如果公司本身应该命中指标，\\n如果指标命中了并且命中的指标详情如条数等都正确，那就是 TP\\n如果指标命中了，但是详情等地方有不符的内容，  那就是 FP\\n\\n如果指标确实不应该命中并且事实也是没命中， 那就是  TN\\n如果应该命中但是没命中，那就是 FN',
  `model_id` int DEFAULT NULL,
  `model_branch_code` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `modify_date` datetime DEFAULT NULL COMMENT '人工修改的时间',
  `result_type_before` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `annotated_result_hashkey` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `index1` (`model_branch_code`,`annotated_result_hashkey`),
  KEY `index2` (`model_id`,`qa_company_item_id`)
) ENGINE=InnoDB AUTO_INCREMENT=145 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qa_company_label`
--

DROP TABLE IF EXISTS `qa_company_label`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qa_company_label` (
  `id` int NOT NULL AUTO_INCREMENT,
  `qa_company_item_id` int NOT NULL,
  `qa_label_id` int NOT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `company_id` (`qa_company_item_id`),
  KEY `label_id` (`qa_label_id`)
) ENGINE=InnoDB AUTO_INCREMENT=225 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qa_dataset`
--

DROP TABLE IF EXISTS `qa_dataset`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qa_dataset` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `create_by` int NOT NULL,
  `org_id` int NOT NULL,
  `product` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属产品',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` int NOT NULL DEFAULT '1' COMMENT '0 禁用\n1 可用',
  `ref_monitor_group_id` int DEFAULT NULL COMMENT '给dataset 添加 items 的时候 会创建(如果不存在) monitor_group 并添加company',
  `qa_type` int DEFAULT '0' COMMENT '0 监控\\n1 尽调',
  `user_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id_unique` (`org_id`,`product`,`name`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qa_dataset_items`
--

DROP TABLE IF EXISTS `qa_dataset_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qa_dataset_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `dataset_id` int NOT NULL,
  `item_id` int DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `index1` (`dataset_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1490 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qa_label`
--

DROP TABLE IF EXISTS `qa_label`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qa_label` (
  `id` int NOT NULL AUTO_INCREMENT,
  `label_name` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `org_id` int NOT NULL,
  `product` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属产品',
  `user_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id_unqiue` (`org_id`,`product`,`label_name`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qa_task`
--

DROP TABLE IF EXISTS `qa_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qa_task` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_name` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `comment` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `qa_type` int NOT NULL DEFAULT '0' COMMENT '0 监控\n1 尽调',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '任务状态：0-未处理，1-执行中, 2-已完成，3-出错',
  `dataset_id` int NOT NULL COMMENT '测试集的id',
  `dataset_size` int DEFAULT NULL,
  `create_username` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `error` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `model_id` int DEFAULT NULL,
  `model_branch_code` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `org_id` int NOT NULL,
  `product` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属产品',
  `ref_batch_id` int DEFAULT NULL COMMENT '关联的batchId 或者 monitorGroupId',
  `task_type` int NOT NULL DEFAULT '0' COMMENT '0 测试任务\n1 评价任务',
  `ref_batch_id_base` int DEFAULT NULL COMMENT '关联的batchId 或者 monitorGroupId',
  `base_model_id` int DEFAULT NULL COMMENT '用作对比基准线的模型',
  `finished_batch_count` int DEFAULT '0' COMMENT '用作对比基准线的模型',
  `qa_taskcol` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id_unqiue` (`org_id`,`product`,`task_name`)
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qa_task_result`
--

DROP TABLE IF EXISTS `qa_task_result`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qa_task_result` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL,
  `result_type` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '以公司为主体， 如果公司本身应该命中指标，\n如果指标命中了并且命中的指标详情如条数等都正确，那就是 TP\n如果指标命中了，但是详情等地方有不符的内容，  那就是 FP\n\n如果指标确实不应该命中并且事实也是没命中， 那就是  TN\n如果应该命中但是没命中，那就是 FN',
  `task_item_id` int NOT NULL,
  `result_group` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Metric\\\\nHitStrategy\\\\nModel',
  `field_key_l1` varchar(220) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'field_key 可以简单的以同一个模型为约束条件下，指标或者指标策略的名字来做唯一值。  field_key_2 则标识全局情况下的唯一标识，这个暂时是冗余字段\n测试目标的字段或者维度， 例如唯一能标识一个指标的id（同一个模型下面的指标名称？动态生成的featureKeyL1?）, 例如能唯一标识一个 指标命中测试的id（同一个模型如指标命中策略名称？）\\\\\\\\n如果是模型评价任务，暂时只想到一个值，比如 是否准确识别到风险',
  `field_key_l2` varchar(220) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'field_key 可以简单的以同一个模型为约束条件下，指标或者指标策略的名字来做唯一值。  field_key_2 则标识全局情况下的唯一标识，这个暂时是冗余字段\n测试目标的字段或者维度， 例如唯一能标识一个指标的id（同一个模型下面的指标名称？动态生成的featureKeyL1?）, 例如能唯一标识一个 指标命中测试的id（同一个模型如指标命中策略名称？）\\\\\\\\n如果是模型评价任务，暂时只想到一个值，比如 是否准确识别到风险',
  `model_branch_code` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` int NOT NULL COMMENT '指标ID\\\\n指标命中规则ID\\\\n模型ID',
  `metrics_id` int DEFAULT NULL COMMENT '指标ID\\\\n指标命中规则ID\\\\n模型ID',
  `strategy_id` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `result_hashkey` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `annotated_result_hashkey` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `task_item_id` (`task_item_id`),
  KEY `index1` (`model_id`,`field_key_l1`,`task_item_id`)
) ENGINE=InnoDB AUTO_INCREMENT=578729 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qa_task_result_analyzed`
--

DROP TABLE IF EXISTS `qa_task_result_analyzed`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qa_task_result_analyzed` (
  `id` int NOT NULL AUTO_INCREMENT,
  `model_id` int NOT NULL COMMENT '指标ID\\\\\\\\n指标命中规则ID\\\\\\\\n模型ID',
  `task_id` int NOT NULL,
  `result_group` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Metric\\\\\\\\nHitStrategy\\\\\\\\nModel',
  `field_key_l1` varchar(220) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'field_key 可以简单的以同一个模型为约束条件下，指标或者指标策略的名字来做唯一值。  field_key_2 则标识全局情况下的唯一标识，这个暂时是冗余字段\\n测试目标的字段或者维度， 例如唯一能标识一个指标的id（同一个模型下面的指标名称？动态生成的featureKeyL1?）, 例如能唯一标识一个 指标命中测试的id（同一个模型如指标命中策略名称？）\\\\\\\\\\\\\\\\n如果是模型评价任务，暂时只想到一个值，比如 是否准确识别到风险',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `tp_count` int NOT NULL,
  `tn_count` int NOT NULL,
  `fp_count` int NOT NULL,
  `fn_count` int NOT NULL,
  `recall` float DEFAULT NULL,
  `accuracy` float DEFAULT NULL,
  `f1_score` float DEFAULT NULL,
  `precision` float DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `index1` (`task_id`,`model_id`,`field_key_l1`)
) ENGINE=InnoDB AUTO_INCREMENT=7556 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `risk_model`
--

DROP TABLE IF EXISTS `risk_model`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `risk_model` (
  `model_id` int NOT NULL AUTO_INCREMENT,
  `model_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `product_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的产品',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `score_strategy` int DEFAULT '1' COMMENT '1 默认方式 - 分数加减\n2 自定义方式 x\n3 自定义方式 y',
  `version_major` int DEFAULT '1',
  `version_minor` int DEFAULT '0',
  `version_patch` int DEFAULT '0',
  `status` int DEFAULT '0' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  `published_date` datetime DEFAULT NULL COMMENT '如果已经正式发布，记录发布日期',
  `deprecated_date` datetime DEFAULT NULL COMMENT '如果已经废弃，废弃的日期',
  `deprecate_start_date` datetime DEFAULT NULL COMMENT '如果已经进入到 废弃阶段(进入维护计划，不再更新) 日期',
  `modified_date` datetime DEFAULT NULL,
  `publish_by` int DEFAULT NULL,
  `create_by` int NOT NULL,
  `update_by` int DEFAULT NULL,
  `published_details` json DEFAULT NULL COMMENT '最终生成的模型json',
  `model_type` int NOT NULL DEFAULT '1' COMMENT '1 风险模型\n2 最终受益人模型\n3 实控人模型',
  `category` int DEFAULT '1' COMMENT '1 系统模型\n2 用户模型',
  `org_id` int DEFAULT '0' COMMENT '模型发布时候对应的组织',
  `extend_from` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `result_setting` json DEFAULT NULL COMMENT '尽调结果等级设置',
  `branch_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型的分组编码\\\\n同一个编码的模型，认为是同一个模型的不同版本。 ',
  `branch_tier` int NOT NULL DEFAULT '0' COMMENT '0 根目录级别模型\\\\\\\\n1 子目录级别模型',
  `branch_count` int NOT NULL DEFAULT '1',
  PRIMARY KEY (`model_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3794 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `risk_model_group_relation`
--

DROP TABLE IF EXISTS `risk_model_group_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `risk_model_group_relation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `group_id` int NOT NULL,
  `model_id` int NOT NULL,
  `order` int DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=123 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ryg_passage_company_wgcl_incr`
--

DROP TABLE IF EXISTS `ryg_passage_company_wgcl_incr`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ryg_passage_company_wgcl_incr` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
  `type` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类型',
  `markedman` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处罚对象',
  `markedmankey` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处罚对象key',
  `markedmanorg` int NOT NULL DEFAULT '0',
  `disposition` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处分类型',
  `violation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '违规行为',
  `punishmentmeasure` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处分措施',
  `processman` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处理人',
  `punishmentamount` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处罚金额',
  `publicdate` datetime NOT NULL COMMENT '公告日期',
  `isadd` int DEFAULT NULL COMMENT '有效标识:1-新增；0-更新；-1-删除',
  `updatedtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `keyno` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '企业id',
  `relatedtype` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '关联类型，多个逗号分隔（1法人，2历史法人，3主要人员，4历史主要人员，5股东，6历史股东）',
  `job` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '职务（多个逗号分隔）',
  PRIMARY KEY (`id`),
  KEY `idx_markedmankey_isadd` (`markedmankey`,`isadd`),
  KEY `idx_keyno_isadd` (`keyno`,`isadd`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='上市违规处理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_socket`
--

DROP TABLE IF EXISTS `user_socket`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_socket` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `org_id` int NOT NULL,
  `product` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属产品',
  `session_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `socket_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `status` int DEFAULT '1' COMMENT '1 在线\n2 离线',
  PRIMARY KEY (`id`),
  KEY `org_id` (`org_id`),
  KEY `session_id` (`session_id`),
  KEY `socket_id` (`socket_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=206433 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `verification_batch_import`
--

DROP TABLE IF EXISTS `verification_batch_import`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `verification_batch_import` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `org_id` int NOT NULL COMMENT '组织ID',
  `file_name` varchar(300) DEFAULT NULL COMMENT '人企核验信息文件名称',
  `create_by` int NOT NULL COMMENT '创建人ID',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `origin_file` varchar(200) DEFAULT NULL COMMENT '原文件地址',
  `statistics_info` json DEFAULT NULL COMMENT '识别统计信息',
  `product_code` varchar(10) NOT NULL DEFAULT 'SAAS_PRO',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=509 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='人企核导入批次记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `verification_batch_import_item`
--

DROP TABLE IF EXISTS `verification_batch_import_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `verification_batch_import_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_id` int NOT NULL COMMENT '批次ID',
  `comp_name` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '企业名称',
  `comp_id` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '企业ID',
  `comp_credit_code` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '企业统一社会信用代码',
  `person_name` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '人员姓名',
  `person_idcard` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'MD5加密后的证件号码',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `flag` tinyint NOT NULL COMMENT '匹配状态：1-匹配成功, 21-匹配失败(未匹配到企业名称), 22-匹配失败(企业名称和统一社会信用代码不一致), 3-不支持',
  `match_by` tinyint NOT NULL COMMENT '匹配方式：0-名称匹配 1-曾用名匹配',
  `parsed_item` json DEFAULT NULL COMMENT 'excel解析后的条目数据',
  `product_code` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'SAAS_PRO',
  PRIMARY KEY (`id`),
  KEY `idx_batch_id` (`batch_id`) COMMENT '基于导入批次记录，优化关联查询性能'
) ENGINE=InnoDB AUTO_INCREMENT=3681 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人企核验批量导入明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `verification_detail`
--

DROP TABLE IF EXISTS `verification_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `verification_detail` (
  `verification_detail_id` int NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `verification_id` int NOT NULL COMMENT '关联的核验记录ID，关联 verification_record 表的 verification_id',
  `comp_type` tinyint NOT NULL COMMENT '企业类型，取值说明：1-TARGET(目标企业)、2-RELATED(关联企业)',
  `comp_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业名称，用于搜索',
  `comp_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业Id',
  `comp_credit_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业统一社会信用代码，用于搜索',
  `comp_regist_status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业登记状态',
  `person_role` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人员角色，逗号隔开',
  `verification_result` tinyint DEFAULT NULL COMMENT '核验结果，取值说明：默认空，1-MATCH(匹配)、2-UNMATCH(不匹配)',
  `relation_path` json DEFAULT NULL COMMENT '关键路径，核验当时的关键路径快照',
  `create_by` int NOT NULL COMMENT '创建人',
  `update_by` int NOT NULL COMMENT '更新人',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '数据状态，默认1，1-有效，0-无效',
  `org_id` int NOT NULL,
  `product_code` varchar(10) NOT NULL DEFAULT 'SAAS_PRO',
  PRIMARY KEY (`verification_detail_id`),
  KEY `idx_verification_id` (`verification_id`) COMMENT '基于核验记录ID的索引，优化关联查询性能'
) ENGINE=InnoDB AUTO_INCREMENT=30332 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='核验结果详情表，存储人企核验结果的详细信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `verification_record`
--

DROP TABLE IF EXISTS `verification_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `verification_record` (
  `verification_id` int NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `verification_type` int NOT NULL COMMENT '核验类型，取值说明：1-REGULAR(常规核验)、2-DEEP(深度核验)',
  `comp_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业名称，用于搜索',
  `comp_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业Id',
  `comp_credit_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业统一社会信用代码，用于搜索',
  `person_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '人员姓名',
  `person_idcard_encrypted` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'MD5加密后的证件号码',
  `person_key_no` varchar(45) DEFAULT NULL COMMENT '人员keyNo',
  `person_idcard_mask` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '打码后的证件号码',
  `person_role` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人员角色，多角色用逗号隔开',
  `verification_result` tinyint DEFAULT NULL COMMENT '核验结果，取值说明：10只做常规核验;11指定目标企业匹配，关联方企业匹配;12指定目标企业匹配，关联方企业不匹配;20只做深度核验;21指定目标企业不匹配，关联方企业匹配;22指定目标企业不匹配，关联方企业不匹配，默认空',
  `create_by` int NOT NULL COMMENT '创建人',
  `update_by` int NOT NULL COMMENT '更新人',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '数据状态，默认1，1-有效，0-无效',
  `org_id` int NOT NULL,
  `product_code` varchar(10) NOT NULL DEFAULT 'SAAS_PRO',
  PRIMARY KEY (`verification_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1672 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='核验记录表，用于记录人企核验的相关信息';
/*!40101 SET character_set_client = @saved_cs_client */;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-29 15:34:35
