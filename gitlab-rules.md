这个是我项目现在的 gitlab yaml 文件， 有以下特点：

1. 跟 unittest 有关的 job， 比如 继承了 .unittest_reporter 的 job， 都会使用 idc_runner_ssh 这个 tag, 这是一个 shell 的 gitlab-runner
2. package.json 中有个 yarn unitest:prepare 命令，执行这个命令的时候会去执行 scripts/test 下面的 parepareForUnittest.sh
   1. 这个脚本会去启动 mysql, redis, es,
   2. 启动 mysql 时候会自动挂载 init_sql/下面的内容
   3. 启动 es 的时候会自动把 es/下面对应的 template 先加载进去，然后再创建一些初始化的索引， 为后续做准备
3. unittest_reporter 的 before_script 是给 shell runner 做一些 运行 test 之前的准备工作 和 after_script 会在 test 执行结束之后，把报告提取一些信息，然后把报告分别推送到 dingtalk 和 kpi 考核系统

以上是特点， 现在我需要做这些事情：

1. 不使用 shell runner 了， 想要切换成 docker runner
2. 使用 docker executor 的话，启动的时候原来 parepareForUnittest.sh 里面做的事情可以需要通过 类似 unit_test:
   stage: test
   image: node:18
   services:
   - name: mysql:8.0
     alias: db
     # 关键配置：挂载初始化脚本
     volumes:
     - $CI_PROJECT_DIR/db/init.sql:/docker-entrypoint-initdb.d/init.sql
       variables:
       MYSQL_ROOT_PASSWORD: "testroot" # 必须设置密码
       MYSQL_DATABASE: "testdb" # 创建默认数据库
   - name: redis:alpine
     alias: cache

这种 service 的方式(docker in docker) 来启动 ， 同时注意启动的时候需要挂载 es/ 下面的 template 文件， 同时需要创建一些初始化的索引 以及 mysql 的初始化脚本

3.  unittest_reporter 的 before_script 应该很多动作都不需要了，但是 after_script 有很多拼接 消息的地方，这些地方有很多自定义的函数，这些函数原来也都是针对 centos7 的 shell runner 写的，现在在 ubuntu 22 也试过，也都是工作的， 但是现在要切换 docker runner 了，那么可能就需要关注一下兼容性，你帮我也可以优化一下。 另外强调一下， 我们用 docker exector 来跑单元测试的话，肯定要使用 node 环境来做，我建议用 node 18 或者 20 来做，同时关注到 after script 还有对 git 的一些使用以及 curl 等， 麻烦帮忙都看一下。
4.  另外请注意 cp -r ${CI_PROJECT_DIR}/coverage $REPORT_DIR; 类似这种代码，它的作用是把 jest 生成的报告， 复制到了宿主机的文件夹， 那边启动了 一个 python 的 http server， 后续可以通过 类似 link="http://$host_ip:8000/$REPORT_PATH/coverage"; 这种让用户可以访问
    以上是我的介绍和要求，帮忙优化成使用 docker executor 并且功能比现在更强大，谢谢
