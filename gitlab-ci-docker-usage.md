# GitLab CI Docker 模板使用说明

## 概述

本项目已经重构了 GitLab CI 配置，将其分为两个部分：

1. ************************.yml** - 通用模板，专注于测试报告处理
2. **.gitlab-ci.yml** - 项目特定配置，包含 services 和环境准备

## 架构设计

### 模板职责分离

- ************************.yml**: 
  - 提供基础的 Docker 环境配置
  - 专注于测试执行后的报告处理和通知
  - 包含钉钉通知、SonarQube 集成等通用功能
  - 可以被多个项目复用

- **.gitlab-ci.yml**:
  - 定义项目特定的 services（MySQL、Redis、Elasticsearch）
  - 包含环境准备脚本（ES 模板加载、索引创建等）
  - 定义具体的测试任务

## 主要改进

### 1. Services 配置移到项目文件
```yaml
# .gitlab-ci.yml 中的 services 配置
.project_unittest_base:
  extends: .unittest_reporter
  services:
    - name: mysql:8.0
      alias: mysql
      # ... MySQL 配置
    - name: redis:7.2
      alias: redis
      # ... Redis 配置
    - name: elasticsearch:7.17.11
      alias: elasticsearch
      # ... ES 配置
```

### 2. 环境准备脚本项目化
```yaml
# .gitlab-ci.yml 中的 before_script
before_script:
  # 基础准备（来自模板）
  - echo "开始单元测试报告处理准备"
  # 项目特定的环境准备
  - yarn install --ignore-optional
  - # ES 模板加载
  - # 索引创建
  - # 服务验证
```

### 3. 测试任务使用项目配置
```yaml
# 测试任务继承项目配置而不是模板
unittest_commit:
  extends:
    - .project_unittest_base  # 而不是 .unittest_reporter
```

## Docker 环境优化

### 1. 基础镜像
- 使用 `node:18-bullseye-slim` 作为基础镜像
- 预装必要的系统工具：git、curl、bc、netcat-openbsd

### 2. 兼容性改进
- 所有 shell 脚本都经过 Docker 环境测试
- 使用 `netcat-openbsd` 替代 `nc` 确保兼容性
- 优化了工具安装顺序，避免重复安装

### 3. 报告处理
- 保持原有的报告生成和推送功能
- 优化了 coverage 目录处理
- 改进了钉钉通知格式

## 使用方法

### 1. 新项目使用
```yaml
# 在新项目的 .gitlab-ci.yml 中
include:
  local: **********************.yml

# 定义项目特定配置
.project_unittest_base:
  extends: .unittest_reporter
  services:
    # 定义你的 services
  before_script:
    # 继承模板的基础准备
    # 添加项目特定的环境准备

# 定义测试任务
unittest_commit:
  extends:
    - .project_unittest_base
```

### 2. 现有项目迁移
1. 将 services 配置从模板移到项目文件
2. 将环境准备脚本移到项目文件
3. 更新测试任务继承关系
4. 测试验证

## 验证方法

运行验证脚本：
```bash
./validate-gitlab-ci.sh
```

## 注意事项

1. **环境变量**: 确保所有必要的环境变量都在 .gitlab-ci.yml 中定义
2. **文件路径**: 注意 ES 模板文件路径是否正确
3. **服务别名**: 确保服务别名与连接配置一致
4. **依赖安装**: 确保 yarn install 在正确的时机执行

## 故障排除

### 常见问题

1. **服务连接失败**
   - 检查服务别名配置
   - 验证端口映射
   - 确认服务启动顺序

2. **ES 模板加载失败**
   - 检查模板文件路径
   - 验证 ES 服务状态
   - 确认网络连接

3. **依赖安装失败**
   - 检查网络连接
   - 验证 package.json
   - 确认 yarn 版本

## 性能优化建议

1. 使用缓存加速依赖安装
2. 并行执行独立的准备步骤
3. 优化 Docker 镜像大小
4. 合理设置超时时间
