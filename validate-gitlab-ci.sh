#!/bin/bash

# GitLab CI 配置验证脚本
# 用于验证 GitLab CI 配置的语法和结构

echo "开始验证 GitLab CI 配置..."

# 检查必要文件是否存在
echo "1. 检查文件存在性..."
if [ ! -f ".gitlab-ci.yml" ]; then
    echo "❌ .gitlab-ci.yml 文件不存在"
    exit 1
fi

if [ ! -f "gitlab-template-docker.yml" ]; then
    echo "❌ gitlab-template-docker.yml 文件不存在"
    exit 1
fi

echo "✅ 必要文件存在"

# 检查 YAML 语法
echo "2. 检查 YAML 语法..."
if command -v python3 &> /dev/null; then
    python3 -c "
import yaml
import sys

try:
    with open('.gitlab-ci.yml', 'r') as f:
        yaml.safe_load(f)
    print('✅ .gitlab-ci.yml 语法正确')
except yaml.YAMLError as e:
    print(f'❌ .gitlab-ci.yml 语法错误: {e}')
    sys.exit(1)

try:
    with open('gitlab-template-docker.yml', 'r') as f:
        yaml.safe_load(f)
    print('✅ gitlab-template-docker.yml 语法正确')
except yaml.YAMLError as e:
    print(f'❌ gitlab-template-docker.yml 语法错误: {e}')
    sys.exit(1)
"
else
    echo "⚠️  Python3 未安装，跳过 YAML 语法检查"
fi

# 检查关键配置
echo "3. 检查关键配置..."

# 检查是否有 services 配置在项目文件中
if grep -q "services:" .gitlab-ci.yml; then
    echo "✅ .gitlab-ci.yml 包含 services 配置"
else
    echo "❌ .gitlab-ci.yml 缺少 services 配置"
fi

# 检查模板是否不包含 services
if grep -q "services:" gitlab-template-docker.yml; then
    echo "⚠️  gitlab-template-docker.yml 仍包含 services 配置（应该移除）"
else
    echo "✅ gitlab-template-docker.yml 不包含 services 配置"
fi

# 检查是否有测试任务
if grep -q "unittest_commit:" .gitlab-ci.yml; then
    echo "✅ .gitlab-ci.yml 包含测试任务"
else
    echo "❌ .gitlab-ci.yml 缺少测试任务"
fi

# 检查 Docker 镜像配置
if grep -q "node:18" gitlab-template-docker.yml; then
    echo "✅ 使用 Node.js 18 镜像"
else
    echo "⚠️  未找到 Node.js 18 镜像配置"
fi

echo "4. 验证完成！"
echo ""
echo "配置说明："
echo "- gitlab-template-docker.yml: 专注于测试报告处理"
echo "- .gitlab-ci.yml: 包含项目特定的 services 和环境准备"
echo "- 测试任务继承 .project_unittest_base 而不是 .unittest_reporter"
