import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { Cacheable } from 'type-cacheable';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { SearchCreditRequest } from './model/SearchCreditRequest';
import { flatten, map, union } from 'lodash';
import { CompanyVerifiedPersonRequest } from './model/CompanyVerifiedPersonRequest';
import { RelatedRiskRequest } from './model/RelatedRiskRequest';
import * as moment from 'moment';
import { ConfigService } from '../../libs/config/config.service';
import { HttpUtilsService } from '../../libs/config/httputils.service';
import { TargetScopeEnums } from '../../libs/enums/dimension/FieldValueEnums';

@Injectable()
export class CompanyDetailService {
  private readonly logger: Logger = QccLogger.getLogger(CompanyDetailService.name);

  constructor(private readonly configService: ConfigService, private readonly redisService: RedisService, private readonly httpUtils: HttpUtilsService) {
    //@ts-ignore useIoRedisAdapter(this.redisService.getClient());
  }

  @Cacheable({ ttlSeconds: 300 })
  public async GetTechRate(keyNo: string) {
    const url = `${this.configService.proxyServer.dataService}/api/ECILocal/GetTechRate`;
    return this.httpUtils.postRequest(url, { keyNo });
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsShiXin(detailId: string) {
    const url = `${this.configService.proxyServer.dataService}/api/Court/GetShiXinDetail`;
    return this.httpUtils.getRequest(url, { shiXinId: detailId, isB: true });
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsAssistance(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/QccSearch/Detail/Assistance`;
    data.isB = true;
    return this.httpUtils.getRequest(url, data);
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsPledge(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/QccDetail/Pledge/DetailV2`;
    return this.httpUtils.getRequest(url, { pledgeId: data.pledgeId });
  }

  /*** 股权质押* @param data* @returns*/
  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsMonitorEquityPawn(id: string) {
    const url = `${this.configService.proxyServer.dataService}/api/QccDetail/Detail/PledgeV2`;
    return this.httpUtils.postRequest(url, { id });
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsBankRuptcy(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/QccDetail/BankRuptcy/Detail`;
    return this.httpUtils.getRequest(url, data);
  }

  /*** 被执行人详情* @param data*/
  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsZhiXing(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/Court/GetZhiXingDetail`;
    //isB标识来自B端请求
    return this.httpUtils.getRequest(url, { zhiXingId: data.id, isB: true });
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsNotAllowEntry(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/QccSearch/List/GetDetailOfNotAllowedEntry`;
    return this.httpUtils.getRequest(url, data);
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsTaxNotice(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/Tax/GetDetailOfOweNotice`;
    data.isB = true;
    return this.httpUtils.getRequest(url, data);
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsEnvPenalty(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/QccDetail/AdminPenalty/DetailNew`;
    return this.httpUtils.getRequest(url, data);
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsLandMortgage(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/LandMgmt/GetDetailsOfMortgage`;
    return this.httpUtils.getRequest(url, data);
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsMpledge(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/ECILocal/GetMPledgeDetail`;
    data.isB = true;
    return this.httpUtils.getRequest(url, data);
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsTax(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/Tax/GetIllegalDetail`;
    return this.httpUtils.getRequest(url, data);
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsSimpleCancellation(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/QccSearch/Detail/SimpleCancellation`;
    return this.httpUtils.getRequest(url, data);
  }

  /*** 注销备案列表* @param data* @returns*/
  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsMonitorCancellationOfFiling(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/QccSearch/List/Enliq`;
    return this.httpUtils.getRequest(url, { keyNo: data.keyNo, isRiskScan: true, isValid: data?.isValid ?? 1 });
  }

  /*** 债券违约详情* @param id 详情id*/
  @Cacheable({ ttlSeconds: 300 })
  public async companyDetailsBond(id: string) {
    const url = `${this.configService.proxyServer.dataService}/api/FinancialInfo/Bond/BondDefaultCourse`;
    return this.httpUtils.postRequest(url, { relatedSec: id });
  }

  /*** 破产重整研报详情* @param id 详情id*/
  @Cacheable({ ttlSeconds: 300 })
  public async companyDetailsBankRuptcyAnnouncement(id: string) {
    const url = `${this.configService.proxyServer.dataService}/api/QccDetail/BankRuptcy/AnnouncementDetail`;
    return this.httpUtils.getRequest(url, { id });
  }

  /*** 根据ids 获取司法案件列表* @param data*/
  @Cacheable({ ttlSeconds: 300 })
  public async caseListByIds(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/Risk/GetCaseListByIds`;
    return this.httpUtils.postRequest(url, { ids: data.ids.join(','), keyNo: data?.keyNo, isB: true });
  }

  /*** 获取产品质量问题-双随机检查详情* @param data*/
  @Cacheable({ ttlSeconds: 300 })
  public async doubleRandomCheckList(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/QccSearch/List/DoubleRandomCheck`;
    return this.httpUtils.getRequest(url, data);
  }

  /*** 获取产品质量问题-药品抽检详情* @param data*/
  @Cacheable({ ttlSeconds: 300 })
  public async getMedicineDetail(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/QccDetail/Drug/SpotCheck`;
    return this.httpUtils.getRequest(url, data);
  }

  /*** 获取产品质量问题-产品抽查详情* @param data*/
  @Cacheable({ ttlSeconds: 300 })
  public async getProductcheckedDetail(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/QccDetail/ProductCheckInfo`;
    return this.httpUtils.postRequest(url, data);
  }

  /*** 获取税务非正常户详情 * @param data*/
  @Cacheable({ ttlSeconds: 300 })
  public async getTaxUnnormals(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/Risk/GetTaxUnnormals`;
    return this.httpUtils.postRequest(url, data);
  }

  /*** 票据违约详情* @param data*/
  @Cacheable({ ttlSeconds: 300 })
  public async getRiskBillDefaultDetail(id: string) {
    const url = `${this.configService.proxyServer.dataService}/api/Risk/BillDefaultDetail`;
    return this.httpUtils.postRequest(url, { id });
  }

  /*** 国央企采购黑名单* @param data*/
  @Cacheable({ ttlSeconds: 300 })
  public async getRiskGovProcurementIllegalDetail(id: string) {
    const url = `${this.configService.proxyServer.dataService}/api/QccDetail/BlackList/Detail`;
    return this.httpUtils.getRequest(url, { id });
  }

  /*** 税务催缴公告详情* @param data*/
  @Cacheable({ ttlSeconds: 300 })
  public async getTaxCallNoticeDetail(id: string) {
    const url = `${this.configService.proxyServer.dataService}/api/QccDetail/GovNotice/Detail`;
    return this.httpUtils.getRequest(url, { id });
  }

  /*** 获取终本案件详情* @param data*/
  @Cacheable({ ttlSeconds: 300 })
  public async getEndExecutionCaseDetail(id: string) {
    const url = `${this.configService.proxyServer.dataService}/api/Court/GetEndExecutionCaseDetail`;
    return this.httpUtils.getRequest(url, { id });
  }

  /*** 土地抵押详情* @param id*/
  @Cacheable({ ttlSeconds: 300 })
  public async getDetailsOfMortgage(id: string) {
    const url = `${this.configService.proxyServer.dataService}/api/LandMgmt/GetDetailsOfMortgage`;
    return this.httpUtils.getRequest(url, { id });
  }

  /*** 土地公示详情* @param id*/
  @Cacheable({ ttlSeconds: 300 })
  public async getDetailsOfPublicity(id: string) {
    const url = `${this.configService.proxyServer.dataService}/api/LandMgmt/GetDetailsOfPublicity`;
    return this.httpUtils.getRequest(url, { id });
  }

  /*** 对外担保详情* @param companyCode: 股票代码* @param no：详情id*/
  @Cacheable({ ttlSeconds: 300 })
  public async getDuiWaiDetail(companyCode: string, no: string) {
    const url = `${this.configService.proxyServer.dataService}/api/CompanyIPO/GetDuiWaiDetail`;
    return this.httpUtils.getRequest(url, { companyCode, no });
  }

  /*** 获取公司风险详情* @param riskId*/
  @Cacheable({ ttlSeconds: 300 })
  public async getECIRiskDetail(riskId: string) {
    const url = `${this.configService.proxyServer.riskService}/Risk/getECIRiskDetail`;
    return this.httpUtils.getRequest(url, { riskId });
  }

  /*** 获取行政处罚新详情* @param riskId*/
  @Cacheable({ ttlSeconds: 300 })
  public async getAdminPenaltyDetail(riskId: string) {
    const url = `${this.configService.proxyServer.dataService}/api/QccDetail/AdminPenalty/DetailNew`;
    return this.httpUtils.getRequest(url, { id: riskId });
  }

  /*** 获取公司风险详情* @param companyCode: 股票代码*/
  @Cacheable({ ttlSeconds: 300 })
  public async getRiskDetailV2(riskId: string) {
    const url = `${this.configService.proxyServer.riskService}/Risk/GetRiskDetailV2`;
    return this.httpUtils.getRequest(url, { riskId });
  }

  /**
   * 获取关联方相关风险(关联方维度)
   * @param param RelatedRiskRequest
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getRelatedRiskV3(param: RelatedRiskRequest) {
    const url = `${this.configService.proxyServer.riskService}/RiskScan/RelatedRiskV3`;
    return this.httpUtils.postRequest(url, param);
  }

  /**
   * 获取社会组织详情
   * @param companyId
   */
  @Cacheable({ ttlSeconds: 300 })
  public async organismDetailsQcc(companyId: string) {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/Organism/GetDetailV2', {
        keyNo: companyId,
      });
    } catch (e) {
      this.logger.error(`http Get Organism/GetDetailV2 err:`, e);
      return null;
    }
  }

  /**
   * 获取资质证书情况
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCompanyCertificationSummary(params: Record<string, any>) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/IPR/Certification/Summary', params);
    } catch (e) {
      this.logger.error(`http getCompanyCertificationSummary err:`, e);
      return null;
    }
  }

  /**
   * 获取资质证书列表
   * @param param
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCompanyCertificationList(param: Record<string, any>) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/IPR/Certification/List', param);
    } catch (e) {
      this.logger.error(`http getCompanyCertificationList err:`, e);
      return null;
    }
  }

  /**
   * 获取证书详情
   * @param id
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCompanyCertificationDetail(id: string) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/IPR/Certification/Detail', {
        id,
      });
    } catch (e) {
      this.logger.error(`http getCompanyCertificationDetail err:`, e);
      return null;
    }
  }

  /**
   * 信用评价汇总
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCompanyCreditSummary(keyNo: string) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/ECILocal/Credit/Summary', {
        keyNo,
      });
    } catch (e) {
      this.logger.error(`http getCompanyCreditSummary err:`, e);
      return null;
    }
  }

  /**
   * 工商高级搜索（多选版）
   * @param data
   */
  @Cacheable({ ttlSeconds: 300 })
  public async searchMultiSelection(data: Record<string, any>) {
    const url = `${this.configService.proxyServer.dataService}/api/ECILocal/SearchMultiSelection`;
    return await this.httpUtils.postRequest(url, data);
  }

  /**
   * 文本解析公司列表
   * @param text
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCompaniesWithFreeText(text: string | string[]) {
    const url = `${this.configService.proxyServer.dataService}/api/ECILocal/GetCompaniesWithFreeText`;
    return await this.httpUtils.postRequest(url, { text, isMergedRequest: true });
  }

  /**
   * 模糊匹配公司工商
   * @param text
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getLinkCompaniesWithFreeText(text: string | string[]) {
    const url = `${this.configService.proxyServer.dataService}/api/ECILocal/GetLinkCompaniesWithFreeText`;
    return await this.httpUtils.postRequest(url, { text });
  }

  /**
   * 供应商与客户信息
   * @param keyNo
   * @param pageIndex
   * @param pageSize
   * @param dataType
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getSupplierCustomer(keyNo: string, pageIndex: number, pageSize: number, dataType: number) {
    const reqUrl = this.configService.proxyServer.dataService + '/api/QccSearch/List/SupplierCustomer';
    return await this.httpUtils.getRequest(reqUrl, { keyNo, pageIndex, pageSize, dataType });
  }

  private static creditMapArr = [
    {
      key: 'tc',
      api: '/api/CompanyInfo/GetCompanyTaxCredit',
    },
    {
      key: 'bbc',
      api: '/api/Bond/BondList',
      params: {
        isAggs: true,
        nodeName: 'ComNewRating',
        sortField: 'RatingDate',
      },
    },
    {
      key: 'ec',
      api: '/api/QccSearch/List/EnvCreditEvaluation',
    },
    {
      key: 'lgc',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 2,
      },
    },
    {
      key: 'cc',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 3,
      },
    },
    {
      key: 'kfc',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 4,
      },
    },
    {
      key: 'spc',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 5,
      },
    },
    {
      key: 'customs',
      api: '/api/QccSearch/List/GetImportExportCredits',
      method: 'POST',
    },
    {
      key: 'fs',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 10,
      },
    },
    {
      key: 'road',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 9,
      },
    },
    {
      key: 'fund',
      api: '/api/QccSearch/List/GetFundManagerRates',
      method: 'POST',
    },
    {
      key: 'deceaseCapiNotice',
      api: '/api/QccSearch/List/DeceaseCapiNotice',
      method: 'POST',
    },
  ];

  /**
   * 获取企业信用列表
   * @param body
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCompanyCreditList(body: SearchCreditRequest) {
    try {
      const type = body.type;
      const curr = CompanyDetailService.creditMapArr.find((item) => item.key == type);
      //减资公告数据接口入参发生了变化
      if (curr.key === 'deceaseCapiNotice') {
        const newBody = { ...body, ids: [body.id] };
        return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + curr.api, newBody);
      }
      if (curr.method == 'POST') {
        return this.httpUtils.postRequest(this.configService.proxyServer.dataService + curr.api, body);
      }
      if (curr.params) {
        Object.assign(body, curr.params);
      }
      return this.httpUtils.getRequest(this.configService.proxyServer.dataService + curr.api, body);
    } catch (e) {
      this.logger.error(`http getCompanyCreditList err:`, e);
      return null;
    }
  }

  /**
   * 专利搜索
   * @param param
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCompanyPatent(param: Record<string, any>) {
    const reqUrl = this.configService.proxyServer.dataService + '/api/QccSearch/IPRSearch/Patent';
    return await this.httpUtils.postRequest(reqUrl, param);
  }

  /**
   * 专利关系
   * @param keyNos
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getPatentRelation(keyNos?: string[]) {
    const reqUrl = this.configService.proxyServer.dataService + '/api/IPR/PatentRelation';
    return await this.httpUtils.postRequest(reqUrl, { keyNos });
  }

  /**
   * 获取企查查行业信息
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getQccIndustries() {
    return await this.httpUtils.postRequest(`${this.configService.proxyServer.dataService}/api/Common/QccIndustries`, null);
  }

  /**
   * 国际专利关系
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getInternationalPatent(params: Record<string, any>) {
    return await this.httpUtils.getRequest(`${this.configService.proxyServer.dataService}/api/QccSearch/SingleApp/InternationalPatent`, params);
  }

  /**
   * 查询著作权软件著作权
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCopyrights(params: Record<string, any>) {
    return await this.httpUtils.postRequest(`${this.configService.proxyServer.dataService}/api/QccSearch/SingleApp/Copyrights`, params);
  }

  /**
   * 公司间软件著作权关系
   * @param keyNos
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getSoftwareCopyright(keyNos: string[]) {
    return await this.httpUtils.postRequest(`${this.configService.proxyServer.dataService}/api/IPR/Relation/SoftwareCopyright`, {
      keyNos,
    });
  }

  /**
   * 企业担保信息
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getGuarantorList(params: Record<string, any>) {
    return await this.httpUtils.getRequest(`${this.configService.proxyServer.dataService}/api/QccSearch/List/Guarantor`, params);
  }

  /**
   * 获取区域列表信息
   * @param withEcoZone
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getAreas(withEcoZone?: boolean) {
    return await this.httpUtils.getRequest(`${this.configService.proxyServer.dataService}/api/Common/GetAreas`, { withEcoZone });
  }

  /**
   * 获取控股公司信息
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getHoldingCompany(params: Record<string, any>) {
    return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/VIP/GetHoldingCompany', params);
  }

  /**
   * 高管关联公司
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getBossDJGData(params: Record<string, any>) {
    return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/person/GetBossDJGData', params);
  }

  /**
   * 获取子公司列表keyNo list
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  async getBranchKeyNos(keyNo: string): Promise<string[]> {
    const response = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/QccSearch/List/Branch', {
      keyNo,
    });
    if (response?.Result) {
      return union(flatten(map(response?.Result, 'KeyNo')));
    }
    return null;
  }

  /**
   * 获取子公司列表 list
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  async getBranchList(keyNo: string): Promise<string[]> {
    return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/QccSearch/List/Branch', {
      keyNo,
    });
  }

  /**
   * 工商变更信息
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCoyHistoryInfo(keyNo: string) {
    return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/History/GetCoyHistoryInfo', { keyNo });
  }

  /**
   * 获取企业疑似实际控制人
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  async getSuspectedActualControllerV3(keyNo: string) {
    return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/Relation/GetSuspectedActualControllerV3', { keyNo });
  }

  /**
   * 获取企业疑似实际控制人V5
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  async getSuspectedActualControllerV5(keyNo: string) {
    return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/Relation/GetSuspectedActualControllerV5', { keyNo });
  }

  /**
   * 获取企业疑似实际控制人
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  async GetSuspectedActualControllerNoPathV2(keyNo: string) {
    return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/Relation/GetSuspectedActualControllerNoPathV2', { keyNo });
  }

  /**
   * 变更记录列表
   * @param params
   */
  // @Cacheable({ ttlSeconds: 300 })
  // public async getChangeInfoList(params: Record<string, any>) {
  //   return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/QccSearch/List/ChangeInfo', params);
  // }

  /**
   * 变更记录列表
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  public async ChangeRecords(params: Record<string, any>) {
    return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/EciLocal/ChangeRecords', params);
  }

  /**
   * 主要人员
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getOrganismPersonList(params: Record<string, any>) {
    return this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/QccSearch/List/OrganismPerson', params);
  }

  /**
   * 历史高管去重
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCoyHistoryEmployee2Info(params: Record<string, any>) {
    return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/History/GetCoyHistoryEmployee2Info', params);
  }

  /**
   * 历史股东信息
   * @param param
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCoyHistoryPartnerInfo(param: Record<string, any>) {
    const url = this.configService.proxyServer.dataService + '/api/History/GetCoyHistoryPartnerInfo';
    return await this.httpUtils.getRequest(url, param);
  }

  /**
   * 受益所有人
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getBenefitDetail(params: Record<string, any>) {
    return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/QccDetail/Benefit/Detail', params);
  }

  /**
   * 主要人员列表
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getEmployeeList(params: Record<string, any>) {
    return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/QccSearch/List/Employee', params);
  }

  /**
   * 历史主要人员列表
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getEmployeeListHistory(params: Record<string, any>) {
    return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/History/GetCoyHistoryEmployee2Info', params);
  }

  /**
   * 工商股东列表
   * @param param
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getPartnerWithGroup(param: Record<string, any>) {
    return this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/ECILocal/GetPartnerWithGroup', param);
  }

  /**
   * 获取公司关联方
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getRelatedList(keyNo: string) {
    const url = `${this.configService.proxyServer.riskService}/Related/List`;
    try {
      return await this.httpUtils.getRequest(url, {
        userId: 'rover',
        keyNo,
        pageSize: 100,
        pageIndex: 1,
        nodeType: '2,4,5',
      });
    } catch (e) {
      this.logger.error(`http Post ${url} err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 300 })
  public async getCompanyAllChattelMortgage(keyNo: string) {
    const url = `${this.configService.proxyServer.dataService}/api/QccSearch/List/MPledge`;
    const params = {
      keyNo,
      pageIndex: 1,
      pageSize: 1000,
    };
    const response = await this.httpUtils.getRequest(url, params);
    return response?.Result || [];
  }

  @Cacheable({ ttlSeconds: 300 })
  async getTaxNoticeDetail(objectId: string) {
    const url = `${this.configService.proxyServer.dataService}/api/Risk/GetTaxReminderDetail`;
    const params = {
      id: objectId,
    };
    const response = await this.httpUtils.postRequest(url, params);
    return response?.Result || {};
  }

  /**
   * b端判断公司人员归属, 数据提供的接口，通过调用外部接口判断指定人员是否归属指定公司
   * @param params
   */
  @Cacheable({ ttlSeconds: 300 })
  async getCompanyVerifiedPerson(params: CompanyVerifiedPersonRequest) {
    const url = `http://10.0.0.4/api/CompanyInfo/GetCompanyVerifiedPerson`;
    const response = await this.httpUtils.postRequest(url, params);
    return response?.Result?.Data || {};
  }

  /**
   * 获取财务报表信息
   * @param keyNo
   * reportPeriodTypes: 0- 只需要最新的一期， 4- 境内年报
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyFinance(keyNo: string, reportPeriodTypes: number[], reportType: number) {
    try {
      const startDate = moment().add('-3', 'years').startOf('year').unix(); // 近3年
      const data = {
        keyNo,
        type: 'cm', // cm 大陆财务 hk 香港财务
        reportType, // 类型 1 主要指标 2 资产负债 3 利润 4 现金流
        reportPeriodTypes,
        currency: '',
        rate: 1, // 采用的汇率计算办法
        isAggs: true,
        sortField: 'reportdate',
        isSortAsc: 'false',
        decimal: 2,
        startDate,
      };
      // 判断是否是香港企业
      if (keyNo.slice(0, 1) === 'h') {
        data.type = 'hk';
      }
      // 目前只支持大陆企业
      /*const { FinanceDataCount } = companyInfo?.CountInfo || {};
      if (FinanceDataCount === 2) {
        data.type = 'hk';
      } else if (FinanceDataCount === 3) {
        data.type = 'oth';
      }*/
      const url = `${this.configService.proxyServer.dataService}/api/EciLocal/Finance/StatementsV2`;
      return this.httpUtils.postRequest(url, data);
    } catch (e) {
      this.logger.error(`getCompanyFinance() err:` + e.message);
      this.logger.error(e);
      return null;
    }
  }

  /**
   * 获取公司归属的数据频道金融机构标签
   * @param keyNo 公司keyNo
   * @returns
   */
  @Cacheable({ ttlSeconds: 300 })
  async getFinancialInstitutions(keyNo: string) {
    const url = `${this.configService.proxyServer.dataService}/api/QccSearch/List/GetFinancialInstitutions`;
    const params = {
      keyNo,
    };
    return this.httpUtils.postRequest(url, params);
  }

  /**
   * 获取风险动态的立案详情
   * @param id
   * @returns
   */
  @Cacheable({ ttlSeconds: 300 })
  async getDetailOfLiAn(id: string) {
    const url = `${this.configService.proxyServer.dataService}/api/Risk/GetDetailOfLiAn`;
    const params = {
      id,
      isB: true,
    };
    return this.httpUtils.getRequest(url, params);
  }

  /**
   * 根据投资人查询公司，多返回了出资比例
   * @param keyNo  主体公司
   * @param fundedRatioLevel 投资比例(不太准) 0:不限 1:<=5% 2:>5% 3:>20% 4:>50% 5:>66.66% 6:=100%
   * @param status 经营状态
   * @param pageSize
   * @param pageIndex
   * @param stockPercent 持股比例区间  '50,100.00001': 持股50%到100%， null不限
   */
  @Cacheable({ ttlSeconds: 300 })
  async getInvestCompany(keyNo: string, fundedRatioLevel = 0, status: number[] = [], pageSize = 10, pageIndex = 1, stockPercent?: string) {
    const url = `${this.configService.proxyServer.dataService}/api/ECILocal/SearchByPromoterWithFundedRatioByKeyNo`;
    const params = {
      keyNo,
      pageSize,
      pageIndex,
    };
    if (fundedRatioLevel) {
      Object.assign(params, { fundedRatioLevel });
    }
    if (status && status.length) {
      Object.assign(params, { status: status.join(',') });
    }
    if (stockPercent) {
      Object.assign(params, { stockPercent });
    }
    return this.httpUtils.getRequest(url, params);
  }

  /**
   * 获取对外投资企业中持股比例大于50%的企业
   * @param companyId
   * @param maxCount
   * @param relatedType
   * @returns
   */
  async getMajorityHeldCompanies(companyId: string): Promise<any> {
    const holdingList = [];
    const pageSize = 100;
    let pageIndex = 1;
    let hasMore = false;
    let holdingCompanyCount = 0;
    let GroupItems = [];
    do {
      const holdingCompanyList = await this.getInvestCompany(companyId, 0, [], pageSize, pageIndex, '50,100.00001');
      holdingCompanyCount = holdingCompanyList?.Paging?.TotalRecords || 0;
      if (holdingCompanyList.Result?.length) {
        holdingList.push(...holdingCompanyList.Result);
      }
      GroupItems = holdingCompanyList?.GroupItems;
      hasMore = holdingCompanyCount > pageIndex * pageSize;
      pageIndex++;
    } while (hasMore);
    return { holdingList, GroupItems };
  }

  /**
   * 根据查询条件获取聚合数据
   */
  async getCountInfos(keyNos: string[], targetScopes: TargetScopeEnums[]): Promise<any> {
    if (!keyNos.length) {
      return null;
    }
    const req = {
      keyNos: keyNos,
      selection: targetScopes,
    };
    return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/ECILocal/GetCountInfos', req);
  }
}
