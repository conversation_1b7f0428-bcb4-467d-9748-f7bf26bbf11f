import { filter, flatten, groupBy, minBy, sortBy, toPairs, uniq } from 'lodash';
import * as moment from 'moment';
import {
  beneficiaryColumnRenderer,
  controlRelationsPath,
  personNameWithTagRenderer,
  renderCompanyNameRelated,
  suspectedRelationship,
} from './table-column-renderer';
import { drawEdge, FULL_RELATIONS_KEY_MAP, getRoles, isCompany, isEdge, isPerson } from './relation-path.util';
import { processRelationRoles } from './path-node/data-node-utils';
import { areaParse, dateTrans, pathParser } from '../../utils/pdf/pdf-table.util';

enum ColorsEnum {
  Default = '#999',
  Positive = '#0aad65',
  Negative = '#F04040',
}

/**
 * 合同纠纷文本颜色映射
 */
const getColorByDescription = (text: string): { color: ColorsEnum } => {
  const colorMap = new Map<string, ColorsEnum>([
    // 正面
    ['支持', ColorsEnum.Positive],
    ['解除财产保全', ColorsEnum.Positive],
    ['对方被驳回', ColorsEnum.Positive],
    ['执行完毕', ColorsEnum.Positive],
    ['申请人被驳回', ColorsEnum.Positive],
    ['部分支持', ColorsEnum.Positive],
    ['同意追加被执行人', ColorsEnum.Positive],
    // 负面
    ['驳回', ColorsEnum.Negative],
    ['不支持', ColorsEnum.Negative],
    ['驳回上诉', ColorsEnum.Negative],
    ['财产保全', ColorsEnum.Negative],
    ['终结本次执行', ColorsEnum.Negative],
  ]);

  return {
    color: colorMap.get(text) || ColorsEnum.Default,
  };
};

// ------------- 经营合规风险 -------------
// 简易注销
const BusinessAbnormal2Columns = [
  {
    title: '公告名称',
    dataIndex: 'annoName',
    width: 158,
  },
  {
    title: '公告开始日期-结束日期',
    dataIndex: 'publishDate',
    width: 235,
  },
  {
    title: '简易注销结果',
    dataIndex: 'resultContent',
  },
];

// 被列入经营异常(未移出)
const BusinessAbnormal3Columns = [
  {
    title: '列入日期',
    dataIndex: 'PublishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  { title: '作出决定机关(列入)', dataIndex: 'Court', width: 270 },
  { title: '列入经营异常名录原因', dataIndex: 'CaseReason' },
];

// 疑似停业歇业停产或被吊销证照
const BusinessAbnormal5Columns = [
  {
    title: '决定书文号',
    dataIndex: 'docNo',
    width: 158,
  },
  {
    title: '违法事实',
    dataIndex: 'punishReason',
  },
  {
    title: '处罚结果',
    dataIndex: 'punishResult',
    placeholder: '未公示',
  },
  {
    title: '处罚单位',
    dataIndex: 'punishOffice',
    width: 100,
  },
  {
    title: '处罚日期',
    dataIndex: 'punishDate',
    width: 88,
  },
];

// ------------- 经营合规风险 -------------

// ------------- 法律风险 -------------

// 税收违法（FIXME: 接口返回异常，修复后需要检查对应的维度）
const TaxationOffencesColumns = [
  {
    title: '发布日期',
    dataIndex: 'PublishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '所属税务机关',
    dataIndex: 'Court',
    width: 100,
  },
  {
    title: '案件性质',
    dataIndex: 'ActionRemark',
  },
  {
    title: '主要违法事实',
    dataIndex: 'CaseReason',
    width: 100,
  },
  {
    title: '法律依据及处理处罚情况',
    dataIndex: 'Title',
  },
];

// 股权冻结
const FreezeEquityColumns = [
  {
    title: '执行通知书文号',
    dataIndex: 'CaseNo',
    width: 108,
  },
  {
    title: '被执行人',
    customRender: (item) => {
      if (item?.SubjectInfo) {
        return item?.SubjectInfo.map((item) => item.Name).join(',') || '-';
      }
      return item?.Name || '-';
    },
    // dataIndex: 'Name',
    // customRender: {
    //   name: 'entities',
    // },
  },
  {
    title: '冻结股权标的企业',
    width: 125,
    customRender: (item) => {
      if (item?.PledgorInfo) {
        return item?.PledgorInfo.map((item) => item.Name).join(',');
      } else {
        return `${item?.Pledgor}`;
      }
    },
  },
  {
    title: '股权数额',
    dataIndex: 'AmountDesc',
    width: 88,
  },
  {
    title: '执行法院',
    dataIndex: 'Court',
    width: 120,
  },
  {
    title: '类型/状态',
    width: 108,
    customRender: (item) => {
      // 兼容老数据，老数据没有statusdesc
      const status = item.statusdesc || item.ExecuteStatus;
      return status ? `${item.TypeDesc} | ${status}` : '-';
    },
  },
];

// 被列入失信被执行人(当前有效)被列入失信被执行人(历史)
const PersonCreditCurrentColumns = [
  {
    title: '案号',
    dataIndex: 'CaseNo',
    width: 158,
  },
  {
    title: '执行法院',
    dataIndex: 'ExecuteGov',
    width: 100,
  },
  {
    title: '涉案金额(元)',
    dataIndex: 'Amount',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  // {
  //   title: '履行情况',
  //   customRender: (item) => {
  //     return item.executeStatus || item.ExecuteStatus || '-';
  //   },
  // },
  { title: '失信行为', dataIndex: 'ActionRemark' },
  {
    title: '立案日期',
    dataIndex: 'LiAnDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '发布日期',
    dataIndex: 'PublicDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

const PersonCreditColumns = [
  {
    title: '案号',
    dataIndex: 'CaseNo',
    width: 128,
  },
  {
    title: '执行法院',
    width: 100,
    customRender: (item) => {
      return (Array.isArray(item.Court) ? item.Court[0] : item.Court) || '-';
    },
  },
  {
    title: '执行依据文号',
    dataIndex: 'OrgNo',
    width: 96,
  },
  {
    title: '涉案金额(元)',
    dataIndex: 'Amount',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  // {
  //   title: '履行情况',
  //   customRender: (item) => {
  //     return item.executeStatus || item.ExecuteStatus || '-';
  //   },
  // },
  { title: '失信行为', dataIndex: 'ActionRemark' },
  {
    title: '立案日期',
    dataIndex: 'LianDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '发布日期',
    dataIndex: 'PublishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

const MainMembersPersonCreditCurrentColumns = [
  {
    title: '案号',
    dataIndex: 'CaseNo',
    width: 158,
  },
  {
    title: '执行法院',
    width: 100,
    customRender: (item) => {
      return (Array.isArray(item.Court) ? item.Court[0] : item.Court) || '-';
    },
  },
  {
    title: '失信被执行人',
    dataIndex: 'SubjectInfo',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '涉案金额(元)',
    dataIndex: 'Amount',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  // {
  //   title: '履行情况',
  //   customRender: (item) => {
  //     return item.executeStatus || item.ExecuteStatus || '-';
  //   },
  // },
  { title: '失信行为', dataIndex: 'ActionRemark' },
  {
    title: '立案日期',
    width: 88,
    dataIndex: 'LianDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  // {
  //   title: '发布日期',
  //   dataIndex: 'PublishDate',
  //   customRender: {
  //     name: 'date',
  //     options: {
  //       pattern: 'X',
  //     },
  //   },
  // },
];

// 限制高消费(当前有效) 历史限制高消费 子公司限制高消费
const RestrictedConsumptionColumns = [
  {
    title: '案号',
    dataIndex: 'CaseNo',
    width: 158,
  },
  {
    title: '限消令对象',
    dataIndex: 'SubjectInfo',
    width: 100,
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '关联对象',
    dataIndex: 'PledgorInfo',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '申请人',
    dataIndex: 'Applicant',
    width: 150,
  },
  {
    title: '立案日期',
    dataIndex: 'LianDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '发布日期',
    dataIndex: 'PublishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 限制出境
const RestrictedOutboundColumns = [
  {
    title: '案号',
    dataIndex: 'CaseNo',
    width: 158,
  },
  {
    title: '限制出境对象',
    dataIndex: 'SubjectInfo',
    customRender: {
      name: 'entities',
    },
  },
  // {
  //   title: '被执行人',
  //   dataIndex: 'PledgorInfo',
  //   customRender: {
  //     name: 'entities',
  //   },
  // },
  // {
  //   title: '被执行人地址',
  //   dataIndex: 'Address',
  // },
  {
    title: '申请执行人',
    dataIndex: 'ApplicantInfo',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '执行标的(元)',
    dataIndex: 'Amount',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  {
    title: '执行法院',
    dataIndex: 'Court',
    width: 100,
  },
  {
    title: '发布日期',
    dataIndex: 'PublishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 破产重整
const BankruptcyColumns = [
  {
    title: '案号',
    dataIndex: 'CaseNo',
    width: 158,
  },
  {
    title: '破产类型',
    dataIndex: 'CaseType',
  },
  {
    title: '被申请人',
    dataIndex: 'Respondent',
  },
  {
    title: '申请人',
    dataIndex: 'Applicant',
    width: 80,
  },
  {
    title: '经办法院',
    dataIndex: 'CourtName',
    width: 100,
  },
  {
    title: '公开日期',
    dataIndex: 'RiskDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 被执行人
const PersonExecutionColumns = [
  {
    title: '案号',
    dataIndex: 'CaseNo',
    width: 158,
  },
  // 被执行人、
  {
    title: '被执行人',
    dataIndex: 'Name',
    width: 158,
  },
  // 执行标的（元）
  {
    title: '执行标的(元)',
    dataIndex: 'BiaoDi',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  // 执行法院
  {
    title: '执行法院',
    dataIndex: 'ExecuteGov',
    // width: 210,
  },
  // 立案日期、内容（详情）
  {
    title: '立案日期',
    dataIndex: 'LiAnDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// ------------- 法律风险 -------------

// ------------- 行政监管风险 -------------

// 行政处罚 环保处罚
const AdministrativePenaltiesColumns = [
  {
    title: '决定书文号',
    width: 158,
    customRender(item) {
      const punishReasonTypeMap = [
        { label: '围串标', value: '201' },
        { label: '分包/转包/挂靠', value: '202' },
        { label: '虚假材料', value: '203' },
        { label: '商业贿赂', value: '301' },
        { label: '其他', value: '0' },
      ];

      if (!item.CaseNo) {
        return '-';
      }

      let content = [];
      content = [`<div>${item.CaseNo}</div>`];
      if (Array.isArray(item.punishreasontype)) {
        item.punishreasontype.forEach((reasonType: string) => {
          const label = punishReasonTypeMap.find((op) => op.value === reasonType)?.label;
          if (label) {
            content.push(`<span class="status-tag list">${label}</span>`);
          }
        });
      }

      return content.join('');
    },
  },
  {
    title: '违法事实',
    dataIndex: 'CaseReason',
    width: 100,
  },
  {
    title: '处罚结果',
    dataIndex: 'Title',
    placeholder: '未公示',
  },
  {
    title: '处罚金额(元)',
    dataIndex: 'Amount',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  {
    title: '处罚单位',
    dataIndex: 'Court',
    width: 100,
  },
  {
    title: '处罚日期',
    dataIndex: 'punishdate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

const AdministrativePenalties2Columns = [
  {
    title: '处罚对象名称',
    dataIndex: 'SubjectInfo',
    width: 95,
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '决定文书号',
    dataIndex: 'caseno',
    width: 80,
  },
  {
    title: '违法事实',
    dataIndex: 'casereason',
    width: 100,
  },
  {
    title: '处罚结果',
    dataIndex: 'title',
    placeholder: '未公示',
  },
  {
    title: '处罚金额(元)',
    dataIndex: 'amount',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  {
    title: '处罚单位',
    dataIndex: 'court',
    width: 100,
  },
  {
    title: '处罚日期',
    dataIndex: 'punishdate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 欠税公告
const TaxArrearsNoticeColumns = [
  {
    title: '欠税税种',
    dataIndex: 'Title',
  },
  {
    title: '欠税余额(元)',
    dataIndex: 'Amount',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  {
    title: '当前新发生的欠税金额(元)',
    dataIndex: 'NewAmount',
    width: 175,
    customRender: {
      name: 'money',
    },
  },
  { title: '发布单位', dataIndex: 'IssuedBy' },
  {
    title: '发布日期',
    dataIndex: 'PublishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 产品召回
const ProductQualityProblem1Columns = [
  {
    title: '召回产品',
    dataIndex: 'Title',
  },
  {
    title: '召回企业',
    dataIndex: 'NameAndKeyNo',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '发布日期',
    dataIndex: 'PublishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 产品抽查不合格
const ProductQualityProblem2Columns = [
  {
    title: '产品名称',
    dataIndex: 'CaseReason',
    width: 100,
  },
  {
    title: '产品类别',
    dataIndex: 'CaseReasonType',
  },
  {
    title: '规格型号',
    dataIndex: 'OrgNo',
  },
  {
    title: '生产单位',
    dataIndex: 'Name',
    width: 158,
  },
  {
    title: '抽查/公告时间',
    dataIndex: 'PublishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '主要不合格项目',
    dataIndex: 'ActionRemark',
  },
];

// 假冒伪劣产品
const ProductQualityProblem3Columns = [
  {
    title: '决定书文号',
    dataIndex: 'CaseNo',
    width: 158,
  },
  {
    title: '处罚事由/违法行为类型',
    dataIndex: 'CaseReason',
    width: 100,
  },
  {
    title: '处罚结果/内容',
    dataIndex: 'Title',
    placeholder: '未公示',
  },
  {
    title: '处罚金额(元)',
    dataIndex: 'Amount',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  {
    title: '处罚单位',
    dataIndex: 'Court',
    width: 100,
  },
  {
    title: '处罚日期',
    dataIndex: 'PunishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 虚假宣传
const ProductQualityProblem4Columns = [
  {
    title: '决定书文号',
    dataIndex: 'CaseNo',
    width: 158,
  },
  {
    title: '处罚事由',
    dataIndex: 'CaseReason',
    width: 100,
  },
  {
    title: '处罚结果',
    dataIndex: 'Title',
    placeholder: '未公示',
  },
  {
    title: '处罚金额(元)',
    dataIndex: 'Amount',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  {
    title: '处罚单位',
    dataIndex: 'Court',
    width: 100,
  },
  {
    title: '处罚日期',
    dataIndex: 'PunishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 其他质量问题
const ProductQualityProblem5Columns = [
  {
    title: '任务编号',
    dataIndex: 'OrgNo',
  },
  {
    title: '任务名称',
    dataIndex: 'Title',
  },
  {
    title: '抽查机关',
    dataIndex: 'Court',
    width: 100,
  },
  {
    title: '完成日期',
    dataIndex: 'PublishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 未准入境
const ProductQualityProblem6Columns = [
  { title: '产品名称', dataIndex: 'Title' },
  { title: '产品类型', dataIndex: 'CaseReasonType' },
  { title: '数量/重量', dataIndex: 'AmountDesc', width: 60 },
  { title: '原因', dataIndex: 'ActionRemark' },
  {
    title: '报送时间',
    dataIndex: 'LianDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
        format: 'YYYY年MM月',
      },
    },
  },
  {
    title: '发布日期',
    dataIndex: 'PublishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 药品抽查
const ProductQualityProblem7Columns = [
  {
    title: '药品品名',
    dataIndex: 'Specs',
    width: 100,
  },
  {
    title: '检查实施机关',
    dataIndex: 'Court',
    width: 100,
  },
  {
    title: '类型',
    dataIndex: 'CaseReasonType',
    width: 60,
  },
  {
    title: '检测结果',
    dataIndex: 'ActionRemark',
  },
];
// 假冒化妆品 (FIXME: 维度已经删除）
// const ProductQualityProblem8Columns = [
//   {
//     title: '产品名称',
//     dataIndex: 'CaseNo',
//   },
//   {
//     title: '规格',
//     dataIndex: 'StockInfo',
//   },
//   {
//     title: '生产商',
//     dataIndex: 'ApplicantInfo',
//     customRender: {
//       name: 'entities',
//     },
//   },
//   {
//     title: '授权商',
//     customRender: {
//       name: 'pledgorInfo',
//     },
//   },
//   {
//     title: '运营单位',
//     dataIndex: 'CaseReason',
//     width: 100,
//   },
//   {
//     title: '公告时间',
//     dataIndex: 'PublishDate',
//     customRender: {
//       name: 'date',
//     },
//   },
// ];

// 食品安全不合格
const ProductQualityProblem9Columns = [
  {
    title: '食品名称',
    dataIndex: 'Title',
  },
  {
    title: '抽检次数',
    dataIndex: 'Amount2',
    width: 60,
    customRender: (text) => {
      return text > 0 ? `第${text}次抽检` : '-';
    },
  },
  {
    title: '被抽检企业',
    dataIndex: 'NameAndKeyNo',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '标称生产企业',
    dataIndex: 'ApplicantInfo',
    customRender: {
      name: 'entities',
    },
  },
  // {
  //   title: '标称生产企业地址',
  //   dataIndex: 'Address',
  // },
  {
    title: '商标',
    dataIndex: 'ActionRemark',
  },
  {
    title: '规格型号',
    dataIndex: 'Specs',
  },
  {
    title: '生产日期/批号',
    dataIndex: 'LianDate',
  },
  {
    title: '抽检结果',
    dataIndex: 'ExecuteStatus',
    customRender: {
      name: 'status',
    },
  },
];

// 被列入严重违法失信企业名录
const CompanyCreditColumns = [
  {
    title: '列入日期',
    dataIndex: 'AddDate',
    // width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '作出决定机关(列入)',
    dataIndex: 'AddOffice',
    width: 220,
  },
  {
    title: '列入严重违法失信企业名单原因',
    dataIndex: 'AddReason',
    width: 220,
  },
];

// 被列入严重违法失信企业名录(历史)
const CompanyCreditColumnsHistory = [
  {
    title: '列入日期',
    dataIndex: 'AddDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '作出决定机关(列入)',
    dataIndex: 'AddOffice',
    width: 95,
  },
  {
    title: '列入严重违法失信企业名单原因',
    dataIndex: 'AddReason',
    width: 150,
  },
  {
    title: '移出日期',
    dataIndex: 'RemoveDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '作出决定机关(移出)',
    dataIndex: 'RemoveOffice',
    width: 95,
  },
  {
    title: '移出严重违法失信企业名单原因',
    dataIndex: 'RemoveReason',
  },
];

// 多次被列入经营异常名录【当下未列入】
const OperationAbnormalColumns = [
  {
    title: '列入日期',
    dataIndex: 'PublishDate',
    // width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '作出决定机关(列入)',
    dataIndex: 'Court',
    width: 95,
  },
  {
    title: '列入经营异常名录原因',
    dataIndex: 'CaseReason',
    width: 122,
  },
  {
    title: '移出日期',
    dataIndex: 'LianDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '作出决定机关(移出)',
    dataIndex: 'ActionRemark',
    width: 95,
  },
  {
    title: '移出经营异常名录原因',
    dataIndex: 'RemoveReason',
    width: 122,
  },
  // {
  //   title: '列入对象',
  //   dataIndex: 'SubjectInfo',
  //   customRender: {
  //     name: 'entities',
  //   },
  // },
];

// ------------- 行政监管风险 -------------

// ------------- 经营稳定性风险 -------------

// 动产抵押
const ChattelMortgageColumns = [
  {
    title: '登记编号',
    dataIndex: 'RegisterNo',
  },
  {
    title: '抵押人',
    dataIndex: 'RelatedCompanyInfo',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '抵押权人',
    dataIndex: 'MPledgeDetail.Pl',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '所有权或使用权归属',
    customRender: (item) => {
      if (item?.MPledgeDetail?.GuaranteeList) {
        const ownerNames = [];
        item.MPledgeDetail.GuaranteeList.forEach((gua: any) => {
          if (gua.KeyNoList) {
            gua.KeyNoList.forEach((knl: any) => {
              if (!ownerNames.includes(knl.Name)) {
                ownerNames.push(knl.Name);
              }
            });
          }
        });
        if (ownerNames.length === 0) {
          return '-';
        }
        if (ownerNames.length > 2) {
          return ownerNames.map((n, index) => {
            return `<div>${n}${ownerNames.length === index + 1 ? '等' : ''}</div>`;
          });
        }
        return ownerNames.map((n) => `<div>${n}</div>`).join('');
      }
      return '-';
    },
  },
  {
    title: '债务人履行债务的期限',
    dataIndex: 'MPledgeDetail.GuaranteedCredRight.FulfillObligation',
  },
  {
    title: '被担保主债权数额',
    dataIndex: 'DebtSecuredAmount',
    width: 60,
  },
  {
    title: '登记日期',
    dataIndex: 'RegisterDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 股权出质
const EquityPledgeColumns = [
  {
    title: '登记编号',
    dataIndex: 'RegistNo',
  },
  {
    title: '出质人',
    width: 90,
    dataIndex: 'PledgorInfo',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '质权人',
    width: 90,
    dataIndex: 'PledgeeInfo',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '出质股权标的企业',
    dataIndex: 'RelatedCompanyInfo',
    width: 123,
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '出质股权数额',
    dataIndex: 'PledgedAmount',
    width: 110,
  },
  {
    title: '登记日期',
    dataIndex: 'RegDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '状态',
    dataIndex: 'Status',
    width: 88,
    customRender: {
      name: 'companyStatus',
    },
  },
];

// 土地抵押
const LandMortgageColumns = [
  {
    title: '土地坐落',
    dataIndex: 'Address',
  },
  {
    title: '抵押人',
    dataIndex: 'MortgagorNames[0].Name',
    width: 158,
  },
  {
    title: '抵押权人',
    dataIndex: 'MortgagePeoples[0].Name',
    width: 158,
  },
  {
    // FIXME: 宽度需确认
    // FIXME: `customRender` 需确认
    title: '抵押起止日期',
    // width: 88,
    customRender: {
      name: 'range',
      options: {
        type: 'date',
        from: 'StartDate',
        to: 'EndDate',
        pattern: 'X',
      },
    },
  },
  {
    title: '抵押面积(公顷)',
    dataIndex: 'MortgageAcreage',
    width: 105,
  },
  {
    title: '抵押金额(万元)',
    dataIndex: 'MortgagePrice',
    width: 105,
    customRender: {
      name: 'money',
      options: {
        precision: 4,
      },
    },
  },
];

// 司法拍卖(机器设备)
const JudicialAuction1Columns = [
  {
    title: '标题/案号',
    width: 158,
    customRender: {
      name: 'judicialAuction',
    },
  },
  {
    title: '起拍价(元)',
    dataIndex: 'yiwu',
    customRender: {
      name: 'money',
    },
  },
  {
    title: '评估价(元)',
    dataIndex: 'EvaluationPrice',
    customRender: {
      name: 'money',
    },
  },
  {
    title: '拍卖时间',
    dataIndex: 'actionremark',
    width: 100,
  },
  {
    title: '处置单位',
    dataIndex: 'executegov',
    width: 100,
  },
];

// 担保明细
const GuaranteeInfoColumns = [
  {
    title: '被担保方',
    dataIndex: 'Vouchee',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '担保方',
    dataIndex: 'Guarantee',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '担保方式',
    dataIndex: 'GuaranteeType',
  },
  {
    title: '担保金额(万元)',
    dataIndex: 'GuaranteeMoney',
    width: 105,
    customRender: {
      name: 'money',
    },
  },
  {
    title: '公告日期',
    dataIndex: 'PublicDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 担保风险
const GuaranteeRiskColumns = [
  {
    title: '保证类型',
    dataIndex: 'GuaranteeType',
  },
  {
    title: '被担保方',
    dataIndex: 'Vouchee',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '担保方',
    dataIndex: 'Guarantee',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '债权人',
    dataIndex: 'Creditor',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '被保证债权本金(万元)',
    dataIndex: 'GuaranteeMoney',
    width: 145,
    customRender: {
      name: 'money',
      options: {
        reduce: 10000,
        precision: 2,
      },
    },
  },
  {
    title: '裁判日期',
    dataIndex: 'Judgedate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '发布日期',
    dataIndex: 'PublicDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 经营范围变更
const MainInfoUpdateScopeColumns = [
  {
    title: '变更日期',
    dataIndex: 'ChangeDate',
    customRender: {
      name: 'date',
    },
  },
  {
    title: '变更前',
    width: 200,
    customRender: {
      name: 'diff',
      options: {
        before: 'Scope',
        after: 'after.Scope',
      },
    },
  },
  {
    title: '变更后',
    width: 200,
    customRender: {
      name: 'diff',
      options: {
        before: 'after.Scope',
        after: 'Scope',
      },
    },
  },
];

// 注册地址变更
const MainInfoUpdateAddressColumns = [
  {
    title: '变更日期',
    dataIndex: 'ChangeDate',
    customRender: {
      name: 'date',
    },
  },
  {
    title: '变更前',
    width: 200,
    customRender: {
      name: 'diff',
      options: {
        before: 'Address',
        after: 'after.Address',
      },
    },
  },
  {
    title: '变更后',
    width: 200,
    customRender: {
      name: 'diff',
      options: {
        before: 'after.Address',
        after: 'Address',
      },
    },
  },
];

// 近期变更受益所有人
const MainInfoUpdateBeneficiaryColumns = [
  {
    title: '变更日期',
    dataIndex: 'ChangeDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '变更前',
    width: 200,
    customRender: (item) => {
      return beneficiaryColumnRenderer(item, 'BeforeContent');
    },
  },
  {
    title: '变更后',
    width: 200,
    customRender: (item) => {
      return beneficiaryColumnRenderer(item, 'AfterContent');
    },
  },
];

// 企业名称变更
const MainInfoUpdateNameColumns = [
  {
    title: '变更日期',
    dataIndex: 'ChangeDate',
    customRender: {
      name: 'date',
    },
  },
  {
    title: '变更前',
    dataIndex: 'CompanyName',
    width: 200,
  },
  {
    title: '变更后',
    dataIndex: 'after.CompanyName',
    width: 200,
  },
];

// 法定代表人变更
const MainInfoUpdateLegalPersonColumns = [
  {
    title: '变更日期',
    dataIndex: 'ChangeDate',
  },
  {
    title: '变更前',
    dataIndex: 'OperName',
    width: 200,
  },
  {
    title: '变更后',
    dataIndex: 'after.OperName',
    width: 200,
  },
];

// 大股东变更
const MainInfoUpdateHolderColumns = [
  {
    title: '变更日期',
    dataIndex: 'ChangeDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '变更前',
    dataIndex: 'BeforeContent',
    width: 200,
    customRender: {
      name: 'json',
      options: {
        parser: 'entities',
      },
    },
  },
  {
    title: '变更后',
    dataIndex: 'AfterContent',
    width: 200,
    customRender: {
      name: 'json',
      options: {
        parser: 'entities',
      },
    },
  },
];

// 董监高变更
const MainInfoUpdateManagerColumns = [
  {
    title: '变更日期',
    dataIndex: 'ChangeDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '变更前',
    dataIndex: 'before.Employees',
    width: 200,
    customRender: (item) => {
      return item?.before?.Employees.map((p) => `${p.EmployeeName || ''}${p.Job ? `, ${p.Job}` : ''}`).join(' ');
    },
  },
  {
    title: '变更后',
    width: 200,
    customRender: (item) => {
      return item?.Employees.map((p) => `${p.EmployeeName || ''}${p.Job ? `, ${p.Job}` : ''}`).join(' ');
    },
  },
];

// 实际控制人变更
const MainInfoUpdatePersonColumns = [
  {
    title: '变更日期',
    dataIndex: 'ChangeDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '变更前',
    dataIndex: 'BeforeContent',
    width: 200,
    customRender: {
      name: 'json',
      options: {
        parser: 'entities',
      },
    },
  },
  {
    title: '变更后',
    dataIndex: 'AfterContent',
    width: 200,
    customRender: {
      name: 'json',
      options: {
        parser: 'entities',
      },
    },
  },
];

// 债券违约
const BondDefaultsColumns = [
  { title: '债券简称', dataIndex: 'BondShortName' },
  { title: '债券类型', dataIndex: 'BondTypeName' },
  { title: '违约状态', dataIndex: 'DefaultStatusDesc' },
  {
    title: '首次违约日期',
    dataIndex: 'FirstDefaultDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'YYYYMMDD',
      },
    },
  },
  {
    title: '累计违约本金(亿元)',
    dataIndex: 'AccuOverdueCapital',
    width: 134,
    customRender: {
      name: 'money',
      options: {
        precision: 2,
      },
    },
  },
  {
    title: '累计违约利息(亿元)',
    dataIndex: 'AccuOverdueInterest',
    width: 134,
    customRender: {
      name: 'money',
      options: {
        precision: 2,
      },
    },
  },
  {
    title: '到期日期',
    dataIndex: 'MaturityDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'YYYYMMDD',
      },
    },
  },
];

// ------------- 经营稳定性风险 -------------

// ------------- 黑名单排查 -------------
// 外部黑名单
const HitOuterBlackListColumns = [
  {
    title: '黑名单名称',
    dataIndex: 'Name',
    width: 108,
    // customRender: {
    //   name: 'entities',
    // },
  },
  {
    title: '命中黑名单类型',
    dataIndex: 'CaseReasonType',
    width: 108,
  },
  {
    title: '风险等级',
    dataIndex: 'level',
    width: 68,
    customRender: {
      name: 'riskLevel',
    },
  },
  {
    title: '列入原因',
    dataIndex: 'CaseReason',
    ellipsis: true,
  },
  {
    title: '列入机关',
    dataIndex: 'Court',
    width: 100,
  },
  {
    title: '列入日期',
    dataIndex: 'Publishdate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 内部黑名单
const HitInnerBlackListColumns = [
  {
    title: '黑名单名称',
    dataIndex: 'companyNameDD',
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
    width: 100,
  },
  {
    title: '列入日期',
    dataIndex: 'joinDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '黑名单有效期',
    dataIndex: 'duration',
    width: 88,
    customRender: {
      name: 'blacklist',
    },
  },
];

const EmploymentRelationshipColumns = [
  {
    title: '关联人员',
    dataIndex: 'personName',
    width: 60,
  },
  {
    title: '关联黑名单企业名称',
    customRender: renderCompanyNameRelated,
  },
  {
    title: '关联路径详情',
    customRender: (item, index) => {
      const HistoryMap = {
        HISEMPLOY: '历史高管',
        HISLEGAL: '历史法人',
        HISINVEST: '历史股东',
      };
      item.leftInfo = HistoryMap[item.typeDD] || item.roleDD;
      item.rightInfo = HistoryMap[item.typeRelated] || item.roleRelated;
      return pathParser(item, index, {
        left: 'companyNameDD',
        leftInfo: 'leftInfo',
        middle: 'personName',
        rightInfo: 'rightInfo',
        right: 'companyNameRelated',
      } as any);
    },
  },
];

const ShareholderColumns = [
  {
    title: '关联黑名单企业名称',
    customRender: (item) => {
      item.role = '历史股东';
      return renderCompanyNameRelated(item);
    },
  },
  {
    title: '持股比例',
    dataIndex: 'stockpercent',
    width: 80,
    customRender: {
      name: 'template',
      options: {
        template: '{value}%',
      },
    },
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
    width: 100,
  },
  {
    title: '列入日期',
    dataIndex: 'joinDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '黑名单有效期',
    dataIndex: 'duration',
    width: 88,
    customRender: {
      name: 'blacklist',
    },
  },
];

const ForeignInvestmentColumns = [
  {
    title: '关联黑名单企业名称',
    customRender: (item) => {
      item.role = '历史对外投资';
      return renderCompanyNameRelated(item);
    },
  },
  {
    title: '持股比例',
    dataIndex: 'stockpercent',
    width: 80,
    customRender: {
      name: 'template',
      options: {
        template: '{value}%',
      },
    },
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
    width: 100,
    ellipsis: true,
  },
  {
    title: '列入日期',
    dataIndex: 'joinDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '黑名单有效期',
    dataIndex: 'duration',
    // FIXME: 与 column-render 逻辑重复
    customRender: (duration) => {
      if (duration === undefined || duration === null) {
        return '';
      }
      const BLACKLIST_DURATION_MAP = {
        '0': '3个月',
        '1': '6个月',
        '2': '1年',
        '3': '2年',
        '4': '3年',
        '5': '5年',
        '-1': '不限',
      };
      return BLACKLIST_DURATION_MAP[duration];
    },
  },
];

// ------------- 黑名单排查 -------------

// 潜在利益冲突
const ConflictInterestColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
    width: 150,
  },
  {
    title: '“潜在利冲”人员编号',
    customRender: personNameWithTagRenderer,
    width: 150,
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
    width: 150,
  },
];

// 潜在利益冲突 相同电话
const ConflictInterestSamePhoneColumns = [
  {
    title: '联系人',
    dataIndex: 'contacts',
    width: 60,
  },
  {
    title: '电话号码',
    dataIndex: 'phones',
  },
  {
    title: '“潜在利冲”人员编号',
    dataIndex: 'personNo',
  },
  {
    title: '“潜在利冲”人员姓名',
    dataIndex: 'name',
    width: 60,
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
    width: 60,
  },
];

// 潜在利益冲突 对外投资
const StaffForeignInvestmentColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
    width: 60,
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
    width: 100,
  },
  {
    title: '持股比例',
    dataIndex: 'stockPercent',
    width: 80,
    customRender: (stockPercent) => {
      const n = parseFloat(stockPercent);
      if (!n) {
        return '';
      }
      return `${n}%`;
    },
  },
  {
    title: '“潜在利冲”人员编号',
    customRender: personNameWithTagRenderer,
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
  },
];

export const StaffWorkingOutsideForeignInvestmentColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
    width: 150,
  },
  {
    title: '“潜在利冲”人员',
    customRender: personNameWithTagRenderer,
    width: 150,
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
    width: 150,
  },
];

// 疑似潜在利益冲突
export const SuspectedInterestConflictColumns = [
  {
    title: '疑似关系',
    customRender: suspectedRelationship,
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
    width: 150,
  },
  {
    title: '“疑似潜在利冲”人员',
    customRender: personNameWithTagRenderer,
    width: 150,
  },
  {
    title: '“疑似潜在利冲”人员分组',
    dataIndex: 'group',
    width: 150,
  },
];

// 注销备案
const CancellationOfFilingColumns = [
  {
    title: '清算组备案日期',
    dataIndex: 'LiqBADate',
    width: 220,
  },
  {
    title: '债权人公告日期',
    width: 220,
    customRender: (record) => {
      const start = dateTrans(record.PublicStartDate);
      if (start === '-' || !start) {
        return '-';
      }
      const end = dateTrans(record.PublicEndDate);
      return `${start} - ${end}`;
    },
  },
  {
    title: '公告状态',
    dataIndex: 'NoticeStatus',
  },

  // {
  //   title: '注销原因',
  //   dataIndex: 'Detail.LiqBAInfo.CancelReason',
  //   width: 100,
  // },
  // {
  //   title: '公告内容',
  //   dataIndex: 'Detail.CreditorNoticeInfo.NoticeContent',
  // },
  // {
  //   title: '公告期',
  //   dataIndex: 'CreditorNoticeDate',
  // },
  // {
  //   title: '债权申报联系人',
  //   dataIndex: 'Detail.CreditorNoticeInfo.ClaimsDeclarationMember',
  // },
  // {
  //   title: '债权申报联系电话',
  //   dataIndex: 'Detail.CreditorNoticeInfo.ClaimsDeclarationTelNo',
  // },
  // {
  //   title: '债权申报地址',
  //   dataIndex: 'Detail.CreditorNoticeInfo.ClaimsDeclarationAddress',
  // },
  // {
  //   title: '登记机关',
  //   dataIndex: 'Detail.LiqBAInfo.BelongOrg',
  // },
];

// 合作方交叉重叠
const ShareholdingRelationshipColumns = [
  {
    title: '关联企业名称',
    width: 158,
    customRender: (item) => {
      item.role = '历史股东';
      return renderCompanyNameRelated(item);
    },
  },
  {
    title: '持股比例',
    dataIndex: 'stockpercent',
    width: 80,
    customRender: {
      name: 'template',
      options: {
        template: '{value}%',
      },
    },
  },
  {
    title: '关联路径详情',
    customRender: {
      name: 'path',
      options: {
        middle: 'companyNameDD',
        rightInfo: 'stockpercent',
        right: 'companyNameRelated',
      },
    },
  },
];

const ServeRelationshipColumns = [
  {
    title: '关联人员',
    dataIndex: 'personName',
    width: 80,
  },
  {
    title: '关联企业名称',
    dataIndex: 'companyNameRelated',
    width: 158,
  },
  {
    title: '关联路径详情',
    customRender: (item, index) => {
      const HistoryMap = {
        HISEMPLOY: '历史高管',
        HISLEGAL: '历史法人',
        HISINVEST: '历史股东',
      };
      item.leftInfo = HistoryMap[item.typeDD] || item.roleDD;
      item.rightInfo = HistoryMap[item.typeRelated] || item.roleRelated;
      return pathParser(item, index, {
        left: 'companyNameDD',
        leftInfo: 'leftInfo',
        middle: 'personName',
        rightInfo: 'rightInfo',
        right: 'companyNameRelated',
      } as any);
    },
  },
];

const PartnershipColumns = [
  {
    title: '关联企业名称',
    customRender: (item) => {
      item.role = '历史对外投资';
      return renderCompanyNameRelated(item);
    },
  },
  {
    title: '持股比例',
    dataIndex: 'stockpercent',
    width: 80,
    customRender: {
      name: 'template',
      options: {
        template: '{value}%',
      },
    },
  },
  {
    title: '关联路径详情',
    customRender: (item, index) => {
      const rightInfo = item.stockpercent ? 'stockpercent' : 'role';
      return pathParser(item, index, {
        middle: 'companyNameDD',
        rightInfo,
        right: 'companyNameRelated',
      } as any);
    },
  },
];

// [未开放维度] 子公司被列入失信被执行人
// const SubsidiaryPersonCreditCurrentColumns = [
//   {
//     title: '案号',
//     width: 158,
//     dataIndex: 'CaseNo',
//   },
//   {
//     title: '被执行子公司',
//     customRender: {
//       name: 'NameAndKeyNo',
//     },
//   },
//   {
//     title: '执行法院',
//     customCell: (item) => {
//       return {
//         domProps: {
//           innerHTML: Array.isArray(item.Court) ? item.Court[0] : item.Court,
//         },
//       };
//     },
//   },
//   {
//     title: '执行依据文号',
//     customRender: {
//       name: 'OrgNo',
//     },
//   },
//   {
//     title: '涉案金额(元)',
//     key: 'amount',
//     dataIndex: 'Amount',
//     customRender: {
//       name: 'money',
//     },
//   },
//   {
//     title: '履行情况',
//     // customCell: (item) => {
//     //   return {
//     //     domProps: {
//     //       innerHTML: item.executeStatus || item.ExecuteStatus || '-',
//     //     },
//     //   };
//     // },
//   },
//   { title: '失信行为', dataIndex: 'ActionRemark' },
//   {
//     key: 'liandate',
//     title: '立案日期',
//     customRender: {
//       name: 'date',
//       options: {
//         pattern: 'X',
//       },
//     },
//
//     // customRender: (item) => {
//     //   return dateFormat(item.lianDate || item.LianDate);
//     // },
//   },
//   {
//     title: '发布日期',
//     key: 'publishdate',
//     customRender: {
//       name: 'date',
//       options: {
//         pattern: 'X',
//       },
//     },
//     // customRender: (item) => {
//     //   return dateFormat(item.publishDate || item.PublishDate);
//     // },
//   },
// ];

const StandalonePartnersColumns = [
  {
    title: '股东名称',
    dataIndex: 'StockName',
    width: 150,
  },
  {
    title: '持股比例',
    dataIndex: 'StockPercent',
    width: 80,
  },
  {
    title: '认缴出资额(万元)',
    dataIndex: 'ShouldCapi',
    width: 150,
  },
  {
    title: '认缴出资日期',
    dataIndex: 'ShoudDate',
  },
];
const StandaloneIPOPartnersColumns = [
  {
    title: '股东名称',
    dataIndex: 'StockName',
  },
  {
    title: '持股比例',
    dataIndex: 'StockPercent',
    width: 150,
  },
  {
    title: '持股数(股)',
    dataIndex: 'ShouldCapi',
    width: 150,
  },
  {
    title: '最终受益股份',
    dataIndex: 'FinalBenefitPercent',
    width: 150,
  },
];
const StandaloneEmployeesColumns = [
  {
    title: '姓名',
    dataIndex: 'Name',
  },
  {
    title: '职务',
    dataIndex: 'Job',
    width: 150,
  },
  {
    title: '持股比例',
    dataIndex: 'StockPercent',
    width: 150,
  },
  {
    title: '最终受益股份',
    dataIndex: 'FinalBenefitPercent',
    width: 150,
  },
];
const StandaloneEmployeesOrgColumns = [
  {
    title: '姓名',
    dataIndex: 'PersonName',
  },
  {
    title: '职务',
    dataIndex: 'Position',
    width: 150,
  },
  {
    title: '成员性别',
    width: 150,
    customRender: (item) => {
      return '-';
    },
  },
  {
    title: '任期',
    dataIndex: 'EndDate',
    width: 150,
  },
];
const StandaloneBranchesColumns = [
  {
    title: '企业名称',
    dataIndex: 'Name',
  },
  {
    title: '负责人',
    dataIndex: 'Oper.Name',
    width: 60,
  },
  {
    title: '地区',
    dataIndex: 'Area',
    customRender: (area) => {
      if (!area) {
        return '';
      }
      return filter(uniq([area.Province, area.City, area.County]), (l) => !!l).join('');
    },
  },
  {
    title: '成立日期',
    dataIndex: 'StartDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '登记状态',
    dataIndex: 'ShortStatus',
    width: 88,
    customRender: {
      name: 'companyStatus',
    },
  },
];
const StandaloneChangeDiffInfoColumns = [
  {
    title: '变更项目',
    dataIndex: 'ChangeItem',
  },
  {
    title: '变更日期',
    dataIndex: 'ChangeDate',
    width: 81,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '变更前',
    dataIndex: 'BeforeInfo',
    width: 200,
    customRender: (list) => {
      if (!list?.[0]?.Content) {
        return '-';
      }
      const html = list
        .map((item) => {
          let content = item.Content;
          if (item?.PercentDiff) {
            if (item.PercentDiff.indexOf('-') > -1) {
              content += `<em style="color: #00ad65">（${item.PercentDiff}）</em>`;
            } else {
              content += `<em>（${item.PercentDiff}）</em>`;
            }
          }
          return content;
        })
        .join('，');
      // const html = list.map(({ Content }) => Content).join('，');
      return `<div class="diff-result">${html}</div>`;
    },
  },
  {
    title: '变更后',
    dataIndex: 'AfterInfo',
    width: 200,
    customRender: (list) => {
      if (!list?.length) {
        return '-';
      }
      const html = list
        .map((item) => {
          if (item?.PercentDiff) {
            if (item.PercentDiff.indexOf('-') > -1) {
              item.Content += `<em style="color: #00ad65">（${item.PercentDiff}）</em>`;
            } else {
              item.Content += `<em>（${item.PercentDiff}）</em>`;
            }
          }
          return item.Content;
        })
        .join('，');

      // const html = list.map(({ Content }) => Content).join('，');
      return `<div class="diff-result">${html}</div>`;
    },
  },
];

/**
 * 疑似实际控制人
 * 李宁（中国）体育用品有限公司
 * https://gitlab.greatld.com:18888/qcc/pc-web/-/blob/3aa8f8aedb5a8a4ff4c471e21dd87a41d8adefdd/src/components/app-datalist/components/yisiaclist/component.js#L34
 */
const ActualControllerV5YisiActual = [
  {
    title: '疑似实际控制人',
    dataIndex: 'Name',
  },
  {
    title: '判定依据',
    width: 350,
    customRender: (item) => {
      const getLegal = (type) => {
        const OperMap = {
          1: '法定代表人',
          2: '执行事务合伙人',
          3: '负责人',
          4: '经营者',
          5: '投资人',
          6: '董事长',
          7: '理事长',
          8: '代表人',
        };
        return OperMap[type];
      };

      const actualTypes = item?.ActualType ?? [];
      return actualTypes.map((type: string | number) => {
        const actualType = Number(type);
        if (actualType === 1) {
          return `<div>企业表决权最大${item.ControlPercent ? `（表决权比例${item.ControlPercent}）` : ''}</div>`;
        }
        if (actualType === 2 && item.PercentTotal && item.PercentTotal !== '0%') {
          return `<div>总持股比例最高（总持股比例${item.PercentTotal}）</div>`;
        }
        if (actualType === 3 && item.Job) {
          let content = `担任${item.Job}`;
          if (getLegal(item.OperType)) {
            content += `、${getLegal(item.OperType)}`;
          }
          return `<div>${content}</div>`;
        }
        return '<div>-</div>';
      });
    },
  },
];

/**
 * 乐视网信息技术（北京）股份有限公司
 */
const ActualControllerV5ActualColumns1 = [
  {
    title: '实际控制人',
    dataIndex: 'Name',
  },
  {
    title: '表决权比例',
    dataIndex: 'ControlPercent',
    width: 150,
  },
];

const ActualControllerV5ActualColumns2 = [
  {
    title: '实际控制人',
    dataIndex: 'Name',
  },
  {
    title: '直接持股比例',
    dataIndex: 'Percent',
    width: 150,
  },
  {
    title: '表决权比例',
    dataIndex: 'ControlPercent',
    width: 150,
  },
];

const ActualControllerV5ActualColumns2WithTitleAndControlPercent = [
  {
    title: '实际控制人(公示信息)',
    dataIndex: 'Name',
  },
  {
    title: '直接持股比例',
    dataIndex: 'Percent',
    width: 150,
  },
  {
    title: '表决权比例(共同持有)',
    dataIndex: 'ControlPercent',
    width: 150,
    attrs: (row, index, dataSource) => {
      // 如果前一条与当前一条内容重合，则不显示
      const prevRow = dataSource[index - 1];
      if (prevRow?.ControlPercent === row?.ControlPercent) {
        return {
          style: 'display: none;',
        };
      }
      // 如果是第一个, 增加 rowspan 属性进行行合并
      const lastDataSource = dataSource.slice(index);
      const rowSpanSize = lastDataSource.reduce((total, item) => {
        return item?.ControlPercent === row?.ControlPercent ? total + 1 : total;
      }, 0);
      return {
        rowSpan: rowSpanSize,
        style: 'vertical-align: middle;',
      };
    },
  },
];

const ActualControllerV5ActualColumns2WithControlPercent = [
  {
    title: '实际控制人',
    dataIndex: 'Name',
  },
  {
    title: '直接持股比例',
    dataIndex: 'Percent',
    width: 150,
  },
  {
    title: '表决权比例(共同持有)',
    dataIndex: 'ControlPercent',
    width: 150,
    attrs: (row, index, dataSource) => {
      // 如果前一条与当前一条内容重合，则不显示
      const prevRow = dataSource[index - 1];
      if (prevRow?.ControlPercent === row?.ControlPercent) {
        return {
          style: 'display: none;',
        };
      }
      // 如果是第一个, 增加 rowspan 属性进行行合并
      const lastDataSource = dataSource.slice(index);
      const rowSpanSize = lastDataSource.reduce((total, item) => {
        return item?.ControlPercent === row?.ControlPercent ? total + 1 : total;
      }, 0);
      return {
        rowSpan: rowSpanSize,
        style: 'vertical-align: middle;',
      };
    },
  },
];

const ActualControllerV5ActualColumns2WithPercentTotal = [
  {
    title: '实际控制人',
    dataIndex: 'Name',
  },
  {
    title: '直接持股比例',
    dataIndex: 'Percent',
    width: 150,
  },
  {
    title: '总持股比例',
    dataIndex: 'PercentTotal',
    width: 150,
  },
  {
    title: '表决权比例',
    dataIndex: 'ControlPercent',
    width: 150,
  },
];

const ActualControllerV5ActualColumns3 = [
  {
    title: '实际控制人',
    dataIndex: 'Name',
  },
  {
    title: '总持股比例',
    dataIndex: 'PercentTotal',
    width: 150,
  },
];
/**
 * 宁德时代新能源科技股份有限公司
 */
const ActualControllerV5ActualColumns3WithTitle = [
  {
    title: '实际控制人（公示信息）',
    dataIndex: 'Name',
  },
  {
    title: '总持股比例',
    dataIndex: 'PercentTotal',
    width: 150,
  },
];

/**
 * 锐奇控股股份有限公司、思必驰科技股份有限公司
 */
const ActualControllerV5ActualColumns4 = [
  {
    title: '实际控制人',
    dataIndex: 'Name',
  },
  {
    title: '直接持股比例',
    dataIndex: 'Percent',
    width: 150,
  },
  {
    title: '总持股比例',
    dataIndex: 'PercentTotal',
    width: 150,
  },
];

/**
 * 湖南艾华集团股份有限公司
 */
const ActualControllerV5ActualColumns4WithTitle = [
  {
    title: '实际控制人（公示信息）',
    dataIndex: 'Name',
  },
  {
    title: '直接持股比例',
    dataIndex: 'Percent',
    width: 150,
  },
  {
    title: '总持股比例',
    dataIndex: 'PercentTotal',
    width: 150,
  },
];

/**
 * 中铁投资集团有限公司
 */
const ActualControllerV5ActualColumns5 = [
  {
    title: '实际控制人',
    dataIndex: 'Name',
  },
  {
    title: '总持股比例',
    dataIndex: 'PercentTotal',
    width: 150,
  },
  {
    title: '表决权比例',
    dataIndex: 'ControlPercent',
    width: 150,
  },
];

const ActualControllerV5ActualColumns5WithPercentTotal = [
  {
    title: '实际控制人',
    dataIndex: 'Name',
  },
  {
    title: '总持股比例',
    dataIndex: 'PercentTotal',
    width: 150,
  },
  {
    title: '表决权比例(共同持有)',
    dataIndex: 'ControlPercent',
    width: 150,
    attrs: (row, index, dataSource) => {
      // 如果前一条与当前一条内容重合，则不显示
      const prevRow = dataSource[index - 1];
      if (prevRow?.ControlPercent === row?.ControlPercent) {
        return {
          style: 'display: none;',
        };
      }
      // 如果是第一个, 增加 rowspan 属性进行行合并
      const lastDataSource = dataSource.slice(index);
      const rowSpanSize = lastDataSource.reduce((total, item) => {
        return item?.ControlPercent === row?.ControlPercent ? total + 1 : total;
      }, 0);
      return {
        rowSpan: rowSpanSize,
        style: 'vertical-align: middle;',
      };
    },
  },
];

/**
 * 中国供销集团有限公司
 */
const ActualControllerV5ActualColumns6 = [
  {
    title: '实际控制人',
    dataIndex: 'Name',
  },
  {
    title: '直接持股比例',
    dataIndex: 'Percent',
    width: 150,
  },
];
/**
 * 中国供销集团有限公司
 */
const ActualControllerV5ActualColumns6WithTitle = [
  {
    title: '实际控制人(公示信息)',
    dataIndex: 'Name',
  },
  {
    title: '直接持股比例',
    dataIndex: 'Percent',
    width: 150,
  },
];

const StandaloneHoldingCompanyColumns = [
  {
    title: '控制企业名称',
    dataIndex: 'Name',
    width: 158,
  },
  {
    title: '投资比例',
    dataIndex: 'PercentTotal',
    width: 80,
  },
  {
    title: '地区',
    dataIndex: 'Province',
    width: 60,
  },
  {
    title: '行业',
    dataIndex: 'Industry',
    width: 100,
  },
  {
    title: '股权链',
    customRender: (_, item, originalData) => {
      const paths = item.Paths || [];
      if (paths.length === 0) {
        return '';
      }
      return paths
        .map((pList, index) => {
          let percentTotal;
          const entities: string[] = [];
          pList.forEach((p, j) => {
            entities.push(`（${p.Percent}）${p.Name}`);
            if (j === pList.length - 1) {
              percentTotal = p.PercentTotal; // NOTE: 取路径中最后一个 `PercentTotal`
            }
          });
          const description = `路径${index + 1}（占比约 ${percentTotal} ）`;
          const graph = [originalData.Name, ...entities].join(' → ');
          return `<div>
            <div style="font-weight: bold;">${description}</div>
            <div>${graph}</div>
          </div>`;
        })
        .join('');
    },
  },
];

// 在外任职
const PunishedEmployeesWorkingOutsideColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
    width: 150,
  },
  {
    title: '人员编号',
    customRender: personNameWithTagRenderer,
    width: 150,
  },
  {
    title: '人员分组',
    dataIndex: 'group',
    width: 150,
  },
];

// 对外投资
const PunishedEmployeesForeignInvestmentColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
    width: 60,
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
    width: 150,
  },
  {
    title: '持股比例',
    dataIndex: 'stockPercent',
    width: 80,
    customRender: (stockPercent) => {
      const n = parseFloat(stockPercent);
      if (!n) {
        return '';
      }
      return `${n}%`;
    },
  },
  {
    title: '人员编号',
    customRender: personNameWithTagRenderer,
    width: 150,
  },
  {
    title: '人员分组',
    dataIndex: 'group',
    width: 150,
  },
];

// 公司、法定代表人/股东/董监高存在涉贿、不正当竞争等刑事犯罪行为
export const CompanyOrMainMembersCriminalOffenceColumns = [
  {
    title: '案件名称/案号',
    width: 158,
    customRender: (item) => {
      let node = '';
      if (item.CaseName) {
        node += `<div>${item.CaseName}</div>`;
      }
      if (Array.isArray(item.AnNoList) && item.AnNoList.length > 0) {
        node += `<div style="color: #808080;">${item.AnNoList.join('、')}</div>`;
      }
      if (!node) {
        return '-';
      }
      return node;
    },
  },
  // {
  //   title: '案件身份',
  //   dataIndex: 'CaseRoleSearch',
  //   customRender: (CaseRoleSearch, item, originalData) => {
  //     if (!CaseRoleSearch) {
  //       return '-';
  //     }
  //     try {
  //       const D = JSON.parse(CaseRoleSearch)?.find((role) => [originalData.companyName].includes(role.P))?.D;
  //       if (!D) return '-';
  //       return `<span>${D?.split(',').join('<br />')}</span>`;
  //     } catch (error) {
  //       console.error(error);
  //       return '-';
  //     }
  //   },
  // },

  {
    title: '当事人',
    dataIndex: 'RoleAmt',
    width: 150,
    customRender: (entities) => {
      if (!entities || !entities.length) {
        return '-';
      }
      return entities.reduce((acc, entity: Record<string, any>) => {
        const roles = entity?.Job ? entity?.Job.split(',').reduce((ra, role) => ra + `<span class="status-tag golden">${role}</span>`, '') : '';
        return acc + `<div><span>${entity.R} - ${entity.P}</span> ${roles}</div>`;
      }, '');
    },
  },
  {
    title: '案由',
    dataIndex: 'CaseReason',
    width: 100,
  },
  // {
  //   title: '案件金额(元)',
  //   dataIndex: 'AmtInfo',
  //   width: 60,
  //   customRender: {
  //     name: 'json',
  //     options: {
  //       file.parser: 'money',
  //     },
  //   },
  // },
  {
    title: '最新案件进程',
    customRender: (item) => {
      return `
        <div>${item.LastestDate ? moment(item.LastestDate * 1000).format('YYYY-MM-DD') : '-'}</div>
        <div>${item.LatestTrialRound}</div>
      `;
    },
  },
  {
    title: '法院',
    dataIndex: 'CourtList',
    // width: 100,
    customRender: {
      name: 'entities',
    },
  },
];

// 近三年负面新闻
const NegativeNewsRecentColumns = [
  {
    title: '标题/标签/来源',
    width: 220,
    customRender: ({ title, codedesc = [], tagsnew = [], source }) => {
      let node = `<div>${title}</div>`;
      const tags = [...codedesc, ...tagsnew];
      if (tags.length > 0) {
        node += '<div>';
        node += tags
          .filter((tag) => !!tag)
          .map((tag) => `<span class="status-tag default">#${tag}</span>`)
          .join('');
        node += '</div>';
      }
      if (source) {
        node += `<div>来源: ${source}</div>`;
      }
      return node;
    },
  },
  {
    title: '发布时间',
    dataIndex: 'publishtime',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '摘要',
    dataIndex: 'summary',
  },
];

const DisputeColumns = [
  {
    title: '文书标题/案号',
    width: 100,
    customRender: (item: Record<string, any>) => {
      if (!item.casename && !item.caseno) {
        return '-';
      }

      let node = '';
      if (item.casename) {
        node += `<div>${item.casename}</div>`;
      }
      if (item.caseno) {
        node += `<div style="color: #808080;">${item.caseno}</div>`;
      }
      if (Array.isArray(item.involveTags) && item.involveTags.length > 0) {
        const tags = item.involveTags
          .map((tagText) => {
            return `<span class="status-tag default">${tagText}</span>`;
          })
          .join('');
        node += `<div>${tags}</div>`;
      }
      return `<div>${node}</div>`;
    },
  },
  {
    title: '案由',
    dataIndex: 'casereason',
    width: 60,
  },
  {
    title: '当事人',
    width: 224,
    customRender: (item: Record<string, any>) => {
      // 获取当事人信息
      const getParties = (_item) => {
        if (_item.caserolegroupbyrolename?.length) {
          return _item.caserolegroupbyrolename
            ?.filter((item2) => item2.LawyerTag === 0)
            ?.flatMap((item2) =>
              item2.DetailList?.map((item3) => ({
                ...item3,
                Role: item2.Role,
              })),
            );
        }

        if (_item.caserole?.length) {
          return _item.caserole.map((item2) => ({
            ...item2,
            Role: item2.R,
            Name: item2.P || item2.ShowName,
            KeyNo: item2.N,
            Org: item2.O,
          }));
        }
        return [];
      };

      const parties = [
        ...getParties(item),
        ...(item.involveRole || []).map((role) => ({
          ...role,
          Role: role.Tag,
        })),
      ];

      if (!parties?.length) {
        return '-';
      }

      return parties
        .map((info) => {
          const content = [];
          content.push(`${info.Role} - ${info.Name}`);
          // 角色为原告、上诉人等合同纠纷，处理文本显示颜色
          if (info.JudgeResultDescription) {
            const theme = getColorByDescription(info.JudgeResultDescription);
            content.push(`<span style="color: ${theme.color}">[${info.JudgeResultDescription}]</span>`);
          }
          if (info.Job) {
            content.push(`<span class="status-tag golden">${info.Job}</span>`);
          }
          return `<div>${content.join(' ')}</div>`;
        })
        .join('');
    },
  },
  {
    title: '案件金额(元)',
    dataIndex: 'amountinvolved',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  {
    title: '裁判结果',
    dataIndex: 'judgeresult',
    // width: 120,
  },
  // {
  //   title: '裁判日期',
  //   dataIndex: 'judgedate',
  //   customRender: {
  //     name: 'date',
  //     options: {
  //       pattern: 'X',
  //     },
  //   },
  // },
  {
    title: '发布日期',
    dataIndex: 'submitdate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

const SpotCheckColumns = [
  {
    title: '检查实施机关',
    dataIndex: 'court',
    width: 100,
  },
  {
    title: '类型',
    dataIndex: 'casereasontype',
    width: 60,
  },
  {
    title: '日期',
    dataIndex: 'publishdate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '结果',
    customRender: (item) => {
      const getColorByText = (value: string) => {
        if (value !== '未发现问题') {
          return '#F04040';
        }
        return 'inherit';
      };
      return `<span style="color: ${getColorByText(item.punishResult)}">${item.punishResult || '-'}</span>`;
    },
  },
];

const SameControllRelation = [
  {
    title: '关联企业名称',
    dataIndex: 'companyName',
    width: 158,
  },
  {
    title: '实际控制人',
    dataIndex: 'name',
    width: 84,
  },
  {
    title: '控制路径',
    customRender: controlRelationsPath,
  },
];

/**
 * 第三方或内部黑名单关联关系表格列渲染逻辑
 * @param {object} item 行数据
 * @param {boolean} showRelationReason 是否显示关联的原因（内部黑名单关联）
 */
export const relationDetailsColumnRender = (item: Record<string, any>, showRelationReason = false) => {
  const groupMaps = groupBy(item.relationTypes, (item) => item.relationType); // 按 relationType 分组
  const groupPairs = toPairs(groupMaps); // map to tuple, eg: `['投资关联', []]`

  const result = groupPairs
    .map(([groupType, groupItems]) => {
      // 使用 `typeDesc` 作为关联类型（相同实际控制人）
      const relationTypeColumn = `<td style="width: 150px;">${groupItems.reduce((r, { typeDesc }) => typeDesc || r, groupType)}</td>`;

      let relationPathColumn = groupItems
        .map((data, index) => {
          // 关联类型
          // const relationType = data.relationType;
          // 关联路径
          let relationPath = '';

          if (['InvestorsRelationship', 'ForeignInvestment'].includes(data.relationTypeKey) || data.direction) {
            // 投资关系 / 对外投资关系
            const stockPercentage = data.stockpercent ? `（${data.stockpercent}%）` : '';
            // 关联路径详情（控股或被控股）
            const entities = [data.companyNameDD, data.companyNameRelated];
            if (data.direction === -24) {
              entities.reverse();
            }
            const [left, right] = entities;
            relationPath = [left, `${stockPercentage}${right}`].join(` → `);
          } else if (['ActualController', 'MainInfoUpdateBeneficiary'].includes(data.relationTypeKey)) {
            // 相同控制人 & 相同受益所有人
            relationPath = pathParser(data, index, {
              left: 'companyNameDD',
              leftInfo: 'relationType',
              middle: 'personName',
              rightInfo: 'relationType',
              right: 'companyNameRelated',
            } as any);
          } else if (data.relationTypeKey && data.relationType) {
            // 董监高/法人关系
            relationPath = pathParser(data, index, {
              left: 'companyNameDD',
              leftInfo: 'roleDD',
              middle: 'personName',
              rightInfo: 'roleRelated',
              right: 'companyNameRelated',
            } as any);
          } else {
            relationPath = '';
          }
          return relationPath ? `<div>${relationPath}</div>` : '';
        })
        .join('');

      relationPathColumn = `<td>${relationPathColumn}</td>`;

      // 关联关系
      const relationReason = `<td style="width: 150px;">${groupItems?.[0]?.reason ?? '-'}</td>`;

      return `<tr>${showRelationReason ? relationReason : ''}${relationTypeColumn}${relationPathColumn}</tr>`;
    })
    ?.join('');

  return result ? `<table class="plain-table"><tbody>${result}</tbody></table>` : '-';
};

const mergeRelationPaths = (relationPaths: any[]) => {
  // const uniquePathsMap = new Map<string, any>();
  const dedupByEdgeMap = new Map<string, any>();

  relationPaths.forEach((nodes) => {
    // 基于 edges 生成唯一ID
    const dedupEdgeId = nodes
      .reduce((r, c) => {
        if (isEdge(c)) {
          return r.concat([`${c.startid}-${c.endid}`]);
        }
        return r.concat();
      }, [])
      .join('-');
    // 命中
    if (dedupByEdgeMap.has(dedupEdgeId)) {
      const newData = dedupByEdgeMap.get(dedupEdgeId).map((t, i) => {
        if (isEdge(t)) {
          return {
            ...t,
            roles: uniq([...(t?.roles || []), ...(nodes[i]?.roles || [])]),
            data: uniq([...(t?.data || []), ...(nodes[i]?.data || [])]),
            groups: [...(t?.groups || []), nodes[i]],
          };
        }
        return t;
      });
      dedupByEdgeMap.set(dedupEdgeId, newData);
    } else {
      dedupByEdgeMap.set(dedupEdgeId, nodes);
    }
  });

  const result = [...dedupByEdgeMap.values()];
  return result;
};

export const convertPathToGraph = (relationPaths: any[], withPathTip = true) => {
  const result = [];
  relationPaths.forEach((nodes, index, source) => {
    const row = [];
    nodes.forEach((node) => {
      if (isEdge(node)) {
        let roles = getRoles(node);
        const groupRoles = (node?.groups ?? []).flatMap((group) => getRoles(group));
        roles = [...roles, ...groupRoles, ...(node?.roles || [])];
        roles = uniq(roles);
        row.push(drawEdge(node.direction, roles.join(', ')));
      } else if (isPerson(node)) {
        row.push(node['Person.name']);
      } else if (isCompany(node)) {
        row.push(node['Company.name'] || node.name);
      }
    });
    if (source.length > 1 && withPathTip) {
      result.push(`<div style="font-weight: bold;">路径 ${index + 1}</div>`);
    }
    result.push(`<div>${row.join('')}</div>`);
  });

  return result.join('');
};

export const convertSuspectPathToGraph = (relationPaths: any[], sperate = '<br>') => {
  const result = [];
  relationPaths.forEach((relation) => {
    const pathArr = [];
    const types = relation.map((item) => item.roleType).filter(Boolean);
    relation.forEach((nodes) => {
      if (nodes.type === 'edge') {
        // 有详细内容的
        const countData = nodes.data?.filter((item) => ['Address', 'Mail', 'ContactNumber'].includes(item.type)) || [];
        countData.forEach((data) => {
          if (data.type === 'Mail') {
            result.push(`相同邮箱: ${data?.data.join(', ')}`);
          } else if (data.type === 'Address') {
            result.push(`相同地址: ${data?.data?.map((_data) => _data.address).join(', ')}`);
          } else {
            result.push(`相同电话: ${uniq(data?.data?.map((_data) => _data.t)).join(', ')}`);
          }
        });

        const pathData = nodes?.data?.filter((item) => !['Address', 'Mail', 'ContactNumber'].includes(item.type));
        const newRoles = nodes?.roles?.filter((role) => !['相同邮箱', '相同经营地址', '相同电话号码'].includes(role));
        if (newRoles.length) {
          pathArr.push({
            ...nodes,
            roles: newRoles,
            data: pathData,
          });
        }
      } else if (uniq([...types, ...['Address', 'Mail', 'ContactNumber']]).length > 3) {
        pathArr.push(nodes);
      }
    });
    result.push(convertPathToGraph([pathArr], false).replace('<div>', '').replace('</div>', ''));
  });
  return result.filter(Boolean).join(sperate);
};

/**
 * 第三方或内部黑名单关联关系表格列渲染逻辑
 * @param {object} item 行数据
 * @param {boolean} showRelationReason 是否显示关联的原因（内部黑名单关联）
 */
const relationDetailsColumnRenderV2 = (item: Record<string, any>, showRelationReason = false) => {
  let result = '';

  if (showRelationReason) {
    result += `<td style="width: 150px;">${item.reason || '-'}</td>`; // Reason
  }
  let minRelationPath = [];
  // 优先取relations2，有权重差别
  if (item.relations2) {
    minRelationPath = item.relations2;
  } else {
    // 计算合并好的线路
    const mergedRelations: any[] = mergeRelationPaths(item.relationPaths.map(processRelationRoles)); // 直接去重
    const minLength = minBy(mergedRelations, 'length').length;
    minRelationPath = mergedRelations.filter((rel) => rel.length === minLength);
  }
  // 兼容数据
  const roleTypes = flatten(item.shortestPath?.length ? item.shortestPath : minRelationPath)
    .filter((_path: any) => _path.type === 'edge')
    .reduce((arr: [], cur: any) => {
      const groupType = (cur.groups || []).flatMap((group) => group.roleType);
      return [...arr, cur.roleType, ...groupType];
    }, []);
  // 关联类型
  const relationTypes = uniq(roleTypes as string[])
    .map((relation: string) => FULL_RELATIONS_KEY_MAP[relation.toLowerCase()])
    .join('、');

  result += `<td style="width: 150px;">${relationTypes}</td>`; // RelationType
  // 关联路径，取最短路径
  const pathSource = convertPathToGraph(minRelationPath); // 按类型渲染
  result += `<td>${pathSource}</td>`; // Path
  return result ? `<table class="plain-table"><tbody><tr>${result}</tr></tbody></table>` : '-';
};

/**
 * 第三方或内部黑名单表格列疑似关联渲染逻辑
 * @param {object} item 行数据
 * @param {boolean} showRelationReason 是否显示关联的原因（内部黑名单关联）
 */
const relationSuspectedRelationColumnRender = (item: Record<string, any>, showRelationReason = false) => {
  let result = '';

  if (showRelationReason) {
    result += `<td style="width: 150px;">${item.reason || '-'}</td>`; // Reason
  }
  // 关联类型
  const relationType = uniq(item.relationTypes).join('、');

  result += `<td style="width: 150px;">${relationType}</td>`; // RelationType
  // 关联路径，取最短路径
  const pathSource = convertSuspectPathToGraph(item.relations); // 按类型渲染
  result += `<td>${pathSource}</td>`; // Path
  return result ? `<table class="plain-table"><tbody><tr>${result}</tr></tbody></table>` : '-';
};

// 与第三方列表企业存在投资任职关联
const CustomerPartnerInvestigationColumns = [
  {
    title: '关联企业名称',
    dataIndex: 'companyNameRelated',
    width: 158,
  },
  {
    title: '关联类型', // 类似股权链
    hdStyle: 'width: 150px;',
    attrs: {
      colSpan: 2,
      style: 'padding: 0; vertical-align: top;',
    },
    customRender(item) {
      if (item.version === 'V2') {
        return relationDetailsColumnRenderV2(item, false);
      }
      return relationDetailsColumnRender(item, false);
    },
  },
  {
    title: '关联路径详情',
    attrs: {
      colSpan: 0,
      style: 'display: none',
    },
  },
];

// 与第三方列表企业存在交叉重叠疑似关联
const CustomerSuspectedRelationColumns = [
  ...CustomerPartnerInvestigationColumns.slice(0, 1),
  {
    title: '关联类型', // 类似股权链
    hdStyle: 'width: 150px;',
    attrs: {
      colSpan: 2,
      style: 'padding: 0; vertical-align: top;',
    },
    customRender(item) {
      return relationSuspectedRelationColumnRender(item, false);
    },
  },
  ...CustomerPartnerInvestigationColumns.slice(2),
];

// 与内部黑名单企业存在投资任职关联
const BlacklistPartnerInvestigationColumns = [
  ...CustomerPartnerInvestigationColumns.slice(0, 1),
  {
    title: '列入原因',
    hdStyle: 'width: 150px;',
    attrs: {
      colSpan: 0,
      style: 'display: none',
    },
  },
  {
    title: '关联类型', // 类似股权链
    hdStyle: 'width: 150px;',
    attrs: {
      colSpan: 3,
      style: 'padding: 0; vertical-align: top;',
    },
    customRender(item) {
      if (item.version === 'V2') {
        return relationDetailsColumnRenderV2(item, true);
      }
      return relationDetailsColumnRender(item, true);
    },
  },
  ...CustomerPartnerInvestigationColumns.slice(2),
];

// 与内部黑名单企业存在疑似关联关系
const BlacklistSuspectedRelationColumns = [
  ...BlacklistPartnerInvestigationColumns.slice(0, 2),
  {
    title: '关联类型', // 类似股权链
    hdStyle: 'width: 150px;',
    attrs: {
      colSpan: 3,
      style: 'padding: 0; vertical-align: top;',
    },
    customRender(item) {
      return relationSuspectedRelationColumnRender(item, true);
    },
  },
  ...BlacklistPartnerInvestigationColumns.slice(3),
];
// 票据违约
const BillDefaultsColumns = [
  {
    title: '承兑人',
    dataIndex: 'CompanyName',
    width: 158,
  },
  {
    title: '截止日期',
    dataIndex: 'EndDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '披露日期',
    dataIndex: 'PublishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '逾期余额(元)',
    dataIndex: 'OverdueBalance',
    width: 90,
    customRender: {
      name: 'money',
      options: {
        precision: 2,
      },
    },
  },
  {
    title: '累计逾期发生额(元)',
    dataIndex: 'AccuOverdueAmount',
    width: 134,
    customRender: {
      name: 'money',
      options: {
        precision: 2,
      },
    },
  },
  {
    title: '状态',
    dataIndex: 'OverdueStatus',
    width: 88,
    customRender: (data) => {
      if (data) {
        return `<span class="status-tag ${data.Code === '3' ? 'danger' : 'gray'}">${data.Desc}</span>`;
      }
      return '-';
    },
  },
];

// 税务催缴公告
const TaxCallNoticeColumns = [
  {
    title: '标题',
    width: 220,
    dataIndex: 'title',
  },
  {
    title: '发布机构',
    dataIndex: 'courtname',
    width: 158,
  },
  {
    title: '发布日期',
    dataIndex: 'publicdate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '公告类型',
    dataIndex: 'noticetype',
  },
];

// 税务催缴
const TaxCallNoticeV2Columns = [
  {
    title: '税种',
    dataIndex: 'TaxCategory',
  },
  {
    title: '欠缴金额(元)',
    dataIndex: 'AmountOwed',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  {
    title: '所属期起',
    width: 88,
    dataIndex: 'PeriodStartDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '所属期止',
    width: 88,
    dataIndex: 'PeriodEndDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '缴款期限',
    width: 88,
    dataIndex: 'PaymentDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '主管税务机关',
    dataIndex: 'TaxKeyNoArray',
    customRender: (list: any[]) => {
      if (Array.isArray(list) && list.length > 0) {
        return list.map(({ Name }) => Name).join('、');
      }
      return '-';
    },
  },
  {
    title: '发布日期',
    width: 88,
    dataIndex: 'PublishDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 终本案件
const EndExecutionCaseColumns = [
  {
    title: '案号',
    dataIndex: 'CaseNo',
    width: 60,
  },
  {
    title: '被执行人',
    dataIndex: 'NameAndKeyNo',
    width: 66,
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '疑似申请执行人',
    dataIndex: 'SqrInfo',
    width: 108,
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '未履行金额(元)',
    dataIndex: 'FailureAct',
    width: 105,
    customRender: {
      name: 'money',
      options: {
        precision: 2,
      },
    },
  },
  {
    title: '执行标的(元)',
    dataIndex: 'ExecuteObject',
    width: 90,
    customRender: {
      name: 'money',
      options: {
        precision: 2,
      },
    },
  },
  {
    title: '执行法院',
    dataIndex: 'Court',
  },
  {
    title: '立案日期',
    dataIndex: 'LiAnDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '终本日期',
    dataIndex: 'EndDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 公安通告
const SecurityNoticeColumns = [
  {
    title: '涉案企业',
    dataIndex: 'name',
    width: 158,
  },
  {
    title: '涉案案由',
    dataIndex: 'reason',
    width: 100,
  },
  {
    title: '发布单位',
    dataIndex: 'publishUnit',
  },
  {
    title: '发布日期',
    width: 88,
    dataIndex: 'publishDate',
    customRender: {
      name: 'date',
    },
  },
];

// 监管处罚
const RegulateFinanceColoums = [
  {
    title: '处罚对象名称',
    dataIndex: 'SubjectInfo',
    width: 95,
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '决定文书号',
    dataIndex: 'caseno',
    width: 80,
  },
  {
    title: '违规事实',
    dataIndex: 'punishReason',
  },
  {
    title: '处理结果',
    dataIndex: 'Title',
  },
  {
    title: '处理单位',
    dataIndex: 'Court',
    width: 95,
  },
  {
    title: '处理日期',
    dataIndex: 'PunishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 减资公告
const CapitalReductionColoums = [
  {
    title: '公告企业',
    dataIndex: 'Name',
    width: 158,
  },
  {
    title: '公告内容',
    dataIndex: 'Content',
  },
  {
    title: '公告期限',
    dataIndex: 'NoticePeriod',
    width: 88,
  },
  {
    title: '公告日期',
    dataIndex: 'DecideDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 劳动纠纷
const LaborContractDisputeColumns = [
  {
    title: '案件名称/案号',
    customRender: (item) => {
      let node = '';
      if (item.CaseNameClean) {
        node += `<div>${item.CaseNameClean}</div>`;
      }
      if (Array.isArray(item.AnNoList) && item.AnNoList.length > 0) {
        node += `<div style="color: #808080;">${item.AnNoList.join('、')}</div>`;
      }
      if (Array.isArray(item.CaseType)) {
        const tags = [];
        item.CaseType.forEach((caseType) => {
          tags.push(`<span class="status-tag default">${caseType}</span>`);
        });
        node += `<div>${tags.join('')}</div>`;
      }
      if (!node) {
        return '-';
      }
      return node;
    },
  },
  {
    title: '案件身份',
    dataIndex: 'CaseRoleIdentity',
    width: 120,
  },
  {
    title: '案由',
    dataIndex: 'CaseReason',
    width: 60,
  },
  {
    title: '案件金额(元)',
    dataIndex: 'Amt',
    width: 90,
    customRender: {
      name: 'money',
      options: {
        precision: 2,
      },
    },
  },
  {
    title: '最新案件进程',
    dataIndex: 'LatestDateTrialRound',
    width: 95,
  },
  {
    title: '法院',
    dataIndex: 'CourtList',
    width: 120,
    customRender: (list) => {
      if (!Array.isArray(list)) {
        return '-';
      }
      return list.join('、') || '-';
    },
  },
];

// 资质筛查
const CertificationColumns = [
  {
    title: '资质类型',
    dataIndex: 'certificationType',
    // attrs: (record, originalData) => {
    attrs: (row, index, dataSource) => {
      // 如果前一条与当前一条内容重合，则不显示
      const prevRow = dataSource[index - 1];
      if (prevRow?.certificationType === row.certificationType) {
        return {
          style: 'display: none;',
        };
      }
      // 如果是第一个, 增加 rowspan 属性进行行合并
      const lastDataSource = dataSource.slice(index);
      const rowSpanSize = lastDataSource.reduce((total, item) => {
        return item.certificationType === row.certificationType ? total + 1 : total;
      }, 0);
      return {
        rowSpan: rowSpanSize,
        style: 'vertical-align: middle;',
      };
    },
  },
  {
    title: '资质名称',
    dataIndex: 'name',
  },
  {
    title: '状态',
    dataIndex: 'expirationDesc',
    width: 60,
  },
  {
    title: '有效期',
    customRender: (record) => {
      const std = record.startDate ? moment(record.startDate * 1000).format('YYYY-MM-DD') : '-';
      const edd = record.endDate ? moment(record.endDate * 1000).format('YYYY-MM-DD') : '-';
      if (std === '-' && edd === '-') {
        return '-';
      }
      return `${std} 至 ${edd}`;
    },
  },
];

interface BenefitJobInfo {
  Type: string;
  TypeDesc: string;
  Job: string;
  JobType?: string;
}

const BaseBenefitPersonColumns = [
  {
    title: '受益人名称',
    dataIndex: 'Name',
  },
  {
    title: '最终受益股份',
    dataIndex: 'PercentTotal',
    width: 140,
  },
  {
    title: '受益类型',
    dataIndex: 'BenefitTypeInfo',
    width: 140,
    customRender: (jobInfoList: BenefitJobInfo[]) => {
      if (!jobInfoList || !Array.isArray(jobInfoList)) {
        return '-';
      }
      return jobInfoList
        .map(({ TypeDesc }) => TypeDesc)
        .filter((typeDesc) => !!typeDesc)
        .join(',');
    },
  },
  {
    title: '任职类型',
    dataIndex: 'BenefitTypeInfo',
    width: 140,
    customRender: (jobInfoList: BenefitJobInfo[]) => {
      if (!jobInfoList || !Array.isArray(jobInfoList)) {
        return '-';
      }
      return jobInfoList
        .map(({ Job }) => Job)
        .filter((typeDesc) => !!typeDesc)
        .join(',');
    },
  },
];

// 受益所有人
const BeneficialOwnerColumns = [
  ...BaseBenefitPersonColumns,
  {
    title: '判定理由',
    dataIndex: 'BenefitTypeInfo',
    width: 140,
    customRender: (jobInfoList: BenefitJobInfo[]) => {
      if (!jobInfoList || !Array.isArray(jobInfoList)) {
        return '-';
      }
      const firstBenefitType = jobInfoList[0].Type;
      const jobtype = jobInfoList[0].JobType;
      const list: string[] = [];
      let haskzrtype = false;

      for (const item of jobInfoList) {
        if (item.TypeDesc) {
          list.push(item.TypeDesc);
        }

        if (item.Type === '2') {
          haskzrtype = true;
        }
      }

      if (firstBenefitType === '1') {
        return '穿透识别出直接或间接拥有超过25%公司股权或者表决权的自然人';
      } else if (firstBenefitType === '6') {
        if (jobtype === '6') {
          return '受政府控制的企事业单位，将其存在控制或影响的主要人员识别为受益所有人';
        }
        return '受政府控制的企事业单位，将其法定代表人视同为受益所有人';
      } else if (['7', '8', '9', '10', '11'].includes(firstBenefitType)) {
        return '未能穿透识别出拥有或超过25%公司股权的自然人，将其他存在控制/影响的自然人视为受益所有人';
      } else if (haskzrtype) {
        return `未能穿透识别出拥有或超过25%公司股权的自然人，将${list.join(',')}视同为受益所有人`;
      } else {
        return `未能穿透识别出拥有或超过25%公司股权或者表决权的自然人，将${list.join(',')}视同为受益所有人`;
      }
    },
  },
];

// 受益自然人
const BenefitNaturalPersonColumns = BaseBenefitPersonColumns;

// 税务处罚
const TaxPenaltiesColumns = [
  {
    title: '决定书文号',
    dataIndex: 'docno',
    width: 158,
  },
  {
    title: '违法事实',
    dataIndex: 'casefacts',
    width: 100,
  },
  {
    title: '处罚结果',
    dataIndex: 'Title',
    placeholder: '未公示',
  },
  {
    title: '处罚金额(元)',
    dataIndex: 'punishamount',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  {
    title: '处罚单位',
    dataIndex: 'Court',
    width: 100,
  },
  {
    title: '处罚日期',
    dataIndex: 'punishdate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];
const TaxReminderColumns = [
  {
    title: '税种',
    // width: '15%',
    dataIndex: 'TaxCategory',
  },
  {
    title: '所属期起',
    width: 88,
    dataIndex: 'PeriodStartDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '所属期止',
    width: 88,
    dataIndex: 'PeriodEndDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '申报期限',
    width: 88,
    dataIndex: 'PaymentDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '主管税务机关',
    dataIndex: 'TaxKeyNoArray',
    customRender: (list: any[]) => {
      if (Array.isArray(list) && list.length > 0) {
        return list.map(({ Name }) => Name).join('、');
      }
      return '-';
    },
  },
  {
    title: '发布日期',
    width: 88,
    dataIndex: 'PublishDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

const StockPledgeColumns = [
  {
    title: '质押人',
    // width: '15%',
    dataIndex: 'Holders',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '质押人参股企业',
    // width: '15%',
    dataIndex: 'Companys',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '质押权人',
    // width: '15%',
    dataIndex: 'Pledgees',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '质押股份总数(股)',
    width: 120,
    dataIndex: 'ShareFrozenNum',
  },
  {
    title: '质押股份市值(元)',
    width: 120,
    dataIndex: 'SZ',
  },
  {
    title: '状态',
    width: 88,
    dataIndex: 'Type',
  },
  {
    title: '公告日期',
    width: 88,
    dataIndex: 'NoticeDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 近期多起开庭公告 序号、案号、案由、当事人、法院	、开庭时间
const NoticeInTimePeriodColumns = [
  {
    title: '案号',
    dataIndex: 'caseNo',
    width: 158,
  },
  {
    title: '案由',
    dataIndex: 'caseReason',
    width: 100,
  },
  {
    title: '当事人',
    dataIndex: 'caseRoleGroup',
    customRender: (caseRoleGroup) => {
      const renderNameList = (list: any[]) => {
        if (list.length > 0) {
          const role = list[0].RN;
          const needNo = list.length > 1;
          return `<div style=${list.length === 1 ? 'display:flex;flex-wrap:wrap;' : ''}>
              <div>${role}：</div>
              ${list
                .map((item, index) => {
                  return `<div>
                    ${needNo ? index + 1 + '.' : ''}
                    <span>${item.ShowName || item.P}</span>
                  </div>`;
                })
                .join('')}
            </div>`;
        }
        return null;
      };
      if (caseRoleGroup?.length > 0) {
        // 里面可能不止原被告，不能用R === 0判断，先按角色排序，再用reduce分类
        const RolesArrMap = sortBy(caseRoleGroup, 'R').reduce((acc, cur) => {
          if (acc[cur.RN]) {
            acc[cur.RN].push(cur);
          } else {
            acc[cur.RN] = [cur];
          }
          return acc;
        }, {});
        return `<div>${Object.values(RolesArrMap).map(renderNameList).join('')}</div>`;
      }
      return '-';
    },
  },
  {
    title: '法院',
    dataIndex: 'court',
    width: 100,
  },
  {
    title: '开庭时间',
    width: 88,
    dataIndex: 'courtDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];
// 被列入非正常户
const BusinessAbnormal4Columns = [
  {
    title: '纳税人识别号',
    dataIndex: 'CaseNo',
  },
  {
    title: '信用类型',
    dataIndex: 'IsValid',
    customRender: (value) => {
      return value ? '税务非正常户' : '税务非正常户(失效)';
    },
  },
  {
    title: '列入机关',
    dataIndex: 'ExecuteGov',
  },
  {
    title: '列入日期',
    width: 88,
    dataIndex: 'JoinDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 票据持续逾期
const PersistentBillOverdueColounms = [
  {
    title: '企业名称',
    dataIndex: 'companyName',
    width: 158,
  },
  {
    title: '统一社会信用代码',
    dataIndex: 'creditCode',
  },
  {
    title: '持续逾期开始时间',
    dataIndex: 'beginDate',
    customRender: (date) => {
      return dateTrans(date, false);
    },
  },
  {
    title: '公告日期',
    width: 88,
    dataIndex: 'publishDate',
    customRender: (date) => {
      return dateTrans(date, false);
    },
  },
];

// 知识产权出质
const IPRPledgeColoumns = [
  {
    title: '出质知产类型',
    dataIndex: 'TypeDesc',
  },
  {
    title: '名称',
    dataIndex: 'Name',
    width: 158,
  },
  {
    title: '出质登记号',
    dataIndex: 'RegNo',
  },
  {
    title: '出质公告日',
    dataIndex: 'PublishDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '出质人名称',
    dataIndex: 'PledgorInfo',
    customRender: (data) => {
      return data.map((item) => item.Name).join('、');
    },
  },
  {
    title: '质权人名称',
    dataIndex: 'PledgeeInfo',
    customRender: (data) => {
      return data.map((item) => item.Name).join('、');
    },
  },
  {
    title: '出质期限',
    width: 120,
    customRender: (item) => {
      const { PledgeStartDate, PledgeEndDate } = item;
      const startDate = dateTrans(PledgeStartDate);
      const endDate = dateTrans(PledgeEndDate);
      if (startDate === '-' && endDate === '-') {
        return '-';
      }
      return `${startDate} 至 ${endDate}`;
    },
  },
];

const CourtNoticeColumns = [
  {
    title: '案号',
    width: 158,
    customRender: (item) => {
      return item.CaseNo || item.caseno || '-';
    },
  },
  {
    title: '案由',
    dataIndex: 'casereason',
    width: 100,
  },
  {
    title: '当事人',
    dataIndex: 'RoleList',
    customRender: (dataList) => {
      const data = dataList.reduce((acc, cur) => {
        const type = cur.Desc;
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(...cur.Items);
        return acc;
      }, {});
      return Object.keys(data)
        .map((key) => {
          return `
          <div>
            <span>${key}：</span>
            ${data[key]
              ?.map((detail, index) => {
                return `<span>${index !== 0 ? '，' : ''}${detail.Name}</span>`;
              })
              ?.join('')}
          </div>
        `;
        })
        .join('');
    },
  },
  {
    title: '法院',
    dataIndex: 'executegov',
    width: 140,
  },
  {
    title: '开庭时间',
    dataIndex: 'liandate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

const JudgementColumns = [
  {
    title: '文书标题',
    width: 158,
    dataIndex: 'casename',
  },
  {
    title: '案件金额(元) ',
    dataIndex: 'amountinvolved',
    width: 90,
    customRender: {
      name: 'money',
    },
  },
  {
    title: '案由',
    dataIndex: 'casereason', // FIXME: 字段存在大小写问题
    width: 140,
  },
  {
    title: '裁判结果',
    dataIndex: 'judgeresult',
  },
  {
    title: '发布日期',
    dataIndex: 'submitdate', // FIXME: 字段存在大小写问题
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '裁判日期',
    dataIndex: 'judgedate', // FIXME: 字段存在大小写问题
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

const BeneficialOwnersControlNumerousEnterprisesColumns = [
  {
    title: '企业名称',
    dataIndex: 'companyName',
    width: 158,
  },
  {
    title: '受益所有人',
    dataIndex: 'benefitName',
    width: 84,
  },
  {
    title: '成立日期',
    width: 88,
    dataIndex: 'startDate',
    customRender: {
      name: 'date',
    },
  },
  {
    title: '法定代表人',
    dataIndex: 'operName',
    width: 84,
  },
  {
    title: '注册地址',
    dataIndex: 'address',
  },
  {
    title: '登记状态',
    dataIndex: 'shortStatus',
    width: 88,
    customRender: {
      name: 'companyStatus',
    },
  },
];

const SeriousViolationColumns = [
  {
    title: '企业名称',
    dataIndex: 'companyNameRelated',
  },
  {
    title: '关联方类型',
    width: 300,
    customRender: {
      name: 'relatedTypeDesc',
    },
  },
  {
    title: '信息类型',
    width: 88,
    customRender: {
      name: 'riskType',
    },
  },
];

const ViolationProcessingsColumns = [
  {
    title: '处罚对象',
    dataIndex: 'markedman',
    width: 120,
  },
  {
    title: '职务',
    dataIndex: 'job',
  },
  {
    title: '违规类型',
    dataIndex: 'type',
  },
  {
    title: '公告日期',
    width: 88,
    dataIndex: 'publicdate',
    customRender: {
      name: 'date',
    },
  },
];

const ReviewAndInvestigationColumns = [
  {
    title: '公告类型',
    width: 120,
    dataIndex: 'type',
  },
  {
    title: '姓名',
    width: 60,
    dataIndex: 'employeeName',
  },
  {
    title: '职务',
    width: 80,
    dataIndex: 'job',
  },
  {
    title: '纪检监察机关',
    dataIndex: 'disciplinaryOrg',
  },
  {
    title: '党纪处分',
    dataIndex: 'partyPunishDesc',
  },
  {
    title: '政务处分',
    dataIndex: 'politicalPunishDesc',
  },
  {
    title: '发布日期',
    dataIndex: 'publishDate',
    width: 88,
    customRender: {
      name: 'date',
    },
  },
  // {
  //   title: '处分详情',
  //   dataIndex: 'punishResult',
  // },
];

// 同实际控制人企业众多增加不确定风险
const QfkRisk2310Columns = [
  {
    title: '企业名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '投资比例',
    dataIndex: 'percentTotal',
    width: 80,
  },
  {
    title: '投资链',
    customRender: {
      name: 'investmentPath',
    },
  },
];

const QfkRisk2210Columns = [
  {
    title: '企业名称',
    dataIndex: 'name',
    width: 140,
  },
  {
    title: '持股比例',
    dataIndex: 'stockPercent',
    width: 80,
  },
  {
    title: '担任法人起止时间',
    width: 122,
    customRender: (record) => {
      return `${record.startDate || ''} 至${record.endDate ? ` ${record.endDate}` : '今'}`;
    },
  },
  {
    title: '注册资本',
    dataIndex: 'regCap',
    width: 100,
    customRender: {
      name: 'moneyWithUnit',
      options: {
        precision: 0,
      },
    },
  },
  {
    title: '地区',
    dataIndex: 'province',
  },
  {
    title: '行业',
    dataIndex: 'industry',
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 88,
    customRender: {
      name: 'companyStatus',
    },
  },
];

// 实际控制人控制企业涉及高风险行业
const QfkRisk6610Columns = [
  {
    title: '企业名称',
    dataIndex: 'companyName',
    width: 158,
  },
  {
    title: '成立日期',
    width: 88,
    dataIndex: 'startDate',
    customRender: {
      name: 'date',
    },
  },
  {
    title: '法定代表人',
    dataIndex: 'operName',
    width: 84,
  },
  {
    title: '经营范围',
    dataIndex: 'scope',
  },
];

// 实际控制人控制企业位于边境贸易区
const QfkRisk6611Columns = [
  {
    title: '企业名称',
    dataIndex: 'companyName',
    width: 158,
  },
  {
    title: '成立日期',
    width: 88,
    dataIndex: 'startDate',
    customRender: {
      name: 'date',
    },
  },
  {
    title: '法定代表人',
    dataIndex: 'operName',
    width: 84,
  },
  {
    title: '注册地址',
    dataIndex: 'address',
  },
];

// 来自高风险I类国家或地区
const QfkRisk1410Columns = [
  {
    title: '发起人及出资人',
    dataIndex: 'stockName',
  },
  {
    title: '来自国家或地区',
    dataIndex: 'area',
    width: 150,
  },
];

// 联系方式或注册地址重复
const QfkRisk2010Columns = [
  {
    title: '企业名称',
    dataIndex: 'companyName',
    width: 158,
  },
  {
    title: '法定代表人',
    dataIndex: 'operName',
    width: 84,
  },
  {
    title: '注册资本',
    dataIndex: 'registCapi',
    customRender: {
      name: 'moneyWithUnit',
      options: {
        precision: 0,
      },
    },
  },
  {
    title: '成立日期',
    width: 88,
    dataIndex: 'startDate',
    customRender: {
      name: 'date',
    },
  },
  {
    title: '登记状态',
    dataIndex: 'shortStatus',
    width: 88,
    customRender: {
      name: 'companyStatus',
    },
  },
];

// 法定代表人控制企业集中注册且均无实缴资本
const QfkRisk6709Columns = [
  {
    title: '企业名称',
    dataIndex: 'companyName',
    width: 240,
  },
  {
    title: '成立日期',
    dataIndex: 'startDate',
    width: 88,
    customRender: {
      name: 'date',
    },
  },
  {
    title: '法定代表人',
    dataIndex: 'operName',
    width: 100, // width: 84,
  },
  {
    title: '异常信息类型',
    customRender: () => {
      return '法定代表人控制企业集中注册且均无实缴资本';
    },
  },
];

// 实际控制人控制企业集中注册且均无实缴资本
const QfkRisk6609Columns = [
  {
    title: '企业名称',
    dataIndex: 'companyName',
    width: 158,
  },
  {
    title: '成立日期',
    width: 88,
    dataIndex: 'startDate',
    customRender: {
      name: 'date',
    },
  },
  {
    title: '法定代表人',
    dataIndex: 'operName',
    width: 84,
  },
  {
    title: '注册地址',
    dataIndex: 'address',
  },
];

// 关联方企业集中注册且均无实缴资本
const QfkRisk7099Columns = [
  {
    title: '企业名称',
    dataIndex: 'companyName',
    width: 158,
  },
  {
    title: '成立日期',
    width: 88,
    dataIndex: 'startDate',
    customRender: {
      name: 'date',
    },
  },
  {
    title: '法定代表人',
    dataIndex: 'operName',
    width: 84,
  },
  {
    title: '注册地址',
    dataIndex: 'address',
  },
  {
    title: '关联方类型',
    dataIndex: 'relatedType',
    width: 150,
    customRender: (text) => {
      return text
        .split(';')
        .map((v) => `<div>${v.trim()}</div>`)
        .join('');
    },
  },
];

// 注册资本降幅过大
const QfkRisk6907Columns = [
  {
    title: '变更日期',
    dataIndex: 'changeDate',
    customRender: {
      name: 'date',
    },
  },
  {
    title: '变更项目',
    dataIndex: 'projectName',
    width: 100,
  },
  {
    title: '变更前',
    dataIndex: 'beforeInfo',
    width: 200,
    customRender: {
      name: 'moneyWithUnit',
      options: {
        precision: 0,
      },
    },
  },
  {
    title: '变更后',
    dataIndex: 'afterInfo',
    width: 200,
    customRender: {
      name: 'moneyWithUnit',
      options: {
        precision: 0,
      },
    },
  },
];

const QfkRisk1312Columns = [
  {
    title: '许可证编号',
    dataIndex: 'licenseNo',
    width: 126,
  },
  {
    title: '业务类型',
    dataIndex: 'type',
  },
  {
    title: '业务覆盖范围',
    dataIndex: 'coverageArea',
    width: 100,
  },
  {
    title: '换证日期',
    width: 88,
    dataIndex: 'renewalDate',
    customRender: {
      name: 'date',
    },
  },
  {
    title: '首次许可日期',
    width: 95,
    dataIndex: 'issueDate',
    customRender: {
      name: 'date',
    },
  },
  {
    title: '有效期至',
    width: 88,
    dataIndex: 'endDate',
    customRender: {
      name: 'date',
    },
  },
  // {
  //   title: '备注',
  //   dataIndex: 'remark',
  // },
];

// 控制权分散
const QfkRisk6803Columns = [
  {
    title: '疑似实控人名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '持股比例',
    dataIndex: 'stockPercent',
  },
];

const QfkRisk6802PartnerListColumns = [
  {
    title: '股东',
    dataIndex: 'stockName',
  },
  {
    title: '持股比例',
    dataIndex: 'stockPercent',
    width: 80,
  },
  {
    title: '认缴出资额(万元)',
    dataIndex: 'shouldCapi',
    width: 140,
  },
  {
    title: '认缴出资日期',
    dataIndex: 'shoudDate',
    customRender: {
      name: 'date',
    },
  },
];

const QfkRisk6802EmployeeListColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '职务',
    dataIndex: 'job',
    width: 300,
  },
];

// 被列入税务非正常户
const TaxUnnormalsColumns = [
  {
    title: '纳税人识别号',
    dataIndex: 'CaseNo',
  },
  {
    title: '列入机关',
    dataIndex: 'ExecuteGov',
  },
  {
    title: '列入日期',
    width: 88,
    dataIndex: 'JoinDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

const SIMILAR_BASIC_REG_INFO = [
  {
    title: '企业名称',
    dataIndex: 'reCompanyName',
    width: 158,
  },
  {
    title: '法定代表人',
    dataIndex: 'reOperName',
    width: 84,
  },
  {
    title: '董监高',
    dataIndex: 'reNameCollect',
    width: 60,
  },
  {
    title: '成立日期',
    dataIndex: 'reStartDate',
    width: 88,
    customRender: {
      name: 'date',
    },
  },
  {
    title: '注册地址',
    dataIndex: 'reAddress',
  },
];

const DECISION_OFFICE_INFO = [
  {
    title: '列入日期',
    dataIndex: 'addDate',
    width: 88,
  },
  {
    title: '作出决定机关',
    dataIndex: 'decisionOffice',
    width: 270,
    customRender: (text, row) => {
      if (row.decisionOffice) {
        return row.decisionOffice;
      }
      if (row.decisionoffice) {
        return row.decisionoffice;
      }
      return '-';
    },
  },
  {
    title: '列入经营异常名录原因',
    dataIndex: 'addReason',
    customRender: (text, row) => {
      return row.addReason || row.addreason || '-';
    },
  },
];

// 疑似空壳企业
const COMPANY_SHELL_COLUMNS = {
  /**
   * ------------------------------------------------
   * 疑似空壳企业: 虚拟表格配置
   * ------------------------------------------------
   */
  // 6.2.2 注册基础信息重叠
  注册基础信息重叠: SIMILAR_BASIC_REG_INFO,
  // 6.2.3 注册信息相似度过高
  注册信息相似度过高: SIMILAR_BASIC_REG_INFO,
  // 6.2.4 一人多企
  一人多企: SIMILAR_BASIC_REG_INFO,

  // 6.2.5 一址多企
  一址多企: [
    {
      title: '公司名称',
      dataIndex: 'companyName',
      width: 158,
    },
    {
      title: '法定代表人',
      dataIndex: 'operName',
      width: 84,
    },
    {
      title: '成立日期',
      dataIndex: 'startDate',
      width: 88,
      customRender: {
        name: 'date',
      },
    },
    {
      title: '注册地址',
      dataIndex: 'address',
    },
  ],
  // 6.2.6 无法联系该企业 √
  无法联系该企业: DECISION_OFFICE_INFO,
  // 6.2.7 未公示年报
  未公示年报: DECISION_OFFICE_INFO,
  // 6.2.8 企业自然人变更时间集中 (FIXME: 未找到对应的表格配置)
  企业自然人变更时间集中: [],
  histShareHolderList: [
    {
      title: '股东',
      dataIndex: 'partnerName',
      width: 158,
    },
    {
      title: '持股比例',
      dataIndex: 'stockPercent',
      width: 80,
    },
    {
      title: '股东类型',
      dataIndex: 'type',
    },
    {
      title: '参股日期',
      width: 88,
      dataIndex: 'inDate',
      customRender: {
        name: 'date',
      },
    },
    {
      title: '退出日期',
      width: 88,
      dataIndex: 'changeDate',
      customRender: {
        name: 'date',
      },
    },
  ],
  historyEmployeeList: [
    {
      title: '姓名',
      dataIndex: 'employeeName',
    },
    {
      title: '职务',
      dataIndex: 'job',
      width: 80,
    },
    {
      title: '任职日期',
      width: 88,
      dataIndex: 'inDate',
      customRender: {
        name: 'date',
      },
    },
    {
      title: '卸职日期',
      width: 88,
      dataIndex: 'changeDate',
      customRender: {
        name: 'date',
      },
    },
  ],
  historyOperList: [
    {
      title: '姓名',
      dataIndex: 'operName',
    },
    {
      title: '任职日期',
      width: 88,
      dataIndex: 'inDate',
      customRender: {
        name: 'date',
      },
    },
    {
      title: '卸任日期',
      width: 88,
      dataIndex: 'changeDate',
      customRender: {
        name: 'date',
      },
    },
  ],
};

const PledgeMergerColumns = [
  {
    title: '登记编号',
    dataIndex: 'registerno',
  },
  {
    title: '抵押人',
    dataIndex: 'DebtorJson',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '抵押权人',
    dataIndex: 'PledgeeJson',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '债务人履行债务的期限',
    customRender: {
      name: 'range',
      options: {
        type: 'date',
        from: 'debttermstart',
        to: 'debttermend',
        pattern: 'X',
      },
    },
  },
  {
    title: '被担保主债权数额',
    dataIndex: 'pledgedamountdesc',
  },
  {
    title: '状态',
    customRender: () => {
      return `<span class="status-tag success">有效</span>`;
    },
  },
  {
    title: '登记日期',
    dataIndex: 'registerstartdate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

const AssetInvestigationAndFreezingColumns = [
  {
    title: '企业名称',
    dataIndex: 'NameAndKeyNo',
    customRender: {
      name: 'entities',
    },
  },
  {
    title: '资产查冻',
    dataIndex: 'seizedassets',
  },
];

const ControllerCompanyColumns = [
  {
    title: '企业名称',
    dataIndex: 'Name',
  },
  {
    title: '状态',
    dataIndex: 'ShortStatus',
    customRender: {
      name: 'companyStatus',
    },
  },
  {
    title: '成立日期',
    dataIndex: 'StartDate',
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '所属地区',
    customRender: (record) => {
      return areaParse(record.Area) || '-';
    },
  },
  {
    title: '注册资本',
    dataIndex: 'RegistCapi',
    customRender: {
      name: 'moneyWithUnit',
      options: {
        precision: 0,
      },
    },
  },
  {
    title: '投资比例',
    dataIndex: 'PercentTotal',
  },
];

export const TABLE_COLUMNS = {
  BusinessAbnormal2: BusinessAbnormal2Columns,
  BusinessAbnormal3: BusinessAbnormal3Columns,
  BusinessAbnormal5: BusinessAbnormal5Columns,
  FreezeEquity: FreezeEquityColumns,
  ChattelMortgage: ChattelMortgageColumns,
  LandMortgage: LandMortgageColumns,
  PersonCreditCurrent: PersonCreditCurrentColumns,
  EquityPledge: EquityPledgeColumns,
  JudicialAuction: JudicialAuction1Columns,
  JudicialAuction1: JudicialAuction1Columns,
  GuaranteeInfo: GuaranteeInfoColumns,
  GuaranteeRisk: GuaranteeRiskColumns,
  RestrictedOutbound: RestrictedOutboundColumns,
  TaxationOffences: TaxationOffencesColumns,
  Bankruptcy: BankruptcyColumns,
  PersonExecution: PersonExecutionColumns,
  ProductQualityProblem1: ProductQualityProblem1Columns,
  ProductQualityProblem2: ProductQualityProblem2Columns,
  ProductQualityProblem3: ProductQualityProblem3Columns,
  ProductQualityProblem4: ProductQualityProblem4Columns,
  ProductQualityProblem5: ProductQualityProblem5Columns,
  ProductQualityProblem6: ProductQualityProblem6Columns,
  ProductQualityProblem7: ProductQualityProblem7Columns,
  // ProductQualityProblem8: ProductQualityProblem8Columns,
  ProductQualityProblem9: ProductQualityProblem9Columns,
  MainInfoUpdateScope: MainInfoUpdateScopeColumns,
  MainInfoUpdateAddress: MainInfoUpdateAddressColumns,
  MainInfoUpdateBeneficiary: MainInfoUpdateBeneficiaryColumns,
  MainInfoUpdateName: MainInfoUpdateNameColumns,
  MainInfoUpdateLegalPerson: MainInfoUpdateLegalPersonColumns,
  MainInfoUpdateHolder: MainInfoUpdateHolderColumns,
  MainInfoUpdateManager: MainInfoUpdateManagerColumns,
  MainInfoUpdatePerson: MainInfoUpdatePersonColumns,
  BondDefaults: BondDefaultsColumns,
  AdministrativePenalties: AdministrativePenaltiesColumns,
  EnvironmentalPenalties: AdministrativePenaltiesColumns,
  CompanyCredit: CompanyCreditColumns,
  CompanyCreditHistory: CompanyCreditColumnsHistory,
  OperationAbnormal: OperationAbnormalColumns,
  TaxArrearsNotice: TaxArrearsNoticeColumns,
  PersonCreditHistory: PersonCreditColumns,
  MainMembersPersonCreditCurrent: MainMembersPersonCreditCurrentColumns,
  MainMembersRestrictedOutbound: RestrictedOutboundColumns,
  // SubsidiaryPersonCreditCurrent: SubsidiaryPersonCreditCurrentColumns,
  SamePhone: ConflictInterestSamePhoneColumns,
  StaffWorkingOutside: ConflictInterestColumns,
  StaffForeignInvestment: StaffForeignInvestmentColumns,
  SuspectedInterestConflict: SuspectedInterestConflictColumns,
  StaffWorkingOutsideForeignInvestment: StaffWorkingOutsideForeignInvestmentColumns,
  HitInnerBlackList: HitInnerBlackListColumns,
  EmploymentRelationship: EmploymentRelationshipColumns,
  Shareholder: ShareholderColumns,
  ForeignInvestment: ForeignInvestmentColumns,
  CancellationOfFiling: CancellationOfFilingColumns,
  HitOuterBlackList: HitOuterBlackListColumns,
  RestrictedConsumptionCurrent: RestrictedConsumptionColumns,
  RestrictedConsumptionHistory: RestrictedConsumptionColumns,
  MainMembersRestrictedConsumptionCurrent: RestrictedConsumptionColumns,
  SubsidiaryRestrictedConsumptionCurrent: RestrictedConsumptionColumns,
  InvestorsRelationship: PartnershipColumns,
  ShareholdingRelationship: ShareholdingRelationshipColumns,
  ServeRelationship: ServeRelationshipColumns,
  PunishedEmployeesWorkingOutside: PunishedEmployeesWorkingOutsideColumns,
  PunishedEmployeesForeignInvestment: PunishedEmployeesForeignInvestmentColumns,
  CompanyOrMainMembersCriminalOffence: CompanyOrMainMembersCriminalOffenceColumns,
  CompanyOrMainMembersCriminalOffenceHistory: CompanyOrMainMembersCriminalOffenceColumns,

  // 股东信息（风险报告独立维度）
  StandalonePartners: StandalonePartnersColumns,
  StandaloneIPOPartners: StandaloneIPOPartnersColumns,
  // 主要人员（风险报告独立维度）
  StandaloneEmployees: StandaloneEmployeesColumns,
  // 主要人员（社会组织）
  StandaloneEmployeesOrg: StandaloneEmployeesOrgColumns,
  // 分支机构（风险报告独立维度）
  StandaloneBranches: StandaloneBranchesColumns,
  // 变更记录（风险报告独立维度）
  StandaloneChangeDiffInfo: StandaloneChangeDiffInfoColumns,

  // 实际控制人（风险报告独立维度）
  ActualControllerV5YisiActual: ActualControllerV5YisiActual,
  ActualControllerV5Actual1: ActualControllerV5ActualColumns1,
  ActualControllerV5Actual2: ActualControllerV5ActualColumns2,
  ActualControllerV5Actual2WithPercentTotal: ActualControllerV5ActualColumns2WithPercentTotal,
  ActualControllerV5Actual2WithTitleAndControlPercent: ActualControllerV5ActualColumns2WithTitleAndControlPercent,
  ActualControllerV5Actual2WithControlPercent: ActualControllerV5ActualColumns2WithControlPercent,
  ActualControllerV5Actual3: ActualControllerV5ActualColumns3,
  ActualControllerV5Actual3WithTitle: ActualControllerV5ActualColumns3WithTitle,
  ActualControllerV5Actual4: ActualControllerV5ActualColumns4,
  ActualControllerV5Actual4WithTitle: ActualControllerV5ActualColumns4WithTitle,
  ActualControllerV5Actual5: ActualControllerV5ActualColumns5,
  ActualControllerV5Actual5WithPercentTotal: ActualControllerV5ActualColumns5WithPercentTotal,
  ActualControllerV5Actual6: ActualControllerV5ActualColumns6,
  ActualControllerV5Actual6WithTitle: ActualControllerV5ActualColumns6WithTitle,

  // 控制企业（风险报告独立维度）
  StandaloneHoldingCompany: StandaloneHoldingCompanyColumns,
  // 负面新闻 NOTE: 特殊处理维度，与 Web 不同
  NegativeNewsRecent: NegativeNewsRecentColumns,
  NegativeNewsHistory: NegativeNewsRecentColumns,
  NegativeNews: NegativeNewsRecentColumns,
  // 买卖合同纠纷
  SalesContractDispute: DisputeColumns,
  // 近3年重大纠纷
  MajorDispute: DisputeColumns,
  CompanyOrMainMembersCriminalInvolve: DisputeColumns,
  CompanyOrMainMembersCriminalInvolveHistory: DisputeColumns,
  AdministrativePenalties2: AdministrativePenalties2Columns,
  AdministrativePenalties3: AdministrativePenalties2Columns,
  // 抽查抽检不合格
  SpotCheck: SpotCheckColumns,
  SameSuspectedActualController: SameControllRelation,
  BlacklistSameSuspectedActualController: SameControllRelation,
  // 交叉重叠关系
  CustomerPartnerInvestigation: CustomerPartnerInvestigationColumns, // 与第三方列表企业存在投资任职关联
  BlacklistPartnerInvestigation: BlacklistPartnerInvestigationColumns, // 与内部黑名单企业存在投资任职关联
  CustomerSuspectedRelation: CustomerSuspectedRelationColumns, // 与第三方列表企业存在投资任职关联
  BlacklistSuspectedRelation: BlacklistSuspectedRelationColumns, // 与内部黑名单企业存在投资任职关联
  BillDefaults: BillDefaultsColumns, // 票据违约
  EndExecutionCase: EndExecutionCaseColumns, // 终本案件
  SecurityNotice: SecurityNoticeColumns, // 终本案件
  RegulateFinance: RegulateFinanceColoums, // 监管处罚
  CapitalReduction: CapitalReductionColoums, // 减资公告
  LaborContractDispute: LaborContractDisputeColumns, // 劳动纠纷
  Certification: CertificationColumns, // 资质筛查
  BeneficialOwner: BeneficialOwnerColumns, // 受益所有人
  BeneficialNaturalPerson: BenefitNaturalPersonColumns, // 受益自然人
  TaxPenalties: TaxPenaltiesColumns, // 税务处罚
  TaxReminder: TaxReminderColumns, // 税务催报
  TaxCallNotice: TaxCallNoticeColumns, // 税务催缴公告
  TaxCallNoticeV2: TaxCallNoticeV2Columns, // 税务催缴
  StockPledge: StockPledgeColumns, // 股权质押
  NoticeInTimePeriod: NoticeInTimePeriodColumns, // 近期多起开庭公告
  BusinessAbnormal4: BusinessAbnormal4Columns, // 被列入非正常户
  UnfairCompetition: LaborContractDisputeColumns, // 不正当竞争纠纷
  PersistentBillOverdue: PersistentBillOverdueColounms, // 票据持续逾期
  IPRPledge: IPRPledgeColoumns, // 知识产权出质

  // Insights
  CourtSessionAnnouncement: CourtNoticeColumns, // 开庭公告（补充）
  Judgement: JudgementColumns, // 案件风险
  BeneficialOwnersControlNumerousEnterprises: BeneficialOwnersControlNumerousEnterprisesColumns, // 受益所有人控制企业众多
  BusinessAnomalies: SeriousViolationColumns, // 关联方成员企业存在异常-企业多个关联方成员存在异常信息
  SeriousViolation: SeriousViolationColumns, // 关联方成员企业存在异常-企业关联方成员存在严重违法事项
  BusinessAnomaliesWithSamePhoneAndAddress: SeriousViolationColumns, // 关联方成员企业存在异常-同联系方式或者同地址企业存在异常
  MoneyLaundering: SeriousViolationColumns, // 关联方成员企业存在异常-企业关联方成员曾发生过洗钱类刑事案件
  RelatedAnnouncement: SeriousViolationColumns, // 关联方成员企业存在异常-关联方成员企业有开庭公告信息
  RelatedCompanies: SeriousViolationColumns, // 关联方成员企业存在异常-关联方企业注销或吊销
  ViolationProcessings: ViolationProcessingsColumns, // 主要成员存在外部关联风险-历史人员存在违规处理
  ReviewAndInvestigation: ReviewAndInvestigationColumns, // 审查调查
  QfkRisk2210: QfkRisk2210Columns, // 同法定代表人企业众多且地区分散: 中欧睿意企业管理有限公司
  QfkRisk2310: QfkRisk2310Columns, // 同实际控制人企业众多增加不确定风险
  QfkRisk6610: QfkRisk6610Columns, // 实际控制人控制企业涉及高风险行业
  QfkRisk6710: QfkRisk6610Columns, // 法定代表人控制企业涉及高风险行业
  QfkRisk2010: QfkRisk2010Columns, // 联系方式或注册地址重复(未验证测试数据)
  QfkRisk6709: QfkRisk6709Columns, // 法定代表人控制企业集中注册且均无实缴资本
  QfkRisk7099: QfkRisk7099Columns, // 关联方企业集中注册且均无实缴资本
  QfkRisk6611: QfkRisk6611Columns, // 实际控制人控制企业位于边境贸易区
  QfkRisk6612: QfkRisk6611Columns, // 实际控制人控制企业边境贸易区占比较高
  QfkRisk6907: QfkRisk6907Columns, // 注册资本降幅过大
  QfkRisk1312: QfkRisk1312Columns, // 支付/融资担保业务被中止或注销
  QfkRisk1410: QfkRisk1410Columns, // 来自高风险I类国家或地区
  QfkRisk1411: QfkRisk1410Columns, // 来自高风险II类国家或地区
  QfkRisk6609: QfkRisk6609Columns, // 实际控制人控制企业集中注册且均无实缴资本(未验证测试数据)
  QfkRisk6803: QfkRisk6803Columns, // 控制权分散
  // 特殊维度: 所有权与经营权分离
  QfkRisk6802EmployeeList: QfkRisk6802EmployeeListColumns,
  QfkRisk6802PartnerList: QfkRisk6802PartnerListColumns,
  TaxUnnormals: TaxUnnormalsColumns, // 被列入税务非正常户

  PledgeMerger: PledgeMergerColumns, // 动产抵押
  AssetInvestigationAndFreezing: AssetInvestigationAndFreezingColumns, // 资产查冻
  ControllerCompany: ControllerCompanyColumns, // 投资异常

  ...COMPANY_SHELL_COLUMNS, // 疑似空壳企业相关表格列
};
