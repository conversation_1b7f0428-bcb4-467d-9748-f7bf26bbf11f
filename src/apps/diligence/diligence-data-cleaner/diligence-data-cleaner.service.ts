import { RabbitMQ } from '@kezhaozhao/message-queue';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable, NotImplementedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { captureException } from '@sentry/core';
import * as Bluebird from 'bluebird';
import { QueueService } from 'libs/config/queue.service';
import { BatchDiligenceEntity } from 'libs/entities/BatchDiligenceEntity';
import { BatchEntity } from 'libs/entities/BatchEntity';
import { BatchJobEntity } from 'libs/entities/BatchJobEntity';
import { BatchResultEntity } from 'libs/entities/BatchResultEntity';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { MonitorMetricsDynamicEntity } from 'libs/entities/MonitorMetricsDynamicEntity';
import { BatchTypeEnums } from 'libs/enums/batch';
import { BatchStatusEnums, ShouldExitBatchStatusEnums } from 'libs/enums/batch/BatchStatusEnums';
import { ProductCodeEnums } from 'libs/enums/ProductCodeEnums';
import { chunk, groupBy } from 'lodash';
import { Logger } from 'log4js';
import { In, Not, Repository } from 'typeorm';
import { DiligenceSnapshotEsService } from '../snapshot/diligence.snapshot.es.service';
import { DiligenceDataCleanerMessagePO } from './model/DiligenceDataCleanerMessagePO';
/**
 * 清理掉监控动态生成过程中产生的尽调以及快照等数据
 * 1. 每次跑全量的监控尽调的时候，清理掉上次监控尽调的数据
 * 2. 如果快照被 dynamic 中的非 status=-1 的数据引用了，则不清理
 *
 * 通过 monitorGroupId, batchId, companyId 来定位这些数据，
 * 清理的数据包括：
 * batch ， batchJob ，batchResult ，batchDiligence ，diligenceHistory ，diligenceSnapshot
 *
 * 每次按照batch清理时候，先全部清理掉batch ， batchJob ，batchResult ，batchDiligence， 如果是保留了部分公司的尽调数据,
 * 则选择性清理 diligenceHistory ，diligenceSnapshot
 */
@Injectable()
export class DiligenceDataCleanerService {
  private readonly logger: Logger = QccLogger.getLogger(DiligenceDataCleanerService.name);
  public readonly cleanerQueue: RabbitMQ;
  public readonly cleanerAnalyzeQueue: RabbitMQ;
  constructor(
    @InjectRepository(MonitorMetricsDynamicEntity) private readonly dynamicRepo: Repository<MonitorMetricsDynamicEntity>,
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(BatchJobEntity) private readonly batchJobRepo: Repository<BatchJobEntity>,
    @InjectRepository(BatchResultEntity) private readonly batchResultRepo: Repository<BatchResultEntity>,
    @InjectRepository(BatchDiligenceEntity) private readonly batchDiligenceRepo: Repository<BatchDiligenceEntity>,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    private readonly queueService: QueueService,
    private readonly snapshotEsService: DiligenceSnapshotEsService,
  ) {
    this.cleanerQueue = this.queueService.qccQueue.createQueue('diligence_data_cleaner');
    this.cleanerAnalyzeQueue = this.queueService.qccQueue.createQueue('diligence_data_cleaner_analyze');
    this.cleanerQueue.consume(this.handleCleanerMessage.bind(this));
    this.cleanerAnalyzeQueue.consume(this.handleCleanerAnalyzeMessage.bind(this));
  }

  /**
   * 先提前分析batch的信息，暂存到表里面，后续再决定要不要删除
   * @param msgPO
   * @returns
   */
  public async handleCleanerAnalyzeMessage(msgPO: DiligenceDataCleanerMessagePO) {
    const { batchId, diligenceIds } = msgPO;
    if (!diligenceIds.length || !batchId) {
      this.logger.error(`handleCleanerAnalyzeMessage() diligenceIds 和 batchId 都不允许为空`);
      return;
    }
  }

  /**
   * 清除普通批量尽调(非风险年检相关的批次)
   *
   * TODO 该方法目前不会被触发，将来如果被调用的时候，是否需要考虑针对一些特定的数据不删除，例如 风险级别是高风险的
   * @param batchId
   */

  async cleanDiligenceData(batchId: number, orgId: number, productCode: ProductCodeEnums) {
    const batchEntity = await this.batchRepo.findOne({
      where: { batchId, batchType: BatchTypeEnums.Diligence, status: In(ShouldExitBatchStatusEnums) },
    });
    if (!batchEntity) {
      this.logger.warn(`batchId: ${batchId} 不存在 或者 状态不是已结束`);
      return 0;
    }
    const nowTimestamp = Date.now();
    if (batchEntity.createDate.getTime() > nowTimestamp + 60 * 60 * 1000 - 1000 * 60 * 60 * 24) {
      this.logger.error(`cleanDiligenceData() batchId: ${batchId} 创建时间离当前时间在一天之内，不清理`);
      captureException(new Error(`cleanDiligenceData() batchId: ${batchId} 创建时间离当前时间在一天之内，不清理`));
      return;
    }

    const pageSize = 500;
    let pageIndex = 1;
    let diligenceTotal = 0;
    do {
      const pageDiligences = await this.batchDiligenceRepo.find({
        where: { batchId },
        select: ['diligenceId'],
        skip: (pageIndex - 1) * pageSize,
        take: pageSize,
      });
      diligenceTotal += pageDiligences.length;
      if (pageDiligences.length === 0) {
        break;
      }
      const diligenceIds = pageDiligences.map((item) => item.diligenceId);
      const chunks = chunk(diligenceIds, 50);
      await Bluebird.map(
        chunks,
        async (chunk) => {
          await this.cleanerQueue.sendMessage({ batchId, diligenceIds: chunk, orgId, productCode });
        },
        { concurrency: 2 },
      );
      if (pageDiligences.length < pageSize) {
        break;
      }
      pageIndex++;
    } while (true);

    await this.cleanBatchRealtedData(batchId);
    return diligenceTotal;
  }

  /**
   * 清理指定的batchId关联的所有尽调数据(监控),如果指定了monitorGroupId，则需要检查一下跟监控分组有关的动态对应的快照不能删除
   */
  public async cleanContinuousDiligenceData(batchId: number, orgId: number, productCode: ProductCodeEnums) {
    const batchEntity = await this.batchRepo.findOne({
      where: { batchId, batchType: BatchTypeEnums.ContinuousDiligence, orgId },
    });
    if (!batchEntity) {
      this.logger.warn(`batchId: ${batchId} 不存在 或者 状态不是已结束`);
      return { diligenceTotal: -1, diligenceDeleteCount: 0 };
    }
    const nowTimestamp = Date.now();
    if (batchEntity.createDate.getTime() > nowTimestamp + 60 * 60 * 1000 - 1000 * 60 * 60 * 24) {
      this.logger.error(`cleanContinuousDiligenceData() batchId: ${batchId} 创建时间离当前时间在一天之内，不清理`);
      captureException(new Error(`cleanContinuousDiligenceData() batchId: ${batchId} 创建时间离当前时间在一天之内，不清理`));
      return { diligenceTotal: -2, diligenceDeleteCount: 0 };
    }

    const monitorGroupId = batchEntity.batchInfo?.monitorGroupId;
    if (!monitorGroupId) {
      this.logger.error(`cleanContinuousDiligenceData() batchId: ${batchId} 不存在 monitorGroupId`);
      return { diligenceTotal: -3, diligenceDeleteCount: 0 };
    }
    // const qb = this.dynamicRepo
    //   .createQueryBuilder('dynamic')
    //   .where('dynamic.org_id = :orgId', { orgId })
    //   .andWhere('dynamic.product = :productCode', { productCode })
    //   .andWhere('dynamic.batch_id = :batchId', { batchId })
    //   .andWhere('dynamic.status != -1 and dynamic.status != -2');
    // if (monitorGroupId) {
    //   qb.andWhere('monitor_group_id = :monitorGroupId', { monitorGroupId });
    // }
    // const dynamicEntities = await qb.getMany();
    // const toExcludeDiligenceIds = Array.from(new Set(dynamicEntities.map((item) => item.diligenceId)));
    const pageSize = 500;
    let pageIndex = 1;
    let diligenceTotal = 0;
    let diligenceDeleteCount = 0;
    let snapshotDeleteCount = 0;
    do {
      const pageDiligences: DiligenceHistoryEntity[] = await this.diligenceHistoryRepo
        .createQueryBuilder('diligenceHistory')
        .select(['diligenceHistory.id', 'diligenceHistory.companyId'])
        .leftJoin('diligenceHistory.batchEntities', 'batch')
        .where('batch.batchId = :batchId', { batchId })
        .skip((pageIndex - 1) * pageSize)
        .take(pageSize)
        .getMany();
      diligenceTotal += pageDiligences.length;
      if (pageDiligences.length === 0) {
        break;
      }
      // 对于被多个batch 引用的diligence ，先忽略
      const diligenceEntities = pageDiligences.filter((b) => {
        return !b.batchEntities?.length || b.batchEntities?.length === 1;
      });
      const diligenceIds = diligenceEntities.map((item) => item.id);
      const companyIds = diligenceEntities.map((item) => item.companyId);

      const qb = this.dynamicRepo
        .createQueryBuilder('dynamic')
        .where('dynamic.orgId = :orgId', { orgId })
        .andWhere('dynamic.product = :productCode', { productCode })
        // .andWhere('dynamic.batch_id = :batchId', { batchId })
        // .andWhere('dynamic.status != -1 and dynamic.status != -2');
        .andWhere('dynamic.monitorGroupId = :monitorGroupId', { monitorGroupId })
        .andWhere(
          '(( dynamic.batchId = :batchId and dynamic.diligenceId IN (:...diligenceIds)) or (dynamic.preBatchId = :batchId and dynamic.companyId IN (:...companyIds)))',
          { diligenceIds, batchId, companyIds },
        );
      const existedDyamics = await qb.getMany();
      // 这个应该分为两类， 一类是 batchId 符合， 一类是preBatchId 符合的（它被后面的batch用作对比）， 对于后者，需要再去找一下它对应的diligenceId
      const currentBatchDyamics = existedDyamics.filter((item) => item.batchId === batchId);
      const preBatchDyamics = existedDyamics.filter((item) => item.preBatchId === batchId);
      // preBatch对应的动态因为没有保存 preDiligenceId， 所以需要去 diligenceEntities 中找到对应的diligenceId
      const preBatchDiligences = diligenceEntities.filter((item) => preBatchDyamics.some((dy) => dy.companyId === item.companyId));
      const toExcludeDiligenceIds = Array.from(
        new Set([...currentBatchDyamics.map((item) => item.diligenceId), ...preBatchDiligences.map((item) => item.id)].filter((t) => t)),
      );
      const toDeleteDiligenceIds = diligenceIds.filter((item) => !toExcludeDiligenceIds.includes(item));
      if (toDeleteDiligenceIds.length === 0) {
        break;
      }
      const childDocsCount = await this.snapshotEsService.countChildDocCount(toDeleteDiligenceIds);
      diligenceDeleteCount += toDeleteDiligenceIds.length;
      snapshotDeleteCount += childDocsCount;
      const chunks = chunk(toDeleteDiligenceIds, 100);
      await Bluebird.map(
        chunks,
        async (chunk) => {
          await this.cleanerQueue.sendMessageV2({ batchId, diligenceIds: chunk, orgId, productCode }, {});
          await Bluebird.delay(Math.random() * 100);
        },
        { concurrency: 1 },
      );
      if (pageDiligences.length < pageSize) {
        break;
      }
      pageIndex++;
    } while (true);

    this.logger.info(`cleanContinuousDiligenceData() batchId: ${batchId} ,diligence=${diligenceDeleteCount}, snapshot=${snapshotDeleteCount}`);
    await this.cleanBatchRealtedData(batchId);
    return { diligenceTotal, diligenceDeleteCount, snapshotDeleteCount };
  }

  /**
   * 多模型尽调，虚拟的batch 类型数据清理
   */
  private async cleanSpecificRiskData(_batchEntity: BatchEntity) {
    throw new NotImplementedException('SpecificRiskData 清理逻辑未实现');
  }

  private async cleanBatchRealtedData(batchId: number) {
    const [result1, result2, result3, result4] = await Bluebird.all([
      this.batchJobRepo.delete({ batchId }),
      this.batchResultRepo.delete({ batchId }),
      this.batchDiligenceRepo.delete({ batchId }),
      this.batchRepo.update({ batchId }, { status: BatchStatusEnums.Deleted }),
    ]);
    this.logger.info(
      `cleanBatchRealtedData() batchId: ${batchId} , batchJob= ${result1.affected} , batchResult= ${result2.affected} , batchDiligence= ${result3.affected} , batch= ${result4.affected} 条数据`,
    );
  }

  public async handleCleanerMessage(msgPO: DiligenceDataCleanerMessagePO) {
    const { batchId, diligenceIds } = msgPO;
    if (!diligenceIds.length || !batchId) {
      this.logger.error(`handleCleanerMessage() diligenceIds 和 batchId 都不允许为空`);
      return;
    }
    const result = await Bluebird.all([this.diligenceHistoryRepo.delete({ id: In(diligenceIds) }), this.snapshotEsService.removeBatchSnapshot(diligenceIds)]);
    return result;
  }

  /**
   * 找出 batch 中 batchType=ContinuousDiligence 的batch ，然后过滤掉 monitor group 中最新的那个batch,
   * 然后删除掉这些batch 中所有的快照数据
   *
   * 正常监控的尽调一天至少运行一次，所以这个任务，只需要根据 batch 的createDate 找到早于昨天的数据，就都是过期的batch了
   *
   */
  async cleanMonitorRelatedBatch() {
    const deadline = new Date();
    deadline.setDate(deadline.getDate() - 7); // 把7天前的批次删掉

    try {
      // 使用LEFT JOIN和IS NULL来提高查询性能
      // const qb = this.batchRepo
      //   .createQueryBuilder('batch')
      //   .leftJoin(MonitorMetricsDynamicEntity, 'dynamic_batch_id', 'dynamic_batch_id.batchId = batch.batchId')
      //   .leftJoin(MonitorMetricsDynamicEntity, 'dynamic_pre_batch_id', 'dynamic_pre_batch_id.preBatchId = batch.batchId')
      //   .where('batch.batchType = :batchType', { batchType: BatchTypeEnums.ContinuousDiligence })
      //   .andWhere('batch.createDate < :deadline', { deadline })
      //   .andWhere('dynamic_batch_id.id IS NULL')
      //   .andWhere('dynamic_pre_batch_id.id IS NULL')
      //   .select('batch.batch_id', 'batch_id')
      //   .addSelect('batch.org_id', 'org_id')
      //   .addSelect('batch.product', 'product')
      //   .take(1000); // 限制结果数量，避免处理过多数据

      const qb = this.batchRepo
        .createQueryBuilder('batch')
        .where('batch.batchType = :batchType', { batchType: BatchTypeEnums.ContinuousDiligence })
        .andWhere('batch.createDate < :deadline', { deadline })
        .andWhere('batch.status != :status', { status: BatchStatusEnums.Deleted });
      const batchEntities = await qb.getMany();

      this.logger.info(`找到 ${batchEntities.length} 个在dynamic表中没有被batch_id或pre_batch_id引用的批次`);

      await Bluebird.map(
        batchEntities,
        async (entity) => {
          await this.cleanContinuousDiligenceData(entity.batchId, entity.orgId, entity.product as unknown as ProductCodeEnums);
        },
        { concurrency: 10 },
      );
      await this.snapshotEsService.removeOrphanDimension();
      return batchEntities.length;
    } catch (error) {
      this.logger.error(`cleanMonitorRelatedBatch 执行失败: ${error.message}`);
      captureException(error);
      return 0;
    }
  }

  /**
   * 把 数据库中已经删除的 batchId在snapshot中对应的快照也删除掉
   * 步骤:
   * 按组织找到 batch 中未删除的 批量任务batch ， 然后去快照中删除无关联的数据：
   * orgId 一致， 有batchId 但是batchId 不在有效列表里面的数据，可以先统计，后续再删除
   *
   *
   */
  async cleanDeprecatedBatch() {
    const deadline = new Date();
    deadline.setDate(deadline.getDate() - 7); // 把7天前的批次删掉

    const qb = this.batchRepo
      .createQueryBuilder('batch')
      .select('distinct(batch.orgId)', 'orgId')
      .where('batch.batchType in (:...batchTypes)', { batchTypes: [BatchTypeEnums.ContinuousDiligence, BatchTypeEnums.Diligence] })
      .andWhere('batch.createDate < :deadline', { deadline })
      .andWhere('batch.status != :status', { status: BatchStatusEnums.Deleted });
    const orgIdRows = await qb.getRawMany();
    const orgIds = orgIdRows?.map((t) => t.orgId);

    let totalCount = 0;
    await Bluebird.map(
      orgIds,
      async (orgId) => {
        const qb2 = this.batchRepo
          .createQueryBuilder('batch')
          .select(['batch.batchId', 'batch.orgId'])
          .where('batch.batchType in (:...batchTypes)', { batchTypes: [BatchTypeEnums.ContinuousDiligence, BatchTypeEnums.Diligence] })
          .andWhere('batch.createDate < :deadline', { deadline })
          .andWhere('batch.status != :status', { status: BatchStatusEnums.Deleted })
          .andWhere('batch.orgId = :orgId', { orgId });
        const rows = await qb2.getMany();
        const shouldKeepBatchIds = rows.map((t) => t.batchId);
        const { batchIds: shouldDeleteBatchIds } = await this.snapshotEsService.findUnexpectedSnapshot(orgId, shouldKeepBatchIds);
        // 确保 batchId 在数据库中确实不存在了,过滤掉数据库中仍然存在的，这里不做任何处理
        const stillExistEntities = await this.batchRepo.find({ where: { batchId: In(shouldDeleteBatchIds), status: Not(BatchStatusEnums.Deleted) } });
        const stillExistBatchIds = stillExistEntities.map((t) => t.batchId);
        const toDeleteBatchIds = shouldDeleteBatchIds.filter((t) => !stillExistBatchIds.includes(t));

        // // 找出来 batchId自身orgId和期望不匹配的
        // const unexceptBatches = await this.batchRepo.find({ where: { batchId: In(toDeleteBatchIds1), orgId: Not(orgId) } });
        // const unexpectedBatchIds = unexceptBatches.map((t) => t.batchId);
        // if (unexpectedBatchIds.length > 0) {
        //   this.logger.error(`发现有batch的orgId不匹配, orgId: ${orgId}, batchIds: ${unexceptBatches.map((t) => t.batchId)}`);
        //   this.logger.info(`orgId=${orgId}找到,  ${unexceptBatches.length} 个在快照中没有被引用的批次`);
        // }
        // const toDeleteBatchIds = batchIds.filter((t) => !unexpectedBatchIds.includes(t));
        if (toDeleteBatchIds.length > 0) {
          totalCount += toDeleteBatchIds.length;
          const chunks = chunk(toDeleteBatchIds, 20);
          const l = chunks.length;
          this.logger.info(`orgId=${orgId},找到 ${toDeleteBatchIds.length} batch需要处理，被分为${chunks.length}个chunk`);
          let processedCount = 0;
          await Bluebird.map(
            chunks,
            async (chunkBatchIds: number[]) => {
              const res = await this.snapshotEsService.removeBatchSnapshotByBatch(chunkBatchIds);
              this.logger.info(
                `删除已经不存在的batchId对应的快照: orgId: ${orgId},deletedSnapshot: ${res}, 已处理 ${processedCount}/${l}, batchId: ${chunkBatchIds.join(
                  ',',
                )}, `,
              );
              processedCount++;
            },
            { concurrency: 1 },
          );
        }
      },
      { concurrency: 1 },
    );
    return totalCount;
  }
}
