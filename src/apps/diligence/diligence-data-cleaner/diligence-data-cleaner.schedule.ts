import { Cron, NestDistributedSchedule } from 'nest-schedule';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DiligenceDataCleanerService } from './diligence-data-cleaner.service';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import Redlock from 'redlock';
import { Injectable } from '@nestjs/common';

@Injectable()
export class DiligenceDataCleanerSchedule extends NestDistributedSchedule {
  private readonly logger: Logger = QccLogger.getLogger(DiligenceDataCleanerSchedule.name);
  private readonly redLock: Redlock;
  constructor(private readonly diligenceDataCleanerService: DiligenceDataCleanerService, private readonly redisService: RedisService) {
    super();
    this.redLock = new Redlock([this.redisService.getClient()], {
      driftFactor: 0.01, // time in ms
      retryCount: 1,
      retryDelay: 200, // time in ms
      retryJitter: 200, // time in ms
    });
  }
  async tryLock(method: string): Promise<TryRelease> {
    await this.redLock.acquire(['lock_' + method], 300 * 1000);
    return () => {
      this.logger.info('release lock: ' + method);
    };
  }

  @Cron('1 0 * * *')
  async cronJob1() {
    await this.diligenceDataCleanerService.cleanMonitorRelatedBatch();
  }
  @Cron('1 1 * * *')
  async cronJob2() {
    await this.diligenceDataCleanerService.cleanDeprecatedBatch();
  }
}
