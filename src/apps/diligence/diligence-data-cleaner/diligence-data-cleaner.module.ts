import { Module } from '@nestjs/common';
import { DiligenceDataCleanerService } from './diligence-data-cleaner.service';
import { MonitorMetricsDynamicEntity } from 'libs/entities/MonitorMetricsDynamicEntity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BatchDiligenceEntity } from 'libs/entities/BatchDiligenceEntity';
import { BatchEntity } from 'libs/entities/BatchEntity';
import { BatchJobEntity } from 'libs/entities/BatchJobEntity';
import { BatchResultEntity } from 'libs/entities/BatchResultEntity';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { DiligenceSnapshotEsService } from '../snapshot/diligence.snapshot.es.service';
import { DiligenceDataCleanerSchedule } from './diligence-data-cleaner.schedule';

@Module({
  imports: [
    TypeOrmModule.forFeature([MonitorMetricsDynamicEntity, BatchEntity, BatchJobEntity, BatchResultEntity, BatchDiligenceEntity, DiligenceHistoryEntity]),
  ],
  providers: [DiligenceDataCleanerService, DiligenceSnapshotEsService, DiligenceDataCleanerSchedule],
})
export class DiligenceDataCleanerModule {}
