import { Test, TestingModule } from '@nestjs/testing';
import { DiligenceDataCleanerService } from './diligence-data-cleaner.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BatchDiligenceEntity } from 'libs/entities/BatchDiligenceEntity';
import { BatchEntity } from 'libs/entities/BatchEntity';
import { BatchJobEntity } from 'libs/entities/BatchJobEntity';
import { BatchResultEntity } from 'libs/entities/BatchResultEntity';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { MonitorMetricsDynamicEntity } from 'libs/entities/MonitorMetricsDynamicEntity';
import { DiligenceSnapshotEsService } from '../snapshot/diligence.snapshot.es.service';
import { AppTestModule } from 'apps/app/app.test.module';
import { ProductCodeEnums } from 'libs/enums/ProductCodeEnums';
import { DiligenceDataCleanerMessagePO } from './model/DiligenceDataCleanerMessagePO';

jest.setTimeout(10000000);
describe('DiligenceDataCleanerService', () => {
  let service: DiligenceDataCleanerService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        AppTestModule,
        TypeOrmModule.forFeature([MonitorMetricsDynamicEntity, BatchEntity, BatchJobEntity, BatchResultEntity, BatchDiligenceEntity, DiligenceHistoryEntity]),
      ],
      providers: [DiligenceDataCleanerService, DiligenceSnapshotEsService],
    }).compile();

    service = module.get<DiligenceDataCleanerService>(DiligenceDataCleanerService);
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    jest.spyOn(service.cleanerQueue, 'sendMessageV2').mockImplementation(async (messageData: DiligenceDataCleanerMessagePO) => {
      await service.handleCleanerMessage(messageData);
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('cleanContinuousDiligenceData() should clean diligence data', async () => {
    const batchId = 50002265;
    const orgId = 1003263;
    const productCode = ProductCodeEnums.Pro;
    const totalCount = await service.cleanContinuousDiligenceData(batchId, orgId, productCode);
    expect(totalCount).toBeGreaterThan(0);
  });

  it('cleanMonitorRelatedBatch() should clean monitor related batch', async () => {
    const totalCount = await service.cleanMonitorRelatedBatch();
    expect(totalCount).toBeGreaterThanOrEqual(0);
  });

  it('findDeprecatedBatch() should find deprecated batch', async () => {
    const totalCount = await service.cleanDeprecatedBatch();
    expect(totalCount).toBeGreaterThanOrEqual(0);
  });
});
