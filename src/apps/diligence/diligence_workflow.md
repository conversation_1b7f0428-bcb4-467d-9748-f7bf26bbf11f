# 尽调系统工作流程

## 整体流程概述

尽调系统是一个复杂的风险评估流程，主要用于对企业进行风险扫描、评估和生成快照。整个流程从用户发起尽调请求开始，经过风险评估、快照生成，最终生成尽调报告。

```mermaid
flowchart TD
    A[开始: 用户请求尽调] --> B[EvaluationService处理尽调请求]
    B --> C[RiskScoreService计算风险得分]
    C --> D[生成DiligenceHistoryEntity记录]
    D --> E[发送消息到snapshotQueue]
    E --> F[DiligenceSnapshotService处理快照生成]
    F --> G{是否有维度命中?}
    G -->|是| H[拆分维度并发送维度快照消息]
    G -->|否| I[记录空快照]
    H --> J[处理各维度快照]
    J --> K[保存快照到ES]
    I --> L[结束尽调流程]
    K --> L
```

## 关键流程节点

### 1. 尽调请求发起

用户通过`DiligenceController`的 API 接口发起尽调请求，主要有两种方式：

- 单企业尽调：通过`/diligence/run`接口
- 批量尽调：通过批量任务系统

### 2. EvaluationService 处理请求

`EvaluationService.getRiskList`方法是单企业尽调的入口，主要流程：

- 权限校验（检查用户是否有权限使用指定的风险模型）
- 获取企业信息
- 创建批次记录（如果是多模型尽调）
- 为每个风险模型执行尽调

### 3. 风险评估计算

`EvaluationService.runDiligenceBySpecificRiskModel`方法处理具体风险模型的尽调：

- 检查是否存在可用的缓存结果
- 调用`RiskScoreService.getModelRisk`计算风险得分
- 生成`DiligenceHistoryEntity`记录
- 发送消息到快照队列

### 4. 快照生成流程

`DiligenceSnapshotService.processSnapshotMessage`方法处理快照生成消息：

- 根据操作类型处理不同阶段的快照生成
- 对于创建操作，提取维度命中结果并拆分为维度快照任务
- 对于维度快照操作，获取维度详情并保存到 ES

## 核心方法：processSnapshotMessage

`processSnapshotMessage`方法是快照生成的核心，负责处理各种快照相关消息。

### 方法流程

1. **接收消息参数**：

   - 获取组织 ID(orgId)、操作类型(operation)等基本信息

2. **根据操作类型处理不同逻辑**：

   a. **创建快照(Create)**：

   - 获取尽调结果、公司信息和快照 ID
   - 提取维度命中结果
   - 为每个维度创建快照任务并发送消息

   b. **维度快照(DimensionSnapshot)**：

   - 获取维度命中结果和详情
   - 查询维度详细数据
   - 保存维度快照到 ES
   - 更新快照进度

   c. **同步维度快照(SyncDimensionSnapshot)**：

   - 从 OSS 同步快照数据到 ES

   d. **刷新批次关联(RefreshBatchRelation)**：

   - 更新批次与尽调记录的关联关系

3. **错误处理和重试**：

   - 使用分布式锁防止重复处理
   - 实现错误重试机制
   - 记录处理进度和错误日志

## 关键辅助方法详解

### 1. prepareSnapshot

此方法用于准备快照生成环境，包括：

- 创建快照 ID
- 初始化快照状态
- 关联尽调记录和批次

```mermaid
flowchart TD
    A[开始] --> B[生成快照ID]
    B --> C[检查尽调记录]
    C --> D[初始化快照状态]
    D --> E[关联批次ID]
    E --> F[返回快照ID]
```

### 2. fetchDimensionDetails

此方法用于获取维度详情数据，是生成维度快照的核心：

- 根据维度类型和参数查询详情
- 处理分页和排序
- 返回格式化的维度数据

```mermaid
flowchart TD
    A[开始] --> B[构建查询参数]
    B --> C[调用dimensionDetailService查询]
    C --> D[格式化结果]
    D --> E[返回维度详情]
```

### 3. refreshSnapshot

此方法用于刷新或重新生成快照：

- 根据尽调 ID 查找记录
- 清除旧的快照数据
- 重新发送快照生成消息

```mermaid
flowchart TD
    A[开始] --> B[查找尽调记录]
    B --> C[清除旧快照]
    C --> D[发送新快照消息]
    D --> E[返回操作结果]
```

## 涉及的主要数据实体

1. **DiligenceHistoryEntity**: 尽调历史记录
2. **BatchEntity**: 批次记录
3. **BatchResultEntity**: 批次结果记录
4. **BatchDiligenceEntity**: 批次与尽调关联记录
5. **RiskModelEntity**: 风险模型记录

## 快照状态说明

快照状态通过`SnapshotStatus`枚举定义：

- **Init**: 初始化状态
- **Processing**: 处理中
- **Done**: 完成
- **Failed**: 失败

## 尽调类型和使用场景

1. **单企业尽调**: 用户通过 UI 直接发起的单企业风险评估
2. **批量尽调**: 通过批量任务系统发起的多企业风险评估
3. **监控尽调**: 监控系统定期发起的企业风险变化评估

## 优化方向

当前尽调系统工作流程有以下几点可以优化：

1. **代码复杂度高**: 核心方法如`processSnapshotMessage`过长，包含多层嵌套逻辑
2. **职责分散**: 快照生成、数据查询、存储等逻辑混合在一起
3. **可测试性差**: 方法依赖关系复杂，难以进行单元测试
4. **错误处理机制不完善**: 缺乏统一的错误处理策略
5. **缺乏领域模型分离**: 业务逻辑与数据访问层混合
6. **并发控制机制可优化**: 当前使用分布式锁，但粒度和超时策略可以优化

## 快照模块优化计划

为了解决上述问题，我们计划对快照模块进行重构优化，具体内容请参考以下文档：

1. [快照模块工作流程](./snapshot/refactor-readme/snapshot_workflow.md) - 详细描述快照模块的工作流程和组件职责
2. [快照模块优化计划](./snapshot/refactor-readme/snapshot_optimization_plan.md) - 详细的优化方案和实施步骤
3. [快照模块目录结构](./snapshot/refactor-readme/snapshot_directory_structure.md) - 优化后的代码组织结构
4. [快照模块迁移计划](./snapshot/refactor-readme/snapshot_migration_plan.md) - 从旧代码到新代码的迁移策略

本次优化将聚焦于 `snapshot/` 目录下的文件，优化后的代码将放置在 `snapshot/v2/` 目录下。优化将采用领域模型、命令模式和依赖注入等现代设计模式，使代码结构更加清晰，职责分离更加明确，为未来的功能扩展和维护奠定坚实基础。
