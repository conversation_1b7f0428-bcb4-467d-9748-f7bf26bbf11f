import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as Bluebird from 'bluebird';
import { Logger } from 'log4js';
import { In, Repository } from 'typeorm';
import { MonitorCompanyEntity } from '../../../../../libs/entities/MonitorCompanyEntity';
import { DimensionHitResultPO } from '../../../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import { RelatedPartyGroupPO } from '../../../../../libs/model/diligence/graph/RelatedPartyGroupPO';
import { MonitorCompanyRelatedStatusEnum } from '../../../../../libs/enums/monitor/MonitorCompanyStatusEnums';
import { MonitorCompanyRelatedPartyEntity } from '../../../../../libs/entities/MonitorCompanyRelatedPartyEntity';

/**
 * 动态持久化服务实现
 */
@Injectable()
export class MonitorDynamicRelatedService {
  private readonly logger: Logger = QccLogger.getLogger(MonitorDynamicRelatedService.name);

  constructor(
    @InjectRepository(MonitorCompanyRelatedPartyEntity) private readonly relatedCompanyRepo: Repository<MonitorCompanyRelatedPartyEntity>,
    @InjectRepository(MonitorCompanyEntity) private readonly monitorCompanyRepo: Repository<MonitorCompanyEntity>,
  ) {}

  /**
   * 关联方变化指标生成条件判断
   * @param dimHitRes 维度命中结果
   */
  public isRelatedMetric(dimHitRes: DimensionHitResultPO[]): boolean {
    return dimHitRes[0].dimensionKey == DimensionTypeEnums.RelatedCompanyChange;
  }

  /**
   * 设置监控主体表上关联的最新关联方变更动态hashKey
   * @param companyIds 公司ID列表
   * @param monitorGroupId 监控分组ID
   * @param hashKey 动态hashKey
   */
  public async setRelatedDynamicHashKey(companyIds: string[], monitorGroupId: number, hashKey = ''): Promise<void> {
    await this.monitorCompanyRepo.update({ companyId: In(companyIds), monitorGroupId }, { relatedDynamicHashKey: hashKey });
  }

  /**
   * 修改监控列表关联方状态
   * @param relatedChangeCompany 发生变化的关联方
   * @param monitorGroupId 监控分组
   * @param companyIdPrimary 主体公司
   * @param relatedStatus 需要的修改的关联方，当前状态
   */
  public async updateRelated(
    relatedChangeCompany: RelatedPartyGroupPO[],
    monitorGroupId: number,
    companyIdPrimary: string,
    relatedStatus: MonitorCompanyRelatedStatusEnum,
  ) {
    const companyIdRelateds = relatedChangeCompany.map((d) => d.companyKeynoRelated);

    const [companyList, count] = await this.relatedCompanyRepo.findAndCount({
      monitorGroupId,
      companyIdPrimary,
      companyIdRelated: In(companyIdRelateds),
      status: relatedStatus,
    });

    if (count > 0) {
      if (MonitorCompanyRelatedStatusEnum.Valid == relatedStatus) {
        // 当前监控关联方是有效，需要变更成失效
        await this.relatedCompanyRepo.update(
          {
            monitorGroupId,
            companyIdPrimary,
            companyIdRelated: In(companyList.map((c) => c.companyIdRelated)),
            status: relatedStatus,
          },
          { status: MonitorCompanyRelatedStatusEnum.Invalid },
        );
      } else {
        // 当前监控关联方是无效，需要变更成有效，并更新relatedType
        await Bluebird.map(companyList, async (company) => {
          const relatedCompany = relatedChangeCompany.find((d) => d.companyKeynoRelated == company.companyIdRelated);
          const relatedTypeStr = relatedCompany.relatedTypes.join(',');
          await this.relatedCompanyRepo.update(
            { id: company.id, status: relatedStatus },
            {
              status: MonitorCompanyRelatedStatusEnum.Valid,
              relatedTypeStr,
            },
          );
        });
      }
    }
    return companyList;
  }
}
