import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DiligenceHistoryEntity } from '../../../../../libs/entities/DiligenceHistoryEntity';
import { AnalyzeMonitorDynamicMessagePO } from '../../../po/AnalyzeMonitorDynamicMessagePO';
import { QueueService } from '../../../../../libs/config/queue.service';
import { SnapshotStatus } from '../../../../../libs/model/diligence/SnapshotDetail';

/**
 * 尽调处理服务实现
 */
@Injectable()
export class MonitorDynamicDiligenceService {
  private readonly logger: Logger = QccLogger.getLogger(MonitorDynamicDiligenceService.name);

  constructor(
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceRepo: Repository<DiligenceHistoryEntity>,
    private readonly queueService: QueueService,
  ) {}

  /**
   * 查找尽调记录
   * @param diligenceIds 尽调ID列表
   */
  public async findDiligence(diligenceIds: number[]): Promise<DiligenceHistoryEntity[]> {
    return this.diligenceRepo.findByIds(diligenceIds);
  }

  /**
   * 将尽调记录按照快照状态分类
   * @param diligenceEntities 尽调实体列表
   * @param batchId 批次ID
   * @returns 已完成和未完成的尽调记录
   */
  public classifyDiligence(
    diligenceEntities: DiligenceHistoryEntity[],
    batchId: number,
  ): {
    finshedDiligence: DiligenceHistoryEntity[];
    unFinshedDiligence: DiligenceHistoryEntity[];
  } {
    const finshedDiligence: DiligenceHistoryEntity[] = [];
    const unFinshedDiligence: DiligenceHistoryEntity[] = [];

    diligenceEntities.forEach((diligence) => {
      if (SnapshotStatus.SUCCESS == diligence.snapshotDetails.status) {
        finshedDiligence.push(diligence);
      } else if (SnapshotStatus.PROCESSING == diligence.snapshotDetails.status) {
        unFinshedDiligence.push(diligence);
      } else {
        // 快照失败
        this.logger.error(`快照失败无法生成动态 batchId: ${batchId}, diligenceId: ${diligence.id}`);
      }
    });

    return { finshedDiligence, unFinshedDiligence };
  }

  /**
   * 重试未完成的尽调记录
   * @param unFinshedDiligence 未完成的尽调记录
   * @param msgPO 消息对象
   * @param retryCount 重试次数
   */
  public async retryUnfinishedDiligence(
    unFinshedDiligence: DiligenceHistoryEntity[],
    msgPO: AnalyzeMonitorDynamicMessagePO,
    retryCount: number,
  ): Promise<void> {
    if (unFinshedDiligence.length > 0) {
      const diligenceIds = unFinshedDiligence.map((d) => d.id);
      if (retryCount > 20) {
        this.logger.error(`重试 #{retryCount} 次，快照依然未完成 batchId: ${msgPO.batchId}, diligenceIds: ${diligenceIds}`);
      } else {
        const ttl = 60 * 1000;
        this.logger.info(`retry sendMessage to handleMetricsAnalyze retryCount: ${retryCount + 1} delay: ${ttl} `);
        await this.queueService.continuousDiligenceAnalyzeQueue.sendMessageV2(Object.assign({}, msgPO, { diligenceIds, retryCount: retryCount + 1 }), { ttl });
      }
    }
  }
}
