import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as Bluebird from 'bluebird';
import { chunk } from 'lodash';
import { Logger } from 'log4js';
import { In, Not, Repository } from 'typeorm';
import { DiligenceHistoryEntity } from '../../../../../libs/entities/DiligenceHistoryEntity';
import { MonitorCompanyEntity } from '../../../../../libs/entities/MonitorCompanyEntity';
import { MonitorGroupEntity } from '../../../../../libs/entities/MonitorGroupEntity';
import { MonitorMetricsDynamicEntity } from '../../../../../libs/entities/MonitorMetricsDynamicEntity';
import { MonitorMetricDynamicEsDoc } from '../../../../../libs/model/monitor/MonitorMetricDynamicEsDoc';
import { MonitorDynamicEsService } from '../../monitor.dynamic.es.service';
import { MonitorDynamicGenerationService } from './monitor-dynamic-generation.service';

/**
 * 动态持久化服务实现
 */
@Injectable()
export class MonitorDynamicPersistenceService {
  private readonly logger: Logger = QccLogger.getLogger(MonitorDynamicPersistenceService.name);

  constructor(
    @InjectRepository(MonitorMetricsDynamicEntity) private readonly dynamicRepo: Repository<MonitorMetricsDynamicEntity>,
    @InjectRepository(MonitorCompanyEntity) private readonly monitorCompanyRepo: Repository<MonitorCompanyEntity>,
    private readonly dynamicEsService: MonitorDynamicEsService,
    private readonly dynamicGenerationService: MonitorDynamicGenerationService,
  ) {}

  /**
   * 保存动态到数据库和ES
   * @param dynamics 动态列表
   * @returns 保存成功数量
   */
  public async saveDynamic(dynamics: MonitorMetricDynamicEsDoc[]): Promise<number> {
    let insertCount = 0;

    const formtedDynamics = await this.dynamicGenerationService.fulfillDynamicContent(dynamics);

    const chunks = chunk(formtedDynamics, 50);

    await Bluebird.all([
      await Bluebird.map(
        chunks,
        async (chunk) => {
          const t = await this.dynamicRepo
            .createQueryBuilder()
            .insert()
            .into(MonitorMetricsDynamicEntity)
            .orIgnore(true)
            .values(chunk)
            .updateEntity(false)
            .execute();
          insertCount += t.raw.affectedRows;
        },
        { concurrency: 5 },
      ),
      await this.dynamicEsService.insertDynamicDoc(dynamics),
    ]);
    return insertCount;
  }

  /**
   * 更新监控企业状态
   * @param finshedDiligence 已完成的尽调记录
   * @param monitorGroupId 监控分组ID
   * @param batchId 批次ID
   * @param inserted 插入的动态数量
   */
  public async updateMonitorCompanyStatus(
    finshedDiligence: DiligenceHistoryEntity[],
    monitorGroupId: number,
    batchId: number,
    inserted: number,
  ): Promise<void> {
    // 更新监控企业关联的最新最高动态等级
    await Bluebird.map(finshedDiligence, async (d) => {
      const highLeverDynamic = await this.dynamicRepo.findOne({
        where: { batchId, monitorGroupId, companyId: d.companyId, status: Not(In([-1, -2])) },
        order: { riskLevel: 'DESC' },
      });
      if (highLeverDynamic?.id) {
        await this.monitorCompanyRepo.update(
          {
            monitorGroupId,
            companyId: d.companyId,
          },
          { riskLevel: highLeverDynamic.riskLevel },
        );
      }
    });

    // 插入的有效的动态
    if (inserted > 0) {
      this.logger.info(`found ${inserted} metrics dynamic for monitorGroup=${monitorGroupId}`);
      await this.dynamicRepo.manager.update(MonitorGroupEntity, monitorGroupId, {
        changesCount: () => `changes_count + ${inserted}`,
      });
    } else {
      this.logger.info(`no metrics dynamic for monitorGroup=${monitorGroupId}`);
    }
  }
}
