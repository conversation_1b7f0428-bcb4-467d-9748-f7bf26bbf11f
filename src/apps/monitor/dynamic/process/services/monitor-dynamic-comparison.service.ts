import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Not, Repository } from 'typeorm';
import * as Bluebird from 'bluebird';
import { MonitorMetricsDynamicEntity } from '../../../../../libs/entities/MonitorMetricsDynamicEntity';
import { MonitorCompanyEntity } from '../../../../../libs/entities/MonitorCompanyEntity';
import { DimensionHitResultPO } from '../../../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import { DynamicDisplayContent } from '../../../../../libs/model/metric/MetricDynamicContentPO';
import { MetricHitDetailsPO } from '../../../../../libs/model/metric/MetricHitDetailsPO';
import { MonitorMetricDynamicEsDoc } from '../../../../../libs/model/monitor/MonitorMetricDynamicEsDoc';
import { DiligenceSnapshotEsCompareService } from '../../../../diligence/snapshot/diligence.snapshot.es.compare.service';
import { DiligenceSnapshotEsService } from '../../../../diligence/snapshot/diligence.snapshot.es.service';
import { MetricStrategyFactory } from '../strategies/metric-strategy.factory';

/**
 * 动态内容比对服务实现
 * 使用策略模式处理不同类型指标
 */
@Injectable()
export class MonitorDynamicComparisonService {
  private readonly logger: Logger = QccLogger.getLogger(MonitorDynamicComparisonService.name);

  constructor(
    @InjectRepository(MonitorMetricsDynamicEntity) private readonly dynamicRepo: Repository<MonitorMetricsDynamicEntity>,
    @InjectRepository(MonitorCompanyEntity) private readonly monitorCompanyRepo: Repository<MonitorCompanyEntity>,
    private readonly snapshotEsCompareService: DiligenceSnapshotEsCompareService,
    private readonly snapshotEsService: DiligenceSnapshotEsService,
    private readonly metricStrategyFactory: MetricStrategyFactory,
  ) {}

  /**
   * 处理不可重复的动态
   * 根据内容比对结果决定是否生成新的动态
   * @param nonRepeatableDynamics 不可重复的动态列表
   */
  public async processNonRepeatableDynamics(nonRepeatableDynamics: MonitorMetricDynamicEsDoc[]): Promise<MonitorMetricDynamicEsDoc[]> {
    const processedDynamics: MonitorMetricDynamicEsDoc[] = [];

    await Bluebird.map(
      nonRepeatableDynamics,
      async (dynamic) => {
        const metricScorePO = dynamic.metricsContent.metricScorePO;
        const isSameMetricStrategy = metricScorePO?.detailsJson?.dynamicStrategy?.isSameMetricStrategy;

        if (isSameMetricStrategy == 1) {
          const dimHitRes = this.findMetricDimensions(metricScorePO);

          // 本次维度有命中数据，然后判断上次快照是否有命中数据
          if (dimHitRes.length > 0) {
            const { batchId, companyId } = dynamic;

            // 找到上次快照的动态记录
            const preMetricDynamic = await this.dynamicRepo.findOne({
              where: {
                companyId: companyId,
                orgId: dynamic.orgId,
                riskModelBranchCode: dynamic.riskModelBranchCode,
                metricsId: dynamic.metricsId,
                batchId: Not(batchId),
              },
              select: ['id', 'batchId', 'metricsContent'],
              order: { createDate: 'DESC' },
            });

            const preBatchId = preMetricDynamic?.batchId;

            // 使用策略模式处理指标特定逻辑
            const strategy = this.metricStrategyFactory.getStrategy(metricScorePO, dimHitRes);
            const processedDynamic = await strategy.processMetric(dynamic, dimHitRes, batchId, preBatchId);

            // 如果有内容变化，加入处理后的动态列表
            if (processedDynamic.metricsContent.displayContent.length > 0) {
              processedDynamics.push(processedDynamic);
            }
          }
        } else {
          // 不需要比对内容的指标，直接加入处理后的动态列表
          processedDynamics.push(dynamic);
        }
      },
      { concurrency: 5 },
    );

    return processedDynamics;
  }

  /**
   * 比对两次动态的具体内容判断动态是否变化
   * 委托给相应的策略类处理
   * @param dynamic 动态内容
   * @param dimHitRes 维度命中结果
   * @param preBatchId 上次批次id
   */
  public async compareDynamicContents(
    dynamic: MonitorMetricDynamicEsDoc,
    dimHitRes: DimensionHitResultPO[],
    preBatchId: number,
  ): Promise<[number, DynamicDisplayContent[]]> {
    const metricScorePO = dynamic.metricsContent.metricScorePO;
    const strategy = this.metricStrategyFactory.getStrategy(metricScorePO, dimHitRes);
    return strategy.compareContent(dynamic, dimHitRes, preBatchId);
  }

  /**
   * 从指标得分中提取维度命中结果
   * @param metircScore 指标得分
   */
  public findMetricDimensions(metircScore: any): DimensionHitResultPO[] {
    const dimHitResults: DimensionHitResultPO[] = [];
    if (metircScore?.totalHits) {
      const array = [];
      array.push(metircScore.hitDetails);
      if (metircScore?.otherHitDetails?.length > 0) {
        array.push(...metircScore.otherHitDetails);
      }
      array.forEach((mhd) => {
        if (mhd?.totalHits) {
          if (mhd?.must?.length) {
            dimHitResults.push(...mhd.must);
          }
          if (mhd?.should?.length) {
            dimHitResults.push(...mhd.should);
          }
          if (mhd?.must_not?.length) {
            dimHitResults.push(...mhd.must_not);
          }
        }
      });
    }
    return dimHitResults.filter((t) => t.dimensionKey);
  }

  /**
   * 重置命中详情
   * 根据比对结果重新修改metricScorePO中每个策略的命中情况
   * @param hitDetails 命中详情
   * @param contents 内容列表
   */
  public resetHitDetails(hitDetails: MetricHitDetailsPO, contents: DynamicDisplayContent[]) {
    let hitDetailsHits = 0;
    const getCount = (contents: DynamicDisplayContent[], strategyId: number) => {
      return contents.filter((c) => c.strategyId == strategyId).reduce((acc, cur) => acc + cur.count, 0);
    };

    hitDetails?.must?.forEach((f) => {
      f.totalHits = getCount(contents, f.strategyId);
      hitDetailsHits += f.totalHits;
    });

    hitDetails?.must_not?.forEach((f) => {
      f.totalHits = getCount(contents, f.strategyId);
      hitDetailsHits += f.totalHits;
    });

    hitDetails?.should?.forEach((f) => {
      f.totalHits = getCount(contents, f.strategyId);
      hitDetailsHits += f.totalHits;
    });

    hitDetails['totalHits'] = hitDetailsHits;
    return hitDetails;
  }

  /**
   * 指标是否纳税人资质变更
   * @param dimHit 维度命中结果
   */
  private isTaxpayerCertificationChange(dimHit: DimensionHitResultPO): boolean {
    return dimHit.dimensionKey == DimensionTypeEnums.TaxpayerCertificationChange;
  }
}
