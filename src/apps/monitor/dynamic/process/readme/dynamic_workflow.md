# 监控动态生成工作流程

## 整体流程概述

监控动态生成是一个复杂的流程，主要用于监控企业风险变化并生成相应的动态通知。整个流程从批量尽调开始，通过消息队列触发动态分析，最终生成并保存动态信息。

```mermaid
flowchart TD
    A[开始: 批量尽调] --> B[ContinuousDiligenceProcessor处理尽调任务]
    B --> C[发送消息到continuousDiligenceAnalyzeQueue]
    C --> D[MonitorDynamicMessageListener.handleMetricsAnalyze消费消息]
    D --> E{尽调是否完成?}
    E -->|未完成| F[重新发送消息延迟执行]
    E -->|已完成| G[处理尽调数据生成动态]
    G --> H[生成占位动态]
    G --> I[生成可重复动态]
    G --> J[生成不可重复动态]
    H --> K[保存动态到数据库和ES]
    I --> K
    J --> K
    K --> L[更新监控企业关联的最新最高动态等级]
    L --> M[结束]
```

## 关键流程节点

### 1. 批量尽调启动

监控分组(MonitorGroup)会启动一个批次(Batch)来进行批量尽调。

### 2. ContinuousDiligenceProcessor 处理任务

`ContinuousDiligenceProcessor.processJobMessage`方法处理每个尽调任务。

### 3. 发送消息到队列

尽调任务处理完成后，发送消息到`continuousDiligenceAnalyzeQueue`队列。

### 4. 消息消费与动态生成

`MonitorDynamicMessageListener.handleMetricsAnalyze`方法消费消息并生成动态。

## 核心方法：handleMetricsAnalyze

`handleMetricsAnalyze`方法是整个流程的核心，负责分析尽调结果并生成监控动态。

### 方法流程

1. **接收消息参数**：

   - 获取组织 ID(orgId)、产品(product)、监控分组 ID(monitorGroupId)、尽调 ID 列表(diligenceIds)、批次 ID(batchId)和重试次数(retryCount)

2. **查找并分类尽调实体**：

   - 通过`findDiligence`方法获取尽调实体
   - 将尽调分为已完成(finshedDiligence)和未完成(unFinshedDiligence)

3. **处理未完成尽调**：

   - 对于未完成的尽调，重新发送消息延迟执行
   - 设置重试次数上限(20 次)，超过则记录错误

4. **准备动态生成数据**：

   - 获取已完成尽调的公司 ID 列表
   - 查询已初始化过的企业指标动态(status=-1 或-2)作为对比基线
   - 查找关联方企业

5. **生成三类动态**：

   a. **占位动态(placeholderDynamics)**：

   - 针对首次尽调且没有命中任何指标的企业
   - 生成状态为-2 的占位记录

   b. **可重复动态(repeatableDynamics)**：

   - 指标配置允许重复生成的动态
   - 根据`allowRepeatedHits`属性判断

   c. **不可重复动态(nonRepeatableDynamics)**：

   - 指标配置不允许重复生成的动态
   - 需要对比两次快照内容是否有变化

6. **处理不可重复动态**：

   - 调用`processHashKeyByIsSameMetric`方法处理不可重复动态
   - 根据指标策略配置`isSameMetricStrategy`判断是否需要比对内容

7. **保存动态**：

   - 调用`saveDynamic`方法将动态保存到数据库和 ES

8. **更新监控企业状态**：
   - 更新监控企业关联的最新最高动态等级
   - 更新监控分组变更计数

## 关键辅助方法详解

### 1. processHashKeyByIsSameMetric

此方法用于处理不可重复动态，通过比对两次快照内容判断是否需要创建新动态。

```mermaid
flowchart TD
    A[开始] --> B{是否需要比对内容?}
    B -->|是| C[查找指标维度]
    B -->|否| D[直接加入处理列表]
    C --> E{是否有维度命中?}
    E -->|是| F[查找上次动态记录]
    E -->|否| D
    F --> G[比对动态内容]
    G --> H{有新增内容?}
    H -->|是| I[重新生成HashKey]
    H -->|否| J[忽略]
    I --> K[更新命中记录]
    K --> L[处理关联方变更]
    L --> D
    D --> M[返回处理后的动态列表]
```

具体步骤：

1. 判断动态是否需要比对内容(`isSameMetricStrategy=1`)
2. 提取指标维度
3. 查找上次动态记录
4. 通过`compareDynamicContents`比对内容
5. 如有新增内容，重新生成 HashKey 并更新
6. 对于关联方变更指标，更新监控主体的关联方变化动态标记

### 2. compareDynamicContents

此方法比对两次动态的具体内容，判断是否有变化。

```mermaid
flowchart TD
    A[开始] --> B{是否有维度命中?}
    B -->|否| C[返回空结果]
    B -->|是| D{是否有上次批次ID?}
    D -->|是| E[查询两次批次差异]
    D -->|否| F[查询当前快照数据]
    E --> G[处理新增数据]
    F --> G
    G --> H[返回命中总数和显示内容]
```

具体步骤：

1. 检查是否有维度命中
2. 根据是否有上次批次 ID 决定比对方式
3. 如有上次批次 ID，调用`searchDimensionDiffsByBatch`比对两次批次差异
4. 如无上次批次 ID，直接取当前快照数据作为新增
5. 特殊处理纳税人资质变更、关联方变更等特殊维度

### 3. saveDynamic

保存动态到数据库和 ES。

```mermaid
flowchart TD
    A[开始] --> B[填充动态内容]
    B --> C[分批处理动态]
    C --> D[保存到数据库]
    C --> E[保存到ES]
    D --> F[统计插入数量]
    E --> F
    F --> G[返回插入成功数量]
```

具体步骤：

1. 调用`fulfillDynamicContent`填充动态内容
2. 将动态分批处理
3. 并行保存到数据库和 ES
4. 统计并返回插入成功数量

### 4. fulfillDynamicContent

填充动态内容，特别处理关联方变化指标。

```mermaid
flowchart TD
    A[开始] --> B{是否需要填充内容?}
    B -->|否| C[返回原动态]
    B -->|是| D[查找指标维度]
    D --> E[查询维度命中详情]
    E --> F{是否有命中?}
    F -->|是| G[添加动态内容]
    F -->|否| C
    G --> H{是否关联方变更?}
    H -->|是| I[更新监控主体标记]
    H -->|否| C
    I --> C
```

具体步骤：

1. 判断动态是否需要填充内容(非初始化且无内容)
2. 提取指标维度
3. 查询维度命中详情
4. 如有命中，添加动态内容
5. 对关联方变更指标，更新监控主体标记

### 5. findMetricDimensions

从指标得分对象中提取维度命中结果。

```mermaid
flowchart TD
    A[开始] --> B{是否有命中?}
    B -->|否| C[返回空数组]
    B -->|是| D[收集命中详情]
    D --> E[处理must条件]
    D --> F[处理should条件]
    D --> G[处理must_not条件]
    E --> H[合并结果]
    F --> H
    G --> H
    H --> I[过滤有效维度]
    I --> J[返回维度列表]
```

具体步骤：

1. 检查指标是否有命中
2. 收集指标的命中详情(hitDetails 和 otherHitDetails)
3. 处理 must、should 和 must_not 条件
4. 合并并过滤有效维度

## 涉及的主要数据实体

1. **MonitorMetricsDynamicEntity**: 监控指标动态实体
2. **DiligenceHistoryEntity**: 尽调历史实体
3. **MonitorGroupEntity**: 监控分组实体
4. **MonitorCompanyEntity**: 监控企业实体
5. **MonitorCompanyRelatedPartyEntity**: 监控企业关联方实体

## 动态状态说明

- **-2**: 占位动态(首次尽调无风险)
- **-1**: 基线动态(首次尽调有风险)
- **0**: 正常动态
- **其他**: 特殊状态动态

## 优化方向

当前工作流程有以下几点可以优化：

1. **代码复杂度高**：主方法`handleMetricsAnalyze`过长，包含多层嵌套逻辑
2. **职责分散**：动态生成、内容比对、保存等逻辑混合在一起
3. **可测试性差**：方法依赖关系复杂，难以进行单元测试
4. **扩展性受限**：添加新类型的动态处理逻辑需要修改多处代码

下一步将通过策略模式或抽象类继承等方式重构代码，提高清晰度和健壮性。
