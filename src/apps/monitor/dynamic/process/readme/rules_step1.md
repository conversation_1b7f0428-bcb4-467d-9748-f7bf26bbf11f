# 优化步骤 1：领域服务分离和接口定义

## 目标

1. 将`MonitorDynamicMessageListener.handleMetricsAnalyze`方法中的功能按照领域职责划分为多个独立服务
2. 定义清晰的接口，为后续依赖注入和单元测试做准备
3. 降低单个类和方法的复杂度，提高系统的可维护性
4. 改善依赖关系，符合单一职责原则

## 预期收益

1. 代码结构更加清晰，每个服务关注特定的领域职责
2. 提高代码可测试性，便于编写单元测试和集成测试
3. 降低`MonitorDynamicMessageListener`的复杂度，使其成为协调者而非执行者
4. 为后续优化和功能扩展打下基础

## 具体任务

### 1. 创建领域服务接口

创建以下接口文件：

1. `IDiligenceService` - 尽调处理相关接口

   - `findDiligence(diligenceIds: number[]): Promise<DiligenceHistoryEntity[]>` - 查询尽调历史
   - `classifyDiligence(diligenceEntities: DiligenceHistoryEntity[]): {finished: DiligenceHistoryEntity[], unfinished: DiligenceHistoryEntity[]}` - 分类尽调实体
   - `retryUnfinishedDiligence(diligenceIds: number[], retryCount: number): Promise<void>` - 重试未完成尽调

2. `IDynamicGenerationService` - 动态生成相关接口

   - `generatePlaceholderDynamics(params: PlaceholderDynamicParams): MonitorMetricDynamicEsDoc[]` - 生成占位动态
   - `classifyMetricDynamics(diligences: DiligenceHistoryEntity[], initDynamics: MonitorMetricsDynamicEntity[]): {repeatable: MonitorMetricDynamicEsDoc[], nonRepeatable: MonitorMetricDynamicEsDoc[]}` - 分类指标动态

3. `IDynamicComparisonService` - 动态内容比对相关接口

   - `processNonRepeatableDynamics(nonRepeatableDynamics: MonitorMetricDynamicEsDoc[]): Promise<MonitorMetricDynamicEsDoc[]>` - 处理不可重复动态
   - `compareDynamicContents(dynamic: MonitorMetricDynamicEsDoc, dimHitRes: DimensionHitResultPO[], preBatchId: number): Promise<[number, DynamicDisplayContent[]]>` - 比对动态内容
   - `findMetricDimensions(metircScore: any): DimensionHitResultPO[]` - 提取指标维度命中结果

4. `IDynamicPersistenceService` - 动态持久化相关接口
   - `saveDynamic(dynamics: MonitorMetricDynamicEsDoc[]): Promise<number>` - 保存动态
   - `updateMonitorCompanyStatus(diligences: DiligenceHistoryEntity[], monitorGroupId: number, batchId: number): Promise<void>` - 更新监控企业状态

### 2. 实现领域服务类

1. `MonitorDiligenceService` - 实现尽调处理服务

   - 从`handleMetricsAnalyze`方法中提取尽调查找、分类和重试逻辑
   - 实现接口中定义的所有方法

2. `MonitorDynamicGenerationService` - 实现动态生成服务

   - 从`handleMetricsAnalyze`方法中提取动态生成和分类逻辑
   - 实现接口中定义的所有方法

3. `MonitorDynamicComparisonService` - 实现动态内容比对服务

   - 从`handleMetricsAnalyze`方法中提取动态比对和处理逻辑
   - 实现接口中定义的所有方法

4. `MonitorDynamicPersistenceService` - 实现动态持久化服务
   - 从`handleMetricsAnalyze`方法中提取保存和状态更新逻辑
   - 实现接口中定义的所有方法

### 3. 重构`MonitorDynamicMessageListener`类

1. 添加依赖注入构造函数，注入上述服务

   ```typescript
   constructor(
     private readonly diligenceService: IDiligenceService,
     private readonly dynamicGenerationService: IDynamicGenerationService,
     private readonly dynamicComparisonService: IDynamicComparisonService,
     private readonly dynamicPersistenceService: IDynamicPersistenceService,
   ) {}
   ```

2. 重构`handleMetricsAnalyze`方法，使用注入的服务替代内联逻辑

   - 使用`diligenceService`处理尽调相关逻辑
   - 使用`dynamicGenerationService`生成和分类动态
   - 使用`dynamicComparisonService`处理不可重复动态
   - 使用`dynamicPersistenceService`保存动态和更新状态

3. 保持方法签名和返回值不变，确保向后兼容性

### 4. 创建模块组织服务

1. 创建`MonitorDynamicProcessModule`模块，管理所有服务的依赖注入
   - 提供所有服务的依赖注入配置
   - 导出所有服务供其他模块使用

## 注意事项

1. 不要修改任何业务逻辑，确保功能的完整性和正确性
2. 使用 TypeScript 类型，避免使用 any 类型
3. 代码风格要统一，与现有代码保持一致
4. 所有新增文件放在`src/apps/monitor/dynamic/process/`目录下
5. 遵循 SOLID 原则，特别是单一职责原则和依赖倒置原则
6. 阅读 dynamic_workflow.md 文件，了解当前的动态生成流程
7. 阅读 dynamic_optimization_plan.md 文件，了解当前的优化方案的整体步骤
