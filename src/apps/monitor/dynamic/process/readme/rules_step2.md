# 优化步骤 2：策略模式处理不同类型指标

## 目标

1. 使用策略模式处理不同类型指标的特殊逻辑
2. 将指标类型特定的处理逻辑从主流程中分离出来
3. 提供可扩展的架构，便于添加新类型指标的处理逻辑
4. 消除条件分支判断，使代码更加整洁

## 预期收益

1. 提高系统扩展性，添加新类型指标处理只需实现新的策略类
2. 隔离不同类型指标的特殊处理逻辑，降低维护难度
3. 改善代码结构，避免条件判断的复杂嵌套
4. 符合开闭原则，对扩展开放，对修改关闭

## 具体任务

### 1. 创建指标策略接口和基类

1. 创建`IMetricStrategy`接口，定义处理不同类型指标的通用方法：

   ```typescript
   interface IMetricStrategy {
     canHandle(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): boolean;
     processMetric(
       dynamic: MonitorMetricDynamicEsDoc,
       dimHitRes: DimensionHitResultPO[],
       batchId: number,
       preBatchId: number,
     ): Promise<MonitorMetricDynamicEsDoc>;
     compareContent(dynamic: MonitorMetricDynamicEsDoc, dimHitRes: DimensionHitResultPO[], preBatchId: number): Promise<[number, DynamicDisplayContent[]]>;
   }
   ```

2. 创建`BaseMetricStrategy`抽象类，实现通用逻辑：
   - 实现`processMetric`方法的通用部分，包括更新动态信息、重新生成 HashKey 等
   - 定义抽象方法让子类实现特定逻辑

### 2. 实现具体策略类

1. `RegularMetricStrategy` - 处理常规指标

   - 实现常规指标的处理逻辑
   - 从原有代码中提取通用指标处理逻辑
   - 重写`canHandle`方法，判断是否为常规指标
   - 实现`compareContent`方法，比对普通指标内容变化

2. `RelatedCompanyMetricStrategy` - 处理关联方变更指标

   - 实现关联方变更特有逻辑
   - 从原有代码中提取关联方变更相关逻辑
   - 重写`canHandle`方法，判断是否为关联方变更指标
   - 实现`compareContent`方法，处理关联方变更的特殊比对逻辑
   - 处理监控企业关联方状态更新

3. `TaxpayerMetricStrategy` - 处理纳税人资质变更指标
   - 实现纳税人资质变更特有逻辑
   - 从原有代码中提取纳税人资质变更相关逻辑
   - 重写`canHandle`方法，判断是否为纳税人资质变更指标
   - 实现`compareContent`方法，处理纳税人资质变更的特殊比对逻辑

### 3. 创建策略工厂

1. 创建`MetricStrategyFactory`类，负责创建和管理策略实例：

   ```typescript
   @Injectable()
   export class MetricStrategyFactory {
     constructor(
       private readonly regularMetricStrategy: RegularMetricStrategy,
       private readonly relatedCompanyMetricStrategy: RelatedCompanyMetricStrategy,
       private readonly taxpayerMetricStrategy: TaxpayerMetricStrategy,
     ) {}

     public getStrategy(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): IMetricStrategy {
       // 依次检查每个策略是否适用
       if (this.taxpayerMetricStrategy.canHandle(metricScorePO, dimHitRes)) {
         return this.taxpayerMetricStrategy;
       }

       if (this.relatedCompanyMetricStrategy.canHandle(metricScorePO, dimHitRes)) {
         return this.relatedCompanyMetricStrategy;
       }

       // 默认使用常规指标策略
       return this.regularMetricStrategy;
     }
   }
   ```

2. 创建`MetricStrategiesModule`模块，管理所有策略的依赖注入：
   - 提供所有策略类和工厂类的依赖注入配置
   - 导出策略工厂供其他模块使用

### 4. 重构`MonitorDynamicComparisonService`

1. 在`MonitorDynamicComparisonService`中使用策略模式：

   ```typescript
   @Injectable()
   export class MonitorDynamicComparisonService implements IDynamicComparisonService {
     constructor(
       @InjectRepository(MonitorMetricsDynamicEntity) private readonly dynamicRepo: Repository<MonitorMetricsDynamicEntity>,
       @InjectRepository(MonitorCompanyEntity) private readonly monitorCompanyRepo: Repository<MonitorCompanyEntity>,
       private readonly snapshotEsCompareService: DiligenceSnapshotEsCompareService,
       private readonly snapshotEsService: DiligenceSnapshotEsService,
       private readonly metricStrategyFactory: MetricStrategyFactory,
     ) {}

     public async processNonRepeatableDynamics(nonRepeatableDynamics: MonitorMetricDynamicEsDoc[]): Promise<MonitorMetricDynamicEsDoc[]> {
       // 使用策略模式处理不同类型的指标
       // ...
     }

     public async compareDynamicContents(
       dynamic: MonitorMetricDynamicEsDoc,
       dimHitRes: DimensionHitResultPO[],
       preBatchId: number,
     ): Promise<[number, DynamicDisplayContent[]]> {
       // 使用策略模式比对内容变化
       const metricScorePO = dynamic.metricsContent.metricScorePO;
       const strategy = this.metricStrategyFactory.getStrategy(metricScorePO, dimHitRes);
       return strategy.compareContent(dynamic, dimHitRes, preBatchId);
     }
   }
   ```

2. 修改`MonitorDynamicComparisonService`使用策略工厂：
   - 将原有的条件判断替换为策略模式
   - 在`processNonRepeatableDynamics`方法中使用策略处理不同类型的指标
   - 在`compareDynamicContents`方法中使用策略比对内容变化

## 注意事项

1. 不要修改任何业务逻辑，确保功能的完整性和正确性
2. 使用 TypeScript 类型，避免使用 any 类型
3. 代码风格要统一，与现有代码保持一致
4. 所有新增文件放在`src/apps/monitor/dynamic/process/strategies/`目录下
5. 策略类应该只关注特定类型指标的处理逻辑，不应包含通用流程逻辑
6. 确保策略模式的引入不会破坏现有功能
7. 遵循 SOLID 原则，特别是开闭原则和依赖倒置原则
8. 阅读 dynamic_workflow.md 文件，了解当前的动态生成流程
9. 阅读 dynamic_optimization_plan.md 文件，了解当前的优化方案的整体步骤
