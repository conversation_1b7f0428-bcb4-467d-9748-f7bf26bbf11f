# 监控动态生成处理流程 V2

本文档详细描述了监控动态生成的完整数据处理流程，基于优化后的领域服务分离和策略模式架构。通过阅读本文档，您可以清晰地了解系统处理数据的各个环节，并在需要进行扩展或修改时，快速定位到相应的代码位置。

## 1. 整体流程概览

监控动态生成流程从`MonitorDynamicMessageListenerV2.handleMetricsAnalyze`方法开始，经过以下主要步骤：

1. **尽调记录处理**：查找尽调记录、分类和处理未完成尽调
2. **数据准备**：获取初始化动态和关联方信息
3. **动态生成与分类**：生成占位动态，并将指标动态分为可重复和不可重复两类
4. **动态内容比对**：对不可重复动态进行内容比对，确定是否有变化
5. **动态持久化**：保存动态到数据库和 ES，更新监控企业状态

整个流程由多个领域服务协作完成，每个服务负责特定的职责，形成了清晰的责任链。

## 2. 入口与流程协调

### 2.1 消息监听器

入口：`MonitorDynamicMessageListenerV2.handleMetricsAnalyze`

```typescript
// 消息队列监听和处理入口
this.continuousDiligenceAnalyzeQueue.consume(this.handleMetricsAnalyze.bind(this));
```

该方法接收来自消息队列的`AnalyzeMonitorDynamicMessagePO`消息，包含：

- `orgId`: 组织 ID
- `product`: 产品代码
- `monitorGroupId`: 监控分组 ID
- `diligenceIds`: 尽调 ID 列表
- `batchId`: 批次 ID
- `retryCount`: 重试次数

### 2.2 流程协调

`handleMetricsAnalyze`方法充当整个流程的协调者，调用各个领域服务完成特定任务：

```typescript
// 处理流程步骤
1. 查找和分类尽调记录 (diligenceService)
2. 处理未完成的尽调记录 (diligenceService)
3. 如果尽调快照都未完成，直接返回0
4. 获取基础数据 (初始化动态和关联方)
5. 生成占位动态 (generationService)
6. 分类指标动态 (generationService)
7. 重置关联方变更状态标签 (persistenceService)
8. 保存占位动态 (persistenceService)
9. 保存可重复动态 (persistenceService)
10. 处理和保存不可重复动态 (comparisonService, persistenceService)
11. 更新监控企业状态 (persistenceService)
```

返回值：包含生成的不同类型动态数量

```typescript
{
  placeholderCount: number;
  repeatableCount: number;
  nonRepeatableCount: number;
  totalCount: number;
}
```

## 3. 尽调处理 - DiligenceService

### 3.1 查找尽调记录

入口：`IDiligenceService.findDiligence`

根据尽调 ID 列表查询尽调历史记录，获取详细信息：

- 公司 ID、名称
- 尽调状态
- 快照 ID 和状态
- 风险模型 ID

### 3.2 分类尽调记录

入口：`IDiligenceService.classifyDiligence`

将尽调记录按照快照完成状态分为两类：

- `finshedDiligence`: 已完成快照的尽调记录（用于生成动态）
- `unFinshedDiligence`: 未完成快照的尽调记录（需要重试）

分类标准：快照状态为完成状态的归为已完成，其他状态归为未完成。

### 3.3 处理未完成尽调

入口：`IDiligenceService.retryUnfinishedDiligence`

对于未完成的尽调记录，根据重试次数进行处理：

- 如果重试次数未超过限制，则重新放入队列等待处理
- 记录日志以便跟踪处理进度

## 4. 动态生成 - DynamicGenerationService

### 4.1 生成占位动态

入口：`IDynamicGenerationService.generatePlaceholderDynamics`

生成占位类型的动态，用于特定场景下的展示：

- 根据尽调记录和风险模型信息构建占位动态
- 设置动态类型为占位类型
- 填充基本信息（组织 ID、公司 ID、时间等）

### 4.2 分类指标动态

入口：`IDynamicGenerationService.classifyMetricDynamics`

将指标动态分类为可重复和不可重复两类：

- **可重复动态**：每次尽调都会生成的动态，不需要与历史记录比对
- **不可重复动态**：需要与历史记录比对，只有内容变化时才生成新动态

分类标准：

- 根据指标配置中的`isSameMetricStrategy`标志判断是否需要比对历史记录
- 特定类型的指标（如关联方变更、纳税人资质变更）由专门的策略处理

### 4.3 填充动态内容

入口：`IDynamicGenerationService.fulfillDynamicContent`

根据尽调结果和指标规则填充动态内容：

- 根据指标配置生成动态的标题和详情内容
- 填充命中规则和计分情况
- 设置展示样式和格式化信息

## 5. 动态比对 - DynamicComparisonService

### 5.1 处理不可重复动态

入口：`IDynamicComparisonService.processNonRepeatableDynamics`

对不可重复的动态进行处理：

1. 提取指标得分和维度命中结果
2. 判断是否需要进行内容比对 (`isSameMetricStrategy == 1`)
3. 查找上次批次的动态记录作为比对基准
4. 使用策略模式选择适合的策略处理不同类型指标
5. 根据比对结果确定是否保留该动态

关键代码：

```typescript
// 使用策略模式处理指标特定逻辑
const strategy = this.metricStrategyFactory.getStrategy(metricScorePO, dimHitRes);
const processedDynamic = await strategy.processMetric(dynamic, dimHitRes, batchId, preBatchId);

// 如果有内容变化，加入处理后的动态列表
if (processedDynamic.metricsContent.displayContent.length > 0) {
  processedDynamics.push(processedDynamic);
}
```

### 5.2 比对动态内容

入口：`IDynamicComparisonService.compareDynamicContents`

委托给策略类比对两次动态内容的变化：

1. 根据指标类型选择适合的策略
2. 调用策略的`compareContent`方法执行比对逻辑
3. 返回命中数量和动态展示内容

### 5.3 提取维度命中结果

入口：`IDynamicComparisonService.findMetricDimensions`

从指标得分对象中提取维度命中结果：

- 包括 must、should、must_not 等不同类型的命中规则
- 过滤出有效的维度命中结果

## 6. 策略模式 - 处理不同类型指标

### 6.1 策略接口和工厂

接口：`IMetricStrategy`
工厂：`MetricStrategyFactory`

策略接口定义了三个核心方法：

- `canHandle`: 判断策略是否适用于特定指标
- `processMetric`: 处理指标特定逻辑
- `compareContent`: 比对指标内容变化

策略工厂根据指标类型选择合适的策略：

```typescript
public getStrategy(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): IMetricStrategy {
  if (this.taxpayerMetricStrategy.canHandle(metricScorePO, dimHitRes)) {
    return this.taxpayerMetricStrategy;
  }

  if (this.relatedCompanyMetricStrategy.canHandle(metricScorePO, dimHitRes)) {
    return this.relatedCompanyMetricStrategy;
  }

  return this.regularMetricStrategy;
}
```

### 6.2 具体策略类型

系统当前实现了三种策略：

1. **RegularMetricStrategy**：处理常规指标

   - 适用于大多数普通指标
   - 执行基本的内容比对逻辑

2. **RelatedCompanyMetricStrategy**：处理关联方变更指标

   - 检测公司关联方的变化情况
   - 特殊处理关联方的添加、删除、变更等情况

3. **TaxpayerMetricStrategy**：处理纳税人资质变更指标
   - 比对纳税人资质的变化
   - 特殊处理资质升级、降级等情况

### 6.3 扩展新策略

要添加新的指标处理策略，需要：

1. 创建新的策略类实现`IMetricStrategy`接口
2. 在`MetricStrategyFactory`中注册新策略
3. 在工厂的`getStrategy`方法中添加选择逻辑

```typescript
// 新策略类示例
@Injectable()
export class NewSpecialMetricStrategy implements IMetricStrategy {
  canHandle(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): boolean {
    // 判断是否是特殊类型指标的逻辑
    return dimHitRes.some((dim) => dim.dimensionKey === 'NEW_SPECIAL_DIMENSION_TYPE');
  }

  async processMetric(/*...参数...*/) {
    // 处理特殊指标逻辑
  }

  async compareContent(/*...参数...*/) {
    // 比对特殊指标内容逻辑
  }
}
```

## 7. 动态持久化 - DynamicPersistenceService

### 7.1 保存动态

入口：`IDynamicPersistenceService.saveDynamic`

将动态保存到数据库和 ES 索引：

1. 将动态对象转换为数据库实体
2. 批量插入数据库
3. 创建 ES 文档索引
4. 返回成功插入的数量

### 7.2 更新监控企业状态

入口：`IDynamicPersistenceService.updateMonitorCompanyStatus`

根据动态生成结果更新监控企业状态：

1. 更新公司的最新批次 ID 和动态数量
2. 更新监控状态和上次监控时间
3. 处理特殊情况下的状态标记

### 7.3 重置关联方变更状态

入口：`IDynamicPersistenceService.resetRelatedDynamicHashKey`

重置公司的关联方变更状态标志：

- 清除关联方变更的动态 HashKey
- 为关联方变更准备状态标记

## 8. 添加新功能的指导

### 8.1 添加新类型指标处理

如果需要添加新类型指标的特殊处理逻辑：

1. 在`strategies`目录下创建新的策略类：

```typescript
@Injectable()
export class NewMetricStrategy extends BaseMetricStrategy {
  canHandle(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): boolean {
    // 判断逻辑
  }

  async compareContent(dynamic: MonitorMetricDynamicEsDoc, dimHitRes: DimensionHitResultPO[], preBatchId: number): Promise<[number, DynamicDisplayContent[]]> {
    // 特殊比对逻辑
  }
}
```

2. 在`MetricStrategiesModule`中注册新策略：

```typescript
@Module({
  providers: [
    // 现有策略...
    NewMetricStrategy,
  ],
  exports: [/* ... */],
})
```

3. 在`MetricStrategyFactory`中添加新策略的依赖注入和选择逻辑：

```typescript
constructor(
  // 现有依赖...
  private readonly newMetricStrategy: NewMetricStrategy,
) {}

public getStrategy(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): IMetricStrategy {
  if (this.newMetricStrategy.canHandle(metricScorePO, dimHitRes)) {
    return this.newMetricStrategy;
  }
  // 现有判断逻辑...
}
```

### 8.2 修改动态内容比对逻辑

如果需要修改或扩展动态内容比对逻辑：

1. 修改相应策略类的`compareContent`方法：

```typescript
async compareContent(dynamic: MonitorMetricDynamicEsDoc, dimHitRes: DimensionHitResultPO[], preBatchId: number): Promise<[number, DynamicDisplayContent[]]> {
  // 1. 提取本次快照中的关键数据
  // 2. 查询上次快照数据用于比对
  // 3. 实现新的比对逻辑
  // 4. 返回命中数量和动态内容
}
```

2. 如果需要访问新的数据源，在策略类的构造函数中注入相应服务。

### 8.3 添加新的动态类型

如果需要添加全新的动态类型：

1. 在`IDynamicGenerationService`接口和实现类中添加相应方法：

```typescript
generateNewTypeDynamics(...params): MonitorMetricDynamicEsDoc[] {
  // 生成新类型动态的逻辑
}
```

2. 在`MonitorDynamicMessageListenerV2.handleMetricsAnalyze`方法中添加处理步骤：

```typescript
// 生成新类型动态
const newTypeDynamics = this.generationService.generateNewTypeDynamics(...);
// 处理和保存新类型动态
```

## 9. 流程图示

```
┌────────────────────┐
│消息队列             │
└───────┬────────────┘
        │
        ▼
┌────────────────────┐
│handleMetricsAnalyze│
└───────┬────────────┘
        │
        ▼
┌────────────────────┐     ┌────────────────────┐
│DiligenceService    │◄────┤1. 查找尽调记录      │
└───────┬────────────┘     └────────────────────┘
        │
        ▼
┌────────────────────┐     ┌────────────────────┐
│DiligenceService    │◄────┤2. 分类尽调记录      │
└───────┬────────────┘     └────────────────────┘
        │
        ▼
┌────────────────────┐     ┌────────────────────┐
│DiligenceService    │◄────┤3. 处理未完成尽调    │
└───────┬────────────┘     └────────────────────┘
        │
        ▼
┌────────────────────┐     ┌────────────────────┐
│DynamicGeneration   │◄────┤4. 生成占位动态      │
└───────┬────────────┘     └────────────────────┘
        │
        ▼
┌────────────────────┐     ┌────────────────────┐
│DynamicGeneration   │◄────┤5. 分类指标动态      │
└───────┬────────────┘     └────────────────────┘
        │
        ▼
┌────────────────────┐     ┌────────────────────┐
│DynamicComparison   │◄────┤6. 处理不可重复动态  │
└───────┬────────────┘     └────────────────────┘
        │               ┌─────────────────────┐
        ├──────────────►│策略1: 常规指标       │
        │               └─────────────────────┘
        │               ┌─────────────────────┐
        ├──────────────►│策略2: 关联方变更     │
        │               └─────────────────────┘
        │               ┌─────────────────────┐
        └──────────────►│策略3: 纳税人资质变更 │
                        └─────────────────────┘
        │
        ▼
┌────────────────────┐     ┌────────────────────┐
│DynamicPersistence  │◄────┤7. 保存动态         │
└───────┬────────────┘     └────────────────────┘
        │
        ▼
┌────────────────────┐     ┌────────────────────┐
│DynamicPersistence  │◄────┤8. 更新企业状态     │
└───────┬────────────┘     └────────────────────┘
        │
        ▼
┌────────────────────┐
│返回结果             │
└────────────────────┘
```

## 10. 总结

监控动态生成流程通过领域服务分离和策略模式实现了高度模块化和可扩展性：

1. **领域服务分离**：将功能划分为尽调处理、动态生成、动态比对和动态持久化四个领域服务，每个服务负责特定职责。

2. **策略模式**：使用策略模式处理不同类型指标的特殊逻辑，便于扩展新的指标类型处理。

3. **流程协调**：`MonitorDynamicMessageListenerV2`作为流程协调者，组织各个服务协同工作。

通过这种架构，我们可以轻松地：

- 添加新的指标类型处理逻辑
- 修改特定环节的处理方式
- 扩展或优化整个流程

在进行代码修改或扩展时，只需定位到相应的领域服务或策略类，进行局部修改，而不影响其他部分，大大降低了维护复杂度和扩展难度。
