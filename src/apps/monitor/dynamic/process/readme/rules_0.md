1. 通过 dynamic_workflow.md 文件，了解当前的动态生成流程
2. 通过 dynamic_optimization_plan.md 文件，了解当前的优化方案
3. 结合这两个内容帮我开始做优化，需要注意以下几点
   1. 不要修改任务业务逻辑，及时逻辑暂时有问题，也先不要优化逻辑，确保这次只调整代码解构
   2. 新增代码都放在 src/apps/monitor/dynamic/process/ 文件夹
   3. 起文件名字时候尽量保持一定的含义，避免随意使用名字，同时注意体现 MonitorDynamic 处理相关的含义，避免过于简单的名字跟项目中其他类的名字重复了
   4. 代码风格要统一，尽量和现有代码保持一致
   5. 优化代码时候，主要精力还是集中在 MonitorDynamicMessageListener.handleMetricsAnalyze 方法及它之后调用的方法
   6. 这次优化主要围绕 dynamic_optimization_plan.md 文件中的优化方案来进行，主要是阶段 1-阶段 4 ，先不用管阶段 5
   7. 优化代码时候，注意使用 typescript 类型，不要使用 any 类型

这是我制定的优化阶段，应该会需要调整很多代码，所以你可以根据我上面的要求，帮我拆分成若干个你认为合理的粒度, 然后帮我分别生成 prompt ，方便后续根据这个 prompt 来帮我逐步进行优化, 名字可以采用 rules_step1.md, rules_step2.md, rules_step3.md, rules_step4.md 这样的命名
