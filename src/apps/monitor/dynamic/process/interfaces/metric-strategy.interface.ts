import { DimensionHitResultPO } from '../../../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { MonitorMetricDynamicEsDoc } from '../../../../../libs/model/monitor/MonitorMetricDynamicEsDoc';
import { DynamicDisplayContent } from '../../../../../libs/model/metric/MetricDynamicContentPO';
import { MetricScorePO } from '../../../../../libs/model/metric/MetricScorePO';

/**
 * 指标策略接口
 * 定义处理不同类型指标的通用方法
 */
export interface IMetricStrategy {
  /**
   * 判断是否可以处理特定指标
   * @param metricScorePO 指标得分对象
   * @param dimHitRes 维度命中结果
   */
  canHandle(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): boolean;

  /**
   * 处理指标特定逻辑
   * @param dynamic 动态内容
   * @param dimHitRes 维度命中结果
   * @param batchId 批次ID
   * @param preBatchId 上次批次ID
   */
  processMetric(dynamic: MonitorMetricDynamicEsDoc, dimHitRes: DimensionHitResultPO[], batchId: number, preBatchId: number): Promise<MonitorMetricDynamicEsDoc>;

  /**
   * 比对指标内容变化
   * @param dynamic 动态内容
   * @param dimHitRes 维度命中结果
   * @param preBatchId 上次批次ID
   */
  compareContent(dynamic: MonitorMetricDynamicEsDoc, dimHitRes: DimensionHitResultPO[], preBatchId: number): Promise<[number, DynamicDisplayContent[]]>;
}
