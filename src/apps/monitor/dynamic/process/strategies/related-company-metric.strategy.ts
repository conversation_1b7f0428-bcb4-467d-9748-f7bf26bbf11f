// import { Injectable } from '@nestjs/common';
// import { InjectRepository } from '@nestjs/typeorm';
// import { Repository } from 'typeorm';
// import { BaseMetricStrategy } from './base-metric.strategy';
// import { DimensionHitResultPO } from '../../../../../libs/model/diligence/dimension/DimensionHitResultPO';
// import { MonitorMetricDynamicEsDoc } from '../../../../../libs/model/monitor/MonitorMetricDynamicEsDoc';
// import { DynamicDisplayContent } from '../../../../../libs/model/metric/MetricDynamicContentPO';
// import { MetricScorePO } from '../../../../../libs/model/metric/MetricScorePO';
// import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
// import * as Bluebird from 'bluebird';
// import { SnapshotChangesTypeEnum } from '../../../../diligence/snapshot/po/SearchDimensionDiffsFromSnapshotRequest';
// import { DiligenceSnapshotEsCompareService } from '../../../../diligence/snapshot/diligence.snapshot.es.compare.service';
// import { DiligenceSnapshotEsService } from '../../../../diligence/snapshot/diligence.snapshot.es.service';
// import { MonitorCompanyEntity } from '../../../../../libs/entities/MonitorCompanyEntity';
// import { SearchAnalyzedDimensionDiffsResponse } from '../../../../diligence/snapshot/po/SearchAnalyzedDimensionDiffsResponse';
// import { In } from 'typeorm';
// import { MonitorCompanyRelatedPartyEntity } from '../../../../../libs/entities/MonitorCompanyRelatedPartyEntity';
// import { MonitorCompanyRelatedStatusEnum } from '../../../../../libs/enums/monitor/MonitorCompanyStatusEnums';

// /**
//  * 关联方变更指标策略类
//  * 处理关联方变更特有逻辑
//  */
// @Injectable()
// export class RelatedCompanyMetricStrategy extends BaseMetricStrategy {
//   constructor(
//     @InjectRepository(MonitorCompanyEntity) private readonly monitorCompanyRepo: Repository<MonitorCompanyEntity>,
//     @InjectRepository(MonitorCompanyRelatedPartyEntity) private readonly relatedCompanyRepo: Repository<MonitorCompanyRelatedPartyEntity>,
//     private readonly snapshotEsCompareService: DiligenceSnapshotEsCompareService,
//     private readonly snapshotEsService: DiligenceSnapshotEsService,
//   ) {
//     super();
//   }

//   /**
//    * 判断是否可以处理特定指标
//    * 处理关联方变更指标
//    * @param metricScorePO 指标得分对象
//    * @param dimHitRes 维度命中结果
//    */
//   public canHandle(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): boolean {
//     return this.isRelatedMetric(dimHitRes);
//   }

//   /**
//    * 比对关联方变更指标内容变化
//    * @param dynamic 动态内容
//    * @param dimHitRes 维度命中结果
//    * @param preBatchId 上次批次ID
//    */
//   public async compareContent(
//     dynamic: MonitorMetricDynamicEsDoc,
//     dimHitRes: DimensionHitResultPO[],
//     preBatchId: number,
//   ): Promise<[number, DynamicDisplayContent[]]> {
//     let totalHits = 0;
//     const displayContents: DynamicDisplayContent[] = [];
//     if (!dimHitRes.length) {
//       return [totalHits, displayContents];
//     }

//     const { batchId, monitorGroupId, companyId, diligenceId, orgId } = dynamic;
//     await Bluebird.map(dimHitRes, async (dimHit) => {
//       const { dimensionKey, strategyId } = dimHit;
//       let dynamicAdd: DynamicDisplayContent;

//       if (preBatchId) {
//         dynamic.preBatchId = preBatchId;
//         dynamic.metricsContent.displayContent = [];

//         // 查找减少的关联方
//         const reducedData = await this.snapshotEsCompareService.searchDimensionDiffsByBatch({
//           dimensionKey: [dimensionKey],
//           batchIds: [batchId, preBatchId],
//           recordIds: [],
//           companyId: companyId,
//           changesType: SnapshotChangesTypeEnum.Removed,
//           pageSize: 1000,
//           pageIndex: 1,
//         });

//         if (reducedData.total > 0) {
//           // 分组内已监控的关联方，如果变成了在减少关联方reducedData中，需要将其 relatedStatus 标记为无效，并且生成一个动态
//           const affectedCompanyList = await this.updateRelated(reducedData, monitorGroupId, companyId, MonitorCompanyRelatedStatusEnum.Valid);
//           if (affectedCompanyList.length > 0) {
//             const invalidRelatedIds = affectedCompanyList.map((r) => r.companyIdRelated);
//             // 将失效的已监控关联方记录到动态中
//             const dynamicReduced: DynamicDisplayContent = {
//               dimensionKey,
//               strategyId,
//               operate: 1,
//               count: affectedCompanyList.length,
//               dimensionContent: reducedData.data
//                 .filter((d) => invalidRelatedIds.includes(d.dimensionContent.companyKeynoRelated))
//                 .map((d) => d.dimensionContent),
//             };
//             totalHits += dynamicReduced.count;
//             displayContents.push(dynamicReduced);
//           }
//         }

//         // 查找新增的关联方
//         const addData = await this.snapshotEsCompareService.searchDimensionDiffsByBatch({
//           dimensionKey: [dimensionKey],
//           batchIds: [batchId, preBatchId],
//           strategyId,
//           recordIds: [],
//           companyId: companyId,
//           changesType: SnapshotChangesTypeEnum.Added,
//           pageSize: 500,
//           pageIndex: 1,
//         });

//         if (addData.total > 0) {
//           // 添加动态内容
//           dynamicAdd = {
//             dimensionKey,
//             strategyId,
//             operate: 0,
//             count: addData.total,
//             dimensionContent: addData.data.map((d) => d.dimensionContent),
//           };
//           totalHits += dynamicAdd.count;
//           displayContents.push(dynamicAdd);

//           // 分组内已监控失效的关联方，如果变成了在新增关联方addData中，需要将relatedStatus变更成有效
//           await this.updateRelated(addData, monitorGroupId, companyId, MonitorCompanyRelatedStatusEnum.Invalid);
//         }
//       } else {
//         // 没有上次快照，直接取出本次快照的内容记录为新增动态内容
//         const addData = await this.snapshotEsService.searchSnapshotData(
//           {
//             companyId,
//             orgId,
//             diligenceId: [diligenceId],
//             dimensionKey: [dimensionKey],
//             pageSize: 1,
//             pageIndex: 1,
//             strategyId,
//           },
//           true,
//         );

//         if (addData.total > 0) {
//           // 添加动态内容
//           dynamicAdd = {
//             dimensionKey,
//             strategyId,
//             operate: 0,
//             count: addData.total,
//             dimensionContent: addData.data,
//           };
//           totalHits += dynamicAdd.count;
//           displayContents.push(dynamicAdd);
//         }
//       }
//     });

//     return [totalHits, displayContents];
//   }

//   /**
//    * 处理完成后的后续操作
//    * 记录关联方变化的动态hashKey
//    * @param dynamic 动态内容
//    */
//   protected async postProcess(dynamic: MonitorMetricDynamicEsDoc): Promise<void> {
//     const { companyId, monitorGroupId, companyMetricsHashkey } = dynamic;
//     // 给监控主体上记录关联方变化的动态hashKey
//     await this.monitorCompanyRepo.update({ companyId, monitorGroupId }, { relatedDynamicHashKey: companyMetricsHashkey });
//   }

//   /**
//    * 修改监控列表关联方状态
//    * @param changeResult es查询出的发生变化的关联方
//    * @param monitorGroupId 监控分组
//    * @param companyIdPrimary 主体公司
//    * @param relatedStatus 需要的修改的关联方，当前状态
//    */
//   private async updateRelated(
//     changeResult: SearchAnalyzedDimensionDiffsResponse,
//     monitorGroupId: number,
//     companyIdPrimary: string,
//     relatedStatus: MonitorCompanyRelatedStatusEnum,
//   ) {
//     const companyIdRelateds = changeResult.data.map((d) => d.dimensionContent.companyKeynoRelated);

//     const [companyList, count] = await this.relatedCompanyRepo.findAndCount({
//       monitorGroupId,
//       companyIdPrimary,
//       companyIdRelated: In(companyIdRelateds),
//       status: relatedStatus,
//     });

//     if (count > 0) {
//       if (MonitorCompanyRelatedStatusEnum.Valid == relatedStatus) {
//         // 当前监控关联方是有效，需要变更成失效
//         await this.relatedCompanyRepo.update(
//           {
//             monitorGroupId,
//             companyIdPrimary,
//             companyIdRelated: In(companyIdRelateds),
//             status: relatedStatus,
//           },
//           { status: MonitorCompanyRelatedStatusEnum.Invalid },
//         );
//       } else {
//         // 当前监控关联方是无效，需要变更成有效，并更新relatedType
//         await Bluebird.map(companyList, async (company) => {
//           const relatedCompany = changeResult.data.find((d) => d.dimensionContent.companyKeynoRelated == company.companyIdRelated);
//           const relatedTypeStr = relatedCompany.dimensionContent.relatedTypes.join(',');
//           await this.relatedCompanyRepo.update(
//             { id: company.id, status: relatedStatus },
//             {
//               status: MonitorCompanyRelatedStatusEnum.Valid,
//               relatedTypeStr,
//             },
//           );
//         });
//       }
//     }
//     return companyList;
//   }

//   /**
//    * 判断是否为关联方变更指标
//    * @param dimHitRes 维度命中结果
//    */
//   private isRelatedMetric(dimHitRes: DimensionHitResultPO[]): boolean {
//     return dimHitRes.length == 1 && dimHitRes[0].dimensionKey == DimensionTypeEnums.RelatedCompanyChange && dimHitRes[0].dimensionFilter.isRelated == false;
//   }
// }
