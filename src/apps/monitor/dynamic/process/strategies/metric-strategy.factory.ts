import { Injectable } from '@nestjs/common';
import { IMetricStrategy } from '../interfaces/metric-strategy.interface';
import { RegularMetricStrategy } from './regular-metric.strategy';
// import { RelatedCompanyMetricStrategy } from './related-company-metric.strategy';
import { TaxpayerMetricStrategy } from './taxpayer-metric.strategy';
import { DimensionHitResultPO } from '../../../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { MetricScorePO } from '../../../../../libs/model/metric/MetricScorePO';

/**
 * 指标策略工厂类
 * 负责根据指标类型创建相应的策略实例
 */
@Injectable()
export class MetricStrategyFactory {
  constructor(
    private readonly regularMetricStrategy: RegularMetricStrategy,
    // private readonly relatedCompanyMetricStrategy: RelatedCompanyMetricStrategy,
    private readonly taxpayerMetricStrategy: TaxpayerMetricStrategy,
  ) {}

  /**
   * 根据指标分数对象和维度命中结果获取合适的策略
   * @param metricScorePO 指标分数对象
   * @param dimHitRes 维度命中结果
   */
  public getStrategy(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): IMetricStrategy {
    // 首先检查特殊类型的指标
    if (this.taxpayerMetricStrategy.canHandle(metricScorePO, dimHitRes)) {
      return this.taxpayerMetricStrategy;
    }

    // if (this.relatedCompanyMetricStrategy.canHandle(metricScorePO, dimHitRes)) {
    //   return this.relatedCompanyMetricStrategy;
    // }

    // 默认使用常规指标策略
    return this.regularMetricStrategy;
  }
}
