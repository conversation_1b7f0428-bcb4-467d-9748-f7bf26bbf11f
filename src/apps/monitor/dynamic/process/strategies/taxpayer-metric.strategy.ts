import { Injectable } from '@nestjs/common';
import { BaseMetricStrategy } from './base-metric.strategy';
import { DimensionHitResultPO } from '../../../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { MonitorMetricDynamicEsDoc } from '../../../../../libs/model/monitor/MonitorMetricDynamicEsDoc';
import { DynamicDisplayContent } from '../../../../../libs/model/metric/MetricDynamicContentPO';
import { MetricScorePO } from '../../../../../libs/model/metric/MetricScorePO';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import * as moment from 'moment/moment';
import { SnapshotChangesTypeEnum } from '../../../../diligence/snapshot/po/SearchDimensionDiffsFromSnapshotRequest';
import { DiligenceSnapshotEsCompareService } from '../../../../diligence/snapshot/diligence.snapshot.es.compare.service';
import { DiligenceSnapshotEsService } from '../../../../diligence/snapshot/diligence.snapshot.es.service';
import { DiligenceHistoryEntity } from '../../../../../libs/entities/DiligenceHistoryEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

/**
 * 纳税人资质变更指标策略类
 * 处理纳税人资质变更特有逻辑
 */
@Injectable()
export class TaxpayerMetricStrategy extends BaseMetricStrategy {
  constructor(
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceRepo: Repository<DiligenceHistoryEntity>,
    private readonly snapshotEsCompareService: DiligenceSnapshotEsCompareService,
    private readonly snapshotEsService: DiligenceSnapshotEsService,
  ) {
    super();
  }

  /**
   * 判断是否可以处理特定指标
   * 处理纳税人资质变更指标
   * @param metricScorePO 指标得分对象
   * @param dimHitRes 维度命中结果
   */
  public canHandle(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): boolean {
    return this.hasTaxpayerCertificationChange(dimHitRes);
  }

  /**
   * 比对纳税人资质变更指标内容变化
   * @param dynamic 动态内容
   * @param dimHitRes 维度命中结果
   * @param preBatchId 上次批次ID
   */
  public async compareContent(
    dynamic: MonitorMetricDynamicEsDoc,
    dimHitRes: DimensionHitResultPO[],
    preBatchId: number,
  ): Promise<[number, DynamicDisplayContent[]]> {
    let totalHits = 0;
    const displayContents: DynamicDisplayContent[] = [];
    if (!dimHitRes.length) {
      return [totalHits, displayContents];
    }

    // 找到纳税人资质变更维度
    const taxpayerDimHit = dimHitRes.find((dimHit) => dimHit.dimensionKey === DimensionTypeEnums.TaxpayerCertificationChange);
    if (!taxpayerDimHit) {
      return [totalHits, displayContents];
    }

    const { batchId, companyId, diligenceId } = dynamic;
    const { dimensionKey, strategyId } = taxpayerDimHit;

    if (preBatchId) {
      // 查找新增的纳税人资质数据
      const addData = await this.snapshotEsCompareService.searchDimensionDiffsByBatch({
        dimensionKey: [dimensionKey],
        batchIds: [batchId, preBatchId],
        strategyId,
        recordIds: [],
        companyId: companyId,
        changesType: SnapshotChangesTypeEnum.Added,
        pageSize: 500,
        pageIndex: 1,
      });

      if (addData.total > 0) {
        // 查询移除的纳税人资质数据
        const removeData = await this.snapshotEsCompareService.searchDimensionDiffsByBatch({
          dimensionKey: [dimensionKey],
          batchIds: [batchId, preBatchId],
          strategyId,
          recordIds: [],
          companyId: companyId,
          changesType: SnapshotChangesTypeEnum.Removed,
          pageSize: 500,
          pageIndex: 1,
        });

        // 获取尽调历史实体，用于获取变更日期
        const diligenceHistoryEntity = await this.diligenceRepo.findOne({
          where: {
            id: diligenceId,
          },
        });

        // 创建纳税人资质变更特有的动态内容格式
        const dynamicAdd: DynamicDisplayContent = {
          dimensionKey,
          strategyId,
          operate: 0,
          count: addData.total,
          dimensionContent: [
            {
              changeBefore: removeData?.data[0]?.dimensionContent?.description || '-',
              changeAfter: addData?.data[0]?.dimensionContent?.description || '-',
              changeDate: moment(diligenceHistoryEntity?.createDate).format('YYYY-MM-DD') || '-',
              CreateDate: moment(diligenceHistoryEntity?.createDate).startOf('day').valueOf() / 1000,
            },
          ],
        };
        totalHits += dynamicAdd.count;
        displayContents.push(dynamicAdd);
      }
    } else {
      // 没有上次快照，直接取出本次快照的内容记录为新增动态内容
      const addData = await this.snapshotEsService.searchSnapshotData(
        {
          companyId,
          diligenceId: [diligenceId],
          dimensionKey: [dimensionKey],
          pageSize: 1,
          pageIndex: 1,
          strategyId,
        },
        true,
      );

      if (addData.total > 0) {
        // 添加动态内容
        const dynamicAdd: DynamicDisplayContent = {
          dimensionKey,
          strategyId,
          operate: 0,
          count: addData.total,
          dimensionContent: addData.data,
        };
        totalHits += dynamicAdd.count;
        displayContents.push(dynamicAdd);
      }
    }

    return [totalHits, displayContents];
  }

  /**
   * 判断是否含有纳税人资质变更指标
   * @param dimHitRes 维度命中结果
   */
  private hasTaxpayerCertificationChange(dimHitRes: DimensionHitResultPO[]): boolean {
    return dimHitRes.some((dimHit) => dimHit.dimensionKey == DimensionTypeEnums.TaxpayerCertificationChange);
  }
}
