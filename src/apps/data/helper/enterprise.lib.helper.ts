import { Injectable } from '@nestjs/common';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitEnterpriseDimensionQueryParam } from '../../../libs/model/diligence/details/request/HitEnterpriseDimensionQueryParam';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import * as _ from 'lodash';
import { cloneDeep, compact, flatMap, isNumber, orderBy, pick, sum } from 'lodash';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { ContractBreachDegree } from '../../../libs/constants/model.constants';
import {
  getCompareResult,
  getCompareResultForArray,
  getIsValidValue,
  getStartEnd,
  getStartTimeByCycle,
  getStartTimeByNaturalCycle,
  getStartTimeByNaturalCycleMonth,
  getTargetDate,
} from '../../../libs/utils/diligence/diligence.utils';
import { toRoundFixed } from '../../../libs/utils/utils';
import * as moment from 'moment/moment';
import { isOrganism } from '../../company/utils';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import { DATE_FORMAT } from '../../../libs/constants/common';
import { dateToTimestamp, getExpirationDate } from '../../../libs/utils/date.utils';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { ConfigService } from '../../../libs/config/config.service';
import { CompanySearchService } from '../../company/company-search.service';
import { HttpUtilsService } from '../../../libs/config/httputils.service';
import { CompanyDetailService } from '../../company/company-detail.service';
import { DimensionHitStrategyFieldsEntity } from '../../../libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums, EsOperator } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { RelatedRiskRequest } from '../../company/model/RelatedRiskRequest';
import { TargetInvestigationEnums, TargetScopeEnums } from '../../../libs/enums/dimension/FieldValueEnums';
import * as Bluebird from 'bluebird';
import { CompanyCertificationConstants } from '../../../libs/constants/company-certification.constants';
import { ApplicationProgressConstant, ShareholdRoleConstant, SourcesInvestInstiteRankConstant } from '../../../libs/constants/company.constants';
import { PersonHelper } from './person.helper';
import { NebulaGraphHelper } from './nebula.graph.helper';
import { BaseRelatedPartyRequest } from '../../../libs/model/company/BaseRelatedPartyRequest';
import { NebulaRelatedEdgeEnums } from '../../../libs/enums/dimension/NebulaRelatedEdgeEnums';
import { Cacheable } from '@type-cacheable/core';
import { RelatedPartyGroupPO } from '../../../libs/model/diligence/graph/RelatedPartyGroupPO';
import { KysCompanyResponseDetails, KysCompanySearchRequest } from '@kezhaozhao/company-search-api';
import { RelatedTypeEnums } from '../../../libs/enums/dimension/RelatedTypeEnums';
import { PersonData } from '../../../libs/model/data/source/PersonData';
import { ESResponse } from '@kezhaozhao/search-utils';
import { RelatedHelper } from './related.helper';
import { RiskChangeCategoryEnum } from '../../../libs/enums/riskchange/RiskChangeCategoryEnum';
import { RiskChangeHelper } from './risk.change.helper';

@Injectable()
export class EnterpriseLibHelper {
  public readonly logger: Logger = QccLogger.getLogger(EnterpriseLibHelper.name);

  constructor(
    public readonly configService: ConfigService,
    public readonly companySearchService: CompanySearchService,
    public readonly httpUtils: HttpUtilsService,
    public readonly companyDetailsService: CompanyDetailService,
    public readonly personHelper: PersonHelper,
    public readonly nebulaGraphHelper: NebulaGraphHelper,
    public readonly relatedHelper: RelatedHelper,
    public readonly riskChangeHelper: RiskChangeHelper,
  ) {}

  @Cacheable({ ttlSeconds: 300 })
  private async postRequestSourcePath(sourcePath: string, reqData: any) {
    const result = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, reqData);
    return result;
  }

  @Cacheable({ ttlSeconds: 300 })
  private async getRequestSourcePath(sourcePath: string, reqData: any) {
    const result = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, reqData);
    return result;
  }

  /**
   * 获取动态数据（工商维度）
   * @param dimension
   * @param data
   * @param category
   */
  async getMainInfoUpdateHolder(dimension: DimensionHitStrategyPO, sourcePath: string, data: Record<string, any>, category: string): Promise<any> {
    const params = {
      keyNo: data.keyNo,
      pageIndex: data.pageIndex,
      pageSize: data.pageSize,
      category,
    };

    const cycle = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.cycle)?.fieldValue[0] || 1;
    // const cycle: number = dimension?.strategyFields.find()?.cycle || 1;

    if (cycle > 0) {
      const dateStart = moment().subtract(cycle, 'year').format(DATE_FORMAT);
      const dateEnd = moment().format(DATE_FORMAT);

      Object.assign(params, { dateStart, dateEnd });
    }

    return await this.httpUtils.getRequest(this.configService.proxyServer.roverService + sourcePath, params);
  }

  /**
   * 营业执照处理
   * @param keyNo
   * @param dateType
   * @param expirationDate
   * @param nowDate
   * @public
   */
  public async getBusinessLicenseResult(keyNo: string, dateType: any, expirationDate: number, nowDate: number) {
    const companyDetailsQcc = await this.companySearchService.companyDetailsQcc(keyNo);
    let expirationDesc = '缺失';
    let startDate = null;
    let endDate = null;
    if (companyDetailsQcc?.TeamEnd >= 0) {
      endDate = companyDetailsQcc.TeamEnd;
      startDate = companyDetailsQcc.TermStart;
      if ((isNumber(endDate) && endDate == 0) || endDate > expirationDate) {
        expirationDesc = '有效';
      } else if (nowDate >= endDate) {
        expirationDesc = '已到期';
      } else {
        switch (dateType) {
          case 1:
            expirationDesc = '近7日到期';
            break;
          case 3:
            expirationDesc = '近3个月到期';
            break;
          case 2:
          default:
            expirationDesc = '近1个月到期';
        }
      }
    }
    if (expirationDesc != '有效') {
      return {
        index: 0,
        name: '营业执照',
        startDate,
        endDate,
        expirationDesc,
      };
    }
    return null;
  }

  /**
   * 经营范围，注册地址，企业名称变更列表
   * @param dimension
   * @param data
   * @public
   */
  public async getCoyHisInfo(dimension: DimensionHitStrategyPO, sourcePath: string, data: Record<string, any>): Promise<HitDetailsBaseResponse> {
    // if (dimension.key === DimensionTypeEnums.MainInfoUpdateManager) {
    //   return this.getMainInfoUpdate(dimension, data);
    // }
    // const sourcePath = dimension?.detailSourcePath ? dimension.detailSourcePath : dimension.sourcePath;
    const reqUrl = this.configService.proxyServer.dataService + sourcePath;
    const dimensionDetails = new HitDetailsBaseResponse();
    const MainInfoUpdatekeyMap = {
      [DimensionTypeEnums.MainInfoUpdateLegalPerson]: 'OperList', // 法定代表人变更
      [DimensionTypeEnums.MainInfoUpdateScope]: 'ScopeList', // 经营范围变更,
      [DimensionTypeEnums.MainInfoUpdateAddress]: 'AddressList', // 注册地址变更
      [DimensionTypeEnums.MainInfoUpdateName]: 'CompanyNameList', // 企业名称变更
    };

    // 工商变更记录分类型 对应不同的维度处理
    let updateInfos = [];
    if (isOrganism(data.keyNo)) {
      const organismList = await this.getOrganismChangeInfo(data.keyNo);
      organismList.forEach((e) => {
        if (e[MainInfoUpdatekeyMap[dimension?.dimensionDef.key]]) {
          updateInfos.push(e[MainInfoUpdatekeyMap[dimension?.dimensionDef.key]]);
        }
      });
    } else {
      const dataResult = await this.httpUtils.getRequest(reqUrl, data);
      if (dataResult?.Status !== 200) {
        return HitDetailsBaseResponse.failed('获取工商变更信息失败');
      }
      updateInfos = dataResult?.Result[MainInfoUpdatekeyMap[dimension?.dimensionDef.key]] || [];
    }
    return await this.getMainInfoUpdateInfos(dimension, data, updateInfos, dimensionDetails);
  }

  /**
   * 社会组织变更记录
   * @param keyNo
   * @public
   */
  public async getOrganismChangeInfo(keyNo: string) {
    try {
      const companyDetail = await this.companySearchService.companyDetailsQcc(keyNo);
      const changeList = companyDetail?.ChangeDiffInfo?.ChangeList;
      if (!changeList?.length) {
        return [];
      }
      const resList = changeList.map((c) => {
        if (c.ProjectName === '法定代表人变更') {
          return {
            OperList: {
              OperName: c.BeforeInfos[0].Content,
              KeyNo: c.BeforeInfos[0].KeyNo,
              ChangeDate: moment(Number(c.ChangeDate) * 1000).format(DATE_FORMAT),
            },
          };
        } else if (c.ProjectName === '住所变更') {
          return {
            AddressList: {
              ChangeDate: moment(Number(c.ChangeDate) * 1000).format(DATE_FORMAT),
              Address: c.BeforeInfos[0].Content,
            },
          };
        } else if (c.ProjectName === '变更业务范围') {
          return {
            ScopeList: {
              ChangeDate: moment(Number(c.ChangeDate) * 1000).format(DATE_FORMAT),
              Scope: c.BeforeInfos[0].Content,
            },
          };
        } else if (c.ProjectName === '名称变更') {
          return {
            CompanyNameList: {
              ChangeDate: moment(Number(c.ChangeDate) * 1000).format(DATE_FORMAT),
              CompanyName: c.BeforeInfos[0].Content,
            },
          };
        }
      });
      return compact(resList);
    } catch (e) {
      this.logger.error(`http Get List/OrganismChangeInfo err:`, e);
      return [];
    }
  }

  /**
   * 查询公司详情接口,获取当前的信息
   * @param dimension
   * @param keyNo
   */
  async getCompanyDetailCurrentScope(dimension: DimensionHitStrategyPO, keyNo: string) {
    // const companyDetail = await this.httpUtils.getRequest(this.configService.dataServer.companyDeatil, {
    //   keyNo: keyNo,
    // });
    const companyDetail = await this.companySearchService.companyDetailsQcc(keyNo);
    if (companyDetail) {
      switch (dimension?.dimensionDef.key) {
        case DimensionTypeEnums.MainInfoUpdateScope:
          return { Scope: companyDetail.Scope };
        case DimensionTypeEnums.MainInfoUpdateAddress:
          return { Address: companyDetail.Address };
        case DimensionTypeEnums.MainInfoUpdateName:
          return { CompanyName: companyDetail.Name };
        case DimensionTypeEnums.MainInfoUpdateLegalPerson:
          return { OperName: companyDetail?.Oper?.Name, KeyNo: companyDetail?.Oper?.KeyNo };
        case DimensionTypeEnums.MainInfoUpdateManager:
          break;
      }
    }
    return null;
  }

  /**
   * 工商变更相关维度
   * @param dimension
   * @param data
   * @returns
   */
  async getMainInfoUpdate(dimension: DimensionHitStrategyPO, sourcePath: string, data: Record<string, any>) {
    // const sourcePath = dimension?.detailSourcePath ? dimension.detailSourcePath : dimension.sourcePath;
    const dimensionDetails = new HitDetailsBaseResponse();
    const MainInfoUpdatekeyMap = {
      [DimensionTypeEnums.MainInfoUpdateManager]: 'EmployeeList', // 董监高变更
    };

    const result = await this.httpUtils.postRequest(this.configService.proxyServer.roverService + sourcePath, data);
    // 工商变更记录分类型 对应不同的维度处理
    const updateInfos = result?.data?.Result[MainInfoUpdatekeyMap[dimension?.dimensionDef.key]] || [];
    return await this.getMainInfoUpdateInfos(dimension, data, updateInfos, dimensionDetails);
  }

  /**
   * qcc详情接口
   * @param dimension
   * @param data
   * @public
   */
  public async getQccDimensionDetail(dimension: DimensionHitStrategyPO, sourcePath: string, data: Record<string, any>): Promise<HitDetailsBaseResponse> {
    const { keyNo } = data;
    // const sourcePath = dimension?.detailSourcePath ? dimension.detailSourcePath : dimension.sourcePath;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const reqUrl = this.configService.proxyServer.dataService + sourcePath;
    let method = 'GET';
    switch (dimension?.dimensionDef.key) {
      case DimensionTypeEnums.JudicialAuction: {
        const cycle = dimension?.getCycle() || 1;
        Object.assign(data, {
          KeyNo: keyNo,
          // 只查询非历史数据
          isValid: '1',
          isSortAsc: data?.order === 'ASC',
        });
        if (cycle !== -1) {
          Object.assign(data, {
            rangeFilters: [
              {
                type: 'liandate',
                rangeList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
              },
            ],
          });
        }
        method = 'POST';
        break;
      }
      case DimensionTypeEnums.CompanyCredit: {
        // 被列入严重违法失信企业名录
        data.isValid = '1';
        break;
      }
      case DimensionTypeEnums.CompanyCreditHistory: {
        data.isValid = getIsValidValue('0');
        // 接口暂不支持时间筛选
        /*const cycle = dimension?.getCycle();
        if (cycle && cycle !== -1) {
          Object.assign(data, {
            rangeFilters: JSON.stringify([
              {
                type: 'liandate',
                dateList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
              },
            ]),
          });
        }*/
        break;
      }
      case DimensionTypeEnums.GuaranteeRisk: {
        //排序字段，可选
        // guaranteedprincipal 被保证债权本金
        // judgedate 裁判日期
        // submitdate 发布日期
        data.sortField = 'submitdate';
        data.role = 1; //角色 1:担保方，2：被担保方，3：债权人
        const cycle = dimension?.getCycle();
        //担保风险
        const guaranteedPrincipal = dimension?.getStrategyFieldByKey(DimensionFieldKeyEnums.guaranteedprincipal);
        if (cycle && cycle !== -1) {
          Object.assign(data, {
            rangeFilters: JSON.stringify([
              {
                type: 'submitdate',
                dateList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
              },
            ]),
          });
        }
        if (guaranteedPrincipal) {
          Object.assign(data, {
            rangeFilters: JSON.stringify([
              {
                type: DimensionFieldKeyEnums.guaranteedprincipal,
                dateList: [getStartEnd(guaranteedPrincipal.fieldValue[0] as number, guaranteedPrincipal.compareType)],
              },
            ]),
          });
        }
        break;
      }
    }
    let dataResult;
    if (method === 'GET') {
      dataResult = await this.httpUtils.getRequest(reqUrl, data);
    } else {
      dataResult = await this.httpUtils.postRequest(reqUrl, data);
    }
    return Object.assign(dimensionDetails, pick(dataResult, ['Result', 'Paging', 'GroupItems']));
  }

  /**
   * 食品安全检查不合格
   * @param data
   * @param dimension
   * @param sourcePath
   * @public
   */
  public async getFoodQualityProblem(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const { keyNo, pageIndex, pageSize } = data;

    const strategySortField = dimension.getSortField();
    const sortField = strategySortField?.field || 'batch';
    const isSortAsc = strategySortField?.order == 'ASC' ? 'true' : 'false';

    const foodQualityResult = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      searchKey: keyNo,
      checkResult: 2,
      sortField,
      isSortAsc,
      pageIndex,
      pageSize,
      isB: true,
    });
    if (foodQualityResult?.Result) {
      // 数据处理，实现原 credit 数据源格式，兼容已有后续相关业务的处理
      foodQualityResult.Result.forEach((item) => {
        const AmountDesc = [];
        if (item.UnqualifiedDetails && item.UnqualifiedDetails.length > 0) {
          item.UnqualifiedDetails.forEach((detail) => {
            AmountDesc.push({
              check_normal: detail.CheckNormal,
              check_result: detail.CheckResult,
              check_name: detail.CheckName,
            });
          });
        }
        Object.assign(item, {
          Title: item.FoodName,
          Amount2: item.CheckRank,
          Address: item.ProdAddress,
          ActionRemark: item.TrademarkInfo,
          Specs: item.Specs,
          LianDate: item.Batch,
          NameAndKeyNo: item.SamplingCompanys,
          ApplicantInfo: item.ProdCompanys,
          ExecuteStatus: item.CheckResult,
          AmountDesc: JSON.stringify(AmountDesc),
          recordId: item.Id,
        });
      });
      return Object.assign(dimensionDetails, pick(foodQualityResult, ['Result', 'Paging']));
    }
    return dimensionDetails;
  }

  /**
   * 公司、法定代表人/股东/董监高存在涉贿、不正当竞争等刑事犯罪行为
   * @param data
   * @param dimension
   * @param sourcePath
   * @public
   */
  public async getCriminalOffence(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const { keyNo, pageIndex, pageSize } = data;
    // 公司的刑事案件
    const crimainalResult = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo,
      caseTypeMain: 'xs',
      roleType: 'D,O',
      pageIndex,
      pageSize,
      isB: true,
    });
    // TOOD: 获取 法定代表人/股东/董监高 的 keyno 然后调用跟公司一样的接口只是换一下keyNo

    if (crimainalResult?.Result) {
      return Object.assign(dimensionDetails, pick(crimainalResult, ['Result']), {
        Paging: {
          PageSize: data.pageSize,
          PageIndex: data.pageIndex,
          TotalRecords: 1,
        },
      });
    }
    return dimensionDetails;
  }

  /**
   * 合同违约
   * @param data
   * @param sourcePath
   * @public
   */
  public async getContractBreach(data: HitEnterpriseDimensionQueryParam, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const breachResult = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
    });
    if (breachResult?.Result) {
      if (Object.keys(ContractBreachDegree).find((levelStr) => levelStr === breachResult.Result?.Revel)) {
        return Object.assign(dimensionDetails, {
          Result: [breachResult.Result],
          Paging: {
            PageSize: data.pageSize,
            PageIndex: data.pageIndex,
            TotalRecords: 1,
          },
        });
      }
    }
    return dimensionDetails;
  }

  /**
   * 动产抵押
   * @param data
   * @param validField
   * @param sourcePath
   * @public
   */
  public async getChattelMortgage(data: HitEnterpriseDimensionQueryParam, validField: DimensionHitStrategyFieldsEntity, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const { keyNo, pageIndex, pageSize } = data;
    const chattelMortgageResult = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo,
      type: 'mortgager', // 动产抵押 身份角色：mortgager-抵押人,pledgee-抵押权人, -全部
      isValid: getIsValidValue(validField?.fieldValue[0]),
      pageIndex,
      pageSize,
      isB: true,
    });
    if (chattelMortgageResult?.Result) {
      return Object.assign(dimensionDetails, pick(chattelMortgageResult, ['Result', 'Paging']));
    }
    return dimensionDetails;
  }

  /**
   * 破产重整
   * @param data
   * @param validField
   * @param sourcePath
   * @public
   */
  public async getBankruptcy(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    validField: DimensionHitStrategyFieldsEntity,
    sourcePath: string,
  ) {
    const { keyNo } = data;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    // 排查对象
    const reqData = {};
    Object.assign(reqData, {
      sortField: data?.field || dimension?.getSortField()?.field || 'riskdate',
      isSortAsc: (data?.order || dimension?.getSortField()?.order) === 'ASC',
      keyNo: keyNo,
      searchKey: keyNo,
      pageSize: data?.pageSize || 5,
      pageIndex: data?.pageIndex || 1,
      isValid: getIsValidValue(validField?.fieldValue[0]),
      isExclude: false,
      isB: true,
    });
    const cycleField = dimension.getCycleField();
    const cycle = cycleField.fieldValue?.[0] as number;
    const operator = cycleField ? EsOperator[cycleField.compareType] : 'gte';
    if (cycle && cycle !== -1) {
      switch (operator) {
        case 'gt':
        case 'gte': {
          Object.assign(reqData, {
            rangeFilters: JSON.stringify([
              {
                type: 'riskdate',
                dateList: [{ start: moment().subtract(cycle, 'year').unix() }],
              },
            ]),
          });
          break;
        }
        case 'lt':
        case 'lte': {
          Object.assign(reqData, {
            rangeFilters: JSON.stringify([
              {
                type: 'riskdate',
                dateList: [{ end: moment().subtract(cycle, 'year').unix() }],
              },
            ]),
          });
          break;
        }
      }
    }
    const bankruptcyResult = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, reqData);
    if (bankruptcyResult?.Result) {
      return Object.assign(dimensionDetails, pick(bankruptcyResult, ['Result', 'Paging', 'GroupItems']));
    }
    return dimensionDetails;
  }

  /**
   * 土地抵押
   * @param data
   * @param dimension
   * @param validField
   * @param sourcePath
   * @public
   */
  public async getLandMortgage(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    validField: DimensionHitStrategyFieldsEntity,
    sourcePath: string,
  ) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const { keyNo, pageIndex, pageSize } = data;
    const landMortgageParams = {
      keyNo,
      type: 'mortgagor', // 土地抵押 角色类型 mortgage 抵押权人, mortgagor 抵押人
      isValid: getIsValidValue(validField?.fieldValue[0]),
      pageIndex,
      pageSize,
      isB: true,
    };

    const cycle = dimension.getCycle();
    if (cycle && cycle !== -1) {
      landMortgageParams['start'] = getStartTimeByCycle(cycle) / 1000;
      landMortgageParams['end'] = moment().unix();
    }
    const landMortgageResult = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, landMortgageParams);
    if (landMortgageResult?.Result) {
      return Object.assign(dimensionDetails, pick(landMortgageResult, ['Result', 'Paging']));
    }
    return dimensionDetails;
  }

  /**
   * 注销备案
   * @param data
   * @param validField
   * @param sourcePath
   * @public
   */
  public async getCancellationOfFiling(data: HitEnterpriseDimensionQueryParam, validField: DimensionHitStrategyFieldsEntity, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const validData = getIsValidValue(validField?.fieldValue[0]);
    const filingResult = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
      isValid: validData,
      isB: true,
    });
    if (filingResult?.Result) {
      const Result = filingResult.Result.sort((a, b) => {
        return new Date(b.LiqBADate).getTime() - new Date(a.LiqBADate).getTime();
      });
      return Object.assign(dimensionDetails, {
        Result,
        Paging: {
          PageSize: data.pageSize,
          PageIndex: data.pageIndex,
          TotalRecords: filingResult?.Result.length,
        },
      });
    }
    return dimensionDetails;
  }

  /**
   * 欠税公告
   * @param data
   * @param dimension
   * @param validField
   * @param sourcePath
   * @public
   */
  public async getTaxArrearsNotice(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    validField: DimensionHitStrategyFieldsEntity,
    sourcePath: string,
  ) {
    //默认查欠税公告 当前有效， isValid = 1
    //0：历史 1：非历史 0,1：不限
    // let validDate = (validField?.fieldValue[0] ?? '1') + ',' + IsValidNumbers;
    // if (validField?.fieldValue[0] == -1) {
    //   validDate = '0,1' + ',' + IsValidNumbers;
    // }
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const validData = getIsValidValue(validField?.fieldValue[0]);
    //如果设置了欠税余额排查条件，欠税公告需要根据欠税金额进行过滤
    const queryPo: DimensionHitStrategyFieldsEntity = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.taxArrearsAmount);

    // this.logger.info('查欠税公告，不限');
    const taxReq = {
      searchKey: data.keyNo,
      pageSize: 500,
      pageIndex: 1,
      isValid: validData,
      isCombin: '1', //是否合并（默认0） 0：不合并 1：合并
      sortField: 'liandate',
      isSortAsc: false,
      isB: true,
    };
    const taxArrearsNoticeRes1 = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, taxReq);
    const taxArrearsNoticeRes = [];
    Array.prototype.push.apply(taxArrearsNoticeRes, taxArrearsNoticeRes1?.Result);
    if (queryPo) {
      const taxRes = taxArrearsNoticeRes?.filter((e) => getCompareResult(e.Amount, queryPo?.fieldValue[0], queryPo.compareType));
      const pageSize = data?.pageSize || 10;
      const pageIndex = data?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;
      const paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: taxRes?.length,
        TaxBalanceAmount: parseFloat(toRoundFixed(sum(taxRes?.map((e) => parseFloat(e.Amount))), 2)),
        TaxBalanceCurrentAmount: parseFloat(toRoundFixed(sum(taxRes?.map((e) => parseFloat(e.NewAmount))), 2)),
      };
      const result = taxRes?.slice(start, end);
      return Object.assign(dimensionDetails, {
        Paging: paging,
        PageIndex: pageIndex,
        Result: result,
      });
    }
    return dimensionDetails;
  }

  /**
   * 税务催缴公告
   * @param data
   * @param validField
   * @param sourcePath
   * @public
   */
  public async getTaxCallNotice(
    data: HitEnterpriseDimensionQueryParam,
    validField: DimensionHitStrategyFieldsEntity,
    sourcePath: string,
    dimension: DimensionHitStrategyPO,
  ) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    // let isValidVal = validField?.fieldValue[0];
    // if (isValidVal === '1') {
    //   isValidVal = [1];
    // } else {
    //   isValidVal = [0, 1];
    // }
    const validData = getIsValidValue(validField?.fieldValue[0]);
    const taxCallRes = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
      isValid: validData,
      pageIndex: 1,
      pageSize: 500,
      dimension: 2, //1:税务催报 2:税务催缴
      sortField: 'publishdate',
      isSortAsc: false,
    });
    if (taxCallRes?.Result) {
      let resultData = taxCallRes.Result;
      const cycle = dimension.getCycle();
      if (cycle && cycle !== -1) {
        const startPublishTime = getStartTimeByCycle(cycle) / 1000;
        resultData = taxCallRes.Result.filter((e) => e.PublishDate >= startPublishTime);
      }
      const pageSize = data?.pageSize || 10;
      const pageIndex = data?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;
      const paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: resultData.length,
        TotalAmount: parseFloat(toRoundFixed(sum(resultData.map((e) => parseFloat(e.AmountOwed))), 2)),
      };
      const result = resultData.slice(start, end);
      return Object.assign(dimensionDetails, {
        Paging: paging,
        Result: result,
      });
    }
    return dimensionDetails;
  }

  /**
   * 税务催报
   * @param data
   * @param validField
   * @param sourcePath
   * @param dimension
   * @public
   */
  public async getTaxReminder(
    data: HitEnterpriseDimensionQueryParam,
    validField: DimensionHitStrategyFieldsEntity,
    sourcePath: string,
    dimension: DimensionHitStrategyPO,
  ) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    // let isValidVal = validField?.fieldValue[0];
    // if (isValidVal === '1') {
    //   isValidVal = [1];
    // } else {
    //   isValidVal = [0, 1];
    // }
    const validData = getIsValidValue(validField?.fieldValue[0]);
    const taxReminderResult = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
      isValid: validData,
      pageIndex: 1,
      pageSize: 500,
      dimension: 1, //1:税务催报 2:税务催缴
      sortField: 'publishdate',
      isSortAsc: false,
    });
    if (taxReminderResult?.Result) {
      let resultData = taxReminderResult.Result;
      const cycle = dimension.getCycle();
      if (cycle && cycle !== -1) {
        const startPublishTime = getStartTimeByCycle(cycle) / 1000;
        resultData = taxReminderResult.Result.filter((e) => e.PublishDate >= startPublishTime);
      }
      const pageSize = data?.pageSize || 10;
      const pageIndex = data?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;
      const paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: resultData.length,
      };
      const result = resultData.slice(start, end);
      return Object.assign(dimensionDetails, {
        Paging: paging,
        Result: result,
      });
    }
    return dimensionDetails;
  }

  /**
   * 国际专利
   * @param data
   * @param dimension
   * @param sourcePath
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getInternationPatent(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: any) {
    const { keyNo } = data;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    // 排查对象
    const reqData = {};
    Object.assign(reqData, {
      sortField: data?.field || dimension?.getSortField()?.field || 'applicationdate',
      isSortAsc: (data?.order || dimension?.getSortField()?.order) === 'ASC',
      keyNo,
      pageSize: data?.pageSize || 100,
      pageIndex: data?.pageIndex || 1,
      searchKey: keyNo,
      searchType: 'ck',
    });
    // 国际专利状态
    const internationPatentStatusParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.internationPatentStatus);
    if (internationPatentStatusParams && internationPatentStatusParams?.fieldValue?.length) {
      Object.assign(reqData, { status: internationPatentStatusParams.fieldValue.join(',') });
    }
    const result = await this.getRequestSourcePath(sourcePath, reqData);
    if (result?.Result) {
      Object.assign(dimensionDetails, pick(result, ['Result', 'Paging', 'GroupItems']));
    }
    return dimensionDetails;
  }

  // 批量获取多个人的控制企业
  public async getBatchControllerCompany(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    sourcePath: any,
  ): Promise<HitDetailsBaseResponse> {
    const { keyNo: companyId } = data;
    const pageIndex = data?.pageIndex || 1;
    const pageSize = data?.pageSize || 10;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const keyNos: string[] = [];
    const relatedRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRoleType);
    if (relatedRoleField) {
      const personDatas: PersonData[] = [];

      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.ActualController)) {
        // 获取企业的实际控制人
        const acList = await this.personHelper.getFinalActualController(companyId, false);
        personDatas.push(...acList);
      }
      if (!personDatas?.length) {
        return dimensionDetails;
      }
      keyNos.push(...personDatas.map((item) => item.keyNo));
    } else {
      keyNos.push(companyId);
    }
    const allRes = (await Bluebird.map(keyNos, async (keyNo) => {
      const params = _.cloneDeep(data);
      Object.assign(params, { pageIndex: 1, pageSize: 500 });
      params.keyNo = keyNo;
      const res = await this.getControllerCompany(params, dimension, sourcePath);
      return res;
    })) as any;
    let allResults: any[] = [];
    if (allRes?.length) {
      allRes.forEach((res) => {
        if (res?.Result?.Names?.length) {
          allResults.push(...res?.Result.Names);
        }
      });
    }
    if (!allResults?.length) {
      return dimensionDetails;
    }
    //实缴资本异常
    const realRegistrationErrorParam = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.realRegistrationError);
    if (realRegistrationErrorParam && realRegistrationErrorParam?.fieldValue?.length) {
      const relateCompanyIds = allResults.map((item) => item.KeyNo);
      const allCompanyInfos =
        ((
          await this.companySearchService.companySearchForKys(
            Object.assign(new KysCompanySearchRequest(), {
              includeFields: ['id', 'name', 'registcapiamount', 'reccapamount'],
              pageIndex: 1,
              pageSize: relateCompanyIds.length,
              filter: { ids: relateCompanyIds },
            }),
          )
        )?.Result as any[]) || [];

      if (realRegistrationErrorParam?.fieldValue[0] === 1) {
        allResults = allResults.filter((t) => {
          const comp = allCompanyInfos.find((r) => r.id === t.KeyNo) as any;
          const reccapamount = parseInt(comp?.reccapamount) || 0;
          if (!reccapamount) {
            return t;
          }
        });
      } else if (realRegistrationErrorParam?.fieldValue[0] === 2) {
        //{ value: 2, label: '当前企业注册资本大于母公司(向上一层)的实缴资本' },
        const relateCompanyIds = allResults.map((item) => item.KeyNo);
        // 获取当前企业的母公司，投资比例，向上一层
        let montherCompanyInfos: KysCompanyResponseDetails[] = [];
        const relatedPartyGroups: RelatedPartyGroupPO[] = await this.nebulaGraphHelper.getCompanyRelatedParties(
          Object.assign(new BaseRelatedPartyRequest(), {
            companyIds: relateCompanyIds,
            filterInvestmentAgency: true,
            relatedTypes: [NebulaRelatedEdgeEnums.MotherCompanyMajorityShareholder],
          }),
          '/relatedParty/motherCompany',
        );
        if (relatedPartyGroups?.length) {
          const companyKeynoRelatedIds = relatedPartyGroups.map((t) => t.companyKeynoRelated);
          const res = await this.companySearchService.companySearchForKys(
            Object.assign(new KysCompanySearchRequest(), {
              includeFields: ['id', 'name', 'registcapiamount', 'reccapamount'],
              pageIndex: 1,
              pageSize: companyKeynoRelatedIds.length,
              filter: { ids: companyKeynoRelatedIds },
            }),
          );
          montherCompanyInfos = res.Result;
        }
        allResults = allResults.filter((t) => {
          // 当前企业的注册资本
          const targetCompany = allCompanyInfos.find((r) => r.id === t.KeyNo);
          const registcapiamount = parseInt(targetCompany?.registcapiamount) || 0;
          let reccapamount = 0;
          const relatedPartyGroup = relatedPartyGroups.find((r) => r.startCompanyKeyno === t.KeyNo);
          if (relatedPartyGroup) {
            const montherCompany = montherCompanyInfos.find((r) => r.id === relatedPartyGroup.companyKeynoRelated) as any;
            if (montherCompany) {
              // 母公司的实缴资本
              reccapamount = parseInt(montherCompany?.reccapamount) || 0;
            }
          }
          if (registcapiamount && reccapamount && registcapiamount > reccapamount) {
            return t;
          }
        });
      }
    }
    dimensionDetails.Paging = {
      PageSize: pageSize || 200,
      PageIndex: pageIndex || 1,
      TotalRecords: allResults.length,
    };
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;
    const sortedData = orderBy(allResults, 'StartDate', 'desc');
    dimensionDetails.Result = sortedData.slice(start, end);
    return dimensionDetails;
  }

  /**
   * 获取控制企业
   * @param data
   * @param dimension
   * @param sourcePath
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getControllerCompany(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    sourcePath: any,
  ): Promise<HitDetailsBaseResponse> {
    const { keyNo } = data;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    // 排查对象
    const reqData = {};
    Object.assign(reqData, {
      sortField: data?.field || dimension?.getSortField()?.field || 'startdate',
      isSortAsc: (data?.order || dimension?.getSortField()?.order) === 'ASC',
      keyNo,
      pageSize: data?.pageSize || 10,
      pageIndex: data?.pageIndex || 1,
    });

    // 企业的注册时间
    const registerDateParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.registerDate);
    if (registerDateParams && registerDateParams?.fieldValue?.length) {
      //timeValue 是数字，比如设置为 1，根据 option中的 unit 单位是天，且 compareType 是 LessThanOrEqual，则表示成立日期小于当前日期1天
      const timeValue = registerDateParams.fieldValue[0];
      const unit = registerDateParams.options[0].unit;
      const compareType = registerDateParams.compareType;
      //根据 compareType 以及当前日期计算出目标日期时间戳 秒
      const targetDate = Math.floor(getTargetDate(timeValue, unit) / 1000);
      if (targetDate) {
        Object.assign(reqData, { startDate: targetDate, endDate: moment().unix() });
      }
    }
    // 企业的总持股比例
    const percentTotalParam = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.percentTotal);
    if (percentTotalParam && percentTotalParam?.fieldValue?.length) {
      const operator = percentTotalParam ? EsOperator[percentTotalParam.compareType] : 'gte';
      switch (operator) {
        case 'gt':
        case 'gte': {
          Object.assign(reqData, {
            stockPercent: `${percentTotalParam.fieldValue[0]},100.00001`,
          });
          break;
        }
        case 'lt':
        case 'lte': {
          Object.assign(reqData, {
            stockPercent: `0,${percentTotalParam.fieldValue[0]}`,
          });
          break;
        }
      }
    }
    const result = await this.getRequestSourcePath(sourcePath, reqData);
    if (result?.Result) {
      Object.assign(dimensionDetails, pick(result, ['Result', 'Paging', 'GroupItems']));
    }
    return dimensionDetails;
  }

  // 批量获取多个人的对外投资信息
  public async getBatchOutwardInvestment(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    sourcePath: any,
  ): Promise<HitDetailsBaseResponse> {
    const { keyNo: companyId } = data;
    const pageIndex = data?.pageIndex || 1;
    const pageSize = data?.pageSize || 10;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const keyNos: string[] = [];
    const relatedRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRoleType);
    if (relatedRoleField) {
      const personDatas: PersonData[] = [];
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.ActualController)) {
        // 获取企业的实际控制人
        const acList = await this.personHelper.getFinalActualController(companyId, false);
        personDatas.push(...acList);
      }
      if (!personDatas?.length) {
        return dimensionDetails;
      }
      keyNos.push(...personDatas.map((item) => item.keyNo));
    } else {
      keyNos.push(companyId);
    }
    const allRes = (await Bluebird.map(keyNos, async (keyNo) => {
      const params = _.cloneDeep(data);
      Object.assign(params, { pageIndex: 1, pageSize: 500 });
      params.keyNo = keyNo;
      const res = await this.getOutwardInvestment(params, dimension, sourcePath);
      return res;
    })) as any;
    let allResults: any[] = [];
    if (allRes?.length) {
      allRes.forEach((res) => {
        if (res?.Result?.length) {
          allResults.push(...res?.Result);
        }
      });
    }
    if (!allResults?.length) {
      return dimensionDetails;
    }

    // 经营范围
    const companySocpeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companySocpe);
    if (companySocpeField && allResults?.length) {
      const allKeyno = allResults.map((t) => t.KeyNo);
      const companyDetails: ESResponse<KysCompanyResponseDetails> = await this.companySearchService.companySearchForKys(
        Object.assign(new KysCompanySearchRequest(), {
          pageIndex: 1,
          pageSize: allKeyno.length,
          includeFields: ['id', 'name', 'scope'],
          filter: { ids: allKeyno },
        }),
      );
      allResults = allResults.filter((t) => {
        const company = companyDetails.Result.find((r) => r.id === t.KeyNo) as any;
        if (company?.scope) {
          return companySocpeField.fieldValue.some((keyword) => company?.scope?.includes(keyword));
        }
      });
    }

    // 企业名称
    const companyNameField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyName);
    if (companyNameField && allResults?.length) {
      allResults = allResults.filter((t) => {
        return companyNameField.fieldValue.some((keyword) => t.Name.includes(keyword));
      });
    }
    // 排除企业名称
    const excludeCompanyNameField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.excludeCompanyName);
    if (excludeCompanyNameField && allResults?.length) {
      allResults = allResults.filter((t) => {
        return excludeCompanyNameField.fieldValue.some((keyword) => !t.Name.includes(keyword));
      });
    }
    // 企查查行业
    const qccIndustryField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.qccIndustry);
    if (qccIndustryField && allResults?.length) {
      allResults = allResults.filter((t) => {
        const QccIndustry = t?.QccIndustry;
        if (QccIndustry) {
          const targetCompanyIndustry = qccIndustryField?.fieldValue[0];
          const levels = targetCompanyIndustry.split('-').length;
          const industryLevels = ['Ac', 'Bc', 'Cc'] as const;
          const sourceIndustryCode = industryLevels
            .map((level) => QccIndustry[level])
            .filter((value) => value !== undefined && value !== null && value !== '')
            .join('-');
          // sourceIndustryCode 只取levels 的长度
          const sourceIndustryCodeString = sourceIndustryCode.split('-').slice(0, levels).join('-');
          if (sourceIndustryCodeString && targetCompanyIndustry) {
            if (getCompareResultForArray(qccIndustryField.compareType, [sourceIndustryCodeString], [targetCompanyIndustry])) {
              return t;
            }
          }
        }
      });
    }

    // 国标行业
    const companyIndustryField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyIndustry);
    if (companyIndustryField && allResults?.length) {
      allResults = allResults.filter((t) => {
        const IndustryV3 = t?.IndustryItem;
        if (IndustryV3) {
          const targetCompanyIndustry = companyIndustryField?.fieldValue[0];
          const levels = targetCompanyIndustry.split('-').length;
          const industryLevels = ['IndustryCode', 'SubIndustryCode', 'MiddleCategoryCode', 'SmallCategoryCode'] as const;
          const sourceIndustryCode = industryLevels
            .map((level) => IndustryV3[level])
            .filter((value) => value !== undefined && value !== null && value !== '')
            .join('-');
          // sourceIndustryCode 只取levels 的长度
          const sourceIndustryCodeString = sourceIndustryCode.split('-').slice(0, levels).join('-');
          if (sourceIndustryCodeString && targetCompanyIndustry) {
            if (getCompareResultForArray(companyIndustryField.compareType, [sourceIndustryCodeString], [targetCompanyIndustry])) {
              return t;
            }
          }
        }
      });
    }

    dimensionDetails.Paging = {
      PageSize: pageSize || 10,
      PageIndex: pageIndex || 1,
      TotalRecords: allResults.length,
    };
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;
    const sortedData = orderBy(allResults, 'InvestInDate', 'desc');
    dimensionDetails.Result = sortedData.slice(start, end);
    return dimensionDetails;
  }

  /**
   * 对外投资
   * @param data
   * @param dimension
   * @param sourcePath
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getOutwardInvestment(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    sourcePath: any,
  ): Promise<HitDetailsBaseResponse> {
    const { keyNo } = data;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const pageIndex = data?.pageIndex || 1;
    const pageSize = data?.pageSize || 10;
    // 排查对象
    const reqData = {};
    Object.assign(reqData, {
      sortField: data?.field || dimension?.getSortField()?.field || 'startdate',
      isSortAsc: (data?.order || dimension?.getSortField()?.order) === 'ASC',
      keyNo,
      pageSize,
      pageIndex,
    });

    // 成立日期
    const registerDateParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.registerDate);
    if (registerDateParams && registerDateParams?.fieldValue?.length) {
      //timeValue 是数字，比如设置为 1，根据 option中的 unit 单位是天，且 compareType 是 LessThanOrEqual，则表示成立日期小于当前日期1天
      const timeValue = registerDateParams.fieldValue[0];
      const unit = registerDateParams.options[0].unit;
      const compareType = registerDateParams.compareType;
      //根据 compareType 以及当前日期计算出目标日期时间戳 秒
      const targetDate = Math.floor(getTargetDate(timeValue, unit) / 1000);
      if (targetDate) {
        Object.assign(reqData, { startDate: targetDate, endDate: moment().unix() });
      }
    }

    // 直接持股比例
    const directShareholdPercentageParam = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.directShareholdPercentage);
    if (directShareholdPercentageParam && directShareholdPercentageParam?.fieldValue?.length) {
      const operator = directShareholdPercentageParam ? EsOperator[directShareholdPercentageParam.compareType] : 'gte';
      switch (operator) {
        case 'gt':
        case 'gte': {
          Object.assign(reqData, {
            stockPercent: `${directShareholdPercentageParam.fieldValue[0]},100.00001`,
          });
          break;
        }
        case 'lt':
        case 'lte': {
          Object.assign(reqData, {
            stockPercent: `0,${directShareholdPercentageParam.fieldValue[0]}`,
          });
          break;
        }
      }
    }
    // 企业状态
    const companyStatusParam = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyStatus);
    if (companyStatusParam && companyStatusParam?.fieldValue?.length) {
      Object.assign(reqData, { status: companyStatusParam.fieldValue.join(',') });
    }

    const result = await this.getRequestSourcePath(sourcePath, reqData);
    if (result?.Result) {
      Object.assign(dimensionDetails, pick(result, ['Result', 'Paging', 'GroupItems']));
    }
    return dimensionDetails;
  }

  /**
   * 上市进程
   * @param data
   * @param dimension
   * @param sourcePath
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getIPOProcess(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: any) {
    const { keyNo } = data;
    const pageIndex = data?.pageIndex || 1;
    const pageSize = data?.pageSize || 50;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    // 排查对象
    const reqData = {};
    Object.assign(reqData, {
      keyNo,
    });
    const Result = await this.getRequestSourcePath(sourcePath, reqData);
    if (!Result || !Result?.Result?.RegisterProcedureList?.length) {
      return dimensionDetails;
    }
    // --------------------------以下是返回结果的过滤判断---------------------------------------
    let isHit = true;
    let hitList = [];
    hitList.push(...Result?.Result?.RegisterProcedureList);
    // 上市进程
    const applicationProgressField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.applicationProgress);
    if (applicationProgressField && isHit && hitList?.length) {
      isHit = false;
      const applicationProgressTargetValues = applicationProgressField.fieldValue.map((t) => ApplicationProgressConstant[t]);
      const applicationProgressSourceValues = hitList.map((t) => t?.Status);
      if (
        applicationProgressTargetValues?.length &&
        applicationProgressSourceValues?.length &&
        getCompareResultForArray(applicationProgressField.compareType, applicationProgressSourceValues, applicationProgressTargetValues)
      ) {
        isHit = true;
      }
      if (isHit) {
      } else {
        hitList = [];
      }
    }

    // 是否终止/中止(上市进程)
    const hasTerminateField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.hasTerminate);
    if (hasTerminateField && isHit && hitList?.length) {
      isHit = false;
      let sourceValue = null;
      const targetValue = applicationProgressField.fieldValue[0] as number;
      const AllStatus = hitList.map((t) => t?.Status);
      if (AllStatus.includes('中止') || AllStatus.includes('终止')) {
        sourceValue = 1; // 终止
      } else {
        sourceValue = 2; // 未终止
      }
      if (sourceValue && targetValue && getCompareResult(sourceValue, targetValue, hasTerminateField.compareType)) {
        isHit = true;
      }
      if (isHit) {
      } else {
        hitList = [];
      }
    }
    const DetailResult = [];
    if (isHit) {
      DetailResult.push({
        keyNo,
        RegisterProcedureList: hitList,
      });
    }
    dimensionDetails.Paging = {
      PageSize: pageSize || 10,
      PageIndex: pageIndex || 1,
      TotalRecords: DetailResult.length,
    };
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;
    const sortedData = orderBy(DetailResult, 'keyNo', 'desc');
    dimensionDetails.Result = sortedData.slice(start, end);
    return dimensionDetails;
  }

  /**
   * 实际控制人信息
   * @param data
   * @param dimension
   * @param sourcePath
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getActuralControllerInformation(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: any) {
    const { keyNo } = data;
    const pageIndex = data?.pageIndex || 1;
    const pageSize = data?.pageSize || 50;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    // 排查对象
    const reqData = {};
    Object.assign(reqData, {
      keyNo,
      isCollapsed: true,
      isOnlyMainPath: true,
    });
    const Result = await this.postRequestSourcePath(sourcePath, reqData);
    if (Result?.Result && Result?.Result?.Names?.length && Result?.Result?.Names[0]?.Names?.PersonList?.length) {
      dimensionDetails.Result.push(...Result?.Result?.Names[0].Names.PersonList);
    }
    if (!dimensionDetails?.Result?.length) {
      return dimensionDetails;
    }

    // --------------------------以下是返回结果的过滤判断---------------------------------------
    let isHit = true;
    let hitList = [];
    hitList.push(...dimensionDetails.Result);
    // 总持股比例
    hitList = hitList.map((t) => {
      const percentTotalNumber = parseFloat(t?.PercentTotal?.replace('%', '')) || 0;
      return Object.assign(t, { percentTotalNumber });
    });
    const percentTotalField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.percentTotal);
    if (percentTotalField && isHit && hitList?.length) {
      isHit = false;
      let filterResult = [];
      const percentTotalValue = percentTotalField.fieldValue?.[0] as number;
      const operator = percentTotalField ? EsOperator[percentTotalField.compareType] : 'gte';
      if (percentTotalValue && percentTotalValue !== -1) {
        switch (operator) {
          case 'gt': {
            // personList 中过滤 percentTotalNumber > percentageValue
            filterResult =
              hitList.filter((t) => {
                return t.percentTotalNumber > percentTotalValue;
              }) || [];
            break;
          }
          case 'gte': {
            filterResult =
              hitList.filter((t) => {
                return t.percentTotalNumber >= percentTotalValue;
              }) || [];
            break;
          }
          case 'lt': {
            filterResult =
              hitList.filter((t) => {
                return t.percentTotalNumber < percentTotalValue;
              }) || [];
            break;
          }
          case 'lte': {
            filterResult =
              hitList.filter((t) => {
                return t.percentTotalNumber <= percentTotalValue;
              }) || [];
            break;
          }
        }
      }
      if (filterResult?.length) {
        isHit = true;
      }
      if (isHit) {
        hitList = filterResult;
      } else {
        hitList = [];
      }
    }

    // 判断实际控制人控制年限, 有实际控制人变更取最新的实际控制人变更时间，没有实际控制人变更的获取企业的注册时间判断
    // 过滤股东角色
    const controllerTimeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.controllerTime);
    if (controllerTimeField && isHit && hitList?.length) {
      isHit = false;
      let filterResult = [];
      // 获取主体企业的实际控制人变更次数的数据25
      const company = await this.companySearchService.companyDetailsQcc(data.keyNo);
      const riskChangeResult = await this.riskChangeHelper.commonCivilRiskChange([data.keyNo], [RiskChangeCategoryEnum.category25], null, null, 100);
      if (riskChangeResult?.Result?.length) {
        riskChangeResult.Result = riskChangeResult.Result.map((item) => {
          const BeforeContent = item.BeforeContent ? JSON.parse(item.BeforeContent) : {};
          const AfterContent = item.AfterContent ? JSON.parse(item.AfterContent) : {};
          item.BeforeContent = BeforeContent;
          item.AfterContent = AfterContent;
          return item;
        });
        filterResult =
          hitList.filter((t) => {
            let monthsDiff = null;
            const riskInfo = riskChangeResult?.Result.find((item) => item.AfterContent?.keyNo === t?.keyNo);
            if (riskInfo) {
              const changeDate = new Date(riskInfo.ChangeDate * 1000);
              const now = new Date();
              monthsDiff = (now.getFullYear() - changeDate.getFullYear()) * 12 + (now.getMonth() - changeDate.getMonth());
            } else {
              const startDate = new Date(company.StartDate * 1000);
              const now = new Date();
              monthsDiff = (now.getFullYear() - startDate.getFullYear()) * 12 + (now.getMonth() - startDate.getMonth());
            }
            if (monthsDiff && getCompareResult(monthsDiff, controllerTimeField.fieldValue[0], controllerTimeField.compareType)) {
              t.monthsDiff = monthsDiff;
              return true;
            }
          }) || [];
      } else {
        filterResult =
          hitList.filter((t) => {
            const startDate = new Date(company.StartDate * 1000);
            const now = new Date();
            const monthsDiff = (now.getFullYear() - startDate.getFullYear()) * 12 + (now.getMonth() - startDate.getMonth());
            if (monthsDiff && getCompareResult(monthsDiff, controllerTimeField.fieldValue[0], controllerTimeField.compareType)) {
              t.monthsDiff = monthsDiff;
              return true;
            }
          }) || [];
      }
      if (filterResult?.length) {
        isHit = true;
      }
      if (isHit) {
        hitList = filterResult;
      } else {
        hitList = [];
      }
    }

    // 判断实际控制人的企业性质
    const isStateOwnedField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isStateOwned);
    if (isStateOwnedField && isHit && hitList?.length) {
      isHit = false;
      const filterResult = [];
      const isStateOwnedFieldValue = isStateOwnedField.fieldValue[0];
      const filterList = hitList.filter((t) => t.Name.includes('国有资产监督管理委员会') || t.Name.includes('国有资产监督管理局'));
      if (isStateOwnedFieldValue === 1 && filterList.length) {
        isHit = true;
        filterResult.push(...filterList);
      } else if (isStateOwnedFieldValue === 2 && !filterList?.length) {
        isHit = true;
        filterResult.push(...hitList);
      }
      if (filterResult?.length) {
        isHit = true;
      }
      if (isHit) {
        hitList = filterResult;
      } else {
        hitList = [];
      }
    }
    dimensionDetails.Paging = {
      PageSize: pageSize || 50,
      PageIndex: pageIndex || 1,
      TotalRecords: hitList.length,
    };
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;
    const sortedData = orderBy(hitList, 'KeyNo', 'desc');
    dimensionDetails.Result = sortedData.slice(start, end);
    return dimensionDetails;
  }

  /**
   * 股东信息
   * @param data
   * @param dimension
   * @param sourcePath
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getShareholderInformation(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: any) {
    const { keyNo } = data;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const pageIndex = data?.pageIndex || 1;
    const pageSize = data?.pageSize || 50;
    // 排查对象
    const reqData = {};
    Object.assign(reqData, {
      sortField: data?.field || dimension?.getSortField()?.field || 'stockpercent',
      isSortAsc: (data?.order || dimension?.getSortField()?.order) === 'ASC',
      keyNo,
      pageSize: data?.pageSize || 50,
      pageIndex: data?.pageIndex || 1,
      type: 'IpoPartners',
    });
    // 直接持股比例
    const directShareholdPercentageField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.directShareholdPercentage);
    if (directShareholdPercentageField) {
      const percentageValue = directShareholdPercentageField.fieldValue?.[0] as number;
      const operator = directShareholdPercentageField ? EsOperator[directShareholdPercentageField.compareType] : 'gte';
      if (percentageValue && percentageValue !== -1) {
        switch (operator) {
          case 'gt':
          case 'gte': {
            Object.assign(reqData, { fundedRatioStart: percentageValue });
            break;
          }
          case 'lt':
          case 'lte': {
            Object.assign(reqData, { fundedRatioEnd: percentageValue });
            break;
          }
        }
      }
    }
    const ipoPartnersResult = await this.getRequestSourcePath(sourcePath, reqData);
    if (ipoPartnersResult?.Result?.length) {
      Object.assign(dimensionDetails, pick(ipoPartnersResult, ['Result', 'Paging', 'GroupItems']));
    } else {
      // IPO股东没有的话取工商的股东信息
      Object.assign(reqData, {
        type: 'Partners',
      });
      const partnersResult = await this.getRequestSourcePath(sourcePath, reqData);
      if (partnersResult?.Result?.length) {
        Object.assign(dimensionDetails, pick(ipoPartnersResult, ['Result', 'Paging', 'GroupItems']));
      }
    }
    if (!dimensionDetails?.Result?.length) {
      return dimensionDetails;
    }

    // --------------------------以下是返回结果的过滤判断---------------------------------------
    let isHit = true;
    let hitList = [];
    // 过滤股东角色
    hitList.push(...dimensionDetails.Result);
    const shareholdRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareholdRole);
    if (shareholdRoleField && isHit && hitList?.length) {
      isHit = false;
      let filterResult = [];
      const shareHoldRole = shareholdRoleField.fieldValue[0];
      // ShareholdRoleConstant 中的 shareHoldRole 等于 majorShareholder的label
      const shareHoldRoleLabel = ShareholdRoleConstant.find((item) => item.value === shareHoldRole)?.label;
      filterResult = hitList.filter((item) => (item?.Tags || []).includes(shareHoldRoleLabel));
      if (filterResult?.length) {
        isHit = true;
      }
      if (isHit) {
        hitList = filterResult;
      } else {
        hitList = [];
      }
    }
    dimensionDetails.Paging = {
      PageSize: pageSize || 10,
      PageIndex: pageIndex || 1,
      TotalRecords: hitList.length,
    };
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;
    const sortedData = orderBy(hitList, 'StockPercentValue', 'desc');
    dimensionDetails.Result = sortedData.slice(start, end);
    return dimensionDetails;
  }

  /**
   * 专利信息v1
   * @param data
   * @param dimension
   * @param sourcePath
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getPatentInfo(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: any, applicationYear?: number) {
    const { keyNo } = data;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    // 排查对象
    const reqData = {};
    let defaultTimeFilterField = 'applicationdate';
    Object.assign(reqData, {
      sortField: data?.field || dimension?.getSortField()?.field || 'applicationdate',
      isSortAsc: (data?.order || dimension?.getSortField()?.order) === 'ASC',
      keyNo,
      pageSize: data?.pageSize || 10,
      pageIndex: data?.pageIndex || 1,
      aggFields: [
        'kindfourcode',
        //'legalstatus',
        //'assigneelist',
        //'publicationyear',
        'applicationyear',
        //'agency',
        //'status',
        //'pubdatekind',
        //'appdatekind',
        //'authorizeyear',
      ],
    });
    // 时间过滤
    // 是否是历史专利
    const isHistoryParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isHistoryPatent);
    if (isHistoryParams && Number(isHistoryParams.fieldValue[0]) === 1) {
      Object.assign(reqData, { isHistory: true });
      defaultTimeFilterField = 'transferouttime';
    }
    // 专利类型过滤
    const patentTypeParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.patentType);
    if (patentTypeParams && patentTypeParams?.fieldValue?.length) {
      Object.assign(reqData, { kindCode: patentTypeParams.fieldValue.join(',') });
    }
    // 专利法律状态
    const patentStatusParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.patentStatus);
    if (patentStatusParams && patentStatusParams?.fieldValue?.length) {
      Object.assign(reqData, { status: patentStatusParams.fieldValue });
    }
    const cycleField = dimension.getCycleField();
    if (cycleField) {
      const cycle = cycleField.fieldValue?.[0] as number;
      const operator = cycleField ? EsOperator[cycleField.compareType] : 'gte';
      if (cycle && cycle !== -1) {
        switch (operator) {
          case 'gt':
          case 'gte': {
            Object.assign(reqData, {
              rangeFilters: [
                {
                  type: defaultTimeFilterField,
                  rangeList: [{ start: moment().subtract(cycle, 'year').unix() }],
                },
              ],
            });
            break;
          }
          case 'lt':
          case 'lte': {
            Object.assign(reqData, {
              rangeFilters: [
                {
                  type: defaultTimeFilterField,
                  rangeList: [{ end: moment().subtract(cycle, 'year').unix() }],
                },
              ],
            });
            break;
          }
        }
      }
    }

    const naturalCycleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.naturalCycle);
    if (naturalCycleField) {
      const naturalCycle = naturalCycleField.fieldValue?.[0] as number;
      const naturalOperator = naturalCycle ? EsOperator[naturalCycleField.compareType] : 'gte';
      if (naturalCycle && naturalCycle !== -1) {
        switch (naturalOperator) {
          case 'gt':
          case 'gte': {
            Object.assign(reqData, {
              rangeFilters: [
                {
                  type: defaultTimeFilterField,
                  rangeList: [
                    {
                      start: moment()
                        .subtract(naturalCycle - 1, 'year')
                        .startOf('year')
                        .unix(),
                    },
                  ],
                },
              ],
            });
            break;
          }
          case 'lt':
          case 'lte': {
            Object.assign(reqData, {
              rangeFilters: [
                {
                  type: defaultTimeFilterField,
                  rangeList: [
                    {
                      end: moment()
                        .subtract(naturalCycle - 1, 'year')
                        .startOf('year')
                        .unix(),
                    },
                  ],
                },
              ],
            });
            break;
          }
        }
      }
    }

    // 监控单位时间内的数据，覆盖cycle的时间
    const dimensionFilter = dimension?.dimensionFilter;
    if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
      Object.assign(reqData, {
        rangeFilters: [
          {
            type: defaultTimeFilterField,
            rangeList: [{ start: Math.ceil(dimensionFilter?.startTime), end: Math.ceil(dimensionFilter?.endTime) }],
          },
        ],
      });
    }

    if (applicationYear) {
      Object.assign(reqData, {
        applicationYear: [applicationYear.toString()],
      });
    }
    const result = await this.postRequestSourcePath(sourcePath, reqData);
    if (result?.Result) {
      return Object.assign(dimensionDetails, pick(result, ['Result', 'Paging', 'GroupItems']));
    }
    return dimensionDetails;
  }

  /**
   * 股权融资
   * @param data
   * @param dimension
   * @param sourcePath
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getEquityFinancing(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: any) {
    const { keyNo } = data;
    const pageIndex = data?.pageIndex || 1;
    const pageSize = data?.pageSize || 10;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    // 排查对象
    const reqData = {};
    Object.assign(reqData, {
      sortField: data?.field || dimension?.getSortField()?.field || 'financedate',
      isSortAsc: (data?.order || dimension?.getSortField()?.order) === 'ASC',
      keyNo,
      pageSize,
      pageIndex,
      type: 1,
      compPageUse: 1,
    });
    // 时间过滤
    const cycleField = dimension.getCycleField();
    if (cycleField) {
      const cycle = cycleField.fieldValue?.[0] as number;
      const operator = cycleField ? EsOperator[cycleField.compareType] : 'gte';
      if (cycle && cycle !== -1) {
        switch (operator) {
          case 'gt':
          case 'gte': {
            Object.assign(reqData, {
              rangeFilters: [
                {
                  type: 'financedate',
                  rangeList: [{ start: moment().subtract(cycle, 'year').unix() }],
                },
              ],
            });
            break;
          }
          case 'lt':
          case 'lte': {
            Object.assign(reqData, {
              rangeFilters: [
                {
                  type: 'financedate',
                  rangeList: [{ end: moment().subtract(cycle, 'year').unix() }],
                },
              ],
            });
            break;
          }
        }
      }
    }

    // 周期时间过滤
    const naturalCycleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.naturalCycle);
    if (naturalCycleField) {
      const naturalCycle = naturalCycleField.fieldValue?.[0] as number;
      const naturalOperator = naturalCycle ? EsOperator[naturalCycleField.compareType] : 'gte';
      if (naturalCycle && naturalCycle !== -1) {
        switch (naturalOperator) {
          case 'gt':
          case 'gte': {
            Object.assign(reqData, {
              rangeFilters: [
                {
                  type: 'financedate',
                  rangeList: [
                    {
                      start: moment()
                        .subtract(naturalCycle - 1, 'year')
                        .startOf('year')
                        .unix(),
                    },
                  ],
                },
              ],
            });
            break;
          }
          case 'lt':
          case 'lte': {
            Object.assign(reqData, {
              rangeFilters: [
                {
                  type: 'financedate',
                  rangeList: [
                    {
                      end: moment()
                        .subtract(naturalCycle - 1, 'year')
                        .startOf('year')
                        .unix(),
                    },
                  ],
                },
              ],
            });
            break;
          }
        }
      }
    }

    const result = await this.postRequestSourcePath(sourcePath, reqData);
    if (result?.Result) {
      return Object.assign(dimensionDetails, pick(result, ['Result', 'Paging', 'GroupItems']));
    }

    // --------------------------以下是返回结果的过滤判断---------------------------------------
    // 是否是投资机构
    let isHit = true;
    let hitList = [];
    hitList.push(...dimensionDetails.Result);
    const isLimitedPartnershipDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isInstitutionalInvestor);
    if (isLimitedPartnershipDimensionField && isHit && hitList?.length) {
      isHit = false;
      const limitedPartnershipList = [];
      const participantDetails = hitList?.map((t) => t.ParticipantDetails);
      // ParticipantType === 1 为投资方
      const participantDetailsList = flatMap(participantDetails)?.filter((t) => t.ParticipantType === 1) || [];
      // category = 2 的时候需要过滤有限合伙
      const category2 = participantDetailsList?.filter((t) => t.Category === 2 && t?.Name?.includes('（有限合伙）'));
      if (category2?.length) {
        limitedPartnershipList.push(...category2);
      }
      // category = 1 的时候不需要过滤有限合伙
      const category1 = participantDetailsList.filter((t) => t.Category === 1);
      if (category1?.length) {
        category1.forEach((t) => {
          const RelationInfo = [{ Name: t.Name, KeyNo: t.KeyNo }];
          t.RelationInfo = RelationInfo;
        });
        limitedPartnershipList.push(...category1);
      }
      // --------------以上位数据处理------------------
      if (isLimitedPartnershipDimensionField.fieldValue[0] === 1) {
        // 有投资机构
        const hasInvestorList = limitedPartnershipList?.filter((t) => t?.RelationInfo?.length);
        if (hasInvestorList?.length) {
          isHit = true;
          hitList = hasInvestorList;
        }
      } else if (isLimitedPartnershipDimensionField.fieldValue[0] === 2) {
        // 无投资机构
        const noInvestorList = limitedPartnershipList?.filter((t) => !t?.RelationInfo?.length);
        if (noInvestorList?.length) {
          isHit = true;
          hitList = noInvestorList;
        }
      }
      if (isHit) {
      } else {
        hitList = [];
      }
    }
    // 投资机构的上榜榜单来源
    const sourcesInvestInstiteRankField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.sourcesInvestInstiteRank);
    if (sourcesInvestInstiteRankField && isHit && hitList) {
      isHit = false;
      const relationInfos = hitList?.map((t) => t?.RelationInfo);
      const relationKeyNos = _.uniq(flatMap(relationInfos)?.map((t) => t.KeyNo) || []);
      if (relationKeyNos.length) {
        const investList = await this.getInvestorSingleAppBdV3(relationKeyNos, sourcesInvestInstiteRankField.fieldValue);
        if (investList?.length) {
          isHit = true;
        }
      }
      if (isHit) {
      } else {
        hitList = [];
      }
    }
    // 产业链
    const industrialChainCoreCompanyContantField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isIndustrialChainCoreCompany);
    if (industrialChainCoreCompanyContantField && isHit && hitList?.length) {
      isHit = false;
      // 获取主体企业的产业链code
      const companyGraphResult = await this.getCompanyGraph(data.keyNo);
      if (companyGraphResult?.Result?.IndustryChains?.length) {
        // 获取产业链上的核心企业
        const investorKeyNos = hitList?.map((t) => t.KeyNo);
        const hitKeyNos = await this.getIndustrialChainCoreCompany(companyGraphResult?.Result?.IndustryChains, '（有限合伙）');
        if (investorKeyNos?.length && hitKeyNos?.length && _.intersection(investorKeyNos, hitKeyNos).length) {
          isHit = true;
        }
      }
      if (isHit) {
      } else {
        hitList = [];
      }
    }
    dimensionDetails.Paging = {
      PageSize: pageSize || 10,
      PageIndex: pageIndex || 1,
      TotalRecords: hitList.length,
    };
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;
    const sortedData = orderBy(hitList, 'FinanceDate', 'desc');
    dimensionDetails.Result = sortedData.slice(start, end);
    return dimensionDetails;
  }

  /**
   * 获取产业链上的核型企业
   * @param IndustryChains
   */
  @Cacheable({ ttlSeconds: 300 })
  async getIndustrialChainCoreCompany(IndustryChains: any[], searchKey?: string) {
    const hitKeyNos: string[] = [];
    await Bluebird.map(IndustryChains, async (industryChain) => {
      const headCode = industryChain?.IndustryChainCode;
      const nodeCodeList = industryChain?.Nodes?.map((t) => t.NodeCode);
      const reqData = {};
      Object.assign(reqData, {
        industryChainCode: headCode,
        relatedNodeCode: nodeCodeList,
        pageSize: 2000, // 暂时设置2000
        pageIndex: 1,
      });
      if (searchKey) {
        Object.assign(reqData, {
          searchKey,
        });
      }
      const industryChainRelatedCompaniesResult = await this.httpUtils.postRequest(
        this.configService.proxyServer.dataService + '/api/IndustryChainV2/GetIndustryChainRelatedCompanies',
        reqData,
      );
      if (industryChainRelatedCompaniesResult?.Result?.length) {
        const chainRelatedCompanyIds = industryChainRelatedCompaniesResult?.Result?.map((t) => t.KeyNo) || [];
        if (chainRelatedCompanyIds?.length) {
          hitKeyNos.push(...chainRelatedCompanyIds);
        }
      }
    });
    return hitKeyNos;
  }

  /**
   * 获取主体企业的产业链
   * @param companyId
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCompanyGraph(companyId: string) {
    const reqData = {};
    Object.assign(reqData, {
      keyNo: companyId,
      isHighRelationship: true,
    });
    const companyGraphResult = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/IndustryChainV2/GetCompanyGraph', reqData);
    return companyGraphResult;
  }

  /**
   * 查询机构上榜榜单信息
   * @param relationKeyNos
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getInvestorSingleAppBdV3(relationKeyNos: any[], sourcesInvestInstiteRank: number[]) {
    const hitKeyNos: string[] = [];
    const rankNames = sourcesInvestInstiteRank?.map((item) => {
      return SourcesInvestInstiteRankConstant?.find((source) => source.value === item)?.label;
    });
    await Bluebird.map(relationKeyNos, async (relationKeyNo) => {
      const reqData = {};
      Object.assign(reqData, {
        publisher: rankNames,
        type: '3',
        keyNo: relationKeyNo,
        pageSize: 1000,
        pageIndex: 1,
      });
      const SingleAppBdV3Result = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/QccSearch/SingleApp/BdV3', reqData);
      if (SingleAppBdV3Result?.Result?.length) {
        hitKeyNos.push(relationKeyNo);
      }
    });
    return hitKeyNos;
  }

  // 获取公司相关人员名称列表
  private async getCompanyPersonNames(companyId: string): Promise<string[]> {
    const personList = await Bluebird.all([
      this.personHelper.getLegalPerson(companyId), // 法定代表人
      this.personHelper.getEmployeeList(companyId), // 获取(当前)主要人员（最新公示+工商登记） 董监高
      this.personHelper.getHisEmployeeData(companyId), // 获取(历史)主要人员（最新公示+工商登记） 董监高
      this.personHelper.getPartnerList(companyId, 'person', true, 200), // 获取股东信息 (自然人)  （最新公示+工商登记）
    ]);
    return _.uniq(flatMap(personList)?.map((t) => t?.name) || []);
  }

  /**
   * 对外投资分析
   * @param data
   * @param dimension
   * @param sourcePath
   */
  public async getOutwardInvestmentAnalysis(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: any) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const pageIndex = data?.pageIndex || 1;
    const pageSize = data?.pageSize || 10;
    let Result: any[] = [];
    const res = await this.getOutwardInvestment(data, dimension, sourcePath);
    if (res.Paging.TotalRecords === 0) {
      return dimensionDetails;
    }
    const outwardInvestmentStatistics: any[] = [];
    const outwardInvestmentStatisticsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.outwardInvestmentStatistics);
    if (outwardInvestmentStatisticsParams) {
      // 注销占比
      if (outwardInvestmentStatisticsParams?.fieldValue[0] === 1) {
        const res = await this.getOutwardInvestment(data, dimension, sourcePath);
        if (res?.GroupItems?.length) {
          const groupItems = res?.GroupItems?.filter((t) => t.key === 'startdateyear' || t.key === 'statuscode') || [];
          let allCount = 0;
          let zhuxiaoCount = 0;
          if (groupItems?.length) {
            allCount = groupItems?.find((t) => t.key === 'startdateyear').items?.reduce((sum, item) => sum + item?.count, 0) || 0;
            zhuxiaoCount =
              groupItems
                ?.find((t) => t.key === 'statuscode')
                .items?.filter((item) => item?.desc === '注销')
                .reduce((sum, item) => sum + item?.count, 0) || 0;
          }
          const ratio = allCount && zhuxiaoCount ? parseFloat(((zhuxiaoCount / allCount) * 100).toFixed(2)) : 0;
          outwardInvestmentStatistics.push({
            type: outwardInvestmentStatisticsParams?.fieldValue[0],
            groupItems,
            N: zhuxiaoCount,
            D: allCount,
            ratio,
          });
        }
      }
    }

    if (outwardInvestmentStatistics?.length) {
      Result.push({
        outwardInvestmentStatistics,
      });
    }
    // 区间左值
    let isHit = true;
    const leftRatioDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.leftRatio);
    if (leftRatioDimensionField && isHit && Result?.length) {
      const patentRatio = Result[0]?.outwardInvestmentStatistics[0]?.ratio || 0;
      isHit = false;
      if (getCompareResult(patentRatio, leftRatioDimensionField.fieldValue[0], leftRatioDimensionField.compareType)) {
        isHit = true;
      }
    }
    // 区间右值
    const rightRatioDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.rightRatio);
    if (rightRatioDimensionField && isHit && Result?.length) {
      const patentRatio = Result[0]?.outwardInvestmentStatistics[0]?.ratio || 0;
      isHit = false;
      if (getCompareResult(patentRatio, rightRatioDimensionField.fieldValue[0], rightRatioDimensionField.compareType)) {
        isHit = true;
      }
    }
    if (!isHit) {
      Result = [];
    }
    dimensionDetails.Paging = {
      PageSize: pageSize || 10,
      PageIndex: pageIndex || 1,
      TotalRecords: Result.length,
    };
    dimensionDetails.Result = Result;
    return dimensionDetails;
  }

  /**
   * 专利分析
   * @param data
   * @param dimension
   * @param sourcePath
   */
  public async getPatentAnalysis(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: any) {
    const { keyNo } = data;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const pageIndex = data?.pageIndex || 1;
    const pageSize = data?.pageSize || 10;
    let Result = [];
    const res = await this.getPatentInfo(data, dimension, sourcePath);
    if (res.Paging.TotalRecords === 0) {
      return dimensionDetails;
    }
    const pantentStatistics: any[] = [];
    let mean = null; // n期平均值
    let cvXn = null; // n期变异系数
    const patentStatisticsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.patentStatistics);
    if (patentStatisticsParams) {
      // 发明专利占比
      if (patentStatisticsParams?.fieldValue[0] === 1) {
        const res = await this.getPatentInfo(data, dimension, sourcePath);
        if (res?.GroupItems?.length) {
          const groupItems = res?.GroupItems?.filter((t) => t.key === 'applicationyear' || t.key === 'kindfourcode') || [];
          let allCount = 0;
          let faMingCount = 0;
          if (groupItems?.length) {
            allCount = groupItems?.find((t) => t.key === 'applicationyear').items?.reduce((sum, item) => sum + item?.count, 0) || 0;
            faMingCount =
              groupItems
                ?.find((t) => t.key === 'kindfourcode')
                .items?.filter((item) => Number(item?.value) === 1 || Number(item?.value) === 2)
                .reduce((sum, item) => sum + item?.count, 0) || 0;
          }
          const ratio = allCount && faMingCount ? parseFloat(((faMingCount / allCount) * 100).toFixed(2)) : 0;
          pantentStatistics.push({
            type: patentStatisticsParams?.fieldValue[0],
            groupItems,
            N: faMingCount,
            D: allCount,
            ratio,
          });
        }
      }
      // 发明专利平均占比
      if (patentStatisticsParams?.fieldValue[0] === 2) {
        const yearPeriodParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.yearPeriod);
        if (yearPeriodParams?.fieldValue?.length) {
          await Bluebird.map(yearPeriodParams.fieldValue, async (yearPeriod) => {
            const applicationYear = moment()
              .subtract(yearPeriod - 1, 'year')
              .year();
            const res = await this.getPatentInfo(data, dimension, sourcePath, applicationYear);
            const groupItems = res?.GroupItems?.filter((t) => t.key === 'applicationyear' || t.key === 'kindfourcode') || [];
            let yearCount = 0;
            let faMingCount = 0;
            if (groupItems?.length) {
              yearCount = groupItems?.find((t) => t.key === 'applicationyear')?.items[0]?.count || 0;
              faMingCount =
                groupItems
                  ?.find((t) => t.key === 'kindfourcode')
                  .items?.filter((item) => Number(item?.value) === 1 || Number(item?.value) === 2)
                  .reduce((sum, item) => sum + item?.count, 0) || 0;
            }
            const ratio = yearCount && faMingCount ? parseFloat(((faMingCount / yearCount) * 100).toFixed(2)) : 0;
            pantentStatistics.push({
              type: patentStatisticsParams?.fieldValue[0],
              publishyear: applicationYear,
              yearPeriod,
              groupItems,
              D: yearCount,
              N: faMingCount,
              ratio,
            });
          });
        }
      }
      // 发明专利授权率
      if (patentStatisticsParams?.fieldValue[0] === 3) {
        const res = await this.getPatentInfo(data, dimension, sourcePath);
        if (res?.GroupItems?.length) {
          const groupItems = res?.GroupItems?.filter((t) => t.key === 'applicationyear' || t.key === 'kindfourcode') || [];
          let faMingCount = 0;
          let shouQuanCount = 0;
          if (groupItems?.length) {
            faMingCount = groupItems?.find((t) => t.key === 'kindfourcode').items?.reduce((sum, item) => sum + item?.count, 0) || 0;
            shouQuanCount =
              groupItems
                ?.find((t) => t.key === 'kindfourcode')
                .items?.filter((item) => Number(item?.value) === 2)
                .reduce((sum, item) => sum + item?.count, 0) || 0;
          }
          const ratio = faMingCount && shouQuanCount ? parseFloat(((shouQuanCount / faMingCount) * 100).toFixed(2)) : 0;
          pantentStatistics.push({
            type: patentStatisticsParams?.fieldValue[0],
            groupItems,
            N: shouQuanCount,
            D: faMingCount,
            ratio,
          });
        }
      }
      // 发明专利平均授权率
      if (patentStatisticsParams?.fieldValue[0] === 4) {
        const yearPeriodParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.yearPeriod);
        if (yearPeriodParams?.fieldValue?.length) {
          await Bluebird.map(yearPeriodParams.fieldValue, async (yearPeriod) => {
            const applicationYear = moment()
              .subtract(yearPeriod - 1, 'year')
              .year();
            const res = await this.getPatentInfo(data, dimension, sourcePath, applicationYear);
            const groupItems = res?.GroupItems?.filter((t) => t.key === 'applicationyear' || t.key === 'kindfourcode') || [];
            let faMingCount = 0;
            let shouQuanCount = 0;
            if (groupItems?.length) {
              faMingCount = groupItems?.find((t) => t.key === 'kindfourcode').items.reduce((sum, item) => sum + item?.count, 0) || 0;
              shouQuanCount =
                groupItems
                  ?.find((t) => t.key === 'kindfourcode')
                  .items?.filter((item) => Number(item?.value) === 2)
                  .reduce((sum, item) => sum + item?.count, 0) || 0;
            }
            const ratio = faMingCount && shouQuanCount ? parseFloat(((shouQuanCount / faMingCount) * 100).toFixed(2)) : 0;
            pantentStatistics.push({
              type: patentStatisticsParams?.fieldValue[0],
              publishyear: applicationYear,
              yearPeriod,
              groupItems,
              D: faMingCount,
              N: shouQuanCount,
              ratio,
            });
          });
        }
      }
      // 发明专利发明人集中度
      if (patentStatisticsParams?.fieldValue[0] === 5) {
        const companyPerNames = await this.getCompanyPersonNames(keyNo);
        data.pageSize = 5000; // 发明人
        const res = await this.getPatentInfo(data, dimension, sourcePath);
        if (res?.Result?.length) {
          const allPatentCount = res?.Result?.length || 0;
          const personPatentCount = res?.Result?.filter((patent) => _.intersection(patent?.InventorList, companyPerNames).length > 0)?.length || 0;
          const ratio = allPatentCount && personPatentCount ? parseFloat(((personPatentCount / allPatentCount) * 100).toFixed(2)) : 0;
          pantentStatistics.push({
            type: patentStatisticsParams?.fieldValue[0],
            N: personPatentCount,
            D: allPatentCount,
            ratio,
          });
        }
      }
      // 以转让方式获取的发明专利占比
      if (patentStatisticsParams?.fieldValue[0] === 6) {
        data.pageSize = 5000;
        const res = await this.getPatentInfo(data, dimension, sourcePath);
        if (res?.Result?.length) {
          const allPatentCount = res?.Result?.length || 0;
          const historyPatentCount = res?.Result?.filter((patent) => Boolean(patent?.HistoryChange?.length))?.length || 0;
          const ratio = allPatentCount && historyPatentCount ? parseFloat(((historyPatentCount / allPatentCount) * 100).toFixed(2)) : 0;
          pantentStatistics.push({
            type: patentStatisticsParams?.fieldValue[0],
            N: historyPatentCount,
            D: allPatentCount,
            ratio,
          });
        }
      }
      // 发明专利的稳定性
      if (patentStatisticsParams?.fieldValue[0] === 7) {
        const yearPeriodParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.yearPeriod);
        if (yearPeriodParams?.fieldValue?.length) {
          await Bluebird.map(yearPeriodParams.fieldValue, async (yearPeriod) => {
            const applicationYear = moment()
              .subtract(yearPeriod - 1, 'year')
              .year();
            const res = await this.getPatentInfo(data, dimension, sourcePath, applicationYear);
            const groupItems = res?.GroupItems?.filter((t) => t.key === 'applicationyear') || [];
            let faMingCount = 0;
            if (groupItems?.length) {
              faMingCount = groupItems?.find((t) => t.key === 'applicationyear').items?.reduce((sum, item) => sum + item?.count, 0) || 0;
            }
            pantentStatistics.push({
              type: patentStatisticsParams?.fieldValue[0],
              publishyear: applicationYear,
              yearPeriod,
              groupItems,
              N: faMingCount,
            });
          });
        }
      }
    }
    if (pantentStatistics.length) {
      //发明专利平均授权率,发明专利平均占比 需要计算变异系数和平均值
      const filterPantentStatistics = pantentStatistics.filter((t) => [2, 4].includes(t?.type));
      // n期平均值X
      // 计算平均值 X = (X1+X2+...+XN)/N
      if (filterPantentStatistics?.length) {
        mean = parseFloat((sum(filterPantentStatistics.map((t) => t.ratio)) / filterPantentStatistics.length).toFixed(2));
        if (mean !== 0) {
          // 避免除以0的情况
          // 计算方差 σ² = [(X1-X)²+(X2-X)²+...(XN-X)²]/(N-1)  使用样本标准差 (我们现在获得的数据范围有限，只能把样本先当总体来看了)
          const squaredDiffs = filterPantentStatistics.map((num) => Math.pow(num.ratio - mean, 2));
          const variance = squaredDiffs.reduce((acc, val) => acc + val, 0) / filterPantentStatistics.length;
          // 计算标准差 σ = √方差
          const stdXn = Math.sqrt(variance);
          // 计算变异系数 CV = (σ/X) * 100%
          cvXn = parseFloat(((stdXn / mean) * 100).toFixed(2));
        }
      }
    }
    if (mean !== null || cvXn !== null) {
      Result.push({
        mean,
        cvXn,
        pantentStatistics,
      });
    } else {
      Result.push({
        pantentStatistics,
      });
    }
    // -----------------------------以下是返回的结果分析
    let isHit = true;
    // 区间左值
    const leftRatioDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.leftRatio);
    if (leftRatioDimensionField && isHit && Result?.length) {
      const patentRatio = Result[0]?.pantentStatistics[0]?.ratio || 0;
      isHit = false;
      if (getCompareResult(patentRatio, leftRatioDimensionField.fieldValue[0], leftRatioDimensionField.compareType)) {
        isHit = true;
      }
    }
    // 区间右值
    const rightRatioDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.rightRatio);
    if (rightRatioDimensionField && isHit && Result?.length) {
      const patentRatio = Result[0]?.pantentStatistics[0]?.ratio || 0;
      isHit = false;
      if (getCompareResult(patentRatio, rightRatioDimensionField.fieldValue[0], rightRatioDimensionField.compareType)) {
        isHit = true;
      }
    }
    // 发明专利稳定性
    const patentStableDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.patentStable);
    if (patentStableDimensionField && isHit && Result?.length) {
      isHit = false;
      let anyYear = null;
      if (Result[0].pantentStatistics.filter((p) => p.N > 0).length >= 3) {
        anyYear = 3;
      } else if (Result[0].pantentStatistics.filter((p) => p.N > 0).length >= 2) {
        anyYear = 2;
      } else if (Result[0].pantentStatistics.some((p) => p.N > 0)) {
        anyYear = 1;
      }
      if (anyYear) {
        if (getCompareResult(anyYear, patentStableDimensionField.fieldValue[0], patentStableDimensionField.compareType)) {
          isHit = true;
        }
      }
    }
    // n期平均值X
    const avgXnDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.avgXn);
    if (avgXnDimensionField && isHit && Result?.length) {
      isHit = false;
      const targetAvgXn = avgXnDimensionField.fieldValue[0];
      const sourceAvgXn = Result[0]?.mean;
      if (getCompareResult(sourceAvgXn, targetAvgXn, avgXnDimensionField.compareType)) {
        isHit = true;
      }
    }
    // n期变异系数
    const cvXnDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.cvXn);
    if (cvXnDimensionField && isHit && Result?.length) {
      isHit = false;
      const sourceCvXn = Result[0]?.cvXn;
      const targetCvXn = cvXnDimensionField.fieldValue[0];
      if (sourceCvXn && targetCvXn && getCompareResult(sourceCvXn, targetCvXn, cvXnDimensionField.compareType)) {
        isHit = true;
      }
    }
    if (!isHit) {
      Result = [];
    }
    dimensionDetails.Paging = {
      PageSize: pageSize || 10,
      PageIndex: pageIndex || 1,
      TotalRecords: Result.length,
    };
    dimensionDetails.Result = Result;
    return dimensionDetails;
  }

  /**
   * 招聘分析
   * @param data
   * @param dimension
   * @param sourcePath
   */
  public async getRecruitmentAnalysis(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const pageIndex = data?.pageIndex || 1;
    const pageSize = data?.pageSize || 10;
    const res = await this.getCompanyRecruitment(data, dimension, sourcePath);
    if (res.Paging.TotalRecords === 0) {
      return dimensionDetails;
    }
    const recruitmentStatisticsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.recruitmentStatistics);
    let Result = [];
    let mean = null; // n期平均值
    let cvXn = null; // n期变异系数
    const recruitmentStatistics: { publishyear: number; yearPeriod: number; groupItems: any[]; D: number; N: number; ratio: number }[] = [];
    if (recruitmentStatisticsParams && recruitmentStatisticsParams?.fieldValue[0] === 1) {
      const yearPeriodParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.yearPeriod);
      if (yearPeriodParams?.fieldValue?.length) {
        await Bluebird.map(yearPeriodParams.fieldValue, async (yearPeriod) => {
          const publishyear = moment()
            .subtract(yearPeriod - 1, 'year')
            .year();
          const res = await this.getCompanyRecruitment(data, dimension, sourcePath, publishyear);
          const groupItems = res?.GroupItems?.filter((t) => t.key === 'education' || t.key === 'publishyear') || [];
          let yearCount = 0;
          let benkeCount = 0;
          if (groupItems?.length) {
            yearCount = groupItems?.find((t) => t.key === 'publishyear')?.items[0]?.count || 0;
            benkeCount =
              groupItems
                ?.find((t) => t.key === 'education')
                .items?.filter((item) => Number(item?.value) <= 4)
                .reduce((sum, item) => sum + item?.count, 0) || 0;
          }
          const ratio = yearCount && benkeCount ? parseFloat(((benkeCount / yearCount) * 100).toFixed(2)) : 0;
          recruitmentStatistics.push({ publishyear, yearPeriod, groupItems, D: yearCount, N: benkeCount, ratio });
        });
      }
      if (recruitmentStatistics.length) {
        const filterRecruitmentStatistics = recruitmentStatistics.filter((t) => t?.yearPeriod);
        // n期平均值X
        // 计算平均值 X = (X1+X2+...+XN)/N
        mean = parseFloat((sum(filterRecruitmentStatistics.map((t) => t.ratio)) / filterRecruitmentStatistics.length).toFixed(2));
        if (mean !== 0) {
          // 避免除以0的情况
          // 计算方差 σ² = [(X1-X)²+(X2-X)²+...(XN-X)²]/(N-1)  使用样本标准差 (我们现在获得的数据范围有限，只能把样本先当总体来看了)
          const squaredDiffs = filterRecruitmentStatistics.map((num) => Math.pow(num.ratio - mean, 2));
          const variance = squaredDiffs.reduce((acc, val) => acc + val, 0) / filterRecruitmentStatistics.length;
          // 计算标准差 σ = √方差
          const stdXn = Math.sqrt(variance);
          // 计算变异系数 CV = (σ/X) * 100%
          cvXn = parseFloat(((stdXn / mean) * 100).toFixed(2));
        }
      }
    }
    Result.push({
      mean,
      cvXn,
      recruitmentStatistics,
    });

    // ---------------以下为指标的命中分析---------------------------
    // n期平均值X
    let isHit = true;
    const avgXnDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.avgXn);
    if (avgXnDimensionField && isHit && Result?.length) {
      isHit = false;
      const targetAvgXn = avgXnDimensionField.fieldValue[0];
      const sourceAvgXn = Result[0]?.mean;
      if (getCompareResult(sourceAvgXn, targetAvgXn, avgXnDimensionField.compareType)) {
        isHit = true;
      }
    }
    // n期变异系数
    const cvXnDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.cvXn);
    if (cvXnDimensionField && isHit && Result?.length) {
      isHit = false;
      const sourceCvXn = Result[0]?.cvXn;
      const targetCvXn = cvXnDimensionField.fieldValue[0];
      if (sourceCvXn && targetCvXn && getCompareResult(sourceCvXn, targetCvXn, cvXnDimensionField.compareType)) {
        isHit = true;
      }
    }
    if (!isHit) {
      Result = [];
    }

    dimensionDetails.Paging = {
      PageSize: pageSize || 10,
      PageIndex: pageIndex || 1,
      TotalRecords: Result.length,
    };
    dimensionDetails.Result = Result;
    return dimensionDetails;
  }

  /**
   * 招聘通知
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getCompanyRecruitment(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: string, publishyear?: number) {
    const { keyNo } = data;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    // 排查对象
    const reqData = {};
    Object.assign(reqData, {
      sortField: data?.field || dimension?.getSortField()?.field || 'publishtime',
      isSortAsc: (data?.order || dimension?.getSortField()?.order) === 'ASC',
      keyNo,
      pageSize: data?.pageSize || 1,
      pageIndex: data?.pageIndex || 1,
    });
    // 时间过滤
    const naturalCycleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.naturalCycle);
    if (naturalCycleField) {
      const naturalCycle = naturalCycleField.fieldValue?.[0] as number;
      const operator = naturalCycleField ? EsOperator[naturalCycleField.compareType] : 'gte';
      if (naturalCycle && naturalCycle !== -1) {
        switch (operator) {
          case 'gt':
          case 'gte': {
            Object.assign(reqData, {
              fromTime: moment()
                .subtract(naturalCycle - 1, 'year')
                .startOf('year')
                .format(DATE_FORMAT),
              toTime: moment().format(DATE_FORMAT),
            });
            break;
          }
        }
      }
    }
    if (publishyear) {
      Object.assign(reqData, {
        publishyear,
      });
    }
    const result = await this.getRequestSourcePath(sourcePath, reqData);
    if (result?.Result) {
      return Object.assign(dimensionDetails, pick(result, ['Result', 'Paging', 'GroupItems']));
    }
    return dimensionDetails;
  }

  /**
   * 开庭公告
   * @param data
   * @param dimension
   * @param validField
   * @param sourcePath
   * @public
   */
  public async getCourtSessionAnnouncement(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    validField: DimensionHitStrategyFieldsEntity,
    sourcePath: string,
  ) {
    const { keyNo } = data;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    // 排查对象
    const reqData = {};
    Object.assign(reqData, {
      sortField: data?.field || dimension?.getSortField()?.field || 'liandate', //sortField必须是liandate,biaodiamount,pubdate其中之一 ，
      isSortAsc: (data?.order || dimension?.getSortField()?.order) === 'ASC',
      KeyNo: keyNo,
      pageSize: data?.pageSize || 5,
      pageIndex: data?.pageIndex || 1,
      isValid: getIsValidValue(validField?.fieldValue[0]),
      isB: true,
    });
    const cycleField = dimension.getCycleField();
    const cycle = cycleField.fieldValue?.[0] as number;
    const operator = cycleField ? EsOperator[cycleField.compareType] : 'gte';
    if (cycle && cycle !== -1) {
      switch (operator) {
        case 'gt':
        case 'gte': {
          Object.assign(reqData, {
            rangeFilters: JSON.stringify([
              {
                type: 'liandate',
                dateList: [{ start: moment().subtract(cycle, 'year').unix() }],
              },
            ]),
          });
          break;
        }
        case 'lt':
        case 'lte': {
          Object.assign(reqData, {
            rangeFilters: JSON.stringify([
              {
                type: 'liandate',
                dateList: [{ end: moment().subtract(cycle, 'year').unix() }],
              },
            ]),
          });
          break;
        }
      }
    }
    const courtDetailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.courtType);
    const courtTypeValue = courtDetailsParams?.fieldValue[0];
    if (courtTypeValue) {
      Object.assign(reqData, {
        type: courtTypeValue,
      });
    }
    const courtCaseReasonParms = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.courtCaseReason);
    const courtCaseReasonValue = courtCaseReasonParms?.fieldValue[0];
    if (courtCaseReasonValue) {
      Object.assign(reqData, {
        caseReason: courtCaseReasonValue,
      });
    }
    const courtRoleParms = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.courtRole);
    const courtRoleValue = courtRoleParms?.fieldValue;
    if (courtRoleValue) {
      Object.assign(reqData, {
        caseRole: courtRoleValue.join(','),
      });
    }
    const courtResult = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, reqData);
    if (courtResult?.Result) {
      return Object.assign(dimensionDetails, pick(courtResult, ['Result', 'Paging', 'GroupItems']));
    }
    return dimensionDetails;
  }

  /**
   * 失信被执行人，历史失信执行人
   * @param data
   * @param sourcePath
   * @public
   */
  public async getPersonCredit(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: string) {
    // 排查对象
    const reqData = {};
    switch (dimension.dimensionDef.key) {
      case DimensionTypeEnums.PersonCreditCurrent: {
        Object.assign(reqData, {
          // 只查询非历史数据
          isValid: '1',
        });
        break;
      }
      case DimensionTypeEnums.PersonCreditHistory: {
        Object.assign(reqData, {
          // 只查询历史数据
          isValid: getIsValidValue('0'),
        });
        break;
      }
    }
    const cycle: number = dimension?.getCycle();
    if (cycle && cycle !== -1) {
      Object.assign(reqData, {
        rangeFilters: JSON.stringify([
          {
            type: 'liandate',
            dateList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
          },
        ]),
      });
    }
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const sortField = data?.field || dimension?.getSortField()?.field || 'liandate';
    const isSortAsc = (data?.order || dimension?.getSortField()?.order) === 'ASC';
    const sortOrder = data?.order || dimension?.getSortField()?.order || 'ASC';
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetInvestigation);
    const fieldValue = detailsParams?.fieldValue[0] || TargetInvestigationEnums.Self;
    switch (fieldValue) {
      // Legal： 当前法人失信
      case TargetInvestigationEnums.Legal: {
        const companyDetail = await this.companySearchService.companyDetailsQcc(data.keyNo);
        if (!companyDetail?.Oper?.KeyNo) {
          return dimensionDetails;
        }
        const legalPerson = companyDetail.Oper.KeyNo;
        const param = Object.assign(reqData, {
          searchKey: legalPerson,
          pageSize: data.pageSize,
          pageIndex: data.pageIndex,
          sortField,
          isSortAsc,
          isB: true,
        });
        const shiXingRes = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, param);
        return Object.assign(dimensionDetails, pick(shiXingRes, ['Result', 'Paging', 'GroupItems']));
      }
      case TargetInvestigationEnums.HisLegal: {
        const res = await this.companyDetailsService.getCoyHistoryInfo(data.keyNo);
        const keyNos: string[] = res?.Result?.OperList?.filter((t) => !!t.KeyNo)
          ?.map((Oper) => Oper.KeyNo)
          .slice(0, 10);
        if (!keyNos?.length) {
          return dimensionDetails;
        }
        const shiXingRes = await Bluebird.map(
          keyNos,
          async (keyNo) => {
            return this.getCompanyShiXingInfo(keyNo, sourcePath, reqData);
          },
          { concurrency: 1 },
        );
        if (!shiXingRes?.length) {
          return dimensionDetails;
        }
        let GroupItems = [];
        const resultData = [];
        shiXingRes.forEach((t) => {
          resultData.push(...t.resultItems);
          if (t?.GroupItems?.length) {
            GroupItems = t.GroupItems;
          }
        });
        if (!resultData?.length) {
          return dimensionDetails;
        }
        const sortResultData = resultData.sort((a, b) => {
          // 获取字段值
          const valueA = a[sortField];
          const valueB = b[sortField];
          // 比较字段值
          if (valueA < valueB) {
            return sortOrder === 'ASC' ? -1 : 1;
          }
          if (valueA > valueB) {
            return sortOrder === 'DESC' ? 1 : -1;
          }
          return 0;
        });
        //内存分页
        const pageSize = data?.pageSize || 10;
        const pageIndex = data?.pageIndex || 1;
        const start = (pageIndex - 1) * pageSize;
        const end = start + pageSize;
        const Paging = {
          PageSize: pageSize,
          PageIndex: pageIndex,
          TotalRecords: sortResultData.length,
        };
        const Result = sortResultData.slice(start, end);
        return Object.assign(dimensionDetails, {
          Paging,
          Result,
          GroupItems,
        });
      }
      case TargetInvestigationEnums.Self: {
        const param = Object.assign(reqData, {
          searchKey: data.keyNo,
          pageSize: data.pageSize,
          pageIndex: data.pageIndex,
          sortField,
          isSortAsc,
          isB: true,
        });
        const shiXingRes = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, param);
        return Object.assign(dimensionDetails, pick(shiXingRes, ['Result', 'Paging', 'GroupItems']));
      }
      case TargetInvestigationEnums.ActualController: {
        const personData = await this.personHelper.getFinalActualController(data.keyNo);
        const keyNoList = personData?.filter((t) => !!t.keyNo)?.map((t) => t.keyNo);
        const result = await this.fetchAndProcessShiXingData(keyNoList, sourcePath, reqData, sortField, sortOrder, data);
        return result ? Object.assign(dimensionDetails, result) : dimensionDetails;
      }

      case TargetInvestigationEnums.LargestShareholder: {
        const partnerData = await this.personHelper.getPartnerList(data.keyNo, 'all');
        const keyNos = partnerData?.filter((p) => !!p.keyNo && p.tags?.includes('大股东')).map((p) => p.keyNo);
        const result = await this.fetchAndProcessShiXingData(keyNos, sourcePath, reqData, sortField, sortOrder, data);
        return result ? Object.assign(dimensionDetails, result) : dimensionDetails;
      }
      case TargetInvestigationEnums.RelatedCompany: {
        const relatedPartyGroupPOS = await this.relatedHelper.getIcbcSFRelateds(data.keyNo);
        const keyNos = relatedPartyGroupPOS?.map((x) => x.companyKeynoRelated).filter((x) => x);
        const result = await this.companyDetailsService.getCountInfos(keyNos, [TargetScopeEnums.ShiXinCount]);
        const filterKeyNos = result?.Result?.filter((x) => x?.ShiXinCount !== undefined && x.ShiXinCount > 0)?.map((x) => x._id);
        const shixinResp = await this.fetchAndProcessShiXingData(filterKeyNos, sourcePath, reqData, sortField, sortOrder, data);
        return shixinResp ? Object.assign(dimensionDetails, shixinResp) : dimensionDetails;
      }
    }
  }

  public async getCompanyShiXingInfo(keyNo: string, sourcePath: string, reqData) {
    const param = Object.assign(reqData, {
      searchKey: keyNo,
      sortField: 'pubdate', //sortField必须是Liandate,Pubdate ，
      isSortAsc: 'ASC',
      pageSize: 200,
      pageIndex: 1,
      isB: true,
    });
    const limited = 3000;
    let totalFetched = 0;
    const resultItems = [];
    let GroupItems = [];
    do {
      const res1 = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, param);
      if (res1.Result?.length) {
        totalFetched += res1.Result.length;
        resultItems.push(...res1.Result);
      }
      GroupItems = res1?.GroupItems;
      if (!res1.Result?.length || res1.Result.length < param.pageSize || totalFetched >= limited) {
        break;
      }
      param.pageIndex++;
    } while (true);
    return { resultItems, GroupItems };
  }

  /**
   * 被执行人
   * @param data
   * @param dimension
   * @param validField
   * @param sourcePath
   * @param dimensionDetails
   * @public
   */
  public async getPersonExecution(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    validField: DimensionHitStrategyFieldsEntity,
    sourcePath: string,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const reqData = {
      isValid: getIsValidValue(validField?.fieldValue[0]),
      sortField: data?.field || 'liandate', //sortField必须是liandate,biaodiamount其中之一 ，
      isSortAsc: data?.order === 'ASC',
      isB: true,
    };
    const cycle: number = dimension?.getCycle();
    if (cycle && cycle !== -1) {
      Object.assign(reqData, {
        rangeFilters: JSON.stringify([
          {
            type: 'liandate',
            dateList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
          },
        ]),
      });
    }
    const targetInvestigationField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetInvestigation);
    const fieldValue = targetInvestigationField?.fieldValue[0] || TargetInvestigationEnums.Self;
    switch (fieldValue) {
      case TargetInvestigationEnums.Self: {
        //兼容汇添富模型
        const param = Object.assign(reqData, {
          searchKey: data.keyNo,
          pageSize: data?.pageSize ?? 10,
          pageIndex: data?.pageIndex ?? 1,
        });
        // 接口暂不支持时间过滤
        /* let cycle: number = dimension?.getCycle();
        if (cycle && cycle !== -1) {
          Object.assign(reqData, {
            rangeFilters: JSON.stringify([
              {
                type: 'liandate',
                dateList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
              },
            ]),
          });
        }*/
        const zhiXingRes = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, param);
        return this.processZhiXingResult(dimensionDetails, zhiXingRes, dimension);
      }

      case TargetInvestigationEnums.ActualController: {
        const personData = await this.personHelper.getFinalActualController(data.keyNo);
        const keyNoList = personData?.filter((t) => !!t.keyNo)?.map((t) => t.keyNo);
        const result = await this.fetchAndProcessZhiXingData(keyNoList, sourcePath, reqData, reqData.sortField, data?.order, data);
        return result ? this.processZhiXingResult(dimensionDetails, result, dimension) : dimensionDetails;
      }

      case TargetInvestigationEnums.LargestShareholder: {
        const partnerData = await this.personHelper.getPartnerList(data.keyNo, 'all');
        const keyNos = partnerData?.filter((p) => !!p.keyNo && p.tags?.includes('大股东')).map((p) => p.keyNo);
        const result = await this.fetchAndProcessZhiXingData(keyNos, sourcePath, reqData, reqData.sortField, data?.order, data);
        return result ? this.processZhiXingResult(dimensionDetails, result, dimension) : dimensionDetails;
      }
      case TargetInvestigationEnums.RelatedCompany: {
        const relatedPartyGroupPOS = await this.relatedHelper.getIcbcSFRelateds(data.keyNo);
        const keyNos = relatedPartyGroupPOS?.map((x) => x.companyKeynoRelated).filter((x) => x);
        const result = await this.companyDetailsService.getCountInfos(keyNos, [TargetScopeEnums.ZhiXingCount]);
        const filterKeyNos = result?.Result?.filter((x) => x?.ZhiXingCount !== undefined && x.ZhiXingCount > 0)?.map((x) => x._id);
        const zhixingResp = await this.fetchAndProcessZhiXingData(filterKeyNos, sourcePath, reqData, reqData.sortField, data?.order, data);
        return zhixingResp ? Object.assign(dimensionDetails, zhixingResp) : dimensionDetails;
      }

      default:
        return dimensionDetails;
    }
  }

  /**
   * 终本案件
   * @param data
   * @param dimension
   * @param validField
   * @param sourcePath
   * @param dimensionDetails
   * @public
   */
  public async getEndExecutionCase(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    validField: DimensionHitStrategyFieldsEntity,
    sourcePath: string,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const reqData = {
      keyNo: data.keyNo,
      pageSize: data.pageSize,
      pageIndex: data.pageIndex,
      isValid: getIsValidValue(validField?.fieldValue[0]),
      sortField: data?.field?.toLowerCase() || 'enddate', //enddate 终本日期 judgedate 立案日期 executeobject 执行标的 failureact 未履行金额 默认 enddate ，
      isSortAsc: data?.order === 'ASC',
      isAgg: true,
      isB: true,
    };
    const cycle = dimension?.getCycle();
    if (cycle && cycle !== -1) {
      Object.assign(reqData, {
        rangeFilters: JSON.stringify([
          {
            type: 'judgedate', //judgedate立案日期
            dateList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
          },
        ]),
      });
    }
    const endCaseRes = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, reqData);
    const failure = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.failure);
    if (failure?.fieldValue[0]) {
      const failureGroupItem: {
        count: number;
        desc: string;
        sum: number;
        value: number;
      } = endCaseRes?.GroupItems?.find((g) => g.key == 'failureGroup')?.items?.[0];
      if (failureGroupItem?.sum) {
        if (getCompareResult(failureGroupItem.sum, Number(failure.fieldValue[0]), failure?.compareType || DimensionFieldCompareTypeEnums.GreaterThanOrEqual)) {
          return Object.assign(dimensionDetails, pick(endCaseRes, ['Result', 'Paging', 'GroupItems']));
        } else {
          return dimensionDetails;
        }
      }
    }
    return Object.assign(dimensionDetails, pick(endCaseRes, ['Result', 'Paging', 'GroupItems']));
  }

  public async getBusinessAbnormal1(data: HitEnterpriseDimensionQueryParam, dimensionDetails: HitDetailsBaseResponse) {
    const keyNo = data.keyNo;
    const companyDetail = await this.companySearchService.companyDetailsQcc(keyNo);
    if (isOrganism(keyNo) && ['撤销', '吊销', '注销', '注销中', '名称核准不通过', '清算', '除名'].includes(companyDetail.ShortStatus)) {
      Object.assign(dimensionDetails, {
        Result: [
          {
            label: '登记状态',
            value: companyDetail.ShortStatus,
            ShortStatus: companyDetail.ShortStatus,
          },
        ],
        Paging: { TotalRecords: 1 },
      });
    }
    if (!isOrganism(keyNo) && ['吊销', '注销', '撤销', '停业', '歇业', '责令关闭', '清算', '除名'].includes(companyDetail.ShortStatus)) {
      Object.assign(dimensionDetails, {
        Result: [
          {
            label: '登记状态',
            value: companyDetail.ShortStatus,
            ShortStatus: companyDetail.ShortStatus,
          },
        ],
        Paging: { TotalRecords: 1 },
      });
    }
    return dimensionDetails;
  }

  /**
   *  被列入非正常户  TODO: 迁移到 companyDetail
   * @param data
   * @param dimensionDetails
   * @returns
   */
  public async getBusinessAbnormal4(data: HitEnterpriseDimensionQueryParam, dimensionDetails: HitDetailsBaseResponse) {
    const companyDetail = await this.companySearchService.companyDetailsQcc(data.keyNo);
    const taxUnnormalCount = companyDetail.CountInfo['TaxUnnormalCount'];
    if (!taxUnnormalCount) {
      return dimensionDetails;
    }
    const taxResponse = await this.companyDetailsService.getTaxUnnormals(data);
    if (!taxResponse) {
      return dimensionDetails;
    }
    const taxUnormals = taxResponse?.Result[0];
    const joinTime = taxUnormals.JoinDate || 0;
    if (joinTime == 0) {
      Object.assign(dimensionDetails, {
        Result: [
          [
            {
              label: '纳税人识别号',
              value: taxUnormals?.CaseNo,
            },
            {
              label: '列入机关',
              value: taxUnormals?.ExecuteGov,
            },
          ],
        ],
        Paging: { TotalRecords: 1 },
      });
    } else {
      Object.assign(dimensionDetails, {
        Result: [
          [
            {
              label: '纳税人识别号',
              value: taxUnormals?.CaseNo,
            },
            { label: '列入机关', value: taxUnormals?.ExecuteGov },
            {
              label: '列入日期',
              value: moment(joinTime * 1000).format(DATE_FORMAT),
            },
          ],
        ],
        Paging: { TotalRecords: 1 },
      });
    }
    return dimensionDetails;
  }

  public async getNoQualityCertification(data: HitEnterpriseDimensionQueryParam, sourcePath: string, dimensionDetails: HitDetailsBaseResponse) {
    const { keyNo, pageSize, pageIndex } = data;
    const result1 = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo,
      isNew: true,
      isNewAgg: true,
    });
    if (result1?.Result?.some((x) => x?.CertificateCode == '*********')) {
      Object.assign(dimensionDetails, {
        Result: [],
        Paging: { TotalRecords: 0, PageIndex: pageIndex, PageSize: pageSize },
      });
    } else {
      Object.assign(dimensionDetails, {
        Result: [{ description: '该企业无有效质量管理体系认证资质' }],
        Paging: { TotalRecords: 1, PageIndex: pageIndex, PageSize: pageSize },
      });
    }
    return dimensionDetails;
  }

  public async getNoCertification(
    data: HitEnterpriseDimensionQueryParam,
    sourcePath: string,
    dimension: DimensionHitStrategyPO,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const { keyNo, pageSize, pageIndex } = data;
    const result1 = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo,
      isNew: true,
      isNewAgg: true,
    });
    const queryPo = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.certification);
    const options: string[] = queryPo.fieldValue;

    Object.assign(dimensionDetails, {
      Result: [],
      Paging: { TotalRecords: 0, PageIndex: pageIndex, PageSize: pageSize },
    });
    for (const item of options) {
      const hasValidCerts = result1?.Result?.find((x) => x?.CertificateCode == item)?.ValidKeywords?.length;
      if (!hasValidCerts) {
        Object.assign(dimensionDetails, {
          Result: [{ description: '该企业无有效关键资质认证' }],
          Paging: { TotalRecords: 1, PageIndex: pageIndex, PageSize: pageSize },
        });
        break;
      }
    }
    return dimensionDetails;
  }

  public async getCertification(
    data: HitEnterpriseDimensionQueryParam,
    sourcePath: string,
    dimension: DimensionHitStrategyPO,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const { keyNo, pageSize, pageIndex } = data;
    const result1 = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo,
      isNew: true,
      isNewAgg: true,
    });
    const nearExpirationType = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.nearExpirationType);
    const dateType = nearExpirationType?.fieldValue?.[0] || 2;
    const expirationDate = getExpirationDate(dateType);
    const nowDate = moment().unix();
    const Result = [];
    // 营业执照处理
    const businessLicense = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessLicense);
    if (businessLicense && businessLicense.status == 1) {
      const businessLicenseResult = await this.getBusinessLicenseResult(keyNo, dateType, expirationDate, nowDate);
      if (businessLicenseResult) {
        Result.push(businessLicenseResult);
      }
    }
    // 资质证书处理
    const queryCertification = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.certification);
    const certifications: string[] = queryCertification.fieldValue;
    for (let i = 0; i < certifications.length; i++) {
      const item = certifications[i];
      // if (item.status == 0) continue;
      const resultItem = result1?.Result?.find((x) => x?.CertificateCode == item);
      const startDate = resultItem?.LatestDoc?.StartDate;
      const endDate = resultItem?.LatestDoc?.EndDate;
      let expirationDesc = '';
      let name = '';
      let index = i;
      if (resultItem) {
        if ((isNumber(endDate) && endDate == 0) || endDate > expirationDate) {
          expirationDesc = '有效';
        } else if (nowDate >= endDate) {
          index = 1000 + i;
          expirationDesc = '已到期';
        } else {
          switch (dateType) {
            case 1:
              expirationDesc = '近7日到期';
              break;
            case 3:
              expirationDesc = '近3个月到期';
              break;
            case 2:
            default:
              expirationDesc = '近1个月到期';
          }
        }
        name = resultItem.CertificateCodeDesc;
      } else {
        name = CompanyCertificationConstants[item];
        index = 1000 + i;
        expirationDesc = '缺失';
      }
      if (expirationDesc != '有效') {
        Result.push({
          index,
          name,
          startDate,
          endDate,
          expirationDesc,
        });
      }
    }
    Object.assign(dimensionDetails, {
      Result: Result.slice((pageIndex - 1) * pageSize, pageIndex * pageSize),
      Paging: { TotalRecords: Result.length, PageIndex: pageIndex, PageSize: pageSize },
    });
    return dimensionDetails;
  }

  public async getBillDefaults(data: HitEnterpriseDimensionQueryParam, sourcePath: string, dimensionDetails: HitDetailsBaseResponse) {
    const response = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
      pageSize: data?.pageSize || 5,
      pageIndex: data?.pageIndex || 1,
      sortField: data?.field || 'publishdate', //默认发布时间排序
      isSortAsc: data?.order === 'ASC',
    });
    if (response?.Result) {
      return Object.assign(dimensionDetails, pick(response, ['Result', 'Paging']));
    }
    return dimensionDetails;
  }

  public async getBondDefaults(
    data: HitEnterpriseDimensionQueryParam,
    validField: DimensionHitStrategyFieldsEntity,
    sourcePath: string,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const postData = {
      keyNo: data.keyNo,
      pageSize: data?.pageSize || 5,
      pageIndex: data?.pageIndex || 1,
      sortField: data?.field || 'maturitydate', //默认到期日期排序，支持[newdefaultdate, firstdefaultdate, overduecapitalval, maturitydate]
      isSortAsc: data?.order === 'ASC',
    };
    if (parseInt(validField.fieldValue[0]) == -1) {
      postData['isValid'] = [0, 1];
      //不限，所有状态数据
      postData['statusList'] = [1, 2, 3];
    } else {
      postData['isValid'] = [1];
      //当前有效，过滤掉已兑付数据
      postData['statusList'] = [1, 2];
    }
    const response = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, postData);
    if (response?.Result) {
      const result = response.Result.map((i) => {
        return {
          Id: i.RelatedSec,
          BondShortName: i.Sname,
          BondTypeName: i.BondTypeSecondCodeDesc,
          FirstDefaultDate: moment(Number(i?.FirstDefaultDate) * 1000).format('YYYYMMDD') || '-',
          AccuOverdueCapital: i?.OverdueCapital || 0,
          AccuOverdueInterest: i?.OverDueInterest || 0,
          MaturityDate: moment(Number(i?.MaturityDate) * 1000).format('YYYYMMDD') || '-',
          DefaultStatusDesc: i?.NewStatusDesc,
        };
      });
      return Object.assign(dimensionDetails, pick(response, ['Paging']), { Result: result });
    }
    return dimensionDetails;
  }

  public async getLiquidation(data: HitEnterpriseDimensionQueryParam, sourcePath: string, dimensionDetails: HitDetailsBaseResponse) {
    const liquidationDetail = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
      isB: true,
    });
    if (liquidationDetail?.Result) {
      return Object.assign(dimensionDetails, {
        Result: [liquidationDetail.Result],
        Paging: {
          PageSize: data.pageSize,
          PageIndex: data.pageIndex,
          TotalRecords: 1,
        },
      });
    }
    return dimensionDetails;
  }

  public async getFinancialHealth(
    data: HitEnterpriseDimensionQueryParam,
    sourcePath: string,
    dimension: DimensionHitStrategyPO,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const financialHealthRes = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
      isB: true,
    });
    const queryPo = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.assetLiabilityRatio);
    if (financialHealthRes?.Result) {
      const assetLiabilityRatio = financialHealthRes.Result?.Data?.IndicatorData?.find((f) => f.K === '偿债能力')?.FieldList?.find((f) => f.K === '资产负债率')
        ?.V?.[0]?.Value;
      if (assetLiabilityRatio && getCompareResult(parseFloat(assetLiabilityRatio), queryPo?.fieldValue[0], queryPo.compareType)) {
        return Object.assign(dimensionDetails, {
          Result: [
            {
              label: '资产负债率',
              value: assetLiabilityRatio,
            },
          ],
          Paging: { TotalRecords: 1 },
        });
      }
    }
    return dimensionDetails;
  }

  public async getBusinessAbnormal2(
    data: HitEnterpriseDimensionQueryParam,
    sourcePath: string,
    // dimension: DimensionHitStrategyPO,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const result = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
      pageSize: data?.pageSize || 5,
      pageIndex: data?.pageIndex || 1,
      isB: true,
    });
    if (result?.Status == 200) {
      const { Result, Paging } = result;
      const newRes = Result.map((r) => {
        return {
          annoName: `${r.CompanyName}简易注销公告`,
          id: r.No,
          publishDate: r.PublicDate,
          resultContent: r.CancellationResultList?.[0]?.ResultContent,
        };
      });
      return Object.assign(dimensionDetails, {
        Result: newRes,
        Paging,
      });
    } else {
      return HitDetailsBaseResponse.failed('接口返回status != 200', DimensionSourceEnums.EnterpriseLib);
    }
  }

  public async getMainInfoUpdateInfos(
    dimension: DimensionHitStrategyPO,
    data: Record<string, any>,
    updateInfos: any[],
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const cycle: number = dimension?.getCycle() || 1;
    const orginUpdateInfos = cloneDeep(updateInfos);
    //filter 根据自定义查询条件筛选
    const filterInfos = [];
    for (let i = updateInfos.length - 1; i >= 0; i--) {
      const item = updateInfos[i];
      if (cycle == -1 || moment().diff(moment(item?.ChangeDate, DATE_FORMAT), 'years', true) <= cycle) {
        const afterItem = orginUpdateInfos[i - 1] || {};
        if (i === 0) {
          item['after'] = await this.getCompanyDetailCurrentScope(dimension, data.keyNo);
        } else {
          item['after'] = afterItem;
        }
        filterInfos.push(item);
      }
    }
    //按照时间倒序
    filterInfos.sort((a, b) => {
      return moment(b?.ChangeDate).diff(moment(a?.ChangeDate));
    });
    const pageSize = data?.pageSize || 10;
    const pageIndex = data?.pageIndex || 1;
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;
    dimensionDetails.Paging = { PageSize: pageSize, PageIndex: pageIndex, TotalRecords: filterInfos.length };
    dimensionDetails.Result = filterInfos.slice(start, end);
    return dimensionDetails;
  }

  public async getExternalRelatedRisk(dimension: DimensionHitStrategyPO, data: HitEnterpriseDimensionQueryParam, dimensionDetails: HitDetailsBaseResponse) {
    const param = Object.assign(new RelatedRiskRequest(), pick(data, ['keyNo', 'pageSize', 'pageIndex']));
    try {
      const cycle = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.cycle)?.fieldValue[0] || 1;
      if (cycle > 0) {
        param.startDate = moment().subtract(cycle, 'years').unix();
        param.endDate = Math.floor(Date.now() / 1000);
      }
      const roleType = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRoleType)?.fieldValue[0];
      if (roleType > 0) {
        param.roleType = roleType;
      }
      const riskTypes = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRiskType)?.fieldValue;

      if (riskTypes.length > 0) {
        //风险类型：22 失信被执行人、24 被执行人、23 限制高消费、25 终本案件、41 严重违法、42 行政处罚、53 经营异常
        //当 riskType大于-1时，需要去获取companyDetailsService.getRelatedRiskV3的所有数据
        const param2 = {
          ...param,
          pageSize: 200,
          pageIndex: 1,
        };
        const limited = 3000;
        let totalFetched = 0;
        const resultItems = [];
        do {
          const res1 = await this.companyDetailsService.getRelatedRiskV3(param2);
          if (res1.Result?.length) {
            totalFetched += res1.Result.length;
            resultItems.push(...res1.Result);
          }
          if (!res1.Result?.length || res1.Result.length < param2.pageSize || totalFetched >= limited) {
            break;
          }
          dimensionDetails.GroupItems = res1?.GroupItems;
          param2.pageIndex++;
        } while (true);
        //根据riskType过滤数据
        const riskTypeArr = resultItems?.filter((item) => item.Risk.find((r) => riskTypes.includes(r.NodeId))?.Count);
        //对riskTypeArr进行分页
        dimensionDetails.Result = riskTypeArr.slice((data.pageIndex - 1) * data.pageSize, data.pageIndex * data.pageSize);
        dimensionDetails.Paging = {
          PageSize: data.pageSize,
          PageIndex: data.pageIndex,
          TotalRecords: riskTypeArr.length,
        };
      } else {
        const result = await this.companyDetailsService.getRelatedRiskV3(param);
        if (result) {
          const { Result, Paging, GroupItems } = result;
          dimensionDetails.Result = Result;
          dimensionDetails.Paging = Paging;
          dimensionDetails.GroupItems = GroupItems;
        }
      }
    } catch (error) {
      return HitDetailsBaseResponse.failed('关联方风险接口返回status != 200', DimensionSourceEnums.CompanyDetail);
    }
    return dimensionDetails;
  }

  /**
   * 金融机构
   * @param data
   * @param dimension
   * @param sourcePath
   * @public
   */
  public async getFinancialInstitution(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const { keyNo } = data;

    const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.financialInstitutionType);

    const result = await this.companyDetailsService.getFinancialInstitutions(keyNo);
    if (result?.Result?.length) {
      const currTypes = result.Result[0]?.DataTypeInfo?.map((t) => t.Code);
      // const currTypeName = result.Result[0].DataTypeInfo.map((t) => t.Desc);
      const hit = getCompareResultForArray(typeField.compareType, typeField.fieldValue, currTypes);
      if (hit) {
        const Result = [
          {
            description: `该企业为重点金融机构满足设置条件要求`,
            dataTypeInfo: result.Result[0].DataTypeInfo,
          },
        ];
        return Object.assign(dimensionDetails, pick(result, ['Paging']), { Result });
      }
    }
    return dimensionDetails;
  }

  public async getQCCCreditRate(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, dimensionDetails: HitDetailsBaseResponse) {
    const creditRate = await this.companySearchService.getCreditRate(data.keyNo);
    if (creditRate?.Score) {
      const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.qccCreditScore);
      const hit = getCompareResult(creditRate.Score, typeField.fieldValue[0], typeField.compareType, typeField.fieldValue[1]);
      if (hit) {
        return Object.assign(dimensionDetails, {
          Result: [pick(creditRate, ['Score', 'ScoreLevel', 'ScoreDesc', 'ScoreDescInfo', 'ScoreDescInfoV2', 'ChangeTips', 'UpdateDate', 'AnalysisReason'])],
          Paging: { TotalRecords: 1 },
        });
      }
    }
    return dimensionDetails;
  }

  //股权结构
  public async getEquityStructureAbnormal(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, sourcePath: string) {
    const circularShareholderField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CircularShareholdingBetweenRelatedParties);
    const targetValue = circularShareholderField?.fieldValue[0] || 1;
    const targetOper = circularShareholderField?.compareType || DimensionFieldCompareTypeEnums.Equal;
    const loopResp = await this.nebulaGraphHelper.getLoopInvestment(3, data.keyNo, sourcePath);
    const circularSourceValue = loopResp?.length > 0 ? 1 : 0;
    return getCompareResult(circularSourceValue, targetValue, targetOper);
  }

  public async getPrimaryShareholderAbnormality(data: HitEnterpriseDimensionQueryParam) {
    let hit = false;
    const partnerList = await this.personHelper.getPartnerList(data.keyNo, 'all');
    if (partnerList.length > 0) {
      hit = false;
      const partnershipList = partnerList?.filter((t) => t?.name?.includes('（有限合伙）'));
      const personList = partnerList?.filter((t) => t?.keyNo?.startsWith('p'));
      //判断一级股东是否只有合伙人股东
      if (partnerList.length === partnershipList?.length && personList.length === 0) {
        const investList = await this.getInvestorSingleAppBdV3(
          partnerList.map((x) => x.keyNo),
          [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
        );
        if (investList?.length > 0) {
          const companyGraphResult = await this.getCompanyGraph(data.keyNo);
          if (companyGraphResult?.Result?.IndustryChains?.length) {
            // 获取产业链上的核心企业
            const investorKeyNos = partnerList.map((x) => x.keyNo);
            const hitKeyNos = await this.getIndustrialChainCoreCompany(companyGraphResult?.Result?.IndustryChains, '（有限合伙）');
            if (!(investorKeyNos?.length && hitKeyNos?.length && _.intersection(investorKeyNos, hitKeyNos).length)) {
              hit = true;
            }
          }
        } else {
          hit = true;
        }
      }
    }

    return hit;
  }

  public async getSecondaryShareholderAbnormality(data: HitEnterpriseDimensionQueryParam) {
    let hit = false;
    const partnerList = await this.personHelper.getPartnerList(data.keyNo, 'all');
    if (partnerList.length > 0) {
      hit = false;
      const partnershipList = partnerList?.filter((t) => t?.name?.includes('（有限合伙）'));
      const personList = partnerList?.filter((t) => t?.keyNo?.startsWith('p'));
      //判断是否有法人股东
      if (partnerList.length > partnershipList?.length && personList.length === 0) {
        const personData = partnerList.filter((t) => !t?.name?.includes('（有限合伙）'));
        for (const personItem of personData) {
          const personDateLevel2List = await this.personHelper.getPartnerList(personItem.keyNo, 'all');
          const partnershipLevel2List = personDateLevel2List?.filter((t) => t?.name?.includes('（有限合伙）'));
          if (partnershipLevel2List?.length === personDateLevel2List.length) {
            const investList = await this.getInvestorSingleAppBdV3(
              partnershipLevel2List.map((x) => x.keyNo),
              [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
            );
            if (investList?.length > 0) {
              const companyGraphResult = await this.getCompanyGraph(data.keyNo);
              if (companyGraphResult?.Result?.IndustryChains?.length) {
                // 获取产业链上的核心企业
                const investorKeyNos = partnershipLevel2List.map((x) => x.keyNo);
                const hitKeyNos = await this.getIndustrialChainCoreCompany(companyGraphResult?.Result?.IndustryChains, '（有限合伙）');
                if (!(investorKeyNos?.length && hitKeyNos?.length && _.intersection(investorKeyNos, hitKeyNos).length)) {
                  hit = true;
                  break;
                }
              }
              break;
            } else {
              hit = true;
              break;
            }
          }
        }
      }
    }
    return hit;
  }

  public async getEmployeeStockPlatform(data: HitEnterpriseDimensionQueryParam, dimension: DimensionHitStrategyPO, dimensionDetails: HitDetailsBaseResponse) {
    const employeeStockPlatformField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.HasEmployeeStockPlatform);

    //如果当前公司股权结构异常 则不命中员工持股平台
    const flag1 = await this.getEquityStructureAbnormal(data, dimension, '/deep/loopInvestment');
    const flag2 = await this.getPrimaryShareholderAbnormality(data);
    const flag3 = await this.getSecondaryShareholderAbnormality(data);
    if (flag1 || flag2 || flag3) {
      return HitDetailsBaseResponse.ok();
    }

    const partnerList = await this.personHelper.getPartnerList(data.keyNo, 'all');
    //董监高
    const personList = await this.personHelper.getEmployeeList(data.keyNo);
    //法人
    const legalPerson = await this.personHelper.getLegalPerson(data.keyNo);

    const pKeyNoPartners = partnerList.filter((x) => x.keyNo?.startsWith('p'));
    const companyPartners = partnerList.filter((x) => x.keyNo && !x.keyNo.startsWith('p'));
    const allRelatedPersons = [...pKeyNoPartners.map((x) => x?.keyNo), ...personList.map((x) => x?.keyNo), ...legalPerson.map((x) => x?.keyNo)];

    const employeeStockPlatformList = [];
    if (companyPartners.length > 0) {
      for (const item of companyPartners) {
        const personData = await this.personHelper.getPartnerList(item.keyNo);
        if (personData.length > 0) {
          if (personData.some((x) => allRelatedPersons.includes(x.keyNo)) && personData.filter((m) => !allRelatedPersons.includes(m.keyNo)).length > 0) {
            employeeStockPlatformList.push(item.keyNo);
          }
        }
      }
    }
    const sourceValue = employeeStockPlatformList.length > 0 ? 1 : 0;
    const hit = getCompareResult(sourceValue, employeeStockPlatformField.fieldValue[0], employeeStockPlatformField.compareType);
    if (hit) {
      return Object.assign(dimensionDetails, {
        Result: employeeStockPlatformList,
        Paging: { TotalRecords: employeeStockPlatformList.length },
      });
    }
    return dimensionDetails;
  }

  public async getShareholdingChangeFrequency(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    dimensionDetails: HitDetailsBaseResponse,
    sourcePath: string,
  ) {
    const strategyFieldByKey = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityChangeFrequency);
    // 分页查询参数初始化
    let currentPage = 1;
    const pageSize = 50;
    const allDataList = [];

    do {
      const resp = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
        keyNo: data.keyNo,
        changeItem: '003001', //股东、发起人变更
        pageIndex: currentPage,
        pageSize: pageSize,
      });

      // 累积分页数据
      if (resp?.Result?.length) {
        allDataList.push(...resp.Result);
      } else {
        break;
      }
      // 当返回结果小于 pageSize 时终止循环
      if (resp?.Result?.length < pageSize) break;

      currentPage++;
    } while (true); // 当返回结果小于pageSize时停止查询

    //根据changeDate过滤时间范围数据 && 根据changeItem过滤投资人变更
    const cycleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.naturalCycle);
    const cycle = cycleField?.fieldValue ? (cycleField.fieldValue?.[0] as number) : 0;
    const timestamp = getStartTimeByNaturalCycle(cycle) / 1000;
    const filterList = allDataList?.filter((x) => x.ChangeDate >= timestamp);
    const changeList = [];
    filterList.forEach((item) => {
      // 先过滤存在 PercentDiff 的条目
      const withPercentDiff = item?.AfterInfo?.filter((x) => x.PercentDiff !== undefined);

      // 检查所有含PercentDiff的条目是否都符合keyNo要求，且至少存在一条数据
      const allValid = withPercentDiff.length > 0 && withPercentDiff.every((x) => x.KeyNo?.startsWith('p'));

      if (allValid) {
        changeList.push(item);
      }
    });
    const hit = getCompareResult(changeList.length, strategyFieldByKey.fieldValue[0], strategyFieldByKey.compareType);

    if (hit) {
      return Object.assign(dimensionDetails, {
        Result: changeList,
        Paging: { TotalRecords: changeList.length },
      });
    }
    return dimensionDetails;
  }

  public async getMainMembersChangeFrequency(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    dimensionDetails: HitDetailsBaseResponse,
    sourcePath: string,
  ) {
    const strategyFieldByKey = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.MainMembersChangeFrequency);
    // 分页查询参数初始化
    let currentPage = 1;
    const pageSize = 50;
    const allDataList = [];

    do {
      const resp = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
        keyNo: data.keyNo,
        pageIndex: currentPage,
        pageSize: pageSize,
      });

      // 累积分页数据
      if (resp?.Result?.length) {
        allDataList.push(...resp?.Result);
      } else {
        break;
      }
      // 当返回结果小于 pageSize 时终止循环
      if (resp.Result.length < pageSize) break;

      currentPage++;
    } while (true); // 当返回结果小于pageSize时停止查询

    const cycleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.naturalCycle);
    const cycle = cycleField?.fieldValue ? (cycleField.fieldValue?.[0] as number) : 0;
    const timestamp = getStartTimeByNaturalCycle(cycle) / 1000;
    const filter = allDataList.filter(
      (x) =>
        (x.ChangeItem.includes('董事') || x.ChangeItem.includes('监事') || x.ChangeItem.includes('经理') || x.ChangeItem.includes('法定代表人')) &&
        x.ChangeDate >= timestamp,
    );

    const hit = getCompareResult(filter.length, strategyFieldByKey.fieldValue[0], strategyFieldByKey.compareType);

    if (hit) {
      return Object.assign(dimensionDetails, {
        Result: filter,
        Paging: { TotalRecords: filter.length },
      });
    }

    return dimensionDetails;
  }

  public async getRelatedCompanyMassRegistrationCancellation(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    dimensionDetails: HitDetailsBaseResponse,
    sourcePath: string,
  ) {
    // 取所有关联方企业
    const res = await this.httpUtils.postRequest(this.configService.proxyServer.graphService + '/api/EREGraph/GetGraph', {
      keyNo: data.keyNo,
    });
    // 获取所有关联方企业的KeyNo并查询注册以及注销信息
    const keyNoList = this.extractNonPersonalIds(res?.Result);
    const targetCompanyDetails: ESResponse<KysCompanyResponseDetails> = await this.companySearchService.companySearchForKys(
      Object.assign(new KysCompanySearchRequest(), {
        pageIndex: 1,
        pageSize: keyNoList.length,
        includeFields: ['id', 'name', 'statuscode', 'startdatecode'],
        filter: { ids: keyNoList },
      }),
    );

    // const relatedPartyGroupPOS = await this.nebulaGraphHelper.getCompanyRelatedParties(req, sourcePath);
    const cycleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.naturalCycle);
    const cycle = cycleField?.fieldValue ? (cycleField.fieldValue?.[0] as number) : 0;
    const timestamp = getStartTimeByNaturalCycleMonth(cycle) / 1000;
    const hitCountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);

    const result = [];
    //过滤注册三个月内大于目标数量
    const createTimeFilter = targetCompanyDetails?.Result?.filter((x) => dateToTimestamp(x.startdatecode.toString(), false) >= timestamp);
    if (createTimeFilter.length) {
      //根据注册时间正序
      createTimeFilter.sort((a, b) => {
        return dateToTimestamp(a.startdatecode.toString(), false) - dateToTimestamp(b.startdatecode.toString(), false);
      });
      //取一条数据后推三个月并过滤如果大于hitCount则返回true
      for (const x of createTimeFilter) {
        const threeMonthsLater =
          moment(dateToTimestamp(x.startdatecode.toString(), false) * 1000)
            .add(3, 'months')
            .valueOf() / 1000;
        const hitCountList = createTimeFilter.filter(
          (y) =>
            dateToTimestamp(y.startdatecode.toString(), false) <= threeMonthsLater &&
            dateToTimestamp(y.startdatecode.toString(), false) >= dateToTimestamp(x.startdatecode.toString(), false),
        );
        if (hitCountList.length && hitCountList.length >= hitCountField.fieldValue[0]) {
          result.push(hitCountList);
          break;
        }
      }
    }
    //过滤注销并且过滤注销时间
    const cancelLoads = targetCompanyDetails?.Result?.filter((x) => [99, 90].includes(Number(x.statuscode)));
    const cancelLoadDates = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/ECILocal/GetDetailsAllInOne', {
      keyNos: cancelLoads.map((x) => x.id),
      selection: ['CheckDate'],
    });
    if (cancelLoadDates.Result?.length > 0) {
      cancelLoadDates.Result.sort((a, b) => {
        return a.CheckDate - b.CheckDate;
      });
      for (const x of cancelLoadDates.Result) {
        const threeMonthsLater =
          moment(x.CheckDate * 1000)
            .add(3, 'months')
            .valueOf() / 1000;
        const hitCountList = cancelLoadDates.Result?.filter((y) => y.CheckDate <= threeMonthsLater && y.CheckDate >= x.CheckDate);
        if (hitCountList.length >= 1) {
          result.push(hitCountList);
        }
      }
    }

    const countResult = [];
    result.forEach((item) => {
      countResult.push(item.length);
    });
    countResult.sort((a, b) => b - a);

    const hit = getCompareResult(countResult[0] || 0, hitCountField.fieldValue[0], hitCountField.compareType);
    if (hit) {
      return Object.assign(dimensionDetails, {
        Result: result,
        Paging: { TotalRecords: countResult[0] },
      });
    }

    return dimensionDetails;
  }

  public async getProvincialHonor(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    dimensionDetails: HitDetailsBaseResponse,
    sourcePath: string,
  ) {
    const strategyFieldByKey = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
    let total = 0;
    const result = [];

    const [resp, approveClassResp] = await Promise.all([
      await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
        keyNo: data.keyNo,
        approveClass: '2',
        isValid: '1',
        pageIndex: 1,
        pageSize: 10,
      }),
      await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
        keyNo: data.keyNo,
        approveClass: '1',
        isValid: '1',
        pageIndex: 1,
        pageSize: 10,
      }),
    ]);

    if (resp?.Status === 200) {
      total = total + resp?.Paging?.TotalRecords || 0;
      result.push(...resp?.Result);
    }

    if (approveClassResp?.Status === 200) {
      total = total + approveClassResp?.Paging?.TotalRecords || 0;
      result.push(...approveClassResp?.Result);
    }
    const hit = getCompareResult(total, strategyFieldByKey.fieldValue[0], strategyFieldByKey.compareType);

    if (hit) {
      return Object.assign(dimensionDetails, {
        Result: result,
        Paging: { TotalRecords: total },
      });
    }
    return dimensionDetails;
  }

  public async getCertificationRevoked(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    dimensionDetails: HitDetailsBaseResponse,
    sourcePath: string,
  ) {
    const strategyFieldByKey = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.HasCertificationRevoked);
    const cycleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.naturalCycle);
    //目前去周期 + 1 且 已失效的数据
    const cycle = cycleField?.fieldValue ? (cycleField.fieldValue?.[0] as number) + 1 : 0;
    const timestamp = getStartTimeByNaturalCycle(cycle) / 1000;
    let total = 0;
    const invalidResp = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
      isCancelType: true,
      pageIndex: 1,
      pageSize: 1000,
    });

    if (invalidResp?.Status === 200) {
      if (invalidResp?.Paging?.TotalRecords > invalidResp?.Paging?.PageSize) {
        this.logger.info('审核未通过查询荣誉取消个数大于1000');
      }
      const filter = invalidResp?.Result?.filter((x) => x.PublishDate >= timestamp && (x.ApproveClass === 1 || x.ApproveClass === 2));
      total = total + filter?.length || 0;
    }

    const sourceValue = total > 0 ? 1 : 0;
    const hit = getCompareResult(sourceValue, strategyFieldByKey.fieldValue[0], strategyFieldByKey.compareType);

    if (hit) {
      if (sourceValue === 1) {
        return Object.assign(dimensionDetails, {
          Result: invalidResp?.Result,
          Paging: { TotalRecords: total },
        });
      } else {
        return Object.assign(dimensionDetails, {
          Result: [],
          Paging: { TotalRecords: 1 },
        });
      }
    }
    return dimensionDetails;
  }

  public async getCreditCheckCount(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionHitStrategyPO,
    dimensionDetails: HitDetailsBaseResponse,
    sourcePath: string,
  ) {
    const req = {
      keyNos: [],
    };
    // 排查keyNo
    const targetField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetInvestigation);
    const targetValue = targetField?.fieldValue[0] ?? TargetInvestigationEnums.Self;
    switch (targetValue) {
      case TargetInvestigationEnums.Self: {
        req.keyNos.push(data.keyNo);
        break;
      }
      case TargetInvestigationEnums.ActualController: {
        const personData = await this.personHelper.getFinalActualController(data.keyNo, false);
        const keyNos = personData?.filter((x) => x.keyNo)?.map((item) => item.keyNo);
        if (keyNos.length > 0) {
          req.keyNos.push(...keyNos);
        }
        break;
      }
      case TargetInvestigationEnums.MajorityHoldingCompanies: {
        const result = await this.companyDetailsService.getMajorityHeldCompanies(data.keyNo);
        const keyNos = result?.holdingList?.length > 0 ? result?.holdingList?.filter((x) => x.KeyNo).map((x) => x.KeyNo) : [];
        if (keyNos.length > 0) {
          req.keyNos.push(...keyNos);
        }
        break;
      }
      default:
        break;
    }
    if (!req.keyNos.length) {
      return dimensionDetails;
    }
    //排查失信维度
    const scopeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetScope);
    const scopeValue = scopeField?.fieldValue ?? [TargetScopeEnums.ShiXinCount];
    Object.assign(req, {
      selection: scopeValue,
    });

    const resp = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, req);
    Object.assign(dimensionDetails, {
      Result: resp?.Result,
      Paging: { TotalRecords: resp?.Result?.length || 0 },
    });
    return dimensionDetails;
  }

  private extractNonPersonalIds(data: any): string[] {
    const ids: string[] = [];

    // 处理当前层级的Collection
    if (data?.Collection?.length) {
      data.Collection.forEach((item: any) => {
        if (item.Id && !item.Id.startsWith('p')) {
          ids.push(item.Id);
        }
      });
    }

    // 递归处理子节点
    if (data?.Children?.length) {
      data.Children.forEach((child: any) => {
        ids.push(...this.extractNonPersonalIds(child));
      });
    }

    return [...new Set(ids)]; // 去重后返回
  }

  private async fetchAndProcessShiXingData(
    keyNos: string[],
    sourcePath: string,
    reqData: any,
    sortField: string,
    sortOrder: 'ASC' | 'DESC',
    data: HitEnterpriseDimensionQueryParam,
  ) {
    if (!keyNos?.length) return null;

    const shiXingRes = await Bluebird.map(keyNos, async (keyNo) => this.getCompanyShiXingInfo(keyNo, sourcePath, reqData), { concurrency: 1 });

    if (!shiXingRes?.length) return null;

    let GroupItems = [];
    const resultData = [];
    shiXingRes.forEach((t) => {
      resultData.push(...t.resultItems);
      if (t?.GroupItems?.length) GroupItems = t.GroupItems;
    });

    if (!resultData.length) return null;

    const sortResultData = resultData.sort((a, b) => {
      const valueA = a[sortField];
      const valueB = b[sortField];
      if (valueA < valueB) return sortOrder === 'ASC' ? -1 : 1;
      if (valueA > valueB) return sortOrder === 'DESC' ? 1 : -1;
      return 0;
    });

    // 内存分页逻辑
    const pageSize = data?.pageSize || 10;
    const pageIndex = data?.pageIndex || 1;
    return {
      Paging: {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: sortResultData.length,
      },
      Result: sortResultData.slice((pageIndex - 1) * pageSize, pageIndex * pageSize),
      GroupItems,
    };
  }

  private async fetchAndProcessZhiXingData(
    keyNos: string[],
    sourcePath: string,
    reqData: any,
    sortField: string,
    sortOrder: 'ASC' | 'DESC',
    data: HitEnterpriseDimensionQueryParam,
  ) {
    if (!keyNos?.length) return null;

    const zhixingRes = await Bluebird.map(keyNos, async (keyNo) => this.getCompanyZhiXingInfo(keyNo, sourcePath, reqData), { concurrency: 1 });

    if (!zhixingRes?.length) return null;

    let GroupItems = [];
    const resultData = [];
    zhixingRes.forEach((t) => {
      resultData.push(...t.resultItems);
      if (t?.GroupItems?.length) GroupItems = t.GroupItems;
    });

    if (!resultData.length) return null;

    const sortResultData = resultData.sort((a, b) => {
      const valueA = a[sortField];
      const valueB = b[sortField];
      if (valueA < valueB) return sortOrder === 'ASC' ? -1 : 1;
      if (valueA > valueB) return sortOrder === 'DESC' ? 1 : -1;
      return 0;
    });

    // 内存分页逻辑
    const pageSize = data?.pageSize || 10;
    const pageIndex = data?.pageIndex || 1;
    return {
      Paging: {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: sortResultData.length,
      },
      Result: sortResultData.slice((pageIndex - 1) * pageSize, pageIndex * pageSize),
      GroupItems,
    };
  }
  public async getCompanyZhiXingInfo(keyNo: string, sourcePath: string, reqData) {
    const param = Object.assign(reqData, {
      searchKey: keyNo,
      pageSize: 200,
      pageIndex: 1,
      isB: true,
    });
    const limited = 3000;
    let totalFetched = 0;
    const resultItems = [];
    let GroupItems = [];
    do {
      const res1 = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, param);
      if (res1.Result?.length) {
        totalFetched += res1.Result.length;
        resultItems.push(...res1.Result);
      }
      GroupItems = res1?.GroupItems;
      if (!res1.Result?.length || res1.Result.length < param.pageSize || totalFetched >= limited) {
        break;
      }
      param.pageIndex++;
    } while (true);
    return { resultItems, GroupItems };
  }

  private processZhiXingResult(dimensionDetails: HitDetailsBaseResponse, result: any, dimension: DimensionHitStrategyPO) {
    const find1 = result?.GroupItems?.find((e) => e.key === 'companynames');
    if (!find1) return Object.assign(dimensionDetails, pick(result, ['Result', 'Paging', 'GroupItems']));

    const zhixinSum = find1?.items[0]?.['sum'];
    const queryPo = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.executionSum);

    if (queryPo?.fieldValue && zhixinSum) {
      const compareResult = getCompareResult(zhixinSum, queryPo.fieldValue[0], queryPo.compareType || DimensionFieldCompareTypeEnums.GreaterThanOrEqual);
      return compareResult ? Object.assign(dimensionDetails, pick(result, ['Result', 'Paging', 'GroupItems'])) : dimensionDetails;
    }
    return Object.assign(dimensionDetails, pick(result, ['Result', 'Paging', 'GroupItems']));
  }
}
