import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { containsKeywords } from '../../../libs/utils/utils';
import { getCompareResult } from '../../../libs/utils/diligence/diligence.utils';
import { DimensionHitStrategyFieldsEntity } from '../../../libs/entities/DimensionHitStrategyFieldsEntity';
import { excludePenaltyResultRedCardMap, penaltyReasonRedCardMap, penaltyResultRedCardMap } from '../../../libs/constants/supervise.punish.constants';

@Injectable()
export class supervisePunishHelper {
  public readonly logger: Logger = QccLogger.getLogger(supervisePunishHelper.name);

  /**
   * 行政处罚，红牌处罚
   */
  public penaltyRedCardField(penaltyRedCardField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    //取关键案由map中的value集合
    const penFieldTargetValues = penaltyRedCardField.fieldValue as number[];
    const penaltyRedCardList: string[] = [];
    const casefacts = item?.casefacts;
    if (casefacts) {
      if (
        containsKeywords(
          casefacts,
          penaltyReasonRedCardMap.map((t) => t.label),
        )
      ) {
        penaltyRedCardList.push(String(casefacts));
      }
    }
    const punishresult = item?.punishresult;
    if (punishresult) {
      if (
        containsKeywords(
          punishresult,
          penaltyResultRedCardMap.map((t) => t.label),
          excludePenaltyResultRedCardMap.map((t) => t.label),
        )
      ) {
        penaltyRedCardList.push(String(punishresult));
      }
    }

    const penaltyRedCardFieldSourceValue = penaltyRedCardList.length > 0 ? 1 : 0;

    if (
      penFieldTargetValues?.length &&
      penaltyRedCardFieldSourceValue &&
      getCompareResult(penaltyRedCardFieldSourceValue, penFieldTargetValues[0], penaltyRedCardField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }
}
