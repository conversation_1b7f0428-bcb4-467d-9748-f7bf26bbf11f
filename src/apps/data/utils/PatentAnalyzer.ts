// 专利分析工具类
import * as moment from 'moment';

// 专利分析相关的类型定义
export interface Patent {
  ApplicationDate: number; // 申请时间
  KindCode: string; //  专利类型： 过滤发明专利
  Status: string; // 专利状态：是否授权
  HistoryChange?: any[]; // 历史变更列表
  InventorList?: string[]; // 发明人列表
}

export interface PatentStats {
  yearPeriod: number; // 年份
  numerator: number; // 分子
  denominator: number; // 分母
  ratio: number; // 比率
}

export interface PatentAnalysisOptions {
  patentRes: { Result: Patent[] };
  yearPeriodField: any;
  patentTypeField: any;
  filterCondition?: (patent: Patent) => boolean;
}

export class PatentAnalyzer {
  private static calculatePatentRatio(numerator: number, denominator: number): number {
    if (!numerator || !denominator) return 0;
    return parseFloat((numerator / denominator).toFixed(2)) * 100;
  }

  private static getPatentsInPeriod(patents: Patent[], yearPeriod: number): Patent[] {
    if (yearPeriod === -1) return patents;
    // 计算指定年份的起止时间
    // yearPeriod: 1-当年, 2-去年, 3-前年
    const targetYear = moment().subtract(yearPeriod - 1, 'years');
    const startDate = targetYear.startOf('year').unix();
    const endDate = targetYear.endOf('year').unix();
    return patents?.filter((e) => e.ApplicationDate >= startDate && e.ApplicationDate <= endDate) || [];
  }

  private static processPatentStats(patents: Patent[], yearPeriod: number, filterFn?: (patent: Patent) => boolean): PatentStats {
    const periodPatents = this.getPatentsInPeriod(patents, yearPeriod);
    const filteredPatents = filterFn ? periodPatents.filter(filterFn) : periodPatents;

    return {
      yearPeriod,
      numerator: filteredPatents.length,
      denominator: periodPatents.length,
      ratio: this.calculatePatentRatio(filteredPatents.length, periodPatents.length),
    };
  }

  static analyze({ patentRes, yearPeriodField, patentTypeField, filterCondition }: PatentAnalysisOptions): PatentStats[] {
    if (!yearPeriodField || !patentTypeField || !patentRes?.Result?.length) {
      return [];
    }

    const targetValues = patentTypeField.fieldValue;
    const baseFilter = (patent: Patent) => targetValues?.includes(patent.KindCode);
    const finalFilter = filterCondition ? (patent: Patent) => baseFilter(patent) && filterCondition(patent) : baseFilter;

    return yearPeriodField.fieldValue.map((yearPeriod) => this.processPatentStats(patentRes.Result, yearPeriod, finalFilter)).filter(Boolean);
  }
}
