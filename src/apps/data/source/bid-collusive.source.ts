import { Injectable } from '@nestjs/common';
import { BaseEsAnalyzeService } from './base-es-analyze.service';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { ConfigService } from '../../../libs/config/config.service';
import { Client } from '@elastic/elasticsearch';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';

/**
 * 行政处罚ES数据源
 */
@Injectable()
export class BidCollusiveSource extends BaseEsAnalyzeService {
  constructor(private readonly configService: ConfigService) {
    super(
      BidCollusiveSource.name,
      new Client({
        nodes: configService.esConfig.bidCollusive.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.bidCollusive.indexName,
    );
  }

  /**
   * @param dimension
   * @param params
   * @returns
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const resp: HitDetailsBaseResponse = await super.getDimensionDetail(dimension, params, analyzeParams);
    return resp;
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    return resp;
  }

  /**
   *
   * @param companyId
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected getDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    return Promise.resolve(undefined);
  }

  /**
   * 查询两公司间围串标处罚记录
   * @param detailParams
   */
  /* async getBidCollusiveList(detailParams: HitDetailsBidBaseQueryParams): Promise<HitDetailsBaseResponse> {
    const { keyNoAndNames, pageSize, pageIndex, keyNos } = detailParams;
    // 当前仅支持行政处罚数据查询，sourcetype=1
    const boolQuery = {
      bool: {
        filter: [{ terms: { datastatus: [0, 1, 10, 11, 13, 91, 93] } }, { terms: { sourcetype: [1] } }],
        should: [],
        minimum_should_match: 1,
      },
    };
    const subKeyNoBoolQuery = {
      bool: {
        should: [],
        minimum_should_match: 2,
      },
    };
    keyNoAndNames
      .map((item) => item.companyId)
      .forEach((companyId) => {
        subKeyNoBoolQuery.bool.should.push({
          term: {
            collusivekeynos: companyId,
          },
        });
      });
    const subNameBoolQuery = {
      bool: {
        should: [],
        minimum_should_match: 2,
      },
    };
    keyNoAndNames
      .map((item) => item.companyName)
      .forEach((companyName) => {
        subNameBoolQuery.bool.should.push({
          term: {
            collusivenames: companyName,
          },
        });
      });
    boolQuery.bool.should.push(subKeyNoBoolQuery);
    boolQuery.bool.should.push(subNameBoolQuery);
    // console.log(JSON.stringify(boolQuery));
    const response = await this.searchEs(
      {
        query: boolQuery,
        from: 0,
        size: 500,
      },
      keyNos[0],
    );
    return Object.assign(new HitDetailsBaseResponse(), {
      Paging: {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: response?.body?.hits?.total?.value || 0,
      },
      Result: response?.body?.hits?.hits?.map((d) => d._source) || [],
    });
  }

  async getCollusiveRecord(collusiveId: string) {
    const boolQuery = {
      bool: {
        filter: [{ term: { id: collusiveId } }],
      },
    };
    const response = await this.searchEs(
      {
        query: boolQuery,
      },
      collusiveId,
    );
    return response?.body?.hits?.hits?.map((d) => d._source) || [];
  }*/
}
