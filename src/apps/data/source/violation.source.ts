import { Injectable } from '@nestjs/common';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import { getCompareResult } from '../../../libs/utils/diligence/diligence.utils';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import * as Bluebird from 'bluebird';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { WGCLEntity } from '../../../libs/entities/WGCLEntity';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import * as _ from 'lodash';
import { flatten } from 'lodash';
import { PersonHelper } from '../helper/person.helper';
import { PersonData } from '../../../libs/model/data/source/PersonData';
import { Cacheable } from '@type-cacheable/core';
import { IAnalyzeService } from './analyze.interface';

@Injectable()
/** 违规处理 */
export class ViolationSource implements IAnalyzeService<HitDetailsBaseQueryParams, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  private readonly logger: Logger = QccLogger.getLogger(ViolationSource.name);

  constructor(@InjectRepository(WGCLEntity) private readonly wgclRepo: Repository<WGCLEntity>, private readonly personHelper: PersonHelper) {}

  async analyze(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    return Bluebird.map(DimensionHitStrategyPOs, async (d: DimensionHitStrategyPO) => {
      /**
       * 处理命中描述信息需要的参数
       */
      let hitCount = 0;
      switch (d.key) {
        case DimensionTypeEnums.ViolationProcessings: {
          const { Paging } = await this.getDimensionDetail(
            d,
            { keyNo: companyId },
            Object.assign(new DimensionAnalyzeParamsPO(), { isScanRisk: true, keyNo: companyId }),
          );
          if (Paging.TotalRecords > 0) {
            hitCount = Paging.TotalRecords;
          }
          break;
        }
        default:
          break;
      }

      // 命中记录条数 规则设置
      const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
      if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
        // 不满足 命中记录条数规则 标记未命中
        hitCount = 0;
      }

      if (hitCount > 0) {
        return processDimHitResPO(d, hitCount);
      }
      return null;
    }).then((item) => item.filter((t) => t));
  }

  /**
   * 获取指定维度详情列表
   * @param dimension
   * @param data
   * @returns
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const { keyNo, field, order } = params;
    const pageIndex = params.pageIndex || 1;
    const pageSize = params.pageSize || 5;
    let dimensionDetails = HitDetailsBaseResponse.ok();

    try {
      const qb = this.wgclRepo.createQueryBuilder('wgcl').where('wgcl.keyno = :keyNo', { keyNo }).andWhere('wgcl.isadd != -1');
      // 时间选择
      const cycle = dimension.getCycle();
      if (cycle > 0) {
        // 近3年
        qb.andWhere('wgcl.publicdate >= DATE_SUB(CURDATE(), INTERVAL :cycle YEAR)', { cycle });
      }

      // 人员身份
      const relationShipField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relationShips);
      if (relationShipField?.fieldValue.length) {
        const relationSql = relationShipField?.fieldValue
          .filter((t) => t)
          .map((v) => `FIND_IN_SET('${v}', wgcl.relatedtype)`)
          .join(' or ');

        qb.andWhere(`(${relationSql})`);
      }

      // 排序逻辑
      const dimSortFiled = dimension.getSortField();
      const sortField = field || dimSortFiled?.field || 'publicdate';
      const sortOrder = order || dimSortFiled?.order || 'DESC';

      const [Result, hitCount] = await qb
        .orderBy(`wgcl.${sortField}`, sortOrder)
        .skip((pageIndex - 1) * pageSize)
        .take(pageSize)
        .getManyAndCount();

      if (hitCount > 0) {
        Object.assign(dimensionDetails, {
          Result,
          Paging: {
            PageSize: pageSize,
            PageIndex: pageIndex,
            TotalRecords: hitCount,
          },
        });
      }
    } catch (error) {
      this.logger.error(`Violation getDimensionDetail err: ${error},dimension: ${dimension?.dimensionDef.key}`);
      dimensionDetails = HitDetailsBaseResponse.failed(error.response?.error || error.message, DimensionSourceEnums.Violation, error.response?.code);
    }
    // 后处理数据补充
    dimensionDetails = await this.getDimensionDetailItemData(dimensionDetails, dimension, params, analyzeParams);
    return dimensionDetails;
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    const { keyNo } = params;
    if (resp?.Result?.length) {
      switch (dimension?.key) {
        case DimensionTypeEnums.ViolationProcessings: {
          const relationShipField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relationShips);
          if (relationShipField?.fieldValue?.length) {
            const personData: PersonData[] = await this.getViolationPersonByCompanyId(keyNo, relationShipField.fieldValue);
            resp.Result = resp?.Result?.map((d) => {
              const jobList = _.uniq(
                personData
                  ?.filter((t) => t.keyNo === d.markedmankey)
                  ?.map((t) => {
                    if (t.history && !t.job.includes('历史')) {
                      t.job = '历史' + t.job;
                    }
                    return t;
                  })
                  ?.map((s) => s.job) ?? [],
              );
              if (jobList?.length) {
                const combinedArray = _.flatMap(jobList, (str) => str.split(','));
                const uniqueList = _.uniq(combinedArray);
                Object.assign(d, {
                  job: uniqueList.join(','),
                });
              }
              return d;
            });
          }
          break;
        }
      }
    }
    return resp;
  }

  @Cacheable({ ttlSeconds: 100 })
  public async getViolationPersonByCompanyId(companyId: string, relationShipField: string[]): Promise<PersonData[]> {
    const tasks = [];
    relationShipField.forEach((value) => {
      switch (value) {
        // 法人
        case '1': {
          tasks.push(this.personHelper.getLegalPerson(companyId));
          break;
        }
        // 历史法定代表人
        case '2': {
          tasks.push(this.personHelper.getHisLegalPerson(companyId));
          break;
        }
        // 获取(当前)主要人员（最新公示+工商登记） 董监高
        case '3': {
          tasks.push(this.personHelper.getEmployeeList(companyId));
          break;
        }
        // 获取(历史)主要人员（最新公示+工商登记） 董监高
        // 历史股东
        case '4':
        case '6': {
          tasks.push(this.personHelper.getHisEmployeeData(companyId));
          break;
        }
        // 股东
        case '5': {
          tasks.push(this.personHelper.getPartnerList(companyId));
          break;
        }
        // 最终实际控制人
        case '7': {
          tasks.push(this.personHelper.getFinalActualController(companyId));
          break;
        }
        //受益自然人
        case '9': {
          tasks.push(this.personHelper.getBenefitList(companyId, false));
          break;
        }
      }
    });
    const personData: PersonData[] = flatten(await Bluebird.all(tasks));
    return personData;
  }
}
