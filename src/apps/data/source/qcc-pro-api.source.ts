/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { ConfigService } from 'libs/config/config.service';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import * as Bluebird from 'bluebird';
import { isOrganism } from '../../company/utils';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { ProDiligenceResponse, ProDiligenceResultItem } from '../../../libs/model/diligence/req&res/ProDiligenceResponse';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import { ProHelper } from '../helper/pro.helper';
import { IAnalyzeService } from './analyze.interface';

/**
 * 专业版数据源接口
 */
@Injectable()
export class QccProApiSource implements IAnalyzeService<HitDetailsBaseQueryParams, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  private readonly logger: Logger = QccLogger.getLogger(QccProApiSource.name);

  constructor(private readonly configService: ConfigService, private readonly proHelper: ProHelper) {}

  async analyze(
    companyId: string,
    dimensions: DimensionHitStrategyPO[],
    params: DimensionAnalyzeParamsPO,
    _?: DimensionAnalyzeParamsPO,
  ): Promise<DimensionHitResultPO[]> {
    const { companyName } = params;
    const proDetail: ProDiligenceResponse = await this.proHelper.getCompanyRiskDetial(companyName);
    return Bluebird.map(
      dimensions,
      async (d: DimensionHitStrategyPO) => {
        const proItem = proDetail?.result?.data?.find((item) => item?.typeCode === d.dimensionDef.typeCode || item?.title === d.dimensionDef.name);
        let totalHits = 0;
        if (proItem) {
          if (proItem.hasDetail != 0) {
            const de = await this.getDimensionDetail(d, {
              keyNo: companyId,
              companyName,
              pageIndex: 1,
              pageSize: 1,
            });
            totalHits = de?.Paging?.TotalRecords;
          } else {
            totalHits = 1;
          }
        } else if (isOrganism(companyId) && d.key === DimensionTypeEnums.BusinessAbnormal1) {
          const de = await this.getDimensionDetail(d, {
            keyNo: companyId,
            companyName,
            pageIndex: 1,
            pageSize: 5,
          });
          totalHits = de?.Paging?.TotalRecords;
        }

        if (totalHits) {
          if (d.key === DimensionTypeEnums.QfkRisk7099) {
            return processDimHitResPO(d, totalHits, {}, false);
          }
          return processDimHitResPO(d, totalHits, proItem.description, true);
        }

        //proItem存在或者（proItem不存在且当前为社会组织）

        // if (proItem || (!proItem && isOrganism(companyId) && d.key === DimensionTypeEnums.BusinessAbnormal1)) {
        //   //TODO 需要获取到真实的totalHits
        //   const de = await this.getDimensionDetail(d, {
        //     keyNo: companyId,
        //     companyName,
        //     pageIndex: 1,
        //     pageSize: 5,
        //   });
        //   totalHits = de?.Paging?.TotalRecords;
        //   if (totalHits) {
        //     return processDimHitResPO(d, totalHits, proItem.description, true);
        //   }
        //   // return this.matched(d, totalHits, proItem?.detailId);
        // }
        return null;
      },
      { concurrency: 3 },
    ).then((item) => item.filter((t) => t));
  }

  async getDimensionDetail(dimension: DimensionHitStrategyPO, data: HitDetailsBaseQueryParams): Promise<HitDetailsBaseResponse> {
    const { keyNo, companyName, pageIndex, pageSize } = data;
    const sourcePath = dimension?.dimensionDef.detailSourcePath ? dimension?.dimensionDef.detailSourcePath : dimension?.dimensionDef.sourcePath;
    let response = HitDetailsBaseResponse.ok();
    try {
      if (dimension.key === DimensionTypeEnums.CompanyShell) {
        const result = await this.proHelper.getCompanyShellDetail(companyName);
        if (result.status === '200' && result?.result?.data.length) {
          await Bluebird.map(result?.result?.data, async (item: ProDiligenceResultItem) => {
            if (item.hasDetail == 1 && item.detailId) {
              let data;
              switch (item.title) {
                case '注册基础信息重叠': {
                  data = await this.proHelper.getSameExecDetail(item.detailId);
                  break;
                }
                case '注册信息相似度过高':
                  data = await this.proHelper.getSimilarExecDetail(item.detailId);
                  break;
                case '一人多企':
                  data = await this.proHelper.getOperExceptionDetail(item.detailId);
                  break;
                case '一址多企':
                  data = await this.proHelper.getSameAddressDetail(item.detailId);
                  break;
                case '无法联系该企业':
                  data = await this.proHelper.getAddrExceptionDetail(item.detailId);
                  break;
                case '未公示年报':
                  data = await this.proHelper.getAnnReportPubDetail(item.detailId);
                  break;
                case '企业自然人变更时间集中':
                  data = await this.proHelper.getHistPersonDetail(item.detailId);
                  break;
                default:
                  break;
              }
              Object.assign(item, { data });
            }
          });

          Object.assign(response, {
            Result: result?.result?.data,
            Paging: { TotalRecords: result?.result?.data.length },
          });
        } else {
          //获取失败？
          response = HitDetailsBaseResponse.failed('接口返回status != 200', DimensionSourceEnums.Pro);
        }
        return response;
      }

      let proItem: ProDiligenceResultItem;

      if (!isOrganism(keyNo)) {
        const proDetail: ProDiligenceResponse = await this.proHelper.getCompanyRiskDetial(companyName);
        if (proDetail?.status !== '200') {
          response = HitDetailsBaseResponse.failed('获取尽调结果失败', DimensionSourceEnums.Pro);
        }
        proItem = proDetail?.result?.data?.find((item) => item?.typeCode === dimension.dimensionDef.typeCode || item?.title === dimension.dimensionDef.name);
      }
      if (proItem) {
        if (proItem.hasDetail == 0) {
          return Object.assign(response, {
            Result: [{ dimensionDesc: proItem?.description }],
            Paging: {
              TotalRecords: 1,
              PageIndex: 1,
              PageSize: pageSize,
            },
          });
        }
        // const companyDetail = await this.companySearchService.companyDetailsQcc(keyNo);
        switch (dimension?.key) {
          case DimensionTypeEnums.QfkRisk6802:
            if (sourcePath) {
              const result = await this.proHelper.getCompanyQfkRisk(proItem.detailId, sourcePath, pageIndex, pageSize);
              if (proItem?.description) {
                result.result['dimensionDesc'] = proItem?.description;
              }
              if (result.status === '200') {
                Object.assign(response, {
                  Result: [result.result],
                  Paging: {
                    TotalRecords: 1,
                    PageIndex: 1,
                    PageSize: pageSize,
                  },
                });
              }
            }
            break;
          default:
            if (sourcePath) {
              const result = await this.proHelper.getCompanyQfkRisk(proItem.detailId, sourcePath, pageIndex, pageSize);
              if (result.status === '200') {
                const { resultList, paging } = result;
                if (proItem?.description && dimension.key !== DimensionTypeEnums.QfkRisk7099) {
                  resultList.forEach((item) => {
                    item['dimensionDesc'] = proItem?.description;
                  });
                }
                if (dimension.key === DimensionTypeEnums.QfkRisk2310) {
                  // 2310 特殊处理, 计算百分比数值用于后续排序
                  resultList.forEach((item) => {
                    item['percentTotalNumber'] = Number(item.percentTotal?.replace('%', ''));
                  });
                }
                if (dimension.key === DimensionTypeEnums.QfkRisk2210) {
                  // 2210 特殊处理, 计算百分比数值用于后续排序
                  resultList.forEach((item) => {
                    if (item.stockPercent && item.stockPercent.includes('%')) {
                      item['stockPercentNumber'] = Number(item.stockPercent?.replace('%', ''));
                    } else {
                      item['stockPercentNumber'] = 0;
                    }
                    if (item.regCap && (item.regCap.includes('万元人民币') || item.regCap.includes('万元'))) {
                      item['regCapNumber'] = Number(item.regCap?.replace('万元人民币', '').replace('万元', ''));
                    } else {
                      item['regCapNumber'] = 0;
                    }
                  });
                }
                Object.assign(response, {
                  Result: resultList,
                  Paging: {
                    TotalRecords: paging?.totalRecords || resultList?.length || 0,
                    PageIndex: paging?.pageIndex || 1,
                    PageSize: paging?.pageSize || pageSize,
                  },
                });
              } else {
                response = HitDetailsBaseResponse.failed('接口返回status != 200', DimensionSourceEnums.Pro);
              }
            } else {
              response = HitDetailsBaseResponse.failed('未知的数据维度且没有配置sourcePath');
            }
            break;
        }
      }
    } catch (error) {
      this.logger.error(`ProService getDimensionDetail err: ${error}, dimension: ${JSON.stringify(dimension)}`);
      // response = HitDetailsBaseResponse.failed(error.message.handler, DimensionSourceEnums.Pro);
      response = HitDetailsBaseResponse.failed(error.response?.error || error.message, DimensionSourceEnums.Pro, error.response?.code);
    }
    return response;
  }
}
