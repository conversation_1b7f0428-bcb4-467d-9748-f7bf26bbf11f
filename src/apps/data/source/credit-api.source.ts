import { BadRequestException, Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { ConfigService } from 'libs/config/config.service';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { isNumber, pick } from 'lodash';
import { CreditBaseFilter } from 'libs/model/data/source/CreditRequestBaseParam';
import { DateRangeRelative } from 'libs/model/data/source/CreditSearchFilter';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import * as Bluebird from 'bluebird';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { HitDetailsCreditParam } from '../../../libs/model/diligence/details/request';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { getAmountConditions } from '../../../libs/utils/diligence/diligence.utils';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyFieldsEntity } from '../../../libs/entities/DimensionHitStrategyFieldsEntity';
import { TargetInvestigationEnums } from '../../../libs/enums/dimension/FieldValueEnums';
import { CompanySearchService } from '../../company/company-search.service';
import { CompanyDetailService } from '../../company/company-detail.service';
import { PersonHelper } from '../helper/person.helper';
import { IAnalyzeService } from './analyze.interface';

/**
 * 信用大数据数据源接口
 */
@Injectable()
export class CreditApiSource implements IAnalyzeService<HitDetailsCreditParam, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  private readonly logger: Logger = QccLogger.getLogger(CreditApiSource.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpUtils: HttpUtilsService,
    private readonly personHelper: PersonHelper,
    private readonly companyService: CompanySearchService,
    private readonly companyDetailsService: CompanyDetailService,
  ) {}

  async analyze(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[], params: DimensionAnalyzeParamsPO): Promise<DimensionHitResultPO[]> {
    throw new BadRequestException('not supported');
  }

  // @Cacheable({ ttlSeconds: 300 })
  async getDimensionDetail(dimension: DimensionHitStrategyPO, requestData: HitDetailsCreditParam): Promise<HitDetailsBaseResponse> {
    this.logger.info(`get hit details for dimension: ${dimension.key}`);
    if (!requestData?.keyNo) {
      throw new BadParamsException(RoverExceptions.Diligence.Detail.NeedKeyNo);
    }
    //查询参数使用优先级： 1. data 参数 2. dimension.strategyModel 如果有值，需要生成条件
    //根据key获取到category，拼接到data中
    let key = dimension.key;
    // 如果是 存在产品质量问题 必须传 subDimensionKey, 返回对应的子维度
    if (key === DimensionTypeEnums.ProductQualityProblem) {
      if (!requestData?.subDimensionKey) {
        throw new BadParamsException(RoverExceptions.Diligence.Detail.NeedSubDimensionKey);
      }
      key = requestData.subDimensionKey;
    }

    const reqData = await this.requestDataConstructor(dimension, requestData);
    if (!reqData) {
      return HitDetailsBaseResponse.failed(RoverExceptions.BadParams.Common.error, DimensionSourceEnums.CreditAPI, RoverExceptions.BadParams.Common.code);
    }
    try {
      const result = await this.httpUtils.postRequest(this.configService.dataServer.searchCredit, reqData);
      switch (dimension.key) {
        case DimensionTypeEnums.MainMembersPersonCreditCurrent:
        case DimensionTypeEnums.MainMembersRestrictedOutbound:
        case DimensionTypeEnums.MainMembersRestrictedConsumptionCurrent: {
          const { personJobSet } = await this.personHelper.getCompanyExecutivesKeyNosV2(requestData.keyNo);
          result?.data?.Result?.forEach((x) => {
            x?.SubjectInfo?.forEach((p) => {
              p['Job'] = personJobSet[p?.KeyNo];
            });
          });
          break;
        }
        case DimensionTypeEnums.EnvironmentalPenalties: {
          result?.data?.Result?.forEach((x) => {
            if (x?.FileUrl?.includes(',')) {
              x.FileUrl = x.FileUrl.split(',')[0];
            }
          });
          break;
        }
      }
      return Object.assign(new HitDetailsBaseResponse(), pick(result.data, ['Result', 'Paging', 'GroupItems']));
    } catch (error) {
      this.logger.error(`CreditService getDimensionDetail err: ${error}`);
      return HitDetailsBaseResponse.failed(error.response?.error || error.message, DimensionSourceEnums.CreditAPI, error.response?.code);
    }
  }

  /**
   * 构造信用大数据请求参数
   * @param key
   */
  async requestDataConstructor(dimension: DimensionHitStrategyPO, data: Record<string, any>) {
    const { pageSize, pageIndex } = data;
    const resultRequestData: Record<string, any> = {};
    const strategySortField = dimension.getSortField();
    const sortField = strategySortField?.field || 'publishdate';
    const isSortAsc = strategySortField?.order == 'ASC' ? true : false;
    resultRequestData['sortField'] = sortField;
    resultRequestData['isSortAsc'] = isSortAsc;
    const filterParam = new CreditBaseFilter();
    if (data.keyNo) {
      filterParam.keynos = [data.keyNo];
    }
    //统计周期
    const cycle = dimension.getCycle();
    if (cycle > 0) {
      if (data.publishdate?.length > 0) {
        filterParam.publishdate = data.publishdate;
      } else {
        filterParam.publishdate = await this.getPublishDate(cycle || 1);
      }
    }

    const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
    if (isValidField?.fieldValue[0] >= 0) {
      filterParam.isValid = isValidField.fieldValue[0] + '';
    } else {
      //不限的时候不传isValid参数
      delete filterParam.isValid;
    }

    if (dimension.strategyFields?.length) {
      await Bluebird.map(dimension.strategyFields, async (sf: DimensionHitStrategyFieldsEntity) => {
        switch (sf?.dimensionFieldKey) {
          case DimensionFieldKeyEnums.penaltiesAmount: //处罚金额
          case DimensionFieldKeyEnums.equityAmount: //股权数额
          case DimensionFieldKeyEnums.taxArrearsAmount: //欠税金额
          case DimensionFieldKeyEnums.registrationAmount: //注册金额
            if (data.amout) {
              filterParam.amount = data.amout;
            } else if (isNumber(sf?.fieldValue[0])) {
              //金额筛选条件 isNumber
              let fieldVal = Number(sf?.fieldValue[0]);
              if (sf.dimensionFieldKey == DimensionFieldKeyEnums.equityAmount) {
                // 股权数额 配置使用万元为单位，信用接口支持的是元，
                fieldVal = fieldVal * 10000;
              }
              filterParam.amount = getAmountConditions(sf.compareType, fieldVal);
            }
            break;
          case DimensionFieldKeyEnums.penaltiesType: //处罚类型
            break;
          case DimensionFieldKeyEnums.executionTarget: //执行标的
            break;
          case DimensionFieldKeyEnums.capitalReduction: //资本降幅
            break;
          case DimensionFieldKeyEnums.changeThreshold: //变更阈值
            break;
          case DimensionFieldKeyEnums.duration: //成立时长
            break;
        }
      });
    }

    const businessAbnormalType = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessAbnormalType);

    switch (dimension.key) {
      case DimensionTypeEnums.OperationAbnormal: // 多次被列入经营异常名录【当下未列入】
        filterParam.isValid = '0';
        if (businessAbnormalType?.fieldValue?.length > 0) {
          filterParam.creditType = [{ category: '08', child: businessAbnormalType.fieldValue }];
        } else {
          filterParam.creditType = [{ category: '08' }];
        }
        break;
      case DimensionTypeEnums.BusinessAbnormal3: // 被列入经营异常名录
        filterParam.isValid = '1';
        if (businessAbnormalType?.fieldValue?.length > 0) {
          filterParam.creditType = [{ category: '08', child: businessAbnormalType.fieldValue }];
        } else {
          filterParam.creditType = [{ category: '08' }];
        }
        break;
      case DimensionTypeEnums.CancellationOfFiling: // 注销备案
        filterParam.creditType = [{ category: '24' }];
        filterParam.isValid = '1';
        break;
      case DimensionTypeEnums.PersonCreditCurrent:
        filterParam.creditType = [{ category: '02' }];
        filterParam.isValid = '1';
        break;
      case DimensionTypeEnums.PersonCreditHistory:
        filterParam.creditType = [{ category: '02' }];
        filterParam.isValid = '0';
        break;
      case DimensionTypeEnums.RestrictedConsumptionCurrent:
        //  排查对象
        if (data?.keyNo) {
          const fieldValue = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetInvestigation)?.fieldValue?.[0] ?? TargetInvestigationEnums.Self;
          const perKeyNos: string[] = [];
          switch (fieldValue) {
            case TargetInvestigationEnums.Legal: {
              const companyDetail = await this.companyService.companyDetailsQcc(data.keyNo);
              if (companyDetail?.Oper?.KeyNo) {
                perKeyNos.push(companyDetail.Oper.KeyNo);
              }
              break;
            }
            case TargetInvestigationEnums.HisLegal: {
              const res = await this.companyDetailsService.getCoyHistoryInfo(data.keyNo);
              if (res?.Result?.OperList?.length) {
                const keyNos: string[] = res?.Result?.OperList?.filter((t) => !!t.KeyNo)?.map((Oper) => Oper.KeyNo);
                if (keyNos?.length) {
                  perKeyNos.push(...keyNos);
                }
              }
              break;
            }
            case TargetInvestigationEnums.Self: {
              perKeyNos.push(data.keyNo);
              break;
            }
          }
          if (perKeyNos?.length > 0) {
            filterParam.keynos = perKeyNos;
          } else {
            return null;
          }
        }
        filterParam.creditType = [{ category: '03' }];
        filterParam.isValid = '1';
        break;
      case DimensionTypeEnums.RestrictedConsumptionHistory:
        //  排查对象
        const fieldValue = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetInvestigation)?.fieldValue?.[0] ?? TargetInvestigationEnums.Self;
        const perKeyNos: string[] = [];
        switch (fieldValue) {
          case TargetInvestigationEnums.Legal: {
            const companyDetail = await this.companyService.companyDetailsQcc(data.keyNo);
            if (companyDetail?.Oper?.KeyNo) {
              perKeyNos.push(companyDetail.Oper.KeyNo);
            }
            break;
          }
          case TargetInvestigationEnums.HisLegal: {
            const res = await this.companyDetailsService.getCoyHistoryInfo(data.keyNo);
            if (res?.Result?.OperList?.length) {
              const keyNos: string[] = res?.Result?.OperList?.filter((t) => !!t.KeyNo)?.map((Oper) => Oper.KeyNo);
              if (keyNos?.length) {
                perKeyNos.push(...keyNos);
              }
            }
            break;
          }
          case TargetInvestigationEnums.Self: {
            perKeyNos.push(data.keyNo);
            break;
          }
        }
        if (perKeyNos?.length > 0) {
          filterParam.keynos = perKeyNos;
        } else {
          return null;
        }
        filterParam.creditType = [{ category: '03' }];
        filterParam.isValid = '0';
        break;
      case DimensionTypeEnums.TaxationOffences:
        filterParam.creditType = [{ category: '19' }];
        // if (!filterParam.publishdate) {
        //   filterParam.publishdate = await this.getPublishDate(3);
        // }
        break;
      case DimensionTypeEnums.Bankruptcy: {
        filterParam.creditType = [{ category: '01' }];
        break;
      }
      case DimensionTypeEnums.PersonExecution:
        filterParam.creditType = [{ category: '05' }];
        if (!filterParam.isValid) {
          filterParam.isValid = '0';
        }
        break;
      case DimensionTypeEnums.ContractBreach:
        filterParam.creditType = [{ category: '38' }];
        break;
      case DimensionTypeEnums.EnvironmentalPenalties:
        filterParam.creditType = [{ category: '12' }];
        filterParam.amount = data.amout;
        // 只取当前有效，历史数据不算
        filterParam.isValid = '1';
        if (filterParam?.publishdate) {
          filterParam.currencedate = filterParam.publishdate;
          delete filterParam.publishdate;
        }
        break;
      case DimensionTypeEnums.BondDefaults:
        filterParam.creditType = [{ category: '30' }];
        if (!filterParam.isValid) {
          filterParam.isValid = '0';
        }
        break;
      case DimensionTypeEnums.ProductQualityProblem1: // 存在产品质量问题-产品召回
        filterParam.creditType = [{ category: '16' }];
        break;
      case DimensionTypeEnums.ProductQualityProblem2: // 存在产品质量问题-产品抽查不合格
        filterParam.creditType = [{ category: '34' }];

        break;
      case DimensionTypeEnums.ProductQualityProblem3: // 存在产品质量问题-假冒伪劣产品
        filterParam['cType'] = ['ZL06'];

        // 假冒伪劣产品用的是 currencedate
        if (filterParam.publishdate) {
          filterParam.currencedate = filterParam.publishdate;
          delete filterParam.publishdate;
        }
        break;
      case DimensionTypeEnums.ProductQualityProblem4: // 存在产品质量问题-虚假宣传˝
        filterParam['cType'] = ['ZL07'];
        if (filterParam.publishdate) {
          filterParam.currencedate = filterParam.publishdate;
          delete filterParam.publishdate;
        }
        break;
      case DimensionTypeEnums.ProductQualityProblem5: // 存在产品质量问题-其他质量问题
        filterParam['cType'] = ['ZL99'];
        break;
      case DimensionTypeEnums.ProductQualityProblem6: // 存在产品质量问题-未准入境
        filterParam.creditType = [{ category: '22' }];
        break;
      case DimensionTypeEnums.ProductQualityProblem7: // 存在产品质量问题-药品抽查【检验不合格】
        filterParam.creditType = [{ category: '33' }];
        // if (!filterParam.publishdate) {
        //   filterParam.lianDate = await this.getPublishDate(3);
        // } else {
        //   filterParam.lianDate = filterParam.publishdate;
        //   delete filterParam.publishdate;
        // }
        break;
      // case DimensionTypeEnums.ProductQualityProblem8: // 存在产品质量问题-假冒化妆品
      //   filterParam.creditType = [{ category: '36' }];
      //   if (!filterParam.publishdate) {
      //     filterParam.publishdate = await this.getPublishDate(3);
      //   }
      //   break;
      case DimensionTypeEnums.ProductQualityProblem9: // 存在产品质量问题-食品安全【检查不合格】
        filterParam.creditType = [{ category: '17' }];
        if (filterParam.publishdate) {
          filterParam.lianDate = filterParam.publishdate;
          delete filterParam.publishdate;
        }
        break;
      case DimensionTypeEnums.MainMembersPersonCreditCurrent: {
        // 主要人员被列入失信被执行人（当前有效）
        filterParam.creditType = [{ category: '02' }];
        // filterParam.isValid = getIsValidValue(isValidField?.fieldValue[0]);
        // if (isValidField?.fieldValue[0] == 0) {
        //   filterParam.isValid = HistoryValidNumbers;
        // }
        delete resultRequestData['searchKey'];
        if (data.keyNo) {
          //TODO  可能存在人员列表很多导致es查询条件超出限制， 具体参考任务 RA-4865
          const { personNos: keyNos } = await this.personHelper.getCompanyExecutivesKeyNosV2(data.keyNo, 'person', true, true);
          if (keyNos?.length > 0) {
            filterParam.keynos = keyNos;
          } else {
            return null;
          }
        }
        break;
      }
      case DimensionTypeEnums.MainMembersRestrictedConsumptionCurrent: // 主要人员限制高消费
        filterParam.creditType = [{ category: '03' }];
        filterParam.isValid = '1';
        delete resultRequestData['searchKey'];
        if (data.keyNo) {
          //TODO  可能存在人员列表很多导致es查询条件超出限制， 具体参考任务 RA-4865
          const { personNos: keyNos } = await this.personHelper.getCompanyExecutivesKeyNosV2(data.keyNo, 'person', true, true);
          if (keyNos?.length > 0) {
            filterParam.keynos = keyNos;
          } else {
            return null;
          }
        }
        break;
      case DimensionTypeEnums.MainMembersRestrictedOutbound: // 主要人员限制出境
        filterParam.creditType = [{ category: '04' }];
        filterParam.isValid = '1';
        delete resultRequestData['searchKey'];
        if (data.keyNo) {
          //  排查对象
          const fieldValue = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetInvestigation)?.fieldValue?.[0] ?? TargetInvestigationEnums.Self;
          const perKeyNos: string[] = [];
          switch (fieldValue) {
            case TargetInvestigationEnums.Legal: {
              const companyDetail = await this.companyService.companyDetailsQcc(data.keyNo);
              if (companyDetail?.Oper?.KeyNo) {
                perKeyNos.push(companyDetail.Oper.KeyNo);
              }
              break;
            }
            case TargetInvestigationEnums.HisLegal: {
              const res = await this.companyDetailsService.getCoyHistoryInfo(data.keyNo);
              if (res?.Result?.OperList?.length) {
                const keyNos: string[] = res?.Result?.OperList?.filter((t) => !!t.KeyNo)?.map((Oper) => Oper.KeyNo);
                if (keyNos?.length) {
                  perKeyNos.push(...keyNos);
                }
              }
              break;
            }
            case TargetInvestigationEnums.Self: {
              perKeyNos.push(data.keyNo);
              break;
            }
          }
          if (perKeyNos?.length > 0) {
            filterParam.keynos = perKeyNos;
          } else {
            return null;
          }
        }
        break;
      case DimensionTypeEnums.SubsidiaryPersonCreditCurrent:
        filterParam.creditType = [{ category: '02' }];
        filterParam.isValid = '0';
        if (data.keyNo) {
          const keyNos = await this.companyDetailsService.getBranchKeyNos(data.keyNo);
          if (keyNos?.length > 0) {
            filterParam.keynos = keyNos;
          } else {
            return null;
          }
        }
        break;
      case DimensionTypeEnums.SubsidiaryRestrictedConsumptionCurrent:
        filterParam.creditType = [{ category: '03' }];
        filterParam.isValid = '0';
        if (data.keyNo) {
          const keyNos = await this.companyDetailsService.getBranchKeyNos(data.keyNo);
          if (keyNos?.length > 0) {
            filterParam.keynos = keyNos;
          } else {
            return null;
          }
        }
        break;
      case DimensionTypeEnums.SubsidiaryRestrictedOutbound:
        filterParam.creditType = [{ category: '04' }];
        filterParam.isValid = '0';
        if (data.keyNo) {
          const keyNos = await this.companyDetailsService.getBranchKeyNos(data.keyNo);
          if (keyNos?.length > 0) {
            filterParam.keynos = keyNos;
          } else {
            return null;
          }
        }
        break;
      default:
        break;
    }

    resultRequestData['filter'] = filterParam;
    resultRequestData['pageSize'] = pageSize;
    resultRequestData['pageIndex'] = pageIndex;
    if (data.aggFields) {
      resultRequestData['aggFields'] = data.aggFields;
    }
    return resultRequestData;
  }

  private async getPublishDate(num: number) {
    return [
      Object.assign(new DateRangeRelative(), {
        currently: true,
        flag: 1,
        number: num * 365,
        unit: 'day',
      }),
    ];
  }
}
