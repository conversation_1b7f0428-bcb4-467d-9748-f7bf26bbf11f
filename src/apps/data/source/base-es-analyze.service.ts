import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import { Client, RequestParams } from '@elastic/elasticsearch';
import { find } from 'lodash';
import { AggBucketItemPO } from '../../../libs/model/data/source/credit.analyze/CreditAggBucketItemPO';
import * as Bluebird from 'bluebird';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { getCompareResult } from '../../../libs/utils/diligence/diligence.utils';
import { IAnalyzeService } from './analyze.interface';

export abstract class BaseEsAnalyzeService implements IAnalyzeService<HitDetailsBaseQueryParams, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  protected readonly logger: Logger;
  protected bucketNamePrefix = 'sub_agg_';

  protected constructor(private readonly name: string, private readonly esClient: Client, private readonly indexName: string) {
    this.logger = QccLogger.getLogger(name);
  }

  async analyze(companyId: string, dimensionHitStrategyPOS: DimensionHitStrategyPO[], params?: DimensionAnalyzeParamsPO): Promise<DimensionHitResultPO[]> {
    if (!companyId || !dimensionHitStrategyPOS?.length) {
      return [];
    }

    const query = await this.getQuery(companyId, dimensionHitStrategyPOS, params);
    const aggs = await this.createAggs(companyId, dimensionHitStrategyPOS, params);
    try {
      const response = await this.searchEs({ size: 0, query, aggs }, companyId);
      // if (this.indexName == 'rover_credit_check_query') {
      //   this.logger.info(`search credit es companyId ${companyId} query: ${JSON.stringify(query)}`);
      //   this.logger.info(`search credit es companyId ${companyId} aggs: ${JSON.stringify(aggs)}`);
      //   this.logger.info(`search credit es companyId ${companyId} response: ${JSON.stringify(response)}`);
      // }
      const bucketData = this.processAggs(response?.body?.aggregations, dimensionHitStrategyPOS);
      return this.processBucketData(bucketData, dimensionHitStrategyPOS);
    } catch (e) {
      this.logger.error(e);
      throw e;
    }
  }

  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const { keyNo, pageIndex, pageSize } = params;
    try {
      const { total, data } = await this.getDetailFromEs(keyNo, dimension, params, analyzeParams);
      const res = Object.assign(new HitDetailsBaseResponse(), {
        Paging: {
          PageSize: pageSize,
          PageIndex: pageIndex,
          TotalRecords: total,
        },
        Result: data,
      });
      const resResult = await this.getDimensionDetailItemData(res, dimension, params, analyzeParams);
      return resResult;
    } catch (error) {
      this.logger.error('getDimensionDetail error!', error);
    }
    return new HitDetailsBaseResponse();
  }

  protected async getQuery(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[], params?: DimensionAnalyzeParamsPO): Promise<object> {
    const query = {
      bool: {
        should: [],
      },
    };
    await Bluebird.map(DimensionHitStrategyPOs, async (po) => {
      const dimQuery = await this.getDimensionQuery(companyId, po, params);
      if (dimQuery) {
        query.bool.should.push(dimQuery);
      }
    });
    if (query.bool.should.length > 0) {
      query.bool['minimum_should_match'] = 1;
    }
    return query;
  }

  protected async createAggs(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[], params?: DimensionAnalyzeParamsPO) {
    const aggs: any = {};
    await Bluebird?.map(DimensionHitStrategyPOs, async (po) => {
      const dimQuery = await this.getDimensionQuery(companyId, po, params);
      const aggsName = `${this.bucketNamePrefix}${po.strategyId}`;
      if (dimQuery) {
        aggs[aggsName] = {
          filter: dimQuery,
          aggs: {},
        };
      }
    });
    return aggs;
  }

  protected processAggs(aggObj: any, DimensionHitStrategyPOs?: DimensionHitStrategyPO[]): AggBucketItemPO[] {
    const bucketData: AggBucketItemPO[] = [];

    Object.keys(aggObj).forEach((bucketName) => {
      // const bucket = aggObj[bucketName];
      const dimensionType = bucketName.replace(this.bucketNamePrefix, '');
      const bucket = aggObj[bucketName];

      const hitCount = bucket['doc_count'];
      if (hitCount > 0) {
        const res: AggBucketItemPO = {
          dimensionType,
          hitCount,
        };
        bucketData.push(res);
      }
    });
    return bucketData;
  }

  protected processBucketData(bucketData: AggBucketItemPO[], dimensionHitStrategyPOs: DimensionHitStrategyPO[]): DimensionHitResultPO[] {
    return bucketData
      .map((item) => {
        const d: DimensionHitStrategyPO = find(dimensionHitStrategyPOs, { strategyId: +item.dimensionType });
        if (!d.key) {
          return null;
        }
        const desData = {
          isHidden: '',
          isHiddenY: '',
        };
        const { hitCount } = item as AggBucketItemPO;

        let hit = true;
        // 命中记录条数 规则设置
        const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
        if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
          // 不满足 命中记录条数规则 标记未命中
          hit = false;
        }

        if (hit) {
          return processDimHitResPO(d, hitCount, desData);
        }
        return null;
      })
      .filter((t) => t);
  }

  protected async getDetailFromEs(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ) {
    const { pageIndex, pageSize } = params;

    const query = await this.getDimensionQuery(companyId, dimension, params, analyzeParams);
    const sort = {};

    const dimSortFiled = dimension.getSortField();
    if (params?.field) {
      sort[params.field] = params?.order || 'DESC';
    } else if (dimSortFiled?.field) {
      sort[dimSortFiled.field] = dimSortFiled.order;
    }

    const response = await this.searchEs(
      {
        from: (pageIndex && pageIndex > 0 ? pageIndex - 1 : 0) * pageSize,
        size: pageSize || 10,
        sort,
        query,
      },
      companyId,
    );
    return {
      total: response?.body?.hits?.total?.value || 0,
      data: response?.body?.hits?.hits?.map((d) => d._source) || [],
    };
  }

  protected searchEs(body, preference: string) {
    const searchRequest: RequestParams.Search = {
      track_total_hits: true,
      index: this.indexName,
      type: '_doc',
      body,
      preference,
    };
    return this.esClient.search(searchRequest);
  }

  protected abstract getDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object>;

  protected abstract getDimensionDetailItemData(
    res: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse>;
}
