/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { ConfigService } from 'libs/config/config.service';
import * as Bluebird from 'bluebird';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { Client } from '@elastic/elasticsearch';
import { BaseEsAnalyzeService } from './base-es-analyze.service';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { getCompareResult } from '../../../libs/utils/diligence/diligence.utils';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';

/**
 * 股权出质数据源接口
 */
@Injectable()
export class PledgeSource extends BaseEsAnalyzeService {
  constructor(private readonly configService: ConfigService) {
    super(
      PledgeSource.name,
      new Client({
        nodes: configService.esConfig.pledge.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.pledge.indexName,
    );
  }

  async analyze(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    return Bluebird.map(
      DimensionHitStrategyPOs,
      async (d: DimensionHitStrategyPO) => {
        let hitCount = 0;
        switch (d.key) {
          case DimensionTypeEnums.EquityPledge:
            {
              const result = await this.getDimensionDetail(
                d,
                Object.assign(new HitDetailsBaseQueryParams(), { keyNo: companyId, pageIndex: 1, pageSize: 1 }),
                Object.assign(new DimensionAnalyzeParamsPO(), { isScanRisk: true, keyNo: companyId }),
              );
              hitCount = result?.Paging?.TotalRecords || 0;
            }
            break;
          default:
            this.logger.error('unreachable code');
        }
        // 命中记录条数 规则设置
        const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
        if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
          // 不满足 命中记录条数规则 标记未命中
          hitCount = 0;
        }

        if (hitCount > 0) {
          return processDimHitResPO(d, hitCount);
        }
        return null;
      },
      { concurrency: 3 },
    ).then((results) => results.filter((t) => t));
  }

  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const resp = await super.getDimensionDetail(dimension, params, analyzeParams);
    return resp;
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    // 数据处理
    if (resp?.Result?.length) {
      resp.Result = resp.Result.map((it) => ({
        No: it.id,
        OldNo: it.glkeyno,
        RegistNo: it.caseno,
        RelatedCompanyInfo: {
          Name: it.name,
          KeyNo: it.keyno,
          Org: 0,
        },
        PledgorInfo: it.pledgorjson && JSON.parse(it.pledgorjson),
        PledgeeInfo: it.pledgeejson && JSON.parse(it.pledgeejson),
        PledgedAmount: it.amountdesc,
        RegDate: it.liandate,
        Status: it.isvalid ? '有效' : '无效',
        KeyNo: it.keyno,
        IsValid: it.isvalid,
        PledgeeNo: it.pledgeeno,
        PledgorNo: it.pledgorno,
      }));
    }
    return resp;
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionHitStrategyPO) {
    const subBool = {
      filter: [],
      should: [],
    };
    if (dimension.key === DimensionTypeEnums.EquityPledge) {
      subBool.filter = [
        {
          term: {
            isvalid: 1,
          },
        },
        {
          term: {
            companynames: companyId,
          },
        },
      ];
      // 排查企业为股权出质人或出质股权标的的企业
      subBool.should = [
        {
          term: {
            pledgorkeywords: companyId,
          },
        },
        {
          term: {
            keyno: companyId,
          },
        },
      ];
      subBool['minimum_should_match'] = 1;
    }
    return { bool: subBool };
  }
}
