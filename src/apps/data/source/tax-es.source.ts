/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { ConfigService } from 'libs/config/config.service';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { useAdapter } from '@type-cacheable/lru-cache-adapter';
import { Client } from '@elastic/elasticsearch';
import { AggBucketItemPO } from 'libs/model/data/source/credit.analyze/CreditAggBucketItemPO';
import { find } from 'lodash';
import { BaseEsAnalyzeService } from './base-es-analyze.service';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import * as Bluebird from 'bluebird';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { getLRUCacheInstance } from '../../../libs/utils/cache.utils';
import { getCompareResult, getStartTimeByCycle } from '../../../libs/utils/diligence/diligence.utils';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';

/**
 * 税务公告
 */
@Injectable()
export class TaxEsSource extends BaseEsAnalyzeService {
  constructor(private readonly configService: ConfigService) {
    super(
      TaxEsSource.name,
      new Client({
        nodes: configService.esConfig.tax.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.tax.indexName,
    );
    useAdapter(getLRUCacheInstance({ max: 1 * 10000, maxAge: 1000 * 30 }));
  }

  /**
   * 税务公告
   * 此处方法里面对Dimension进行处理，以及维度详情的一些数据查询
   * @param dimension
   * @param params
   * @returns
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const resp: HitDetailsBaseResponse = await super.getDimensionDetail(dimension, params, analyzeParams);
    return resp;
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    return resp;
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionHitStrategyPO): Promise<object> {
    const ids = [companyId];

    const subBool = {
      filter: [],
    };
    switch (dimension.key) {
      case DimensionTypeEnums.TaxCallNotice: {
        subBool.filter.push(
          { term: { type: '36' } },
          {
            bool: {
              should: [
                {
                  regexp: {
                    title: '.*申报催报.*',
                  },
                },
                {
                  regexp: {
                    title: '.*申报催缴.*',
                  },
                },
              ],
              minimum_should_match: 1,
            },
          },
          { terms: { companynames: ids } },
        );
        const cycle = dimension.getCycle();
        if (cycle > 0) {
          const timestamp = getStartTimeByCycle(cycle);
          subBool.filter.push({ range: { publicdate: { gte: Math.ceil(timestamp / 1000) } } });
        }
        subBool.filter.push({ term: { isvalid: 1 } });
        // const validParam = dimension.strategyModel.detailsParams.find((p) => p.field == DimensionFieldKeyEnums.isValid);
        // if (Number(validParam?.fieldVal) >= 0) {
        //   subBool.filter.push({ term: { isvalid: Number(validParam.fieldVal) } });
        // }
        return { bool: subBool };
      }
    }
    return { bool: subBool };
  }

  protected async createAggs(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]) {
    const aggs: any = {};
    await Bluebird?.map(DimensionHitStrategyPOs, async (po) => {
      const dimQuery = await this.getDimensionQuery(companyId, po);
      const aggsName = `${this.bucketNamePrefix}${po.strategyId}`;
      aggs[aggsName] = {
        filter: dimQuery,
        aggs: {
          // id去重数量
          unique_id_count: {
            cardinality: {
              field: 'id',
            },
          },
        },
      };
    });
    return aggs;
  }

  protected processAggs(aggObj: any, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): AggBucketItemPO[] {
    const bucketData: AggBucketItemPO[] = [];
    DimensionHitStrategyPOs.forEach((po) => {
      const aggsName = `${this.bucketNamePrefix}${po.strategyId}`;
      const bucket = aggObj[aggsName];
      const hitCount = bucket['unique_id_count']['value'];
      if (hitCount > 0) {
        const res: AggBucketItemPO = {
          dimensionType: po.strategyId + '',
          hitCount,
        };
        bucketData.push(res);
      }
    });
    return bucketData;
  }

  protected processBucketData(bucketData: AggBucketItemPO[], DimensionHitStrategyPOs: DimensionHitStrategyPO[]): DimensionHitResultPO[] {
    return bucketData
      .map((item) => {
        const d: DimensionHitStrategyPO = find(DimensionHitStrategyPOs, { strategyId: +item.dimensionType });
        const desData = {
          isHidden: '',
          isHiddenY: '',
        };
        const { hitCount } = item as AggBucketItemPO;
        let hit = true;
        // 命中记录条数 规则设置
        const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
        if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
          // 不满足 命中记录条数规则 标记未命中
          hit = false;
        }

        if (hit) {
          return processDimHitResPO(d, hitCount, desData);
        }
        return null;
      })
      .filter((t) => t);
  }
}
