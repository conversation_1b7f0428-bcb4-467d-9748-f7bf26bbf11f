import { CompanyDetailService } from 'apps/company/company-detail.service';
import { AnnualReportType } from 'libs/constants/risk.change.constants';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { getCompareResultForArray, getCompareResult } from 'libs/utils/diligence/diligence.utils';
import { Injectable } from '@nestjs/common';
import { BaseHelper } from './base.helper';

@Injectable()
export class CompanyFinaceHelper {
  constructor(private readonly baseHelper: BaseHelper, private readonly companyDetailService: CompanyDetailService) {}
  /**
   * 企业公告是年报的数据
   * @param annualReportTypeField
   * @param item
   */
  public categoryAnnualReportField(annualReportTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const annualReportTypeFieldTargetValues = annualReportTypeField?.fieldValue as number[];
    const annualReportTypeFieldSourceValues: number[] = [];
    if (item?.ChangeExtend?.TS) {
      const ts = Number(item.ChangeExtend.TS);
      AnnualReportType.forEach((t) => {
        if (ts === t.value) {
          annualReportTypeFieldSourceValues.push(t.value);
        }
      });
    }
    if (
      annualReportTypeFieldTargetValues?.length &&
      annualReportTypeFieldSourceValues?.length &&
      getCompareResultForArray(annualReportTypeField.compareType, annualReportTypeFieldSourceValues, annualReportTypeFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 近一期的
   * @param retainedProfitField
   * @param item
   * @param keyNo
   */
  public async categoryRetainedProfitField(retainedProfitField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    const retainedProfitFieldTargetValues = retainedProfitField?.fieldValue as number[];
    const financeList = await this.companyDetailService.getCompanyFinance(keyNo, [0], 1);
    let retainedProfitFieldSourceValues = null;

    const ReportList = financeList?.Result?.ReportFields?.map((t) => {
      // 过滤t.ReportType === '主要指标' && t.AccountType === '利润表' && t.AccountNameZh === '净利润';
      if (t.ReportType === '主要指标' && t.AccountType === '利润表' && t.AccountNameZh === '净利润') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList?.length) {
      retainedProfitFieldSourceValues = ReportList[0]?.FieldList[0]?.OriginalValue;
    }

    if (
      retainedProfitFieldTargetValues?.length &&
      retainedProfitFieldSourceValues !== null &&
      getCompareResult(retainedProfitFieldSourceValues, retainedProfitFieldTargetValues[0], retainedProfitField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 净利润同比
   * @param netProfitRatioField
   * @param item
   * @param keyNo
   */
  async categoryNetProfitRatioField(netProfitRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    const netProfitRatioFieldTargetValues = netProfitRatioField?.fieldValue as number[];
    const financeList = await this.companyDetailService.getCompanyFinance(keyNo, [0], 1);
    let netProfitRatioFieldSourceValue = null;
    const ReportList = financeList?.Result?.ReportFields?.map((t) => {
      // 过滤t.ReportType === '主要指标' && t.AccountType === '利润表' && t.AccountNameZh === '净利润';
      if (t.ReportType === '主要指标' && t.AccountType === '利润表' && t.AccountNameZh === '净利润') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList?.length) {
      netProfitRatioFieldSourceValue = ReportList[0]?.FieldList[0]?.YoyGrowth;
    }
    if (
      netProfitRatioFieldTargetValues?.length &&
      netProfitRatioFieldSourceValue !== null &&
      getCompareResult(netProfitRatioFieldSourceValue, netProfitRatioFieldTargetValues[0], netProfitRatioField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 营业收入同比
   * @param revenueRatioField
   * @param item
   * @param keyNo
   */
  async categoryRevenueRatioField(revenueRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    const revenueRatioFieldTargetValues = revenueRatioField?.fieldValue as number[];
    // 如果只需要取近一期的数据，就把reportPeriodTypes传0, 取全部的近三年的数据不传reportPeriodTypes
    const financeList = await this.companyDetailService.getCompanyFinance(keyNo, [0], 1);
    let revenueRatioFieldSourceValue = null;
    const ReportList = financeList?.Result?.ReportFields?.map((t) => {
      // 过滤t.ReportType === '主要指标' && t.AccountType === '利润表' && t.AccountNameZh === '营业收入';
      if (t.ReportType === '主要指标' && t.AccountType === '利润表' && t.AccountNameZh === '营业收入') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList?.length) {
      revenueRatioFieldSourceValue = ReportList[0]?.FieldList[0]?.YoyGrowth; // YoyGrowth 营业收入同比
    }
    if (
      revenueRatioFieldTargetValues?.length &&
      revenueRatioFieldSourceValue !== null &&
      getCompareResult(revenueRatioFieldSourceValue, revenueRatioFieldTargetValues[0], revenueRatioField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 应收账款同比
   * @param accountsReceivableRatioField
   * @param item
   * @param keyNo
   */
  async categoryAccountsReceivableRatioField(accountsReceivableRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    const accountsReceivableRatioFieldTargetValues = accountsReceivableRatioField?.fieldValue as number[];
    // 如果只需要取近一期的数据，就把reportPeriodTypes传0, 取全部的近三年的数据不传reportPeriodTypes
    const financeList = await this.companyDetailService.getCompanyFinance(keyNo, [], 2);
    let nowValue = null;
    let lastValue = null;
    let accountsReceivableRatioFieldSourceValue = null;
    const ReportList = financeList?.Result?.ReportFields?.map((t) => {
      if (t.ReportType === '资产负债表' && t.AccountType === '流动资产' && t.AccountNameZh === '应收账款') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList?.length) {
      nowValue = ReportList[0]?.FieldList[0]?.OriginalValue;
      lastValue = ReportList[0]?.FieldList[1]?.OriginalValue; // YoyGrowth 营业收入同比
    }
    if (!nowValue || !lastValue) {
      const ReportList = financeList?.Result?.ReportFields?.map((t) => {
        if (t.ReportType === '资产负债表' && t.AccountType === '流动资产' && t.AccountNameZh === '应收票据及应收账款') {
          return t;
        }
      }).filter((t) => t);
      if (ReportList?.length) {
        nowValue = ReportList[0]?.FieldList[0]?.OriginalValue;
        lastValue = ReportList[0]?.FieldList[1]?.OriginalValue; // YoyGrowth 营业收入同比
      }
    }
    if (nowValue && lastValue) {
      // 计算同比
      accountsReceivableRatioFieldSourceValue = ((nowValue - lastValue) / lastValue) * 100;
    }
    if (
      accountsReceivableRatioFieldTargetValues?.length &&
      accountsReceivableRatioFieldSourceValue !== null &&
      getCompareResult(accountsReceivableRatioFieldSourceValue, accountsReceivableRatioFieldTargetValues[0], accountsReceivableRatioField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 存货同比
   * @param accountsReceivableRatioField
   * @param item
   * @param keyNo
   */
  async categoryInventoryRatioField(inventoryRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    const inventoryRatioFieldTargetValues = inventoryRatioField?.fieldValue as number[];
    // 如果只需要取近一期的数据，就把reportPeriodTypes传0, 取全部的近三年的数据不传reportPeriodTypes
    const financeList = await this.companyDetailService.getCompanyFinance(keyNo, [], 2);
    let nowValue = null;
    let lastValue = null;
    let inventoryRatioFieldSourceValue = null;
    const ReportList = financeList?.Result?.ReportFields?.map((t) => {
      if (t.ReportType === '资产负债表' && t.AccountType === '流动资产' && t.AccountNameZh === '存货') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList?.length) {
      nowValue = ReportList[0]?.FieldList[0]?.OriginalValue;
      lastValue = ReportList[0]?.FieldList[1]?.OriginalValue;
    }
    if (nowValue && lastValue) {
      // 计算同比
      inventoryRatioFieldSourceValue = ((nowValue - lastValue) / lastValue) * 100;
    }
    if (
      inventoryRatioFieldTargetValues?.length &&
      inventoryRatioFieldSourceValue !== null &&
      getCompareResult(inventoryRatioFieldSourceValue, inventoryRatioFieldTargetValues[0], inventoryRatioField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 有息负债同比
   *
   * 短期借款+应付票据+一年内到期非流动负债+长期借款+应付债券
   *
   *
   *
   * @param categoryInterestBearingLiabilitiesRatioField
   * @param item
   * @param keyNo
   */
  async categoryInterestBearingLiabilitiesRatioField(interestBearingLiabilitiesRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    const interestBearingLiabilitiesRatioFieldTargetValues = interestBearingLiabilitiesRatioField?.fieldValue as number[];
    // 如果只需要取近一期的数据，就把reportPeriodTypes传0, 取全部的近三年的数据不传reportPeriodTypes
    const financeList = await this.companyDetailService.getCompanyFinance(keyNo, [], 2);
    let nowValue = null;
    let lastValue = null;
    let interestBearingLiabilitiesRatioFieldSourceValue = null;
    const ReportList = financeList?.Result?.ReportFields?.map((t) => {
      if (
        t.ReportType === '资产负债表' &&
        t.AccountType === '流动负债' &&
        (t.AccountNameZh === '短期借款' || t.AccountNameZh === '应付票据' || t.AccountNameZh === '一年内到期非流动负债')
      ) {
        return t;
      } else if (
        t.ReportType === '资产负债表' &&
        t.AccountType === '流动负债' &&
        (t.AccountNameZh === '短期借款' || t.AccountNameZh === '应付账款及票据' || t.AccountNameZh === '一年内到期非流动负债')
      ) {
        return t;
      } else if (t.ReportType === '资产负债表' && t.AccountType === '非流动负债' && (t.AccountNameZh === '长期借款' || t.AccountNameZh === '应付债券')) {
        return t;
      }
    }).filter((t) => t);
    if (ReportList?.length) {
      for (const item of ReportList) {
        const fieldList = item?.FieldList;
        if (fieldList && fieldList.length > 0 && fieldList[0] && fieldList[0].OriginalValue) {
          nowValue += fieldList[0].OriginalValue;
        }
      }
      for (const item of ReportList) {
        const fieldList = item?.FieldList;
        if (fieldList && fieldList.length > 0 && fieldList[1] && fieldList[1].OriginalValue) {
          lastValue += fieldList[1].OriginalValue;
        }
      }
    }
    if (nowValue && lastValue) {
      // 计算同比
      interestBearingLiabilitiesRatioFieldSourceValue = ((nowValue - lastValue) / lastValue) * 100;
    }
    if (
      interestBearingLiabilitiesRatioFieldTargetValues?.length &&
      interestBearingLiabilitiesRatioFieldSourceValue !== null &&
      getCompareResult(
        interestBearingLiabilitiesRatioFieldSourceValue,
        interestBearingLiabilitiesRatioFieldTargetValues[0],
        interestBearingLiabilitiesRatioField.compareType,
      )
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 有息负债/年度总收入占比
   * @param ibdAnnualRevRatioField
   * @param item
   * @param keyNo
   */
  async categoryIbdAnnualRevRatioField(ibdAnnualRevRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    const ibdAnnualRevRatioFieldTargetValues = ibdAnnualRevRatioField?.fieldValue as number[];
    // 如果只需要取近一期的数据，就把reportPeriodTypes传0, 取全部的近三年的数据不传reportPeriodTypes
    const financeList = await this.companyDetailService.getCompanyFinance(keyNo, [], 2);
    // 有息负债总额
    let ibdAnnualTotalValue = null;
    let ibdAnnualRevRatioFieldSourceValue = null;
    const ReportList = financeList?.Result?.ReportFields?.map((t) => {
      if (
        t.ReportType === '资产负债表' &&
        t.AccountType === '流动负债' &&
        (t.AccountNameZh === '短期借款' || t.AccountNameZh === '应付票据' || t.AccountNameZh === '一年内到期非流动负债')
      ) {
        return t;
      } else if (t.ReportType === '资产负债表' && t.AccountType === '非流动负债' && (t.AccountNameZh === '长期借款' || t.AccountNameZh === '应付债券')) {
        return t;
      }
    }).filter((t) => t);
    if (ReportList?.length) {
      for (const item of ReportList) {
        const fieldList = item?.FieldList;
        if (fieldList && fieldList.length > 0 && fieldList[0] && fieldList[0].OriginalValue) {
          ibdAnnualTotalValue += fieldList[0].OriginalValue;
        }
      }
    }
    // 年度总收入
    let annualRevenueTotalVaule = null;
    const financeList2 = await this.companyDetailService.getCompanyFinance(keyNo, [0], 1);
    const ReportList2 = financeList2?.Result?.ReportFields?.map((t) => {
      // 过滤t.ReportType === '主要指标' && t.AccountType === '利润表' && t.AccountNameZh === '营业收入';
      if (t.ReportType === '主要指标' && t.AccountType === '利润表' && t.AccountNameZh === '营业收入') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList2?.length) {
      annualRevenueTotalVaule = ReportList2[0]?.FieldList[0]?.OriginalValue; // YoyGrowth 营业收入同比
    }

    if (ibdAnnualTotalValue && annualRevenueTotalVaule) {
      //有息负债/年度总收入 占比
      ibdAnnualRevRatioFieldSourceValue = (ibdAnnualTotalValue / annualRevenueTotalVaule) * 100;
    }
    if (
      ibdAnnualRevRatioFieldTargetValues?.length &&
      ibdAnnualRevRatioFieldSourceValue !== null &&
      getCompareResult(ibdAnnualRevRatioFieldSourceValue, ibdAnnualRevRatioFieldTargetValues[0], ibdAnnualRevRatioField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * （货币资金+交易性金融资产）/（短期借款+应付票据）
   * @param cmAndStbRatioField
   * @param item
   * @param keyNo
   */
  async categoryCmAndStbRatioField(cmAndStbRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    const cmAndStbRatioFieldTargetValues = cmAndStbRatioField?.fieldValue as number[];
    // 如果只需要取近一期的数据，就把reportPeriodTypes传0, 取全部的近三年的数据不传reportPeriodTypes
    const financeList = await this.companyDetailService.getCompanyFinance(keyNo, [], 2);
    let value1 = null; // 货币资金
    let cmAndStbRatioFieldSourceValue = null;
    const ReportList1 = financeList?.Result?.ReportFields?.map((t) => {
      if (t.ReportType === '资产负债表' && t.AccountType === '流动资产' && t.AccountNameZh === '货币资金') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList1?.length) {
      value1 = ReportList1[0]?.FieldList[0]?.OriginalValue || 0;
    }
    let value2 = null; // 衍生金融资产
    const ReportList2 = financeList?.Result?.ReportFields?.map((t) => {
      if (t.ReportType === '资产负债表' && t.AccountType === '流动资产' && t.AccountNameZh === '交易性金融资产') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList2?.length) {
      value2 = ReportList2[0]?.FieldList[0]?.OriginalValue || 0;
    }

    let value3 = null; // 短期借款
    const ReportList3 = financeList?.Result?.ReportFields?.map((t) => {
      if (t.ReportType === '资产负债表' && t.AccountType === '流动负债' && t.AccountNameZh === '短期借款') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList3?.length) {
      value3 = ReportList3[0]?.FieldList[0]?.OriginalValue || 0;
    }

    let value4 = null; // 应付票据
    const ReportList4 = financeList?.Result?.ReportFields?.map((t) => {
      if (t.ReportType === '资产负债表' && t.AccountType === '流动负债' && t.AccountNameZh === '应付票据') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList4?.length) {
      value4 = ReportList4[0]?.FieldList[0]?.OriginalValue || 0;
    }
    if (value1 + value2 > 0 && value3 + value4 > 0) {
      cmAndStbRatioFieldSourceValue = ((value1 + value2) / (value3 + value4)) * 100;
    }
    if (
      cmAndStbRatioFieldTargetValues?.length &&
      cmAndStbRatioFieldSourceValue !== null &&
      getCompareResult(cmAndStbRatioFieldSourceValue, cmAndStbRatioFieldTargetValues[0], cmAndStbRatioField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 负债合计/资产合计
   * @param totalLiabToAssetsRatioField
   * @param newItem
   * @param keyNo
   */
  async categoryTotalLiabToAssetsRatioField(totalLiabToAssetsRatioField: DimensionHitStrategyFieldsEntity, newItem: any, keyNo: string) {
    let hit = false;
    const totalLiabToAssetsRatioFieldTargetValues = totalLiabToAssetsRatioField?.fieldValue as number[];
    // 如果只需要取近一期的数据，就把reportPeriodTypes传0, 取全部的近三年的数据不传reportPeriodTypes
    const financeList = await this.companyDetailService.getCompanyFinance(keyNo, [], 2);
    let value1 = null;
    let totalLiabToAssetsRatioFieldSourceValue = null;
    const ReportList1 = financeList?.Result?.ReportFields?.map((t) => {
      if (t.ReportType === '资产负债表' && t.AccountType === '流动负债' && t.AccountNameZh === '流动负债合计') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList1?.length) {
      value1 = ReportList1[0]?.FieldList[0]?.OriginalValue || 0;
    }
    let value2 = null;
    const ReportList2 = financeList?.Result?.ReportFields?.map((t) => {
      if (t.ReportType === '资产负债表' && t.AccountType === '非流动负债' && t.AccountNameZh === '非流动负债合计') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList2?.length) {
      value2 = ReportList2[0]?.FieldList[0]?.OriginalValue || 0;
    }

    let value3 = null;
    const ReportList3 = financeList?.Result?.ReportFields?.map((t) => {
      if (t.ReportType === '资产负债表' && t.AccountType === '流动资产' && t.AccountNameZh === '流动资产合计') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList3?.length) {
      value3 = ReportList3[0]?.FieldList[0]?.OriginalValue || 0;
    }

    let value4 = null;
    const ReportList4 = financeList?.Result?.ReportFields?.map((t) => {
      if (t.ReportType === '资产负债表' && t.AccountType === '非流动资产' && t.AccountNameZh === '非流动资产合计') {
        return t;
      }
    }).filter((t) => t);
    if (ReportList4?.length) {
      value4 = ReportList4[0]?.FieldList[0]?.OriginalValue || 0;
    }
    if (value1 + value2 && value3 + value4 > 0) {
      totalLiabToAssetsRatioFieldSourceValue = ((value1 + value2) / (value3 + value4)) * 100;
    }
    if (
      totalLiabToAssetsRatioFieldTargetValues?.length &&
      totalLiabToAssetsRatioFieldSourceValue !== null &&
      getCompareResult(totalLiabToAssetsRatioFieldSourceValue, totalLiabToAssetsRatioFieldTargetValues[0], totalLiabToAssetsRatioField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 连续X年经营活动产生的现金流量净额
   * @param totalLiabToAssetsRatioField
   * @param newItem
   * @param keyNo
   */
  async categoryCashFlowFromActivitiesAmountField(cashFlowFromActivitiesAmountField: DimensionHitStrategyFieldsEntity, newItem: any, keyNo: string) {
    let hit = false;
    const targetValueConsecutiveYearCount = cashFlowFromActivitiesAmountField.fieldValue[0]?.consecutiveYearCount;
    const targetValueCashFlowFromActivitiesAmount = cashFlowFromActivitiesAmountField.fieldValue[0]?.cashFlowFromActivitiesAmount;
    const targetValueCashFlowFromActivitiesAmountCompareType = cashFlowFromActivitiesAmountField.fieldValue[0]?.cashFlowFromActivitiesAmountCompareType;

    // 如果只需要取近一期的数据，就把reportPeriodTypes传0, 取全部的近三年的数据不传reportPeriodTypes
    const financeList = await this.companyDetailService.getCompanyFinance(keyNo, [], 4);
    const ReportList = financeList?.Result?.ReportFields?.map((t) => {
      if (t.ReportType === '现金流量表' && t.AccountType === '经营活动产生的现金流量' && t.AccountNameZh === '经营活动产生的现金流量净额') {
        return t;
      }
    }).filter((t) => t);
    let hitCount = 0;
    if (ReportList?.length && targetValueConsecutiveYearCount) {
      for (let i = 0; i < targetValueConsecutiveYearCount; i++) {
        const sourceValueCashFlowFromActivitiesAmount = ReportList[0]?.FieldList[i]?.OriginalValue || 0;
        if (
          targetValueCashFlowFromActivitiesAmount !== null &&
          sourceValueCashFlowFromActivitiesAmount !== null &&
          getCompareResult(sourceValueCashFlowFromActivitiesAmount, targetValueCashFlowFromActivitiesAmount, targetValueCashFlowFromActivitiesAmountCompareType)
        ) {
          hitCount += 1;
        }
      }
    }
    if (hitCount === targetValueConsecutiveYearCount) {
      hit = true;
    }
    return hit;
  }
}
