import { Injectable } from '@nestjs/common';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { getCompareResult } from 'libs/utils/diligence/diligence.utils';

@Injectable()
export class BankLitigationHelper {
  /**
   * 裁判文书
   * 原告为银行或金融租赁
   * @param bankOrFlField
   * @param item
   */
  public checkBankOrFinancialLeasingField49(bankOrFlField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const bankOrFlFieldTargetValues = bankOrFlField.fieldValue as number[];
    const I = item?.ChangeExtend?.I;
    const bankOrFlList: any[] = [];
    if (I.length > 0) {
      const find = I.find((t) => t.Role === '原告');
      if (find && (find.Name.includes('银行') || find.Name.includes('金融租赁'))) {
        bankOrFlList.push(find);
      }
    }

    const fieldSourceValue = bankOrFlList.length > 0 ? 1 : 0;

    if (bankOrFlFieldTargetValues?.length && fieldSourceValue && getCompareResult(fieldSourceValue, bankOrFlFieldTargetValues[0], bankOrFlField.compareType)) {
      hit = true;
    }
    return hit;
  }

  /**
   * 裁判文书
   * 原告为银行或金融租赁
   * @param bankOrFlField
   * @param item
   */
  public checkBankOrFinancialLeasingField4(bankOrFlField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const bankOrFlFieldTargetValues = bankOrFlField.fieldValue as number[];
    const I = item?.ChangeExtend?.K;
    const bankOrFlList: any[] = [];
    if (I.length > 0) {
      const find = I.find((t) => t.RN === '原告');
      if (find && (find.Name.includes('银行') || find.Name.includes('金融租赁'))) {
        bankOrFlList.push(find);
      }
    }

    const fieldSourceValue = bankOrFlList.length > 0 ? 1 : 0;

    if (bankOrFlFieldTargetValues?.length && fieldSourceValue && getCompareResult(fieldSourceValue, bankOrFlFieldTargetValues[0], bankOrFlField.compareType)) {
      hit = true;
    }
    return hit;
  }

  /**
   * 开庭公告
   * 原告为银行或金融租赁
   * @param bankOrFlField
   * @param item
   */
  public checkBankOrFinancialLeasingField18(bankOrFlField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const bankOrFlFieldTargetValues = bankOrFlField.fieldValue as number[];
    const I = item?.ChangeExtend?.D;
    const bankOrFlList: any[] = [];
    if (I.length > 0) {
      const filter = I.filter((t) => t.RN === '原告' && (t.P.includes('银行') || t.P.includes('金融租赁')));
      if (filter.length > 0) {
        bankOrFlList.push(filter);
      }
    }

    const fieldSourceValue = bankOrFlList.length > 0 ? 1 : 0;

    if (bankOrFlFieldTargetValues?.length && fieldSourceValue && getCompareResult(fieldSourceValue, bankOrFlFieldTargetValues[0], bankOrFlField.compareType)) {
      hit = true;
    }
    return hit;
  }

  /**
   * 法院公告
   * 原告为银行或金融租赁
   * @param bankOrFlField
   * @param item
   */
  public checkBankOrFinancialLeasingField7(bankOrFlField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const bankOrFlFieldTargetValues = bankOrFlField.fieldValue as number[];
    const I = item?.ChangeExtend?.J;
    const bankOrFlList: any[] = [];
    if (I.length > 0) {
      const find = I.find((t) => t.Role === '原告');
      if (find && (find.Name.includes('银行') || find.Name.includes('金融租赁'))) {
        bankOrFlList.push(find);
      }
    }

    const fieldSourceValue = bankOrFlList.length > 0 ? 1 : 0;

    if (bankOrFlFieldTargetValues?.length && fieldSourceValue && getCompareResult(fieldSourceValue, bankOrFlFieldTargetValues[0], bankOrFlField.compareType)) {
      hit = true;
    }
    return hit;
  }

  /**
   * 送达公告
   * 原告为银行或金融租赁
   * @param bankOrFlField
   * @param item
   */
  public checkBankOrFinancialLeasingField27(bankOrFlField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const bankOrFlFieldTargetValues = bankOrFlField.fieldValue as number[];
    const I = item?.ChangeExtend?.D;
    const bankOrFlList: any[] = [];
    if (I.length > 0) {
      const find = I.find((t) => t.RN === '原告');
      if (find && (find.P.includes('银行') || find.P.includes('金融租赁'))) {
        bankOrFlList.push(find);
      }
    }

    const fieldSourceValue = bankOrFlList.length > 0 ? 1 : 0;

    if (bankOrFlFieldTargetValues?.length && fieldSourceValue && getCompareResult(fieldSourceValue, bankOrFlFieldTargetValues[0], bankOrFlField.compareType)) {
      hit = true;
    }
    return hit;
  }

  /**
   * 诉前调解
   * 原告为银行或金融租赁
   * @param bankOrFlField
   * @param item
   */
  public checkBankOrFinancialLeasingField90(bankOrFlField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const bankOrFlFieldTargetValues = bankOrFlField.fieldValue as number[];
    const I = item?.ChangeExtend?.I;
    const bankOrFlList: any[] = [];
    if (I.length > 0) {
      const find = I.find((t) => t.Role === '原告');
      if (find && (find.Name.includes('银行') || find.Name.includes('金融租赁'))) {
        bankOrFlList.push(find);
      }
    }

    const fieldSourceValue = bankOrFlList.length > 0 ? 1 : 0;

    if (bankOrFlFieldTargetValues?.length && fieldSourceValue && getCompareResult(fieldSourceValue, bankOrFlFieldTargetValues[0], bankOrFlField.compareType)) {
      hit = true;
    }
    return hit;
  }
}
