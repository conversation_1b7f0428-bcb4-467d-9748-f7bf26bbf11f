import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { PersonHelper } from 'apps/data/helper/person.helper';
import dynamicConstant from 'apps/data/risk.copy.from.c/dynamic-constant';
import { IntellectualType } from 'libs/constants/risk.change.constants';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { getCompareResult, getCompareResultForArray } from 'libs/utils/diligence/diligence.utils';
import { includes } from 'lodash';
import { Logger } from 'log4js';
import * as moment from 'moment';
@Injectable()
export class BaseHelper {
  private readonly logger: Logger = QccLogger.getLogger(BaseHelper.name);
  constructor(private readonly personHelper: PersonHelper) {}

  /**
   *  限制高消费，限制对象，1-企业本身，2-法人代表
   * @param restricterTypeField
   * @param newItem
   */
  public restricterTypeField(restricterTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const restricterTypeFieldTargetValues = restricterTypeField?.fieldValue as number[];
    const CSourceValues = item?.ChangeExtend?.C ? JSON.parse(item?.ChangeExtend?.C) : []; // 企业本身的限高 ES样例： [{\"KeyNo\":\"1cf6a30d6ed960bc889214399b990ea8\",\"Name\":\"深圳市英威诺科技有限公司\",\"Org\":0}]
    const ESourceValues = item?.ChangeExtend?.E ? JSON.parse(item?.ChangeExtend?.E) : []; // 法人代表的限高  ES样例： [{\"KeyNo\":\"pc7b9bceae3f02e25f82b6f0fbd2371e\",\"Name\":\"鲁葳\",\"Org\":0}]
    const restricterTypeFieldSourceValues: number[] = [];
    if (CSourceValues?.length && CSourceValues?.map((t) => t.KeyNo)?.includes(item.KeyNo)) {
      restricterTypeFieldSourceValues.push(1);
    }
    if (ESourceValues?.length && ESourceValues?.map((t) => t.KeyNo)?.includes(item.KeyNo)) {
      restricterTypeFieldSourceValues.push(2);
    }
    if (
      restricterTypeFieldTargetValues?.length &&
      restricterTypeFieldSourceValues?.length &&
      getCompareResultForArray(restricterTypeField.compareType, restricterTypeFieldSourceValues, restricterTypeFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  public checkAmountField(amountField: DimensionHitStrategyFieldsEntity, rawValue: any, conversionRate = 10000) {
    let hit = false;
    const amountFieldTargetValue = amountField?.fieldValue as number[];
    if (rawValue !== null) {
      const amount = parseFloat(((rawValue ? Number(rawValue) : 0) / conversionRate).toFixed(2));
      if (amountFieldTargetValue?.length && getCompareResult(amount, amountFieldTargetValue[0], amountField.compareType)) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 拍卖类型  新增${changeInfo.Q === 1 ? '破产' : '司法'}拍卖
   * @param auctionTypeField
   * @param newItem
   */
  public auctionTypeField(auctionTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const auctionTypeFieldTargetValues = auctionTypeField?.fieldValue as number[];
    const auctionTypeFieldSourceValues: number[] = [];
    if (item?.ChangeExtend?.Q === 0) {
      auctionTypeFieldSourceValues.push(1); // 破产
    } else if (item?.ChangeExtend?.Q === 1) {
      auctionTypeFieldSourceValues.push(2); // 司法
    }
    if (
      auctionTypeFieldTargetValues?.length &&
      auctionTypeFieldSourceValues?.length &&
      getCompareResultForArray(auctionTypeField.compareType, auctionTypeFieldSourceValues, auctionTypeFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 知识产权： 1-出质人，2-质权人
   * @param intellectualRoleField
   * @param newItem
   */
  public category86IntellectualRole(intellectualRoleField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const intellectualRoleFieldTargetValues = intellectualRoleField?.fieldValue as number[];
    const intellectualRoleFieldSourceValues: number[] = [];
    if (item?.ChangeExtend?.B) {
      const BInfos = item?.ChangeExtend?.B ? JSON.parse(item.ChangeExtend.B) : [];
      const BKeyNos = BInfos?.map((t) => t?.KeyNo).filter((r) => !!r) || [];
      if (BKeyNos?.length && BKeyNos.includes(item?.KeyNo)) {
        intellectualRoleFieldSourceValues.push(1); // 出质人
      }
    }
    if (item?.ChangeExtend?.C?.length) {
      const CInfos = item?.ChangeExtend?.C ? JSON.parse(item.ChangeExtend.C) : [];
      const CKeyNos = CInfos?.map((t) => t?.KeyNo).filter((r) => !!r) || [];
      if (CKeyNos?.length && CKeyNos.includes(item?.KeyNo)) {
        intellectualRoleFieldSourceValues.push(2); // 质权人
      }
    }
    if (
      intellectualRoleFieldTargetValues?.length &&
      intellectualRoleFieldSourceValues?.length &&
      getCompareResultForArray(intellectualRoleField.compareType, intellectualRoleFieldTargetValues, intellectualRoleFieldSourceValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 知识产权分类：1-专利，2-商标 （ChangeExtend.F = 1 专利）
   */
  public category86IntellectualType(intellectualTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const inspectionResultTypeFieldTargetValues = intellectualTypeField?.fieldValue as number[];
    const inspectionResultTypeFieldSourceValues: number[] = [];
    if (item?.ChangeExtend?.F !== null) {
      IntellectualType.forEach((t) => {
        if (item?.ChangeExtend?.F === t.value) {
          inspectionResultTypeFieldSourceValues.push(t.value);
        }
      });
    }
    if (
      inspectionResultTypeFieldTargetValues?.length &&
      inspectionResultTypeFieldSourceValues?.length &&
      getCompareResultForArray(intellectualTypeField.compareType, inspectionResultTypeFieldSourceValues, inspectionResultTypeFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 金额属性的判断，单位是元，设置的阀值单位是万元
   * @param amountField 属性设置的阈值
   * @param rawValue 当前的动态的金额值
   * @param conversionRate 设置的阀值与动态金额值的转换关系，默认是10000，即设置的阈值单位是万元，动态金额值单位是元
   * @returns
   */
  public amountField(amountField: DimensionHitStrategyFieldsEntity, rawValue: any, conversionRate = 10000) {
    let hit = false;
    const amountFieldTargetValues = amountField?.fieldValue[0] as number[];
    if (rawValue !== null) {
      const amount = parseFloat(((rawValue ? Number(rawValue) : 0) / conversionRate).toFixed(2));
      const minTargetValue = amountFieldTargetValues[0] || undefined;
      const maxTargetValue = amountFieldTargetValues[1] || undefined;
      const isMatch = (minTargetValue === undefined || amount >= minTargetValue) && (maxTargetValue === undefined || amount < maxTargetValue);
      if (isMatch) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 食品安全抽检产品源
   * 判断typeField.fieldValue,默认为[1,2] 1-销售的产品,2-生产的产品
   * 判断ChangeExtend的 F(销售源),G(生产源)中的 KeyNo是否能和 targetKeyNo匹配
   * @param typeField
   * @param item
   */
  public category79Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const targetKeyNo = item.KeyNo;
    const typeTargetValues = typeField?.fieldValue as number[];
    const matchValues: number[] = [];
    if (!item?.ChangeExtend) {
      return hit;
    }
    const changeExtend = item.ChangeExtend;
    if (changeExtend) {
      if (changeExtend?.F) {
        const targetCompanies1 = JSON.parse(changeExtend.F);
        if (targetCompanies1?.length && targetCompanies1.map((m) => m.KeyNo).includes(targetKeyNo)) {
          matchValues.push(1);
        }
      }
      if (changeExtend?.G) {
        const targetCompanies2 = JSON.parse(changeExtend.G);
        if (targetCompanies2?.length && targetCompanies2.map((m) => m.KeyNo).includes(targetKeyNo)) {
          matchValues.push(2);
        }
      }
    }
    if (typeTargetValues?.length && matchValues?.length && getCompareResultForArray(typeField.compareType, matchValues, typeTargetValues)) {
      hit = true;
    }
    return hit;
  }

  /**
   * 融资动态筛选
   * TODO:退市动态暂未找到获取方式
   * @param typeField
   * @param item
   */
  public category28Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const changeInfo = item.ChangeExtend;
    if (!includes(dynamicConstant.financingRound, changeInfo.C)) {
      // 默认排除获得融资的融资动态
      hit = true;
    }
    return hit;
  }

  /**
   * 经营地址变更，只取上一年的数据
   * @param newItem
   */
  public filterLastYearData(newItem: any) {
    let hit = false;
    const annualDate = newItem?.ChangeExtend?.D || null;
    if (annualDate) {
      // 获取当前时间上一年的初的时间戳
      const startDate = moment().subtract(1, 'year').startOf('year').unix();
      if (Number(annualDate) >= startDate) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 判断是否命中负面/正面新闻
   * @param holderRoles 角色字段
   * @param topics 主题字段
   * @param newItem 新闻项
   * @returns 是否命中
   */
  public async hitNegativePositiveNewsField(
    holderRoles: DimensionHitStrategyFieldsEntity,
    topics: string[],
    newItem: any,
    targetKeyNo: string,
  ): Promise<boolean> {
    let hit = false;
    try {
      // 判断角色是否匹配
      const targetHolderRoleFieldValues = holderRoles?.fieldValue as number[];

      //股权角色 1-大股东，2-实际控制人，3-公司主体
      if (targetHolderRoleFieldValues.find((t) => t === 2)) {
        // 实控人判断
        // 取到 targetKeyNo的实控人列表, 判断keyNo是否实控人
        const acList = await this.personHelper.getFinalActualController(targetKeyNo, false);
        const companyList = newItem?.ChangeExtend?.companyList.map((c) => c.keyNo);
        //判断 acList 和 companyList 的交集
        const intersection = acList.filter((ac) => companyList.includes(ac.keyNo));
        if (intersection.length > 0) {
          hit = true;
        }
      }
      if (targetHolderRoleFieldValues.find((t) => t === 3)) {
        // 公司主体判断
        const companyList = newItem?.ChangeExtend?.companyList?.map((c) => c.keyNo);
        //判断 acList 和 companyList 的交集
        const intersection = companyList?.includes(targetKeyNo);
        if (intersection?.length) {
          hit = true;
        }
      }

      // 判断主题是否匹配
      const topicSourceValue = newItem?.ChangeExtend?.newTags;
      //如果主题为空，则不命中
      if (!topicSourceValue || !topics?.length) {
        // 判断新闻主题是否在策略定义的主题中
        hit = topics?.includes(topicSourceValue);
      }
      return hit;
    } catch (error) {
      this.logger.error(`hitNegativePositiveNewsField error: ${error}`);
      return false;
    }
  }
}
