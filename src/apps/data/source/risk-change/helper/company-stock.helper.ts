import { HolderRoleType, SharePledgeStatusType, EquityPledgeStatusType } from 'libs/constants/risk.change.constants';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { PersonData } from 'libs/model/data/source/PersonData';
import { getCompareResult, getCompareResultForArray } from 'libs/utils/diligence/diligence.utils';
import { processAmountString } from 'libs/utils/utils';
import { split, uniq } from 'lodash';
import { PersonHelper } from '../../../helper/person.helper';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CompanyStockHelper {
  constructor(private readonly personHelper: PersonHelper) {}
  /**
   * 股权出质
   * @param typeField
   * @param item
   */
  public category12Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    //所持股份被质押比例/总股本被质押比例 在95%以上
    const changeInfo = item.ChangeExtend;
    const hasPercent = changeInfo.Percent && Number(changeInfo.Percent) !== 0;
    if (hasPercent) {
      let rationValue;
      const match = changeInfo.Percent.match(/\d+(?:\.\d+)?/);
      if (match) {
        rationValue = parseFloat(match[0]);
      }
      const targetValue = typeField.fieldValue[0];
      const compareType = typeField.compareType;
      if (Number(changeInfo.T) === 2) {
        //占所持股份比例
        hit = getCompareResult(rationValue, targetValue, compareType);
      } else if (Number(changeInfo.T) === 1) {
        //占该公司股权比例
        hit = getCompareResult(rationValue, targetValue, compareType);
      }
    }
    return hit;
  }

  /**
   * 股权质押
   * @param typeField
   * @param item
   */
  public category50Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    //所持股份被质押比例/总股本被质押比例 在95%以上
    let hit = false;
    //所持股份被质押比例/总股本被质押比例 在95%以上
    const changeInfo = item.ChangeExtend;
    const hasRatioValue = changeInfo.C && Number(changeInfo.C) !== 0;
    if (hasRatioValue) {
      //changeInfo.Type === 1 ? '占所持股份比例' : '占总股本的比例',
      let rationValue;
      const match = changeInfo.C.match(/\d+(?:\.\d+)?/);
      if (match) {
        rationValue = parseFloat(match[0]);
      }
      const targetValue = typeField.fieldValue[0];
      const compareType = typeField.compareType;
      //占所持股份比例 或者 占总股本的比例
      hit = getCompareResult(rationValue, targetValue, compareType);
    }
    return hit;
  }

  /**
   * 司法拍卖限额
   * @param typeField
   * @param item
   */
  public limitPriceTypeField(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const targetValue = typeField.fieldValue?.[0];
    if (!targetValue) {
      //相当于没设置，就是不限的逻辑
      return true;
    }
    const changeInfo = item.ChangeExtend;
    if (!changeInfo?.C) {
      return hit;
    }
    const limitPrice = Number(changeInfo.C);
    const compareType = typeField.compareType;
    if (getCompareResult(limitPrice, targetValue, compareType)) {
      hit = true;
    }
    return hit;
  }

  /**
   * 企业公告
   * @param typeField
   * @param item
   */
  public categoryAnnouncementReportField(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const changeInfo = item.ChangeExtend;
    const targetValue = typeField.fieldValue;
    //公告类型
    const categoryTypeValue = split(changeInfo.TS, ',')
      .map((t) => Number(t))
      .filter((t) => t);
    if (!targetValue?.length) {
      //相当于没设置，就是不限的逻辑
      return true;
    }
    if (categoryTypeValue?.length) {
      const compareResult = getCompareResultForArray(typeField.compareType, categoryTypeValue, targetValue);
      if (compareResult) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 角色身份判断 keynos 是否 targetKeyNo 的 大股东或者实控人 中指定的任一身份
   * @param holderRoleField 对身份的属性设置
   * @param keyNo 需要判断身份的公司或个人
   * @param targetKeyNo 发生动态的公司
   */
  public async hitHolderRoleField(holderRoleField: DimensionHitStrategyFieldsEntity, keyNos: string[], targetKeyNo: string) {
    let hit = false;
    const hitKeyNos: string[] = [];
    const targetHolderRoleFieldValues = holderRoleField?.fieldValue as number[];
    if (targetHolderRoleFieldValues.includes(1)) {
      // 大股东判断
      // 取到 targetKeyNo的股东列表, 判断keyNo是否大股东
      const partnerList = await this.personHelper.getPartnerList(targetKeyNo, 'all');
      partnerList.forEach((partner) => {
        if (keyNos.includes(partner?.keyNo) && partner?.tags.includes('大股东')) {
          hitKeyNos.push(partner?.keyNo);
        }
      });
    }
    if (targetHolderRoleFieldValues.includes(2)) {
      // 实控人判断
      // 取到 targetKeyNo的实控人列表, 判断keyNo是否实控人
      const acList = await this.personHelper.getFinalActualController(targetKeyNo, false);
      acList.forEach((ac) => {
        if (keyNos.includes(ac?.keyNo)) {
          hitKeyNos.push(ac?.keyNo);
        }
      });
    }
    if (hitKeyNos?.length) {
      hit = true;
    }
    return { hit, hitKeyNos };
  }

  private getHitHolderKeyNo(typeField: DimensionHitStrategyFieldsEntity, employeeList: PersonData[]) {
    let keyNos: string[] = [];
    // 获取命中的主要人员
    if (employeeList?.length) {
      const targetValues = typeField?.fieldValue as number[];
      if (targetValues?.length) {
        targetValues.forEach((r) => {
          const label = HolderRoleType.find((item) => item.value === r).label;
          const employees = employeeList?.filter((e) => e.tags.includes(label));
          keyNos.push(...employees.map((t) => t.keyNo));
        });
      }
      if (keyNos?.length) {
        keyNos = uniq(keyNos);
      }
    }
    return keyNos;
  }

  /**
   * 股权冻结
   * @param holderRoleField
   * @param employeeList
   */
  public async holderRoleFieldCategory26(holderRoleField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    const employeeList: PersonData[] = [];
    if (holderRoleField.fieldValue.includes(1)) {
      // 大股东
      const partnerList = await this.personHelper.getPartnerList(keyNo, 'all');
      const bigStockers = partnerList.filter((partner) => partner?.tags.includes('大股东'));
      if (bigStockers?.length) {
        employeeList.push(...bigStockers);
      }
    }
    if (holderRoleField.fieldValue.includes(2)) {
      // 实控人
      const acList = await this.personHelper.getFinalActualController(keyNo, false);
      if (acList?.length) {
        employeeList.push(...acList);
      }
    }
    if (!employeeList?.length) {
      return hit;
    }
    const targetHolderRoleFieldValues = this.getHitHolderKeyNo(holderRoleField, employeeList);
    if (!targetHolderRoleFieldValues?.length) {
      return hit;
    }
    const changeInfo = item.ChangeExtend;
    if (changeInfo?.KeyNo && changeInfo?.CompanyId) {
      const sourceHolderRoleFieldValues = [changeInfo?.KeyNo];
      if (
        changeInfo.CompanyId === keyNo &&
        targetHolderRoleFieldValues?.length &&
        sourceHolderRoleFieldValues?.length &&
        getCompareResultForArray(holderRoleField.compareType, sourceHolderRoleFieldValues, targetHolderRoleFieldValues)
      ) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 股权质押
   * @param holderRoleField
   * @param employeeList
   */
  public async holderRoleFieldCategory50(holderRoleField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    const employeeList: PersonData[] = [];
    if (holderRoleField.fieldValue.includes(1)) {
      // 大股东
      const partnerList = await this.personHelper.getPartnerList(keyNo, 'all');
      const bigStockers = partnerList.filter((partner) => partner?.tags.includes('大股东'));
      if (bigStockers?.length) {
        employeeList.push(...bigStockers);
      }
    }
    if (holderRoleField.fieldValue.includes(2)) {
      // 实控人
      const acList = await this.personHelper.getFinalActualController(keyNo, false);
      if (acList?.length) {
        employeeList.push(...acList);
      }
    }
    if (!employeeList?.length) {
      return hit;
    }
    const targetHolderRoleFieldValues = this.getHitHolderKeyNo(holderRoleField, employeeList);
    if (!targetHolderRoleFieldValues?.length) {
      return hit;
    }
    const changeInfo = item.ChangeExtend;
    if (changeInfo?.HolderArray?.length && changeInfo?.Company?.K) {
      const sourceHolderRoleFieldValues = changeInfo.HolderArray.map((h) => h.KeyNo);
      if (
        changeInfo.Company.K === keyNo &&
        targetHolderRoleFieldValues?.length &&
        sourceHolderRoleFieldValues?.length &&
        getCompareResultForArray(holderRoleField.compareType, sourceHolderRoleFieldValues, targetHolderRoleFieldValues)
      ) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   *  股权冻结金额
   * @param equityFrozenAmountField
   * @param item
   */
  public equityFrozenAmountFieldCategory26(equityFrozenAmountField: DimensionHitStrategyFieldsEntity, item: any) {
    const changeInfo = item.ChangeExtend;
    // changeInfo 中的数据结构 changeInfo.EquityAmount = 4500万元人民币
    let hit = false;
    if (!changeInfo?.EquityAmount) {
      return hit;
    }
    const sourceValue = processAmountString(changeInfo.EquityAmount);
    if (sourceValue !== null) {
      const targetValue1 = equityFrozenAmountField?.fieldValue[0];
      const targetValue2 = equityFrozenAmountField?.fieldValue[1];
      const compareType = equityFrozenAmountField.compareType;
      if (getCompareResult(sourceValue, targetValue1, compareType, targetValue2)) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   *  股权质押-质押占总股本比例
   * @param stockPledgeRatioField
   * @param item
   */
  public stockPledgeRatioFieldCategory50(stockPledgeRatioField: DimensionHitStrategyFieldsEntity, item: any) {
    const changeInfo = item.ChangeExtend;
    // changeInfo 中的数据结构 changeInfo.C = 3.59%
    let hit = false;
    if (!changeInfo?.C) {
      return hit;
    }
    const sourceValue = processAmountString(changeInfo.C, true);
    if (sourceValue !== null) {
      const targetValue1 = stockPledgeRatioField?.fieldValue[0];
      const targetValue2 = stockPledgeRatioField?.fieldValue[1];
      if (getCompareResult(sourceValue, targetValue1, stockPledgeRatioField.compareType, targetValue2)) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   *  股权质押-质押股份数量(股)
   * @param stockPledgeQuantityField
   * @param item
   */
  public stockPledgeQuantityFieldCategory50(stockPledgeQuantityField: DimensionHitStrategyFieldsEntity, item: any) {
    const changeInfo = item.ChangeExtend;
    // changeInfo 中的数据结构 changeInfo.B = 1095.00万
    let hit = false;
    if (!changeInfo?.B) {
      return hit;
    }
    const sourceValue = processAmountString(changeInfo.B);
    if (sourceValue !== null) {
      const targetValue1 = stockPledgeQuantityField?.fieldValue[0];
      const targetValue2 = stockPledgeQuantityField?.fieldValue[1];
      if (getCompareResult(sourceValue, targetValue1, stockPledgeQuantityField.compareType, targetValue2)) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 股权质押的状态
   * @param sharePledgeStatusField
   * @param newItem
   */
  public sharePledgeStatusFieldCategory50(sharePledgeStatusField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const sharePledgeStatusFieldTargetValues = sharePledgeStatusField?.fieldValue as number[];
    const sharePledgeStatusFieldSourceValues: number[] = [];
    if (item?.ChangeExtend?.T) {
      SharePledgeStatusType.forEach((t) => {
        if (item?.ChangeExtend?.T === t.label) {
          sharePledgeStatusFieldSourceValues.push(t.value);
        }
      });
    }
    if (
      sharePledgeStatusFieldTargetValues?.length &&
      sharePledgeStatusFieldSourceValues?.length &&
      getCompareResultForArray(sharePledgeStatusField.compareType, sharePledgeStatusFieldSourceValues, sharePledgeStatusFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 股权出质
   * @param holderRoleField
   * @param employeeList
   */
  public async holderRoleFieldCategory12(holderRoleField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    const employeeList: PersonData[] = [];
    if (holderRoleField.fieldValue.includes(1)) {
      // 大股东
      const partnerList = await this.personHelper.getPartnerList(keyNo, 'all');
      const bigStockers = partnerList.filter((partner) => partner?.tags.includes('大股东'));
      if (bigStockers?.length) {
        employeeList.push(...bigStockers);
      }
    }
    if (holderRoleField.fieldValue.includes(2)) {
      // 实控人
      const acList = await this.personHelper.getFinalActualController(keyNo, false);
      if (acList?.length) {
        employeeList.push(...acList);
      }
    }
    if (!employeeList?.length) {
      return hit;
    }
    const targetHolderRoleFieldValues = this.getHitHolderKeyNo(holderRoleField, employeeList);
    if (!targetHolderRoleFieldValues?.length) {
      return hit;
    }
    const changeInfo = item.ChangeExtend;
    if (changeInfo?.KeyNo && changeInfo?.CompanyId) {
      const sourceHolderRoleFieldValues = [changeInfo?.KeyNo];
      if (
        changeInfo.CompanyId === keyNo &&
        targetHolderRoleFieldValues?.length &&
        sourceHolderRoleFieldValues?.length &&
        getCompareResultForArray(holderRoleField.compareType, sourceHolderRoleFieldValues, targetHolderRoleFieldValues)
      ) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 股权出质的状态
   * @param equityPledgeStatusField
   * @param newItem
   */
  public equityPledgeStatusFieldCategory12(equityPledgeStatusField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const equityPledgeStatusFieldTargetValues = equityPledgeStatusField?.fieldValue as number[];
    const equityPledgeStatusFieldSourceValues: number[] = [];
    if (item?.ChangeExtend?.Status) {
      EquityPledgeStatusType.forEach((t) => {
        if (item?.ChangeExtend?.Status === t.label) {
          equityPledgeStatusFieldSourceValues.push(t.value);
        }
      });
    }
    if (
      equityPledgeStatusFieldTargetValues?.length &&
      equityPledgeStatusFieldSourceValues?.length &&
      getCompareResultForArray(equityPledgeStatusField.compareType, equityPledgeStatusFieldSourceValues, equityPledgeStatusFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   *  股权出质比例
   * @param equityPledgeRatioField
   * @param item
   */
  public equityPledgeRatioFieldCategory12(equityPledgeRatioField: DimensionHitStrategyFieldsEntity, item: any) {
    const changeInfo = item.ChangeExtend;
    // changeInfo 中的数据结构 changeInfo.Percent = 100.00%
    let hit = false;
    if (!changeInfo?.Percent) {
      return hit;
    }
    const sourceValue = processAmountString(changeInfo.Percent, true);
    if (sourceValue !== null) {
      const targetValue1 = equityPledgeRatioField?.fieldValue[0];
      const targetValue2 = equityPledgeRatioField?.fieldValue[1];
      if (getCompareResult(sourceValue, targetValue1, equityPledgeRatioField.compareType, targetValue2)) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 出质股份数量
   * @param equityPledgeQuantityField
   * @param item
   */
  public equityPledgeQuantityFieldCategory12(equityPledgeAmountField: DimensionHitStrategyFieldsEntity, item: any) {
    const changeInfo = item.ChangeExtend;
    // changeInfo 中的数据结构 changeInfo.PledgedAmount = 126.9472万股
    let hit = false;
    if (!changeInfo?.PledgedAmount || !changeInfo?.PledgedAmount.includes('股')) {
      return hit;
    }
    const sourceValue = processAmountString(changeInfo.PledgedAmount);
    if (sourceValue !== null) {
      const targetValue1 = equityPledgeAmountField?.fieldValue[0];
      const targetValue2 = equityPledgeAmountField?.fieldValue[1];
      if (getCompareResult(sourceValue, targetValue1, equityPledgeAmountField.compareType, targetValue2)) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 出质股权数额
   * @param equityPledgeAmountField
   * @param item
   */
  public equityPledgeAmountFieldCategory12(equityPledgeAmountField: DimensionHitStrategyFieldsEntity, item: any) {
    const changeInfo = item.ChangeExtend;
    // changeInfo 中的数据结构 changeInfo.PledgedAmount = 13000.0万元
    let hit = false;
    if (!changeInfo?.PledgedAmount || !changeInfo?.PledgedAmount.includes('元')) {
      return hit;
    }
    const sourceValue = processAmountString(changeInfo.PledgedAmount);
    if (sourceValue !== null) {
      const targetValue1 = equityPledgeAmountField?.fieldValue[0];
      const targetValue2 = equityPledgeAmountField?.fieldValue[1];
      if (getCompareResult(sourceValue, targetValue1, equityPledgeAmountField.compareType, targetValue2)) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 资产拍卖
   * @param typeField
   * @param item
   */
  public category75Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const changeInfo = item.ChangeExtend;
    if (!changeInfo?.B) {
      return hit;
    }
    const limitPrice = Number(changeInfo.B);
    const targetValue = typeField.fieldValue?.[0];
    if (!targetValue) {
      //相当于没设置，就是不限的逻辑
      return true;
    }
    const compareType = typeField.compareType;
    if (getCompareResult(limitPrice, targetValue, compareType)) {
      hit = true;
    }
    return hit;
  }

  /**
   * 询价评估
   * @param typeField
   * @param item
   */
  public category59Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    if (!typeField?.fieldValue?.length) {
      return true;
    }
    const changeInfo = item.ChangeExtend;
    changeInfo.G = changeInfo.G ? JSON.parse(changeInfo.G) : {};
    let tNames = [];
    //询价结果是一个区间，取最小值和最大值
    const limitPrice = [];
    if (changeInfo.G?.length) {
      tNames = changeInfo.G.sort((a, b) => parseFloat(a.EvaluationPrice) - parseFloat(b.EvaluationPrice));
      if (tNames.length > 1) {
        limitPrice.push(Number(tNames[0].EvaluationPrice));
        limitPrice.push(Number(tNames[tNames.length - 1].EvaluationPrice));
      } else {
        limitPrice.push(Number(tNames[0].EvaluationPrice));
      }
    }
    const compareType = typeField.compareType;
    const targetValue = typeField.fieldValue[0];
    //对询价结果进行比较，因为询价结果为区间，最少 1 个值，最多两个值
    if (limitPrice.length === 2) {
      const compareResult1 = getCompareResult(limitPrice[0], targetValue, compareType);
      const compareResult2 = getCompareResult(limitPrice[1], targetValue, compareType);
      if (compareResult1 || compareResult2) {
        hit = true;
      }
    } else if (limitPrice.length === 1) {
      const compareResult = getCompareResult(limitPrice[0], targetValue, compareType);
      if (compareResult) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 股权冻结：
   * equityFreezeScope 股权冻结范围：changeInfo.T2 !== 1  不等1 （失效，无效，解除） changeInfo?.T === 1 企业股权被冻结  , changeInfo?.T === 2 持有股权被冻结
   * @param equityFreezeScopeField
   * @param item
   */
  public equityFreezeScopeFieldCategory26(equityFreezeScopeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const equityFreezeScopeTargetValues = equityFreezeScopeField?.fieldValue as number[];
    const equityFreezeScopeSourceValues: number[] = [];
    if (item?.ChangeExtend?.T2 === 1) {
      // 失效，无效，解除 的非新增的数据
      return hit;
    }
    if (item?.ChangeExtend?.T === 1) {
      //企业股权被冻结
      equityFreezeScopeSourceValues.push(1);
    } else if (item?.ChangeExtend?.T === 2) {
      //持有股权被冻结
      equityFreezeScopeSourceValues.push(2);
    }
    if (
      equityFreezeScopeTargetValues?.length &&
      equityFreezeScopeSourceValues?.length &&
      getCompareResultForArray(equityFreezeScopeField.compareType, equityFreezeScopeSourceValues, equityFreezeScopeTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }
}
