import { CompChangeAnalysisRole } from 'libs/constants/risk.change.constants';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { PersonData } from 'libs/model/data/source/PersonData';
import { getCompareResult, getCompareResultForArray } from 'libs/utils/diligence/diligence.utils';
import * as _ from 'lodash';
import { Injectable } from '@nestjs/common';

@Injectable()
export class MainEmployeeHelper {
  /**
   * 主要人员变更
   * @param compChangeRoleField // 变更角色
   * @param changeThresholdField  // 变更阀值
   * @param newItem  // 当前的这条变更记录
   * @param Results   // 周期年内所有变更记录
   * @param personDatas // 当前的主要人员
   * 测试Id：14c564b52ebb12a9d463559c640cfc53
   *
   * 分子: 周期内当前角色的变更次数,人员去重
   * 分母：自然年初当前角色的人数
   *
   * 分母如果是0 的情况下，就是一直新增的，变更就是100%
   *
   */

  public changeThresholdField(
    compChangeRoleField: DimensionHitStrategyFieldsEntity,
    changeThresholdField: DimensionHitStrategyFieldsEntity,
    newItem: any,
    periodResults: any[],
    personDatas: PersonData[],
  ) {
    let hit = false;
    const compChangeRoleFieldTargetValues = compChangeRoleField?.fieldValue as number[];
    const targetRoleLabel = CompChangeAnalysisRole?.find((t) => t.value === compChangeRoleFieldTargetValues[0])?.label;
    // 1，自然年内所有当前角色的变更记录
    const incRoleChangeList: any[] = [];
    const decRoleChangeList: any[] = [];
    periodResults.forEach((t) => {
      const ChangeExtend = t?.ChangeExtend ? JSON.parse(t?.ChangeExtend) : null;
      if (ChangeExtend) {
        // 变更
        if (ChangeExtend?.D?.length) {
          const B_RoleChangeList = ChangeExtend?.D?.filter((t) => t?.B?.includes(targetRoleLabel)) || [];
          if (B_RoleChangeList?.length) {
            decRoleChangeList.push(...B_RoleChangeList);
          }
          const C_RoleChangeList = ChangeExtend?.D?.filter((t) => t?.C?.includes(targetRoleLabel)) || [];
          if (C_RoleChangeList?.length) {
            incRoleChangeList.push(...C_RoleChangeList);
          }
        }
        // 退出
        if (ChangeExtend?.E?.length) {
          const E_RoleChangeList = ChangeExtend?.E?.filter((t) => t?.B?.includes(targetRoleLabel)) || [];
          if (E_RoleChangeList?.length) {
            decRoleChangeList.push(...E_RoleChangeList);
          }
        }
        // 新增
        if (ChangeExtend?.F?.length) {
          const F_RoleChangeList = ChangeExtend?.F?.filter((t) => t?.B?.includes(targetRoleLabel)) || [];
          if (F_RoleChangeList?.length) {
            incRoleChangeList.push(...F_RoleChangeList);
          }
        }
      }
    });
    // 2，当前公司所有当前角色人员的总个数
    const personCount = personDatas?.filter((t) => t?.job?.includes(targetRoleLabel))?.length;
    // 3, 分母roleChangeCount: 自然年初当前角色的人数
    const initCount = personCount - incRoleChangeList.length + decRoleChangeList.length;
    // 4, 分子totalChangeCount: 计算变更的人数
    const allList = [...incRoleChangeList, ...decRoleChangeList];
    const nonNullKeyNoList = allList.filter((item) => item.K !== null);
    const nullKeyNoList = allList.filter((item) => item.K === null);
    const uniqueKeyNoSet = new Set(nonNullKeyNoList.map((item) => item.K)); // 以 keyNo 去重计算 nonNullKeyNoList 的数量
    const nonNullKeyNoCount = uniqueKeyNoSet.size;
    const uniqueNameSet = new Set(nullKeyNoList.map((item) => item.A)); // 以 name 去重计算 nullKeyNoList 的数量
    const nullKeyNoCount = uniqueNameSet.size;
    const totalChangeCount = nonNullKeyNoCount + nullKeyNoCount;
    const changeThresholdFieldTargetValues = changeThresholdField?.fieldValue as number[];
    // 4，当前公司所有当前角色人员的总个数/自然年内当前角色的变更记录个数
    let changeThresholdFieldSourceValue;
    if (totalChangeCount === 0) {
      // 分子=0没有变更记录
      hit = false;
    } else {
      if (initCount === 0) {
        hit = true; // 分母为0的时候，变更率为100%
      } else {
        changeThresholdFieldSourceValue = (totalChangeCount / initCount) * 100;
      }
    }
    if (
      changeThresholdFieldTargetValues?.length &&
      changeThresholdFieldSourceValue !== null &&
      getCompareResult(changeThresholdFieldSourceValue, changeThresholdFieldTargetValues[0], changeThresholdField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 主要人员变更分析，变更角色
   * @param compChangeRoleField
   * @param newItem
   */
  public hitCompChangeRoleField(compChangeRoleField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const compChangeRoleFieldTargetValues = compChangeRoleField?.fieldValue as number[];
    let compChangeRoleFieldSourceValues: number[] = [];
    // 变更
    const DChange = (item?.ChangeExtend?.D as any[]) || [];
    const EChange = (item?.ChangeExtend?.E as any[]) || [];
    const FChange = (item?.ChangeExtend?.F as any[]) || [];
    const combinedChangeList = _.concat(DChange, EChange, FChange);
    if (combinedChangeList?.length) {
      CompChangeAnalysisRole.forEach((t) => {
        combinedChangeList.forEach((r) => {
          if (r?.B?.includes(t.label) || r?.C?.includes(t.label)) {
            compChangeRoleFieldSourceValues.push(t.value);
          }
        });
      });
    }
    compChangeRoleFieldSourceValues = _.uniq(compChangeRoleFieldSourceValues);
    if (
      compChangeRoleFieldTargetValues?.length &&
      compChangeRoleFieldSourceValues?.length &&
      getCompareResultForArray(compChangeRoleField.compareType, compChangeRoleFieldSourceValues, compChangeRoleFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }
}
