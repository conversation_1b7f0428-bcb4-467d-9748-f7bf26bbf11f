import { CourtRole } from 'libs/constants/case.constants';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { getCompareResultForArray } from 'libs/utils/diligence/diligence.utils';
import * as _ from 'lodash';
import { Injectable } from '@nestjs/common';

@Injectable()
export class JudgementHelper {
  /**
   * 法院公告
   * @param judicialRoleTypeField
   * @param item
   */
  public category7(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const judicialRoleTargetValues = judicialRoleTypeField?.fieldValue as string[];
    let judicialRoleSourceValues: string[] = [];
    const RNList = item?.ChangeExtend?.J?.filter((t) => t?.KeyNo === item?.KeyNo)
      .map((r) => r?.RoleType)
      .filter((t) => !!t);
    if (RNList?.length) {
      const courtRoleTypes = CourtRole.map((t) => Number(t.value));
      RNList.forEach((t) => {
        if (courtRoleTypes.includes(t)) {
          judicialRoleSourceValues.push(String(t));
        }
      });
    }
    judicialRoleSourceValues = _.uniq(judicialRoleSourceValues);
    if (
      judicialRoleTargetValues?.length &&
      judicialRoleSourceValues?.length &&
      getCompareResultForArray(judicialRoleTypeField.compareType, judicialRoleSourceValues, judicialRoleTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   *  送达公告
   * @param judicialRoleTypeField
   * @param item
   */
  public category27(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const judicialRoleTargetValues = judicialRoleTypeField?.fieldValue as string[];
    let judicialRoleSourceValues: string[] = [];
    const RNList = item?.ChangeExtend?.D?.filter((t) => t?.N === item?.KeyNo)
      .map((r) => r?.RT)
      .filter((t) => !!t);
    if (RNList?.length) {
      const courtRoleTypes = CourtRole.map((t) => Number(t.value));
      RNList.forEach((t) => {
        if (courtRoleTypes.includes(t)) {
          judicialRoleSourceValues.push(String(t));
        }
      });
    }
    judicialRoleSourceValues = _.uniq(judicialRoleSourceValues);
    if (
      judicialRoleTargetValues?.length &&
      judicialRoleSourceValues?.length &&
      getCompareResultForArray(judicialRoleTypeField.compareType, judicialRoleSourceValues, judicialRoleTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 诉前调解
   * @param judicialRoleTypeField
   * @param item
   */
  public category90(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const judicialRoleTargetValues = judicialRoleTypeField?.fieldValue as string[];
    let judicialRoleSourceValues: string[] = [];
    const RNList = item?.ChangeExtend?.I?.filter((t) => t?.KeyNo === item?.KeyNo)
      .map((r) => r?.RoleType)
      .filter((t) => !!t);
    if (RNList?.length) {
      const courtRoleTypes = CourtRole.map((t) => Number(t.value));
      RNList.forEach((t) => {
        if (courtRoleTypes.includes(t)) {
          judicialRoleSourceValues.push(String(t));
        }
      });
    }
    judicialRoleSourceValues = _.uniq(judicialRoleSourceValues);
    if (
      judicialRoleTargetValues?.length &&
      judicialRoleSourceValues?.length &&
      getCompareResultForArray(judicialRoleTypeField.compareType, judicialRoleSourceValues, judicialRoleTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 开庭公告
   * @param judicialRoleTypeField
   * @param item
   */
  public category18(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const judicialRoleTargetValues = judicialRoleTypeField?.fieldValue as string[];
    let judicialRoleSourceValues: string[] = [];
    const RNList = item?.ChangeExtend?.D?.filter((t) => t?.N === item?.KeyNo)
      .map((r) => r?.RT)
      .filter((t) => !!t);
    if (RNList?.length) {
      const courtRoleTypes = CourtRole.map((t) => Number(t.value));
      RNList.forEach((t) => {
        if (courtRoleTypes.includes(t)) {
          judicialRoleSourceValues.push(String(t));
        }
      });
    }
    judicialRoleSourceValues = _.uniq(judicialRoleSourceValues);
    if (
      judicialRoleTargetValues?.length &&
      judicialRoleSourceValues?.length &&
      getCompareResultForArray(judicialRoleTypeField.compareType, judicialRoleSourceValues, judicialRoleTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 立案信息
   * @param judicialRoleTypeField
   * @param item
   */
  public category49(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const judicialRoleTargetValues = judicialRoleTypeField?.fieldValue as string[];
    let judicialRoleSourceValues: string[] = [];
    const RNList = item?.ChangeExtend?.I?.filter((t) => t?.KeyNo === item?.KeyNo)
      .map((r) => r?.RoleType)
      .filter((t) => !!t);
    if (RNList?.length) {
      const courtRoleTypes = CourtRole.map((t) => Number(t.value));
      RNList.forEach((t) => {
        if (courtRoleTypes.includes(t)) {
          judicialRoleSourceValues.push(String(t));
        }
      });
    }
    judicialRoleSourceValues = _.uniq(judicialRoleSourceValues);
    if (
      judicialRoleTargetValues?.length &&
      judicialRoleSourceValues?.length &&
      getCompareResultForArray(judicialRoleTypeField.compareType, judicialRoleSourceValues, judicialRoleTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**

   * 裁判文书：ChangeExtend K.RN---->KeyNo 过滤得到RN(被上诉人（原审原告）,被上诉人（原审原告、反诉被告）,被告人,被告,原审第三人)
   * @param judicialRoleTypeField
   * @param item
   */
  public category4(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const judicialRoleTargetValues = judicialRoleTypeField?.fieldValue as string[];
    let judicialRoleSourceValues: string[] = [];
    const RNList = item?.ChangeExtend?.K?.filter((t) => t?.KeyNo === item?.KeyNo || t?.SupNameAndKeyNo?.KeyNo === item?.KeyNo)
      .map((r) => r?.RT)
      .filter((t) => !!t); // ES数据样例 "K":[{"KeyNo":"9d617b6a180e982c42166d71f5281f8c","R":0,"RT":12,"T":"","Org":0,"RN":"申请执行人","ShowName":"宁波银行股份有限公司杭州城西支行","Name":"宁波银行股份有限公司杭州城西支行"},{"KeyNo":"abc948a75096352ecea04ce614b8efc4","R":0,"RT":12,"T":"","Org":0,"RN":"申请执行人","ShowName":"杭州东信创业投资有限公司","Name":"杭州东信创业投资有限公司"},{"KeyNo":"63ca9d6fcf0049cdbf256ed530bcf219","R":1,"RT":22,"T":"","Org":0,"RN":"被执行人","ShowName":"飞耀控股集团有限公司","Name":"飞耀控股集团有限公司"},{"KeyNo":"p8e2db21088b414f9e203e179241c875","R":1,"RT":22,"T":"","Org":2,"RN":"被执行人","ShowName":"张跃飞","Name":"张跃飞"},{"KeyNo":"","R":1,"RT":22,"T":"","Org":-2,"RN":"被执行人","ShowName":"许*","Name":"许婷"}]
    if (RNList?.length) {
      const courtRoleTypes = CourtRole.map((t) => Number(t.value));
      RNList.forEach((t) => {
        if (courtRoleTypes.includes(t)) {
          judicialRoleSourceValues.push(String(t));
        }
      });
    }
    judicialRoleSourceValues = _.uniq(judicialRoleSourceValues);
    if (
      judicialRoleTargetValues?.length &&
      judicialRoleSourceValues?.length &&
      getCompareResultForArray(judicialRoleTypeField.compareType, judicialRoleSourceValues, judicialRoleTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }
}
