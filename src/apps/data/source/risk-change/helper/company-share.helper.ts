import { KysCompanySearchRequest } from '@kezhaozhao/company-search-api';
import { CompanySearchService } from 'apps/company/company-search.service';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { getCompareResult } from 'libs/utils/diligence/diligence.utils';
import { processAmountString } from 'libs/utils/utils';
import { PersonHelper } from '../../../helper/person.helper';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CompanyShareHelper {
  private logger: Logger = QccLogger.getLogger(CompanyShareHelper.name);
  constructor(private readonly personHelper: PersonHelper, private readonly companySearchService: CompanySearchService) {}
  /**
   * 判断近n个月，实际控制人合计增持或减持是否超过x%
   * @param holderRoleField 角色字段
   * @param shareChangeStatusField 变更趋势字段 (1增加, 0减少)
   * @param changeThresholdField 变更比例阈值字段
   * @param allPeriodList 近n个月的数据列表
   * @param keyNo 企业keyNo
   */
  public async calculatePeriodHolderRoleChangeThreshold(
    holderRoleField: DimensionHitStrategyFieldsEntity,
    shareChangeStatusField: DimensionHitStrategyFieldsEntity,
    changeThresholdField: DimensionHitStrategyFieldsEntity,
    allPeriodList: any[],
    keyNo: string,
  ): Promise<boolean> {
    try {
      if (!allPeriodList?.length || !holderRoleField || !shareChangeStatusField || !changeThresholdField) {
        return false;
      }

      // 获取目标角色类型值
      const targetHolderRoleFieldValues = holderRoleField?.fieldValue as number[];
      if (!targetHolderRoleFieldValues?.length) {
        return false;
      }

      // 获取变更趋势类型(增持或减持)
      const targetShareChangeStatus = shareChangeStatusField?.fieldValue?.[0] as number;
      if (targetShareChangeStatus === undefined || targetShareChangeStatus === null) {
        return false;
      }

      // 获取变更比例阈值
      const targetChangeThreshold = changeThresholdField?.fieldValue?.[0] as number;
      if (targetChangeThreshold === undefined || targetChangeThreshold === null) {
        return false;
      }

      // 根据角色过滤相关记录
      let filteredRecords = [];

      // 处理不同角色类型
      for (const roleType of targetHolderRoleFieldValues) {
        let roleRecords = [];

        if (roleType === 1) {
          // 大股东
          roleRecords = allPeriodList.filter((item) => {
            // IsBP为1表示有大股东变更
            return item.ChangeExtend?.IsBP === '1' || item.IsBP === '1';
          });
        } else if (roleType === 2) {
          // 实际控制人
          const acList = await this.personHelper.getFinalActualController(keyNo, false);
          const acKeyNos = acList.map((ac) => ac.keyNo);

          roleRecords = allPeriodList.filter((item) => {
            const itemKeyNo = item.ChangeExtend?.K || item.K;
            return acKeyNos.includes(itemKeyNo);
          });
        } else if (roleType === 3) {
          // 公司主体
          roleRecords = allPeriodList.filter((item) => {
            const itemKeyNo = item.ChangeExtend?.K || item.K;
            return itemKeyNo === keyNo;
          });
        }

        filteredRecords = [...filteredRecords, ...roleRecords];
      }

      // 根据变更趋势过滤记录
      filteredRecords = filteredRecords.filter((item) => {
        // 0减少, 1增加
        const changeStatus = item.ChangeStatus;
        return changeStatus === targetShareChangeStatus;
      });

      if (!filteredRecords.length) {
        return false;
      }

      // 计算变更比例总和
      let totalChangeRate = 0;
      for (const record of filteredRecords) {
        const beforeContent = record.BeforeContent || record.B || '0%';
        const afterContent = record.AfterContent || record.C || '0%';

        // 提取百分比数值
        const beforePercentMatch = String(beforeContent).match(/(\d+(?:\.\d+)?)/);
        const afterPercentMatch = String(afterContent).match(/(\d+(?:\.\d+)?)/);

        const beforePercent = beforePercentMatch ? parseFloat(beforePercentMatch[0]) : 0;
        const afterPercent = afterPercentMatch ? parseFloat(afterPercentMatch[0]) : 0;

        // 计算本次变更比例
        const changeRate = Math.abs(afterPercent - beforePercent);
        totalChangeRate += changeRate;
      }

      // 判断总变更比例是否超过阈值
      return getCompareResult(totalChangeRate, targetChangeThreshold, changeThresholdField.compareType);
    } catch (error) {
      this.logger.error(`calculatePeriodHolderRoleChangeThreshold error: ${error}`);
      return false;
    }
  }

  /**
   * 72 成员变更
   * 判断是否是PEVC 机构进来融资
   * @param isPEVCField
   * @param newItem
   * @param keyNo
   */
  async category72isPEVCField(isPEVCField: DimensionHitStrategyFieldsEntity, newItem: any, keyNo: string) {
    let hit = false;
    const allkeyNo: string[] = [];
    const changIncPartners = newItem?.ChangeExtend?.PartInfo?.H || [];
    const newIncPartners = newItem?.ChangeExtend?.PartInfo?.F || [];
    if (changIncPartners?.length) {
      const sourceKeyNos = changIncPartners?.map((item: any) => item.K) || [];
      allkeyNo.push(...sourceKeyNos);
    }
    if (newIncPartners?.length) {
      const sourceKeyNos = newIncPartners?.map((item: any) => item.K) || [];
      allkeyNo.push(...sourceKeyNos);
    }
    let sourceIsPEVC = 0;
    if (allkeyNo?.length) {
      const res = await this.companySearchService.companySearchForKys(
        Object.assign(new KysCompanySearchRequest(), {
          includeFields: ['id', 'name', 'commonlist'],
          pageIndex: 1,
          pageSize: allkeyNo.length,
          filter: { ids: allkeyNo },
        }),
      );
      if (res.Result.length) {
        res.Result.forEach((t) => {
          const PEVCList = t?.commonlist?.filter((r) => r.k === '2') || [];
          if (PEVCList?.length) {
            sourceIsPEVC = 1;
          }
        });
      }
    }
    const targetisPEVCFieldValues = isPEVCField.fieldValue as number[];
    if (!targetisPEVCFieldValues?.length) {
      return hit;
    }
    if (targetisPEVCFieldValues?.length && sourceIsPEVC != null && getCompareResult(sourceIsPEVC, targetisPEVCFieldValues[0], isPEVCField.compareType)) {
      hit = true;
    }
    return hit;
  }

  async category72periodShareRatioChangeField(
    periodShareRatioChangeField: DimensionHitStrategyFieldsEntity,
    item: any,
    keyNo: string,
    allPeriodList: any[],
    actualControllerKeyNo: string,
  ) {
    let hit = false;
    const periodShareRatioChangeFieldFieldValue = periodShareRatioChangeField.fieldValue[0] as any;
    const targetShareChangeRateValue = periodShareRatioChangeFieldFieldValue?.shareChangeRate;
    const shareChangeRateCompareType = periodShareRatioChangeFieldFieldValue?.shareChangeRateCompareType;
    // 先对item数据中的json字段做预处理
    const getPartner = (partInfoKey: string) =>
      allPeriodList
        .flatMap((item) => {
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              const value = item[key];
              try {
                item[key] = value ? JSON.parse(value) : {};
              } catch (error) {
                item[key] = value;
              }
            }
          });
          return item?.ChangeExtend?.PartInfo?.[partInfoKey] || [];
        })
        .find((partner) => partner.K === actualControllerKeyNo) || null;
    let maxStock = 0;
    const partners = [
      { partner: getPartner('D'), field: 'B' },
      { partner: getPartner('H'), field: 'B' },
      { partner: getPartner('F'), field: 'C' },
    ];
    for (const { partner, field } of partners) {
      if (partner) {
        const baseStock = partner[field] || '0';
        const match = baseStock.match(/\d+(?:\.\d+)?/);
        if (match) {
          const stock = parseFloat(match[0]);
          if (stock > maxStock) {
            maxStock = stock;
          }
        }
      }
    }
    let actorStock = 0;
    const partner = item?.ChangeExtend?.PartInfo?.D || [];
    const partnerInfo = partner?.find((t) => t.K === actualControllerKeyNo);
    const partnerStock = partnerInfo?.C || '0';
    const partnerMatch = partnerStock.match(/\d+(?:\.\d+)?/);
    if (partnerMatch) {
      actorStock = parseFloat(partnerMatch[0]);
    }
    // 计算变更比例
    let sourceShareChangeRateFieldValue = null;
    sourceShareChangeRateFieldValue = Math.abs(maxStock - actorStock);
    if (
      sourceShareChangeRateFieldValue &&
      targetShareChangeRateValue &&
      shareChangeRateCompareType &&
      getCompareResult(sourceShareChangeRateFieldValue, targetShareChangeRateValue, shareChangeRateCompareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 72 成员变更，变更趋势
   * @param shareChangeStatusField
   * @param newItem
   * @param keyNoHits
   */
  category72ShareChangeStatusField(shareChangeStatusField: DimensionHitStrategyFieldsEntity, newItem: any, keyNoHits: any[]) {
    let hit = false;
    if (!keyNoHits?.length) {
      return hit;
    }
    const decreasePartners = newItem?.ChangeExtend?.PartInfo?.D || [];
    const actorInfo = decreasePartners?.find((t) => t.K === keyNoHits[0]);
    if (!actorInfo?.B || !actorInfo?.C) {
      return hit;
    }
    let beforeStock = actorInfo?.B || '0';
    const beforeMatch = beforeStock.match(/\d+(?:\.\d+)?/);
    if (beforeMatch) {
      beforeStock = parseFloat(beforeMatch[0]);
    }
    let afterStock = actorInfo?.C || '0';
    const afterMatch = afterStock.match(/\d+(?:\.\d+)?/);
    if (afterMatch) {
      afterStock = parseFloat(afterMatch[0]);
    }
    let sourceShareChangeStatusFieldValue = null;
    if (beforeStock > afterStock) {
      sourceShareChangeStatusFieldValue = 0; // 股份减少
    } else {
      sourceShareChangeStatusFieldValue = 1; // 股份增加
    }
    const targetShareChangeStatusFieldValues = shareChangeStatusField.fieldValue as number[];
    if (!targetShareChangeStatusFieldValues?.length) {
      return hit;
    }
    if (
      targetShareChangeStatusFieldValues?.length &&
      sourceShareChangeStatusFieldValue != null &&
      getCompareResult(sourceShareChangeStatusFieldValue, targetShareChangeStatusFieldValues[0], shareChangeStatusField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 72成员变更
   * 判断是否是实际控制人的变更
   * @param holderRoleField
   * @param newItem
   * @param keyNo
   *   // PartInfo  D 股份下降， H 股份上升，F 股份新增
   */
  public async category72holderRoleField(holderRoleField: DimensionHitStrategyFieldsEntity, newItem: any, keyNo: string) {
    let hit = false;
    let hitKeyNos = [];
    const holderRoleFieldTargetValues = holderRoleField?.fieldValue as number[];
    if (!holderRoleFieldTargetValues?.length) {
      return { hit, hitKeyNos };
    }
    const decreasePartners = newItem?.ChangeExtend?.PartInfo?.D || [];
    //const changIncPartners = newItem?.ChangeExtend?.PartInfo?.H || [];
    //const newIncPartners = newItem?.ChangeExtend?.PartInfo?.F || [];
    if (!decreasePartners?.length) {
      return { hit, hitKeyNos };
    }
    const sourceKeyNos = decreasePartners?.map((item: any) => item.K) || [];
    if (holderRoleFieldTargetValues.includes(2)) {
      // 实际控制人
      const acList = await this.personHelper.getFinalActualController(keyNo, false);
      hitKeyNos = acList?.map((ac) => ac.keyNo) || [];
    }
    if (hitKeyNos?.length && sourceKeyNos?.length && sourceKeyNos.includes(hitKeyNos[0])) {
      hit = true;
    }
    return { hit, hitKeyNos };
  }

  /**
   * 判断是否是实际控制人
   * @param holderRoleField
   * @param item
   */
  async category114holderRoleField(holderRoleField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    let BeforeContent = null;
    let AfterContent = null;
    let ChangeExtend = null;
    for (const extend of item.ChangeExtend as any[]) {
      const Before = extend.BeforeContent ? JSON.parse(extend.BeforeContent) : {};
      const After = extend.AfterContent ? JSON.parse(extend.AfterContent) : {};
      const Change = extend.ChangeExtend ? JSON.parse(extend.ChangeExtend) : {};
      if (Change.T === 2) {
        BeforeContent = Before;
        AfterContent = After;
        ChangeExtend = Change;
        break;
      }
    }
    if (!BeforeContent || !AfterContent || !ChangeExtend) {
      return hit;
    }

    const holderRoleFieldTargetValues = holderRoleField?.fieldValue as number[];
    if (!holderRoleFieldTargetValues?.length) {
      return hit;
    }
    // TODO 目前只实现了实际控制人的数据
    if (holderRoleFieldTargetValues.includes(2)) {
      // 实际控制人
      const acList = await this.personHelper.getFinalActualController(keyNo, false);
      const acKeyNos = acList.map((ac) => ac.keyNo);
      const changeKeyNo = BeforeContent?.KeyNo || null;
      if (changeKeyNo && acKeyNos.includes(changeKeyNo)) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 变更趋势
   * @param shareChangeStatusField
   * @param item
   */
  async category114shareChangeStatusField(shareChangeStatusField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    let BeforeContent = null;
    let AfterContent = null;
    let ChangeExtend = null;
    for (const extend of item.ChangeExtend as any[]) {
      const Before = extend.BeforeContent ? JSON.parse(extend.BeforeContent) : {};
      const After = extend.AfterContent ? JSON.parse(extend.AfterContent) : {};
      const Change = extend.ChangeExtend ? JSON.parse(extend.ChangeExtend) : {};
      if (Change.T === 2) {
        BeforeContent = Before;
        AfterContent = After;
        ChangeExtend = Change;
        break;
      }
    }
    if (!BeforeContent || !AfterContent || !ChangeExtend) {
      return hit;
    }
    let beforeStock = BeforeContent?.PercentTotal || '0';
    const beforeMatch = beforeStock.match(/\d+(?:\.\d+)?/);
    if (beforeMatch) {
      beforeStock = parseFloat(beforeMatch[0]);
    }
    let afterStock = AfterContent?.PercentTotal || '0';
    const afterMatch = afterStock.match(/\d+(?:\.\d+)?/);
    if (afterMatch) {
      afterStock = parseFloat(afterMatch[0]);
    }
    let sourceShareChangeStatusFieldValue = null;
    if (beforeStock > afterStock) {
      sourceShareChangeStatusFieldValue = 0; // 股份减少
    } else {
      sourceShareChangeStatusFieldValue = 1; // 股份增加
    }

    const targetShareChangeStatusFieldValues = shareChangeStatusField.fieldValue as number[];
    if (!targetShareChangeStatusFieldValues?.length) {
      return hit;
    }

    if (
      targetShareChangeStatusFieldValues?.length &&
      sourceShareChangeStatusFieldValue != null &&
      getCompareResult(sourceShareChangeStatusFieldValue, targetShareChangeStatusFieldValues[0], shareChangeStatusField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 变更前持股比例
   * @param beforeContentField
   * @param newItem
   */
  category114beforeContentField(beforeContentField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    let BeforeContent = null;
    let AfterContent = null;
    let ChangeExtend = null;
    for (const extend of item.ChangeExtend as any[]) {
      const Before = extend.BeforeContent ? JSON.parse(extend.BeforeContent) : {};
      const After = extend.AfterContent ? JSON.parse(extend.AfterContent) : {};
      const Change = extend.ChangeExtend ? JSON.parse(extend.ChangeExtend) : {};
      if (Change.T === 2) {
        BeforeContent = Before;
        AfterContent = After;
        ChangeExtend = Change;
        break;
      }
    }
    if (!BeforeContent || !AfterContent || !ChangeExtend) {
      return hit;
    }

    const targetbeforeContentFieldValues = beforeContentField.fieldValue as number[];
    if (!targetbeforeContentFieldValues?.length) {
      return hit;
    }
    let sourcebeforeContentFieldValue = BeforeContent?.PercentTotal || '0';
    const match = sourcebeforeContentFieldValue.match(/\d+(?:\.\d+)?/);
    if (match) {
      sourcebeforeContentFieldValue = parseFloat(match[0]);
    }
    if (
      targetbeforeContentFieldValues?.length &&
      sourcebeforeContentFieldValue &&
      getCompareResult(sourcebeforeContentFieldValue, targetbeforeContentFieldValues[0], beforeContentField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  category114afterContentField(afterContentField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    let BeforeContent = null;
    let AfterContent = null;
    let ChangeExtend = null;
    for (const extend of item.ChangeExtend as any[]) {
      const Before = extend.BeforeContent ? JSON.parse(extend.BeforeContent) : {};
      const After = extend.AfterContent ? JSON.parse(extend.AfterContent) : {};
      const Change = extend.ChangeExtend ? JSON.parse(extend.ChangeExtend) : {};
      if (Change.T === 2) {
        BeforeContent = Before;
        AfterContent = After;
        ChangeExtend = Change;
        break;
      }
    }
    if (!BeforeContent || !AfterContent || !ChangeExtend) {
      return hit;
    }

    const targetafterContentFieldValues = afterContentField.fieldValue as number[];
    if (!targetafterContentFieldValues?.length) {
      return hit;
    }
    let sourceafterContentFieldValue = AfterContent.PercentTotal || '0';
    const match = sourceafterContentFieldValue.match(/\d+(?:\.\d+)?/);
    if (match) {
      sourceafterContentFieldValue = parseFloat(match[0]);
    }
    if (
      targetafterContentFieldValues?.length &&
      sourceafterContentFieldValue &&
      getCompareResult(sourceafterContentFieldValue, targetafterContentFieldValues[0], afterContentField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /* public equityPledgeRatioFieldCategory12(equityPledgeRatioField: DimensionHitStrategyFieldsEntity, item: any) {
                   const changeInfo = item.ChangeExtend;
                   // changeInfo 中的数据结构 changeInfo.Percent = 100.00%
                   let hit = false;
                   if (!changeInfo?.Percent) {
                     return hit;
                   }
                   const sourceValue = processAmountString(changeInfo.Percent, true);
                   if (sourceValue !== null) {
                     const targetValue1 = equityPledgeRatioField?.fieldValue[0];
                     const targetValue2 = equityPledgeRatioField?.fieldValue[1];
                     if (getCompareResult(sourceValue, targetValue1, equityPledgeRatioField.compareType, targetValue2)) {
                       hit = true;
                     }
                   }
                   return hit;
                 }*/

  /**
   * 差值比例
   * @param differenceRatioField
   * @param holders
   * @param keyNoHits
   */
  hitDifferenceRatioField(differenceRatioField: DimensionHitStrategyFieldsEntity, holders: any[], keyNoHits: string[]) {
    let hit = false;
    if (!holders?.length && !keyNoHits?.length) {
      return hit;
    }
    const hitsHolderKeyNos: string[] = [];
    keyNoHits.forEach((t) => {
      // holders中的K === t 的数据
      const holder = holders.find((h) => h.K === t);
      if (holder && holder?.B && holder?.C) {
        // B 为之前的持股比例
        // C 为之后的持股比例
        const beforeContent = processAmountString(holder?.B, true);
        const afterContent = processAmountString(holder?.C, true);
        // ((B-C)/B) *100 保留两位小数
        const sourceValue = parseFloat(((beforeContent - afterContent) / beforeContent).toFixed(2)) * 100;
        const targetValue = differenceRatioField.fieldValue[0];
        if (sourceValue && targetValue && getCompareResult(sourceValue, targetValue, differenceRatioField.compareType)) {
          hitsHolderKeyNos.push(t);
        }
      }
    });
    if (hitsHolderKeyNos?.length) {
      hit = true;
    }
    return hit;
  }

  /**
   * 绝对值占比
   ;   ;* @param absRatioField
   * @param holders
   * @param keyNoHits
   */
  hitAbsRatioField(absRatioField: DimensionHitStrategyFieldsEntity, holders: any[], keyNoHits: string[]) {
    let hit = false;
    if (!holders?.length && !keyNoHits?.length) {
      return hit;
    }
    const hitsHolderKeyNos: string[] = [];
    keyNoHits.forEach((t) => {
      // holders中的K === t 的数据
      const holder = holders.find((h) => h.K === t);
      if (holder && holder?.B && holder?.C) {
        // B 为之前的持股比例
        // C 为之后的持股比例
        const beforeContent = processAmountString(holder?.B, true);
        const afterContent = processAmountString(holder?.C, true);
        // (B-C )*100 保留两位小数
        const sourceValue = parseFloat((beforeContent - afterContent).toFixed(2));
        const targetValue = absRatioField.fieldValue[0];
        if (sourceValue && targetValue && getCompareResult(sourceValue, targetValue, absRatioField.compareType)) {
          hitsHolderKeyNos.push(t);
        }
      }
    });
    if (hitsHolderKeyNos?.length) {
      hit = true;
    }
    return hit;
  }
}
