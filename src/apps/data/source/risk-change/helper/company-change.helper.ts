import { Injectable } from '@nestjs/common';
import { CompanySearchService } from 'apps/company/company-search.service';
import {
  BusinessStatusMap,
  CapitalReductionRateType,
  ChangeRegisteredCapitalType,
  SimpleCancelTypeConstant,
  STIChangeRegisteredCapitalType,
} from 'libs/constants/risk.change.constants';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { excludeAmountUnits, getCompareResult, getCompareResultForArray } from 'libs/utils/diligence/diligence.utils';
import * as _ from 'lodash';

@Injectable()
export class CompanyChangeHelper {
  /**
   * 命中法人代表变更
   * @param layTypesField
   * @param ChangeExtend
   * @public
   */
  public hitLayTypesField(layTypesField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const layTypesFieldTargetValues = layTypesField?.fieldValue as number[];
    if (layTypesFieldTargetValues?.length) {
      const layTypesFieldSourceValue = item?.ChangeExtend?.C;
      if (layTypesFieldSourceValue && getCompareResultForArray(layTypesField.compareType, [layTypesFieldSourceValue], layTypesFieldTargetValues)) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 命中法人代表变更 72
   * @param layTypesField
   * @param ChangeExtend
   * @public
   */
  public hitLayTypesField72(layTypesField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    //法人变更 72 特殊处理
    Object.keys(item).forEach((key) => {
      if (['ChangeExtend'].includes(key)) {
        const value = item[key];
        try {
          item[key] = value ? JSON.parse(value) : {};
        } catch (error) {
          item[key] = value;
        }
      }
    });
    let hit = false;
    const layTypesFieldTargetValues = layTypesField?.fieldValue as number[];
    if (layTypesFieldTargetValues?.length && layTypesFieldTargetValues.includes(1)) {
      if (
        [DimensionFieldCompareTypeEnums.ExceptAny, DimensionFieldCompareTypeEnums.ExceptAll].includes(layTypesField.compareType) &&
        item?.ChangeExtend?.OperInfo === null
      ) {
        hit = true;
        item.ChangeExtend.OperInfo = null;
        item.ChangeExtend = JSON.stringify(item.ChangeExtend);
        return hit;
      }
      const layTypesFieldSourceValue = item?.ChangeExtend?.OperInfo?.C;
      if (layTypesFieldSourceValue && getCompareResultForArray(layTypesField.compareType, [layTypesFieldSourceValue], layTypesFieldTargetValues)) {
        hit = true;
        item.ChangeExtend.PartInfo = null;
        item.ChangeExtend.EmpInfo = null;
        item.ChangeExtend = JSON.stringify(item.ChangeExtend);
      }
    }
    return hit;
  }

  /**动产抵押*/
  public category15Field(dimension: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const changeInfo = item.ChangeExtend ? item.ChangeExtend : {};
    if (changeInfo.A) {
      changeInfo.A = changeInfo.A.trim();
      changeInfo.A = excludeAmountUnits(changeInfo.A);
      const targetValue = dimension.fieldValue[0];
      const compareType = dimension.compareType;
      const sourceValue = Number(changeInfo.A) || 0;
      if (targetValue >= 0 && getCompareResult(sourceValue, targetValue, compareType)) {
        hit = true;
      }
    }
    return hit;
  }

  //判断被申请人是否为目标企业 被申请人取changeExtend.C
  // 破产重整
  public category58Field(item: any) {
    const hit = false;
    const CSourceValue = item?.ChangeExtend?.C ? JSON.parse(item?.ChangeExtend?.C) : {};
    if (CSourceValue) {
      return CSourceValue.some((x) => x.KeyNo === item.KeyNo);
    }
    return hit;
  }

  /**土地抵押*/
  public category30Field(companyId: string, dimension: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const changeInfo = item.ChangeExtend ? item.ChangeExtend : {};

    if (changeInfo?.T && changeInfo?.T === 2) {
      if (companyId === changeInfo.F?.KeyNo) {
        if (changeInfo.B) {
          //changeInfo.B = excludeAmountUnits(changeInfo.B.trim());
          const targetValue = dimension.fieldValue[0];
          const compareType = dimension.compareType;
          const sourceValue = Number(changeInfo.B) || 0;
          if (targetValue >= 0 && getCompareResult(sourceValue, targetValue, compareType)) {
            hit = true;
          }
        }
      }
    }
    return hit;
  }

  /**担保信息*/
  public category101Field(dimension: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const changeInfo = item.ChangeExtend ? item.ChangeExtend : {};

    if (changeInfo?.T && changeInfo?.T === 1) {
      if (changeInfo.A) {
        //changeInfo.B = excludeAmountUnits(changeInfo.B.trim());
        const targetValue = dimension.fieldValue[0];
        const compareType = dimension.compareType;
        const sourceValue = Number(changeInfo.A) || 0;
        if (targetValue >= 0 && getCompareResult(sourceValue, targetValue, compareType)) {
          hit = true;
        }
      }
    }
    return hit;
  }

  /**税务催缴*/
  public category131Field(dimension: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const changeInfo = item.ChangeExtend ? item.ChangeExtend : {};
    if (changeInfo.B) {
      //changeInfo.B = excludeAmountUnits(changeInfo.B.trim());
      const targetValue = dimension.fieldValue[0] * 10000;
      const compareType = dimension.compareType;
      const sourceValue = Number(changeInfo.B) || 0;
      if (targetValue >= 0 && getCompareResult(sourceValue, targetValue, compareType)) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 是否变更为股东 1-股东，2-非股东
   * @param isBPField
   * @param item
   * @public
   */
  public hitIsBPField(isBPField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const isBPFieldTargetValues = isBPField?.fieldValue as number[];
    const isBPFieldSourceValue = item?.ChangeExtend?.IsBP;
    if (isBPFieldTargetValues?.length && isBPFieldSourceValue && getCompareResult(isBPFieldSourceValue, isBPFieldTargetValues[0], isBPField.compareType)) {
      hit = true;
    }
    return hit;
  }

  /**
   * 变更前持股比例
   * @param beforeContentField
   * @param item
   * @public
   */
  public hitBeforeContentField(beforeContentField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const beforeContentFieldTargetValues = beforeContentField?.fieldValue as number[];
    let beforeContentFieldSourceValue;
    const BeforeContent = item?.BeforeContent || '0';
    const match = BeforeContent.match(/\d+(?:\.\d+)?/);
    if (match) {
      beforeContentFieldSourceValue = parseFloat(match[0]);
    }
    if (
      beforeContentFieldTargetValues?.length &&
      beforeContentFieldSourceValue !== null &&
      getCompareResult(beforeContentFieldSourceValue, beforeContentFieldTargetValues[0], beforeContentField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 持股比例变更趋势 1增加, 0减少
   * @param changeStatusField
   * @param item
   * @public
   */
  public hitShareChangeStatusField(changeStatusField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const changeStatusFieldTargetValues = changeStatusField?.fieldValue as number[];
    const changeStatusFieldSourceValue = item?.ChangeStatus; // 0 减少
    if (
      changeStatusFieldTargetValues?.length &&
      changeStatusFieldSourceValue !== null &&
      changeStatusFieldSourceValue !== undefined &&
      getCompareResult(changeStatusFieldSourceValue, changeStatusFieldTargetValues[0], changeStatusField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**持股比例变更幅度*/
  public hitShareChangeRateField(changeRateField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const changeRateFieldTargetValues = changeRateField?.fieldValue as number[];

    if (item?.BeforeContent && item?.AfterContent) {
      const beforeRate = parseFloat(item?.BeforeContent) / 100 || 0;
      const afterRate = parseFloat(item?.AfterContent) / 100 || 0;
      const changeRate = Math.abs((afterRate - beforeRate) / beforeRate); // 计算股权变化幅度的绝对值
      if (changeRateFieldTargetValues?.length && getCompareResult(changeRate, changeRateFieldTargetValues[0] / 100, changeRateField.compareType)) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 变更趋势，1-增减
   * 变更前持股比例，0
   *
   * @param thresholdCountField
   * @param allPeriodList
   */
  public hitTimePeriodThresholdCountField(thresholdCountField: DimensionHitStrategyFieldsEntity, allPeriodList: any[]) {
    let hit = false;
    const filteredList = allPeriodList.filter((t) => t.BeforeContent === '' && t.ChangeStatus === 1); // 变更趋势是新增，变更前投资金额是""
    const thresholdCountFieldSourceValue = filteredList.length;
    const thresholdCountFieldTargetValues = thresholdCountField?.fieldValue as number[];
    if (
      thresholdCountFieldTargetValues?.length &&
      thresholdCountFieldSourceValue &&
      getCompareResult(thresholdCountFieldSourceValue, thresholdCountFieldTargetValues[0], thresholdCountField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**行业变更*/
  public async hitIndustryThresholdField(
    companySearchService: CompanySearchService,
    typeField: DimensionHitStrategyFieldsEntity,
    subHitData: any[],
    companyId: string,
  ) {
    let hit = false;
    const fieldTargetValues = typeField?.fieldValue as number[];
    let fields: string[] = [];
    if ([2, 3].includes(fieldTargetValues[0])) {
      fields = ['QccIndustry', 'Registcapiamount', 'KeyNo', 'Name'];
    } else if ([0, 1].includes(fieldTargetValues[0])) {
      fields = ['IndustryV3', 'Registcapiamount', 'KeyNo', 'Name'];
    }
    const result = await companySearchService.companyDetailsQcc(companyId, fields);
    const mismatchList: any[] = [];
    const allList: any[] = [];
    for (const subHit of subHitData) {
      const item = _.cloneDeep(subHit); // 不改变allPeriodList 中的值
      // 先对item数据中的json字段做预处理
      Object.keys(item).forEach((key) => {
        if (['Extend1', 'ChangeExtend'].includes(key)) {
          const value = item[key];
          try {
            item[key] = value ? JSON.parse(value) : {};
          } catch (error) {
            item[key] = value;
          }
        }
      });
      const changeInfo = item.ChangeExtend;
      const companyName = changeInfo?.A;
      const companyKeyNo = changeInfo?.K;
      const { IndustryV3, QccIndustry, Registcapiamount, KeyNo } = await companySearchService.companyDetailsQcc(companyKeyNo, fields);
      if (!KeyNo) {
        continue;
      }

      // 获取两个IndustryV3对象
      let currentIndustry = null;
      let resultIndustry = null;
      let industryLevels = null;
      if ([2, 3].includes(fieldTargetValues[0])) {
        currentIndustry = QccIndustry;
        resultIndustry = result.QccIndustry;
        industryLevels = ['Ac', 'Bc', 'Cc'] as const;
      } else if ([0, 1].includes(fieldTargetValues[0])) {
        currentIndustry = IndustryV3;
        resultIndustry = result.IndustryV3;
        industryLevels = ['IndustryCode', 'SubIndustryCode', 'MiddleCategoryCode'] as const;
      }

      // 遍历所有层级进行对比
      const hasMismatch = industryLevels.some((level) => {
        // 仅对比两个对象都存在的层级
        if (currentIndustry[level] && resultIndustry[level]) {
          return currentIndustry[level] !== resultIndustry[level];
        }
        return false; // 任一对象没有该层级则跳过
      });

      // 所有共有层级都匹配才算命中
      if (hasMismatch) {
        mismatchList.push({ companyId: companyKeyNo, Registcapiamount });
      }
      allList.push({ companyId: companyKeyNo, Registcapiamount });
    }

    const mismatchSum = mismatchList.reduce((sum, item) => sum + Number(item?.Registcapiamount?.Value || 0), 0);

    const allSum = allList.reduce((sum, item) => sum + Number(item?.Registcapiamount?.Value || 0), 0);
    let resultValue = null;
    if ([2, 3].includes(fieldTargetValues[0])) {
      resultValue = mismatchList.length > subHitData.length / 2 || mismatchSum > allSum / 2 ? 2 : 3;
    } else if ([0, 1].includes(fieldTargetValues[0])) {
      resultValue = mismatchList.length > subHitData.length / 2 || mismatchSum > allSum / 2 ? 1 : 0;
    }
    if (fieldTargetValues?.length && resultValue && getCompareResult(resultValue, fieldTargetValues[0], typeField.compareType)) {
      hit = true;
    }
    return hit;
  }

  /**
   * 对外投资变更
   * 变更趋势 1增加, 2减少  对外投资变更
   * @param changeStatusField
   * @param item
   * @public
   */
  public hitChangeStatusField(changeStatusField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const changeStatusFieldTargetValues = changeStatusField?.fieldValue as number[];
    const changeStatusFieldSourceValue = item?.ChangeStatus;
    if (
      [
        DimensionFieldCompareTypeEnums.LessThan,
        DimensionFieldCompareTypeEnums.LessThanOrEqual,
        DimensionFieldCompareTypeEnums.GreaterThan,
        DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        DimensionFieldCompareTypeEnums.Equal,
        DimensionFieldCompareTypeEnums.Between,
      ].includes(changeStatusField.compareType)
    ) {
      if (
        changeStatusFieldTargetValues?.length &&
        changeStatusFieldSourceValue &&
        getCompareResult(changeStatusFieldSourceValue, changeStatusFieldTargetValues[0], changeStatusField.compareType)
      ) {
        hit = true;
      }
    } else if (
      [
        DimensionFieldCompareTypeEnums.ContainsAny,
        DimensionFieldCompareTypeEnums.ExceptAll,
        DimensionFieldCompareTypeEnums.ContainsAll,
        DimensionFieldCompareTypeEnums.ExceptAny,
      ].includes(changeStatusField.compareType)
    ) {
      if (
        changeStatusFieldTargetValues?.length &&
        changeStatusFieldSourceValue &&
        getCompareResultForArray(changeStatusField.compareType, [changeStatusFieldSourceValue], changeStatusFieldTargetValues)
      ) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 对外投资变更
   * 变更后持股比例
   * @param afterContentField
   * @param item
   * @public
   */
  public hitAfterContentField(afterContentField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const afterContentFieldTargetValues = afterContentField?.fieldValue as number[];
    let afterContentFieldSourceValue;
    const AfterContent = item?.AfterContent || '0';
    const match = AfterContent.match(/\d+(?:\.\d+)?/);
    if (match) {
      afterContentFieldSourceValue = parseFloat(match[0]);
    }
    if (
      afterContentFieldTargetValues?.length &&
      afterContentFieldSourceValue !== null &&
      getCompareResult(afterContentFieldSourceValue, afterContentFieldTargetValues[0], afterContentField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 是否币种变更 1币种变更, 0不是币种变更
   * @param currencyChangeField
   * @param item
   * @public
   */
  public hitCurrencyChangeField(currencyChangeField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const currencyChangeFieldTargetValues = currencyChangeField?.fieldValue as number[];
    const currencyChangeFieldSourceValue = item?.Extend1?.T;
    if (
      currencyChangeFieldTargetValues?.length &&
      currencyChangeFieldSourceValue !== null &&
      getCompareResult(currencyChangeFieldSourceValue, currencyChangeFieldTargetValues[0], currencyChangeField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  public hitCategory123CurrencyChangeField(currencyChangeField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const currencyChangeFieldTargetValues = currencyChangeField?.fieldValue as number[];
    const changeInfo = item.ChangeExtend ? item.ChangeExtend : {};
    const changeE = changeInfo.E ? JSON.parse(changeInfo.E) : {};
    const currencyChangeFieldSourceValue = changeE?.Bc === changeE?.Ac ? 0 : 1;
    if (
      currencyChangeFieldTargetValues?.length &&
      currencyChangeFieldSourceValue &&
      getCompareResult(currencyChangeFieldSourceValue, currencyChangeFieldTargetValues[0], currencyChangeField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 注册资本变更趋势 1减少, 2增加
   * @param regisCapitalTrendField
   * @param item
   * @public
   */
  public hitRegisCapitalTrendField(regisCapitalTrendField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const regisCapitalTrendFieldTargetValues = regisCapitalTrendField?.fieldValue as number[];
    const regisCapitalTrendFieldSourceValue = item?.ChangeExtend?.T;
    if (
      regisCapitalTrendFieldTargetValues?.length &&
      regisCapitalTrendFieldSourceValue &&
      getCompareResult(regisCapitalTrendFieldSourceValue, regisCapitalTrendFieldTargetValues[0], regisCapitalTrendField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   *  注册资本变更比例
   * @param regisCapitalChangeRatioField
   * @param item
   * @public
   */
  public hitRegisCapitalChangeRatioField(regisCapitalChangeRatioField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const regisCapitalChangeRatioFieldTargetValues = regisCapitalChangeRatioField?.fieldValue as number[];
    //ChangeExtend.A-ChangeExtend.B/ChangeExtend.A
    let regisCapitalChangeRatioFieldSourceValue;
    const beforeRegisCap = item?.ChangeExtend?.A;
    const afterRegisCap = item?.ChangeExtend?.B;
    if (beforeRegisCap && afterRegisCap) {
      const beforeNumberRegisCap = beforeRegisCap.endsWith('万元人民币') ? beforeRegisCap.substring(0, beforeRegisCap.length - 5) : beforeRegisCap;
      const afterNumberRegisCap = afterRegisCap.endsWith('万元人民币') ? afterRegisCap.substring(0, afterRegisCap.length - 5) : afterRegisCap;
      const beforeCap = Number(beforeNumberRegisCap) || 0;
      const afterCap = Number(afterNumberRegisCap) || 0;
      regisCapitalChangeRatioFieldSourceValue = ((Number(beforeCap) - Number(afterCap)) / Number(beforeCap)) * 100;
    }
    if (
      regisCapitalChangeRatioFieldTargetValues?.length &&
      regisCapitalChangeRatioFieldSourceValue &&
      getCompareResult(regisCapitalChangeRatioFieldSourceValue, regisCapitalChangeRatioFieldTargetValues[0], regisCapitalChangeRatioField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 企业减资幅度筛选
   * @param dimension
   * @param companyDetailElement
   * @private
   */
  public capitalReduceSelectCompareResult(dimension: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    let capitalReductionRatio;
    const changeInfo = item.ChangeExtend ? item.ChangeExtend : {};
    const changeE = changeInfo.E ? JSON.parse(changeInfo.E) : {};
    const beforeCapitalReduce = changeE?.Bm;
    const afterCapitalReduce = changeE?.Am;

    if (beforeCapitalReduce && afterCapitalReduce) {
      const beforeCap = Number(beforeCapitalReduce) || 0;
      const afterCap = Number(afterCapitalReduce) || 0;

      capitalReductionRatio = ((Number(beforeCap) - Number(afterCap)) / Number(beforeCap)) * 100;

      const defaultStatus = dimension?.fieldValue[0] || [10000, 50000];
      const operator2 = dimension?.compareType || DimensionFieldCompareTypeEnums.ContainsAny;
      const recCapLabels: string[] = [];
      const min = defaultStatus[0] || undefined;
      const max = defaultStatus[1] || undefined;
      const isMatch = (min === undefined || capitalReductionRatio >= min) && (max === undefined || capitalReductionRatio < max);
      if (isMatch) {
        const type = CapitalReductionRateType.find((t) => {
          const min = t.value[0] || undefined;
          const max = t.value[1] || undefined;
          return (min === undefined || capitalReductionRatio >= min) && (max === undefined || capitalReductionRatio < max);
        });
        recCapLabels.push(type.label);
      }
      const defaultStatusType = CapitalReductionRateType.find((t) => {
        const compmin = t.value[0] || undefined;
        const compmax = t.value[1] || undefined;
        return (min === undefined || compmin === min) && (max === undefined || compmax === max);
      });

      if (getCompareResultForArray(operator2, recCapLabels, [defaultStatusType.label])) {
        hit = true;
      }
    }
    return hit;
  }

  /**
   * 经营状态 40-清算, 90-吊销, 85-责令关闭, 70-停业，99-注销, 80-撤销, 75-歇业, 50-迁入, 60-迁出
   * @param businessStatusField
   * @param item
   * @public
   */
  public category38(businessStatusField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const businessStatusFieldTargetValues = businessStatusField?.fieldValue as number[];
    const businessStatusFieldSourceValues: number[] = [];
    if (item?.ChangeExtend?.B) {
      BusinessStatusMap.forEach((t) => {
        if (item?.ChangeExtend?.B?.includes(t.label)) {
          businessStatusFieldSourceValues.push(t.value);
        }
      });
    }
    if (
      businessStatusFieldTargetValues?.length &&
      businessStatusFieldSourceValues?.length &&
      getCompareResultForArray(businessStatusField.compareType, businessStatusFieldSourceValues, businessStatusFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 检查目标企业是否满足上市条件命中策略
   * @param listedField 维度命中策略字段配置（包含目标值、比较类型等）
   * @param item 当前检查项数据
   * @param keyNo 企业唯一标识（如注册号/股票代码）
   * @returns 返回是否命中上市条件策略 (true/false)
   *
   * 逻辑说明：
   * 1. 通过keyNo查询企业详情数据
   * 2. 根据企业上市状态关键字(含'F_4')判断是否上市
   * 3. 将上市状态(1/0)与策略配置的目标值进行比对
   * 4. 返回比对结果作为命中状态
   *
   * 注：原代码中ST股判断逻辑被注释，当前版本仅检查基础上市状态
   */
  public async checkListedField(companySearchService: CompanySearchService, listedField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    let hit = false;
    const listedTargetValues = listedField.fieldValue as number[];
    const companyDetail = await companySearchService.companyDetailsKys(keyNo);
    const resultList: string[] = [];
    if (companyDetail && this.getListStatus(companyDetail.result, companyDetail.result?.['stockinfo'])) {
      resultList.push(keyNo);
    }
    const listedFieldSourceValue = resultList.length > 0 ? 1 : 0;

    if (listedTargetValues?.length && listedFieldSourceValue && getCompareResult(listedFieldSourceValue, listedTargetValues[0], listedField.compareType)) {
      hit = true;
    }

    return hit;
  }

  private getListStatus(company: any, stockinfo: string[]) {
    const listingStatusKw = company?.listingstatuskw;
    if (listingStatusKw?.length > 0 && listingStatusKw.includes('F_4')) {
      return true;
      // if (!stockinfo?.join('')?.includes('ST')) {
      //   // 上市(非ST、*ST)
      //   return 3;
      // }
      // // 已上市
      // return 1;
    }
    // 未上市
    return false;
  }

  /**
   * 注册资本变更趋势
   * 公司1个自然年度内拟减少注册资本超过其原注册资本5%
   * @param periodRegisCapitalField
   * @param newItem
   * @param baseLineItem
   */
  public hitPeriodRegisCapitalField(periodRegisCapitalField: DimensionHitStrategyFieldsEntity, newItem: any, baseLineItems: any[]) {
    let hit = false;
    // 自然年初的注册资本
    if (!baseLineItems?.length) {
      return hit;
    }
    let beforeRegisCapitalNumber;
    const beforeContent = baseLineItems[0]?.BeforeContent;
    if (beforeContent) {
      const afterNumberRegisCap = beforeContent.endsWith('万元人民币') ? beforeContent.substring(0, beforeContent.length - 5) : beforeContent;
      beforeRegisCapitalNumber = Number(afterNumberRegisCap) || 0; // 单位万元
    }
    //  现在的变更后的注册资本
    let nowRegisCapitalNumber;
    const afterContentString = newItem?.AfterContent;
    if (afterContentString) {
      const afterNumberRegisCap = afterContentString.endsWith('万元人民币')
        ? afterContentString.substring(0, afterContentString.length - 5)
        : afterContentString;
      nowRegisCapitalNumber = Number(afterNumberRegisCap) || 0; // 单位万元
    }
    // 变更趋势，变更比例
    const targetValuePeriodTrend = periodRegisCapitalField.fieldValue[0]?.valuePeriodTrend;
    const targetValuePeriodThreShold = periodRegisCapitalField.fieldValue[0]?.valuePeriodThreShold;
    const valuePeriodThreSholdCompareType = periodRegisCapitalField.fieldValue[0]?.valuePeriodThreSholdCompareType;
    if (
      beforeRegisCapitalNumber !== null &&
      nowRegisCapitalNumber !== null &&
      targetValuePeriodTrend &&
      targetValuePeriodThreShold &&
      valuePeriodThreSholdCompareType
    ) {
      //1-减少，2-新增
      let sourceValuePeriodThreShold;
      if (targetValuePeriodTrend === 1 && nowRegisCapitalNumber < beforeRegisCapitalNumber) {
        sourceValuePeriodThreShold = ((beforeRegisCapitalNumber - nowRegisCapitalNumber) / beforeRegisCapitalNumber) * 100;
      } else if (targetValuePeriodTrend === 2 && nowRegisCapitalNumber > beforeRegisCapitalNumber) {
        sourceValuePeriodThreShold = ((nowRegisCapitalNumber - beforeRegisCapitalNumber) / nowRegisCapitalNumber) * 100;
      }
      hit = getCompareResult(sourceValuePeriodThreShold, targetValuePeriodThreShold, valuePeriodThreSholdCompareType);
    }
    return hit;
  }

  //减资公告
  public hitPeriodRegisCapitalField123(periodRegisCapitalField: DimensionHitStrategyFieldsEntity, baseLineItem: any[], newItem: any) {
    let hit = false;

    const amountList: number[] = [];
    baseLineItem.forEach((x) => {
      const beforeContent = x.BeforeContent;
      if (beforeContent) {
        amountList.push(excludeAmountUnits(beforeContent));
      }
      const afterContent = x.AfterContent;
      if (afterContent) {
        amountList.push(excludeAmountUnits(afterContent));
      }
    });
    if (!amountList) {
      return hit;
    }
    amountList.sort((a, b) => b - a);
    //注册资本峰值
    const registerCapitalMax = amountList[0];
    //注册资本谷值
    const registerCapitalMin = amountList[amountList.length - 1];

    // 变更趋势，变更比例
    const targetValuePeriodTrend = periodRegisCapitalField.fieldValue[0]?.valuePeriodTrend;
    const targetValuePeriodThreShold = (periodRegisCapitalField.fieldValue[0]?.valuePeriodThreShold[0] as number[]) || [20, 50];
    const valuePeriodThreSholdCompareType = periodRegisCapitalField.fieldValue[0]?.valuePeriodThreSholdCompareType;
    if (registerCapitalMax !== null && registerCapitalMin !== null && targetValuePeriodTrend && targetValuePeriodThreShold && valuePeriodThreSholdCompareType) {
      const changeInfo = newItem.ChangeExtend ? newItem.ChangeExtend : {};
      const changeE = changeInfo.E ? JSON.parse(changeInfo.E) : {};
      const afterCapitalReduce = (changeE?.Am as number) / 10000;
      const sourceValuePeriodThreShold = ((registerCapitalMax - registerCapitalMin) / afterCapitalReduce) * 100;

      const recCapLabels: string[] = [];
      const min = targetValuePeriodThreShold[0] || undefined;
      const max = targetValuePeriodThreShold[1] || undefined;
      const isMatch = (min === undefined || sourceValuePeriodThreShold >= min) && (max === undefined || sourceValuePeriodThreShold < max);
      if (isMatch) {
        const type = ChangeRegisteredCapitalType.find((t) => {
          const min = t.value[0] || undefined;
          const max = t.value[1] || undefined;
          return (min === undefined || sourceValuePeriodThreShold >= min) && (max === undefined || sourceValuePeriodThreShold < max);
        });
        recCapLabels.push(type.label);
      }
      const defaultStatusType = ChangeRegisteredCapitalType.find((t) => {
        const compmin = t.value[0] || undefined;
        const compmax = t.value[1] || undefined;
        return (min === undefined || compmin === min) && (max === undefined || compmax === max);
      });

      if (getCompareResultForArray(valuePeriodThreSholdCompareType, recCapLabels, [defaultStatusType.label])) {
        hit = true;
      }
      // hit = getCompareResult(sourceValuePeriodThreShold, targetValuePeriodThreShold, valuePeriodThreSholdCompareType);
    }
    return hit;
  }

  public hitMainInfoUpdateCapitalChange(periodRegisCapitalField: DimensionHitStrategyFieldsEntity, baseLineItem: any[]) {
    let hit = false;

    const amountList: number[] = [];
    baseLineItem.forEach((x) => {
      const beforeContent = x.BeforeContent;
      if (beforeContent) {
        amountList.push(excludeAmountUnits(beforeContent));
      }
      const afterContent = x.AfterContent;
      if (afterContent) {
        amountList.push(excludeAmountUnits(afterContent));
      }
    });
    if (!amountList) {
      return hit;
    }
    amountList.sort((a, b) => b - a);
    //注册资本峰值
    const registerCapitalMax = amountList[0];
    //注册资本当前值
    const registerCapitalCurrent = excludeAmountUnits(baseLineItem[0]?.AfterContent);

    // 变更趋势，变更比例
    const targetValuePeriodTrend = periodRegisCapitalField.fieldValue[0]?.valuePeriodTrend;
    const targetValuePeriodThreShold = (periodRegisCapitalField.fieldValue[0]?.valuePeriodThreShold[0] as number[]) || [20, 50];
    const valuePeriodThreSholdCompareType = periodRegisCapitalField.fieldValue[0]?.valuePeriodThreSholdCompareType;
    if (
      registerCapitalMax !== null &&
      registerCapitalCurrent !== null &&
      targetValuePeriodTrend &&
      targetValuePeriodThreShold &&
      valuePeriodThreSholdCompareType
    ) {
      const sourceValuePeriodThreShold = ((registerCapitalMax - registerCapitalCurrent) / registerCapitalMax) * 100;

      const recCapLabels: string[] = [];
      const min = targetValuePeriodThreShold[0] || undefined;
      const max = targetValuePeriodThreShold[1] || undefined;
      const isMatch = (min === undefined || sourceValuePeriodThreShold >= min) && (max === undefined || sourceValuePeriodThreShold < max);
      if (isMatch) {
        const type = STIChangeRegisteredCapitalType.find((t) => {
          const min = t.value[0] || undefined;
          const max = t.value[1] || undefined;
          return (min === undefined || sourceValuePeriodThreShold >= min) && (max === undefined || sourceValuePeriodThreShold < max);
        });
        recCapLabels.push(type.label);
      }
      const defaultStatusType = STIChangeRegisteredCapitalType.find((t) => {
        const compmin = t.value[0] || undefined;
        const compmax = t.value[1] || undefined;
        return (min === undefined || compmin === min) && (max === undefined || compmax === max);
      });

      if (getCompareResultForArray(valuePeriodThreSholdCompareType, recCapLabels, [defaultStatusType.label])) {
        hit = true;
      }
      // hit = getCompareResult(sourceValuePeriodThreShold, targetValuePeriodThreShold, valuePeriodThreSholdCompareType);
    }
    return hit;
  }

  /**
   * 简易注销
   * @param typeField
   * @param item
   */
  public category23Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const changeInfo = item.ChangeExtend;
    //简易注销结果
    const category23Result = changeInfo.B;
    //判断 category23Result 字符串中是否包含 simpleCancelTypeMap 中的值,包含返回对应的key值,与typeField.fieldValue进行比较
    const targetValue = typeField.fieldValue;
    const compareType = typeField.compareType;
    // SimpleCancelTypeConstant 枚举中的label === category23Result , 则取value
    const sourceValue = SimpleCancelTypeConstant.find((t) => t.label === category23Result)?.value || null;
    if (!sourceValue) {
      return hit;
    }
    const category23ResultCompareResult = getCompareResultForArray(compareType, [sourceValue], targetValue);
    if (category23ResultCompareResult) {
      hit = true;
    }
    return hit;
  }
}
