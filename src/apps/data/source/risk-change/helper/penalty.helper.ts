import { Injectable } from '@nestjs/common';
import { BusinessAbnormalType, FinancialSupervisionType, PenaltiesType } from 'libs/constants/punish.constants';
import {
  excludePenaltyResult107Map,
  excludePenaltyResult22Map,
  InspectionResultTypeMap,
  penaltyReason107Map,
  penaltyReason22Map,
  penaltyResult107Map,
  penaltyResult22Map,
  PenaltyUnitType,
} from 'libs/constants/risk.change.constants';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { getCompareResult, getCompareResultForArray } from 'libs/utils/diligence/diligence.utils';
import { containsKeywords } from 'libs/utils/utils';
import * as _ from 'lodash';

@Injectable()
export class PenaltyHelper {
  /**
   * 行政处罚，红牌处罚
   */
  public penaltyRedCardFieldCategory107(penaltyRedCardField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    //取关键案由map中的value集合
    const penFieldTargetValues = penaltyRedCardField.fieldValue as number[];
    const penaltyRedCardList: string[] = [];
    const B = item?.ChangeExtend?.B;
    if (B) {
      if (
        containsKeywords(
          B,
          penaltyReason107Map.map((t) => t.label),
        )
      ) {
        penaltyRedCardList.push(String(B));
      }
    }
    const C = item?.ChangeExtend?.C;
    if (C) {
      if (
        containsKeywords(
          C,
          penaltyResult107Map.map((t) => t.label),
          excludePenaltyResult107Map.map((t) => t.label),
        )
      ) {
        penaltyRedCardList.push(String(C));
      }
    }

    const penaltyRedCardFieldSourceValue = penaltyRedCardList.length > 0 ? 1 : 0;

    if (
      penFieldTargetValues?.length &&
      penaltyRedCardFieldSourceValue &&
      getCompareResult(penaltyRedCardFieldSourceValue, penFieldTargetValues[0], penaltyRedCardField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 环保处罚，红牌处罚
   */
  public penaltyRedCardFieldCategory22(penaltyRedCardField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;

    const penFieldTargetValues = penaltyRedCardField?.fieldValue as number[];
    const penaltyRedCardList: string[] = [];
    const A = item?.ChangeExtend?.A;
    if (A) {
      if (
        containsKeywords(
          A,
          penaltyReason22Map.map((t) => t.label),
        )
      ) {
        penaltyRedCardList.push(String(A));
      }
    }
    const B = item?.ChangeExtend?.B;
    if (B) {
      if (
        containsKeywords(
          B,
          penaltyResult22Map.map((t) => t.label),
          excludePenaltyResult22Map.map((t) => t.label),
        )
      ) {
        penaltyRedCardList.push(String(B));
      }
    }

    const penaltyRedCardFieldSourceValue = penaltyRedCardList.length > 0 ? 1 : 0;

    if (
      penFieldTargetValues?.length &&
      penaltyRedCardFieldSourceValue &&
      getCompareResult(penaltyRedCardFieldSourceValue, penFieldTargetValues[0], penaltyRedCardField.compareType)
    ) {
      hit = true;
    }

    return hit;
  }

  /**
   * 行政处罚，处罚单位
   * @param penaltyUnitField
   * @param newItem
   */
  public penaltyUnitField(penaltyUnitField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const penaltyUnitFieldTargetValues = penaltyUnitField?.fieldValue as number[];
    const penaltyUnitFieldSourceValues: number[] = [];
    if (item?.ChangeExtend?.A) {
      PenaltyUnitType.forEach((t) => {
        if (item?.ChangeExtend?.A?.includes(t.label)) {
          penaltyUnitFieldSourceValues.push(t.value);
        }
      });
    }
    // 如果为空的
    if (
      [DimensionFieldCompareTypeEnums.ExceptAny, DimensionFieldCompareTypeEnums.ExceptAll].includes(penaltyUnitField.compareType) &&
      penaltyUnitFieldSourceValues?.length === 0
    ) {
      hit = true;
    }
    if (
      penaltyUnitFieldTargetValues?.length &&
      penaltyUnitFieldSourceValues?.length &&
      getCompareResultForArray(penaltyUnitField.compareType, penaltyUnitFieldSourceValues, penaltyUnitFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 行政处罚，处罚单位
   * @param penaltyIssuingUnitField
   * @param newItem
   */
  public penaltyIssuingUnitField(penaltyIssuingUnitField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    //历史模型配置的number 做兼容强转string
    const penaltyUnitFieldTargetValues = penaltyIssuingUnitField?.fieldValue?.map(String) || [];
    const penaltyUnitFieldSourceValues: any[] = [];
    if (item?.ChangeExtend?.G1) {
      const g1 = item?.ChangeExtend?.G1?.toString() ?? '';
      const values = penaltyIssuingUnitField.options.filter((t) => t.value === g1).map((t) => t.value);
      penaltyUnitFieldSourceValues.push(...values);
    }
    if (penaltyUnitFieldTargetValues.includes('9907') && item?.ChangeExtend?.G2 === '9907') {
      penaltyUnitFieldSourceValues.push(item?.ChangeExtend?.G2);
    }
    // 如果为空的
    if (
      [DimensionFieldCompareTypeEnums.ExceptAny, DimensionFieldCompareTypeEnums.ExceptAll].includes(penaltyIssuingUnitField.compareType) &&
      penaltyUnitFieldSourceValues?.length === 0
    ) {
      hit = true;
    }
    if (
      penaltyUnitFieldTargetValues?.length &&
      penaltyUnitFieldSourceValues?.length &&
      getCompareResultForArray(penaltyIssuingUnitField.compareType, penaltyUnitFieldSourceValues, penaltyUnitFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 行政处罚，处罚单位
   * @param penaltyUnitField
   * @param newItem
   */
  public penaltyUnitField31(penaltyUnitField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const penaltyUnitFieldTargetValues = penaltyUnitField?.fieldValue as number[];
    const penaltyUnitFieldSourceValues: number[] = [];
    if (item?.ChangeExtend?.D) {
      penaltyUnitField.options.forEach((t) => {
        if (item?.ChangeExtend?.D?.includes(t.label)) {
          penaltyUnitFieldSourceValues.push(t.value);
        }
      });
    }
    // 如果为空的
    if (
      [DimensionFieldCompareTypeEnums.ExceptAny, DimensionFieldCompareTypeEnums.ExceptAll].includes(penaltyUnitField.compareType) &&
      penaltyUnitFieldSourceValues?.length === 0
    ) {
      hit = true;
    }
    if (
      penaltyUnitFieldTargetValues?.length &&
      penaltyUnitFieldSourceValues?.length &&
      getCompareResultForArray(penaltyUnitField.compareType, penaltyUnitFieldSourceValues, penaltyUnitFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 行政处罚，处罚单位 117
   * @param penaltyUnitField
   * @param newItem
   */
  public penaltyUnitField117(penaltyUnitField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const penaltyUnitFieldTargetValues = penaltyUnitField?.fieldValue as number[];
    const penaltyUnitFieldSourceValues: number[] = [];
    if (item?.ChangeExtend?.B) {
      penaltyUnitField.options.forEach((t) => {
        if (item?.ChangeExtend?.B?.includes(t.label)) {
          penaltyUnitFieldSourceValues.push(Number(t.value));
        }
      });
    }
    // 如果为空的
    if (
      [DimensionFieldCompareTypeEnums.ExceptAny, DimensionFieldCompareTypeEnums.ExceptAll].includes(penaltyUnitField.compareType) &&
      penaltyUnitFieldSourceValues?.length === 0
    ) {
      hit = true;
    }
    if (
      penaltyUnitFieldTargetValues?.length &&
      penaltyUnitFieldSourceValues?.length &&
      getCompareResultForArray(penaltyUnitField.compareType, penaltyUnitFieldSourceValues, penaltyUnitFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 行政处罚的种类： 命中行政处罚种类punishType：ChangeExtend.L
   * @param punishTypeField
   * @param newItem
   */
  public punishTypeField(punishTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const punishTypeFieldTargetValues = punishTypeField?.fieldValue as string[];
    const punishTypeFieldSourceValues: string[] = [];
    if (item?.ChangeExtend?.L) {
      PenaltiesType.forEach((t) => {
        if (item?.ChangeExtend?.L?.includes(t.esCode)) {
          punishTypeFieldSourceValues.push(t.value);
        }
      });
    }
    if (
      punishTypeFieldTargetValues?.length &&
      punishTypeFieldSourceValues?.length &&
      getCompareResultForArray(punishTypeField.compareType, punishTypeFieldSourceValues, punishTypeFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 经营异常类型
   * @param businessAbnormalTypeField
   * @param newItem
   */
  public businessAbnormalTypeField(businessAbnormalTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const businessAbnormalTypeFieldTargetValues = businessAbnormalTypeField?.fieldValue as string[];
    const businessAbnormalTypeFieldSourceValues: string[] = [];
    if (item?.ChangeExtend?.D?.key) {
      BusinessAbnormalType.forEach((t) => {
        if (item?.ChangeExtend?.D.key?.includes(t.esCode)) {
          businessAbnormalTypeFieldSourceValues.push(t.value);
        }
      });
    }
    if (
      businessAbnormalTypeFieldTargetValues?.length &&
      businessAbnormalTypeFieldSourceValues?.length &&
      getCompareResultForArray(businessAbnormalTypeField.compareType, businessAbnormalTypeFieldSourceValues, businessAbnormalTypeFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 环保处罚: 命中ChangeExtend.H
   * @param punishEnvTypeField
   * @param newItem
   */
  public punishEnvTypeField(punishEnvTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const punishEnvTypeFieldTargetValues = punishEnvTypeField?.fieldValue as string[];
    const punishEnvTypeFieldSourceValues: string[] = [];
    if (item?.ChangeExtend?.H) {
      PenaltiesType.forEach((t) => {
        if (item?.ChangeExtend?.H?.includes(t.esCode)) {
          punishEnvTypeFieldSourceValues.push(t.value);
        }
      });
    }
    if (
      punishEnvTypeFieldTargetValues?.length &&
      punishEnvTypeFieldSourceValues?.length &&
      getCompareResultForArray(punishEnvTypeField.compareType, punishEnvTypeFieldSourceValues, punishEnvTypeFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 金融监管处罚原因  ChangeExtend.I
   * @param financialPenaltyCauseTypeField
   * @param item
   */
  public financialPenaltyCauseTypeField(financialPenaltyCauseTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const financialPenaltyCauseTypeFieldTargetValues = financialPenaltyCauseTypeField?.fieldValue as string[];
    let financialPenaltyCauseTypeFieldSourceValues: string[] = [];
    if (item?.ChangeExtend?.I) {
      FinancialSupervisionType.forEach((t) => {
        if (item?.ChangeExtend?.I?.includes(t.esCode)) {
          financialPenaltyCauseTypeFieldSourceValues.push(t.value);
        }
      });
    }
    financialPenaltyCauseTypeFieldSourceValues = _.uniq(financialPenaltyCauseTypeFieldSourceValues);
    if (
      financialPenaltyCauseTypeFieldTargetValues?.length &&
      financialPenaltyCauseTypeFieldSourceValues?.length &&
      getCompareResultForArray(
        financialPenaltyCauseTypeField.compareType,
        financialPenaltyCauseTypeFieldSourceValues,
        financialPenaltyCauseTypeFieldTargetValues,
      )
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 抽样检查结果 不合格（ChangeExtend.E）
   * @param inspectionResultTypeField
   * @param item
   */
  public inspectionResultTypeField(inspectionResultTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const inspectionResultTypeFieldTargetValues = inspectionResultTypeField?.fieldValue as number[];
    const inspectionResultTypeFieldSourceValues: number[] = [];
    if (item?.ChangeExtend?.E !== null) {
      InspectionResultTypeMap.forEach((t) => {
        if (item?.ChangeExtend?.E === t.value) {
          inspectionResultTypeFieldSourceValues.push(t.value);
        }
      });
    }
    if (
      inspectionResultTypeFieldTargetValues?.length &&
      inspectionResultTypeFieldSourceValues?.length &&
      getCompareResultForArray(inspectionResultTypeField.compareType, inspectionResultTypeFieldSourceValues, inspectionResultTypeFieldTargetValues)
    ) {
      hit = true;
    }
    return hit;
  }
}
