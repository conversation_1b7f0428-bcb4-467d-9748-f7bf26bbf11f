import { Injectable } from '@nestjs/common';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { MongodbSearchHelper } from 'apps/data/helper/mongodb.search.helper';
import { getFilterCaseIds, buildCaseTitleDesc } from 'apps/data/risk.copy.from.c/risk';
import * as Bluebird from 'bluebird';
import { caseReasonContractDisputeMap, financialReasonMap } from 'libs/constants/case.constants';
import { CaseTypes } from 'libs/constants/judgement.constants';
import { keyCauseActionMap } from 'libs/constants/risk.change.constants';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { getCompareResult, getCompareResultForArray } from 'libs/utils/diligence/diligence.utils';
import { isCodeExactMatch, isCodePrefixMatch } from 'libs/utils/utils';
import * as _ from 'lodash';
@Injectable()
export class CaseReasonHelper {
  logger: any;
  constructor(private readonly companyDetailService: CompanyDetailService, private readonly mongodbHelper: MongodbSearchHelper) {}
  /**
   * 根据案由首字母判断案件类型 A: 刑事案由 B: 民事案由 C: 执行案由 D: 行政案由 E: 其他案由
   * @param caseTypeField
   * @param item
   */
  public checkCaseTypeField(caseTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const caseTypeTargetValues = caseTypeField?.fieldValue as string[];

    const caseTypeReasonValues = caseTypeTargetValues.map((typeValue) => {
      return CaseTypes.find((t) => t.value === typeValue)?.caseReasonValue;
    });

    let caseTypeSourceValues: string[] = [];
    const RC = item?.ChangeExtend?.RC;
    if (RC) {
      if (caseTypeReasonValues.some((t) => isCodePrefixMatch(t, RC))) {
        caseTypeSourceValues.push(String(RC.slice(0, 1)));
      }
    }
    caseTypeSourceValues = _.uniq(caseTypeSourceValues);
    if (caseTypeSourceValues?.length && getCompareResultForArray(caseTypeField.compareType, caseTypeSourceValues, caseTypeReasonValues)) {
      hit = true;
    }
    return hit;
  }

  /**
   * 关键案由：ChangeExtend.RC---->caseReason 过滤得到RC(关键路由),fieldValue为空则默认为关键案由
   * @param judicialRoleTypeField
   * @param item
   */
  public caseReasonTypeFieldByKeyCauseAction(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    //取关键案由map中的value集合
    const judicialRoleTargetValues = keyCauseActionMap.map((item) => item.value) as string[];
    let judicialRoleSourceValues: string[] = [];
    const RC = item?.ChangeExtend?.RC;
    if (RC) {
      const keyCauseActionList = keyCauseActionMap.map((t) => t.value);
      if (keyCauseActionList.includes(RC)) {
        judicialRoleSourceValues.push(String(RC));
      }
    }
    judicialRoleSourceValues = _.uniq(judicialRoleSourceValues);
    if (judicialRoleSourceValues?.length && getCompareResultForArray(judicialRoleTypeField.compareType, judicialRoleSourceValues, judicialRoleTargetValues)) {
      hit = true;
    }
    return hit;
  }

  /**
   * 案由
   *
   * 判断当前案由是否是目标案由子类或者目标案由
   * @param caseReasonField
   * @param item
   */
  public caseReasonTypeField(caseReasonField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    //取关键案由map中的value集合
    const caseReasonTargetList = caseReasonField.fieldValue as string[];
    if (!caseReasonTargetList.length) {
      //如果filedValue为空则默认为关键案由
      return this.caseReasonTypeFieldByKeyCauseAction(caseReasonField, item);
    }
    let caseReasonList: string[] = [];
    const RC = item?.ChangeExtend?.RC;
    if (RC) {
      if (caseReasonTargetList.some((t) => isCodePrefixMatch(t, RC))) {
        caseReasonList.push(String(RC));
      }
    }
    caseReasonList = _.uniq(caseReasonList);
    if (
      (caseReasonList?.length || caseReasonField.compareType === DimensionFieldCompareTypeEnums.ExceptAny) &&
      getCompareResultForArray(caseReasonField.compareType, caseReasonList, caseReasonTargetList)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 是否是合同纠纷
   */
  public checkContractDisputeField(contractDisputeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    //取关键案由map中的value集合
    const contractDisputeFieldTargetValues = contractDisputeField.fieldValue as number[];
    const contractDisputeList: string[] = [];
    const RC = item?.ChangeExtend?.RC;
    if (RC) {
      if (caseReasonContractDisputeMap.some((t) => (t.isFullMatch ? isCodeExactMatch(t.value, RC) : isCodePrefixMatch(t.value, RC)))) {
        contractDisputeList.push(String(RC));
      }
    }

    const penaltyRedCardFieldSourceValue = contractDisputeList.length > 0 ? 1 : 0;

    if (
      contractDisputeFieldTargetValues?.length &&
      penaltyRedCardFieldSourceValue &&
      getCompareResult(penaltyRedCardFieldSourceValue, contractDisputeFieldTargetValues[0], contractDisputeField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  /**
   * 是否金融涉诉
   *
   */
  public checkFinancialReasonField(financialReasonField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const contractDisputeFieldTargetValues = financialReasonField.fieldValue as number[];
    const caseReasonFinancialList = financialReasonMap.map((t) => t.value) as string[];
    const contractDisputeList: string[] = [];
    const RC = item?.ChangeExtend?.RC;
    if (RC) {
      if (caseReasonFinancialList.some((t) => isCodePrefixMatch(t, RC))) {
        contractDisputeList.push(String(RC));
      }
    }

    const penaltyRedCardFieldSourceValue = contractDisputeList.length > 0 ? 1 : 0;

    if (
      contractDisputeFieldTargetValues?.length &&
      typeof penaltyRedCardFieldSourceValue === 'number' &&
      getCompareResult(penaltyRedCardFieldSourceValue, contractDisputeFieldTargetValues[0], financialReasonField.compareType)
    ) {
      hit = true;
    }
    return hit;
  }

  async getCaseTitleDescData(items: any[], isDisplay, changeExtendMap) {
    const objectKeyDictionary = {};
    await Bluebird.map(items, async (item) => {
      try {
        switch (item.Category) {
          //诉前调解
          case RiskChangeCategoryEnum.category232:
          case RiskChangeCategoryEnum.category90: {
            // const url = `${this.configService.proxyServer.dataService}/api/Risk/GetDetailOfLiAn`;
            // const resp = await this.httpUtils.getRequest(url, { id: item.ObjectId, isB: true });
            const resp = await this.companyDetailService.getDetailOfLiAn(item.ObjectId);
            const caseSearchId = resp?.Result?.CaseSearchId;
            if (caseSearchId && caseSearchId.length > 0) {
              objectKeyDictionary[item.ObjectId] = caseSearchId[0];
            }
            break;
          }
          //询价评估
          // case RiskChangeCategoryEnum.category59: {
          //   const resp = await this.httpUtils.getRequest(this.configService.proxyServer.riskService + '/Risk/GetRiskDetailV2', {
          //     keyNo: item.companyId,
          //   });
          //   objectKeyDictionary[item.ObjectId] = resp?.Result?.CaseSearchId;
          //   break;
          // }
          case RiskChangeCategoryEnum.category22:
          case RiskChangeCategoryEnum.category121:
          case RiskChangeCategoryEnum.category63:
          case RiskChangeCategoryEnum.category238:
          case RiskChangeCategoryEnum.category107: {
            //补充
            objectKeyDictionary[item.ObjectId] = item.ObjectId;
            break;
          }
          default: {
            break;
          }
        }
      } catch (e) {
        this.logger.error(`RiskChange getCaseTitleDesc request: ${JSON.stringify(item)}`, e);
      }
    });
    const ids = await getFilterCaseIds(items, isDisplay);
    if (ids.length > 0) {
      const objectKeys = await this.getCaseSearchIds(ids);
      //补充
      Object.assign(objectKeyDictionary, objectKeys);
    }
    //组装build
    if (!objectKeyDictionary || Object.keys(objectKeyDictionary).length === 0) {
      return items;
    }
    return buildCaseTitleDesc(items, isDisplay, objectKeyDictionary, changeExtendMap);
  }

  public async getCaseSearchIds(ids) {
    let allResults = [];
    let currentPage = 1;
    const pageSize = 500;
    let hasMore = true;

    while (hasMore) {
      const { resList } = await this.mongodbHelper.findListByIds(ids, 'Qcc_CaseSearchRelationShip', pageSize, currentPage);
      if (resList?.length) {
        allResults = allResults.concat(resList);
        if (resList.length < pageSize) {
          hasMore = false;
        } else {
          currentPage++;
        }
      } else {
        hasMore = false;
      }
    }

    if (allResults.length) {
      return _.fromPairs(_.map(allResults, (d) => [d.WdId, d.CaseSearchId]));
    }
    return null;
  }
}
