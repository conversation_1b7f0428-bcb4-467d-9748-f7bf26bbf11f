import { Module } from '@nestjs/common';
import { RiskChangeHelper } from '../../helper/risk.change.helper';
import { CompanyShareHelper } from './helper/company-share.helper';
import { CompanyStockHelper } from './helper/company-stock.helper';
import { BankLitigationHelper } from './helper/bank-litigation.helper';
import { RelatedAnalyzeHelper } from './helper/related-analyze.helper';
import { CaseReasonHelper } from './helper/case-reason.helper';
import { CompanyChangeHelper } from './helper/company-change.helper';
import { CompanyFinaceHelper } from './helper/company-finace.helper';
import { JudgementHelper } from './helper/judgement.helper';
import { MainEmployeeHelper } from './helper/main-employee.helper';
import { PenaltyHelper } from './helper/penalty.helper';
import { BaseHelper } from './helper/base.helper';
import { DimensionHitDetailProcessor } from './dimension-hit-detail.processor';
import { RelatedDimensionHitDetailProcessor } from './related-dimension-hit-detail.processor';

@Module({
  imports: [],
  providers: [
    BankLitigationHelper,
    CaseReasonHelper,
    CompanyChangeHelper,
    CompanyFinaceHelper,
    CompanyShareHelper,
    CompanyStockHelper,
    JudgementHelper,
    MainEmployeeHelper,
    PenaltyHelper,
    RelatedAnalyzeHelper,
    BaseHelper,
    DimensionHitDetailProcessor,
    RelatedDimensionHitDetailProcessor,
  ],
  exports: [
    BankLitigationHelper,
    CaseReasonHelper,
    CompanyChangeHelper,
    CompanyFinaceHelper,
    CompanyShareHelper,
    CompanyStockHelper,
    JudgementHelper,
    MainEmployeeHelper,
    PenaltyHelper,
    RelatedAnalyzeHelper,
    BaseHelper,
    DimensionHitDetailProcessor,
    RelatedDimensionHitDetailProcessor,
  ],
})
export class RiskChangeHelperModule {}
