import { QccLogger } from '@kezhaozhao/qcc-logger';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { Injectable } from '@nestjs/common';
import * as Bluebird from 'bluebird';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { PersonData } from 'libs/model/data/source/PersonData';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { processAmountString } from 'libs/utils/utils';
import { cloneDeep } from 'lodash';
import { PersonHelper } from '../../helper/person.helper';
import { RiskChangeHelper } from '../../helper/risk.change.helper';
import { Logger } from 'log4js';
import { BaseHelper } from 'apps/data/source/risk-change/helper/base.helper';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response/HitDetailsBaseResponse';

@Injectable()
export class DimensionHitDetailProcessor {
  private readonly logger: Logger = QccLogger.getLogger(DimensionHitDetailProcessor.name);
  searchEs: (body: any, companyId: string) => Promise<any>;
  constructor(private readonly riskChangeHelper: RiskChangeHelper, private readonly personHelper: PersonHelper, private readonly baseHelper: BaseHelper) {}

  bindRiskChangeEsSearchFn(esSearchFn: (body: any, companyId: string) => Promise<any>) {
    this.searchEs = esSearchFn;
  }
  /**
   * 针对每条风险动态详情再做分析判断是否命中
   * @param detailResp 动态详情
   * @param dimension  维度定义
   * @param params
   * @returns
   */
  @TraceLog({ throwError: true, spanType: 3, spanName: 'detailAnalyze' })
  async fetchHits(detailResp: HitDetailsBaseResponse, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<any[]> {
    const hitData = [];
    await Bluebird.map(detailResp.Result, async (itemRaw) => {
      try {
        const newItem = cloneDeep(itemRaw);
        // 先对item数据中的json字段做预处理
        // 先对item数据中的json字段做预处理
        Object.keys(newItem).forEach((key) => {
          if (['Extend1', 'ChangeExtend'].includes(key)) {
            const value = newItem[key];
            try {
              newItem[key] = value ? JSON.parse(value) : {};
            } catch (error) {
              newItem[key] = value;
            }
          }
        });
        let isHit = true;
        switch (newItem.Category) {
          // 经营地址变更
          case RiskChangeCategoryEnum.category139: {
            if (isHit) {
              isHit = this.baseHelper.filterLastYearData(newItem);
            }
            break;
          }
          case RiskChangeCategoryEnum.category72: {
            const layTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.layTypes);
            if (layTypesField && isHit) {
              isHit = this.riskChangeHelper.hitLayTypesField72(layTypesField, itemRaw);
            }
            const isBPField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBP);
            if (isBPField && isHit) {
              const IsBP = newItem?.ChangeExtend?.PartInfo?.IsBP ? Number(newItem.ChangeExtend.PartInfo.IsBP) : null;
              newItem.ChangeExtend.IsBP = IsBP;
              isHit = this.riskChangeHelper.hitIsBPField(isBPField, newItem);
            }
            // PartInfo  D 股份下降， H 股份上升，F 股份新增
            const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            const actorKeyNoHits = [];
            if (holderRoleField && isHit) {
              const { hit, hitKeyNos } = await this.riskChangeHelper.category72holderRoleField(holderRoleField, newItem, params.keyNo);
              isHit = hit;
              if (hitKeyNos?.length) {
                actorKeyNoHits.push(...hitKeyNos);
              }
            }
            // 变更趋势下降
            const shareChangeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeStatus);
            if (shareChangeStatusField && isHit) {
              isHit = this.riskChangeHelper.category72ShareChangeStatusField(shareChangeStatusField, newItem, actorKeyNoHits);
            }
            // 是否是PEVC融资
            const isPEVCField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isPEVC);
            if (isPEVCField && isHit) {
              isHit = await this.riskChangeHelper.category72isPEVCField(isPEVCField, newItem, params.keyNo);
            }
            // 周期内持股比例变更
            const periodShareRatioChangeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodShareRatioChange);
            if (periodShareRatioChangeField && isHit) {
              const periodShareRatioChangeFieldFieldValue = periodShareRatioChangeField.fieldValue[0] as any;
              const timePeriod = periodShareRatioChangeFieldFieldValue?.timePeriod || null;
              if (!timePeriod) {
                isHit = false;
              }
              const periodRes = await this.riskChangeHelper.commonCivilRiskChange(
                [params.keyNo],
                [RiskChangeCategoryEnum.category72],
                timePeriod,
                'month',
                10000,
              );
              if (periodRes.Paging.TotalRecords > 10000) {
                // 先记录，超过10000 ，代码需要优化改成游标查询
                this.logger.error(`RiskChange category72 Max WindowSize CompanyId ${params.keyNo}`);
              }
              isHit = await this.riskChangeHelper.category72periodShareRatioChangeField(
                periodShareRatioChangeField,
                newItem,
                params.keyNo,
                periodRes.Result,
                actorKeyNoHits[0],
              );
            }
            break;
          }
          // 负面/正面新闻
          case RiskChangeCategoryEnum.category62:
          case RiskChangeCategoryEnum.category66:
          case RiskChangeCategoryEnum.category67: {
            const holderRoles = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            const topics = this.riskChangeHelper.getDimesionTopics(dimension);
            if (holderRoles && topics) {
              isHit = await this.baseHelper.hitNegativePositiveNewsField(holderRoles, topics, newItem, params.keyNo);
            }
            break;
          }
          // 法定代表人变更
          case RiskChangeCategoryEnum.category39: {
            const layTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.layTypes);
            if (layTypesField && isHit) {
              isHit = this.riskChangeHelper.hitLayTypesField(layTypesField, newItem);
            }
            break;
          }
          // 主要人员变更
          case RiskChangeCategoryEnum.category46: {
            const compChangeRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.compChangeRole);
            if (compChangeRoleField && isHit) {
              isHit = this.riskChangeHelper.hitCompChangeRoleField(compChangeRoleField, newItem);
            }
            const baselineDateField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.baselineDate);
            const changeThresholdField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.changeThreshold);
            if (baselineDateField && changeThresholdField && compChangeRoleField && isHit) {
              const baselineDateFieldTargetValues = baselineDateField?.fieldValue as number[];
              if (baselineDateFieldTargetValues?.length) {
                const periodRes = await this.riskChangeHelper.commonCivilRiskChange(
                  [params.keyNo],
                  [RiskChangeCategoryEnum.category46],
                  baselineDateFieldTargetValues[0],
                  'year',
                  10000,
                );
                if (periodRes.Paging.TotalRecords > 10000) {
                  // 先记录，超过10000 ，代码需要优化改成游标查询
                  this.logger.error(`RiskChange category46 Max WindowSize CompanyId ${params.keyNo}`);
                }
                if (compChangeRoleField && changeThresholdField && isHit && periodRes?.Result?.length) {
                  // 获取当前所有人的信息
                  const personDatas: PersonData[] = [];
                  const [list1, list2] = await Bluebird.all([
                    this.personHelper.getEmployeeData(params.keyNo, 'IpoEmployees'),
                    this.personHelper.getEmployeeData(params.keyNo, 'Employees'),
                  ]);
                  personDatas.push(...list1, ...list2);
                  isHit = this.riskChangeHelper.changeThresholdField(
                    compChangeRoleField,
                    changeThresholdField,
                    newItem,
                    periodRes?.Result as any[],
                    personDatas,
                  );
                }
              }
            }
            break;
          }
          //动产抵押
          case RiskChangeCategoryEnum.category15: {
            const riskCategories = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.guaranteedPrincipal);
            if (riskCategories && isHit) {
              isHit = this.riskChangeHelper.category15Field(riskCategories, newItem);
            }
            break;
          }
          //土地抵押
          case RiskChangeCategoryEnum.category30: {
            //校验向企业抵押 以及 抵押权人
            const riskCategories = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.landMortgageAmount);
            if (riskCategories && isHit) {
              isHit = this.riskChangeHelper.category30Field(params.keyNo, riskCategories, newItem);
            }
            break;
          }
          //担保信息
          case RiskChangeCategoryEnum.category53:
          case RiskChangeCategoryEnum.category101: {
            //校验changeInfo.T === 1 提供担保
            const riskCategories = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.guaranteeAmount);
            if (riskCategories && isHit) {
              isHit = this.riskChangeHelper.category101Field(riskCategories, newItem);
            }
            break;
          }
          //税务催缴
          case RiskChangeCategoryEnum.category131: {
            const riskCategories = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.AmountOwed);
            if (riskCategories && isHit) {
              isHit = this.riskChangeHelper.category131Field(riskCategories, newItem);
            }
            break;
          }
          //减资公告
          case RiskChangeCategoryEnum.category123: {
            const currencyChangeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.currencyChange);
            if (currencyChangeField && isHit) {
              isHit = this.riskChangeHelper.hitCategory123CurrencyChangeField(currencyChangeField, newItem);
            }
            const regisCapitalChangeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.capitalReductionRate);
            if (regisCapitalChangeRatioField && isHit) {
              isHit = this.riskChangeHelper.capitalReduceSelectCompareResult(regisCapitalChangeRatioField, newItem);
            }
            const changeRangeRegisCapitalCycle = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodRegisCapital);
            if (changeRangeRegisCapitalCycle && isHit) {
              const valuePeriodBaseLine = changeRangeRegisCapitalCycle.fieldValue[0]?.valuePeriodBaseLine;
              if (valuePeriodBaseLine) {
                const periodRes = await this.riskChangeHelper.commonCivilRiskChange(
                  [params.keyNo],
                  [RiskChangeCategoryEnum.category37],
                  valuePeriodBaseLine,
                  'year',
                  10000,
                );
                if (periodRes.Paging.TotalRecords > 10000) {
                  // 先记录，超过10000 ，代码需要优化改成游标查询
                  this.logger.error(`RiskChange category37 Max WindowSize CompanyId ${params.keyNo}`);
                }
                if (periodRes?.Result?.length) {
                  isHit = this.riskChangeHelper.hitPeriodRegisCapitalField123(changeRangeRegisCapitalCycle, periodRes.Result, newItem);
                } else {
                  isHit = false;
                }
              }
            }
            break;
          }
          // 股东股份变更
          case RiskChangeCategoryEnum.category44: {
            // "A" = "updatelist.Count",//股比下降count
            // "B" = "dellist.Count",//退出count
            // "C" = "addlist.Count",//新增count
            // "D" = "updatelist",//股比下降列表
            // "E" = "dellist",//退出列表
            // "F" = "addlist",//新增列表
            // "G" = "riselist.Count,//上升count
            // "H" = "riselist,//上升列表
            // "IsBP" = "1","0",//是否有大股东变更 1有 2无
            // "BP" = largerPartnersChange//大股东变更列表
            let holders = [];
            // 目前只处理股比上升或下降，不包含股东退出或者新增
            const shareChangeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeStatus);
            if (shareChangeStatusField && isHit) {
              isHit = false;
              if (shareChangeStatusField.fieldValue.includes(0) && newItem.ChangeExtend.A > 0) {
                // 股比下降 newItem.ChangeExtend.A > 0
                isHit = true;
                holders = newItem.ChangeExtend.D;
              }
              if (shareChangeStatusField.fieldValue.includes(1) && newItem.ChangeExtend.G > 0) {
                // 股比上升
                isHit = true;
                holders = newItem.ChangeExtend.H;
              }
            }
            // 股权角色
            const keyNoHits: string[] = [];
            const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            if (holderRoleField && isHit && holders.length) {
              const holderKeys = holders.map((d) => d.K);
              const { hit, hitKeyNos } = await this.riskChangeHelper.hitHolderRoleField(holderRoleField, holderKeys, params.keyNo);
              isHit = hit;
              if (hitKeyNos?.length) {
                keyNoHits.push(...hitKeyNos);
              }
            }

            //差值比例
            const differenceRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.differenceRatio);
            if (differenceRatioField && isHit) {
              isHit = this.riskChangeHelper.hitDifferenceRatioField(differenceRatioField, holders, keyNoHits);
            }
            // 绝对值比例
            const absRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.absRatio);
            if (absRatioField && isHit) {
              isHit = this.riskChangeHelper.hitAbsRatioField(absRatioField, holders, keyNoHits);
            }
            //变更前持股比例
            const beforeContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beforeContent);
            if (beforeContentField && isHit && keyNoHits?.length) {
              const holder = holders.find((h) => h.K === keyNoHits[0]);
              if (holder && holder?.B) {
                newItem.BeforeContent = holder.B;
              }
              isHit = this.riskChangeHelper.hitBeforeContentField(beforeContentField, newItem);
            }
            //变更后持股比例
            const afterContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.afterContent);
            if (afterContentField && isHit) {
              const holder = holders.find((h) => h.K === keyNoHits[0]);
              if (holder && holder?.C) {
                newItem.AfterContent = holder.C;
              }
              isHit = this.riskChangeHelper.hitAfterContentField(afterContentField, newItem);
            }
            break;
          }
          //持股比例变更
          case RiskChangeCategoryEnum.category204:
          case RiskChangeCategoryEnum.category68: {
            const shareChangeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeStatus);
            if (shareChangeStatusField && isHit) {
              isHit = this.riskChangeHelper.hitShareChangeStatusField(shareChangeStatusField, newItem);
            }

            const beforeContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beforeContent);
            if (beforeContentField && isHit) {
              isHit = this.riskChangeHelper.hitBeforeContentField(beforeContentField, newItem);
            }

            const afterContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.afterContent);
            if (afterContentField && isHit) {
              isHit = this.riskChangeHelper.hitAfterContentField(afterContentField, newItem);
            }

            const isBPField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBP);
            if (isBPField && isHit) {
              isHit = this.riskChangeHelper.hitIsBPField(isBPField, newItem);
            }

            const shareChangeRateField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeRate);
            if (shareChangeRateField && isHit) {
              isHit = this.riskChangeHelper.hitShareChangeRateField(shareChangeRateField, newItem);
            }
            const keyNoHits: string[] = [];
            const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            if (holderRoleField && isHit) {
              const { hit, hitKeyNos } = await this.riskChangeHelper.hitHolderRoleField(holderRoleField, [params.keyNo], newItem.ChangeExtend.K);
              isHit = hit;
              if (hitKeyNos?.length) {
                keyNoHits.push(...hitKeyNos);
              }
            }
            break;
          }
          //对外投资变更
          case RiskChangeCategoryEnum.category203:
          case RiskChangeCategoryEnum.category17: {
            const changeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.changeStatus);
            if (changeStatusField && isHit) {
              isHit = this.riskChangeHelper.hitChangeStatusField(changeStatusField, newItem);
            }

            const afterContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.afterContent);
            if (afterContentField && isHit) {
              isHit = this.riskChangeHelper.hitAfterContentField(afterContentField, newItem);
            }

            const isBPField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBP);
            if (isBPField && isHit) {
              isHit = this.riskChangeHelper.hitIsBPField(isBPField, newItem);
            }
            const beforeContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beforeContent);
            if (beforeContentField && isHit) {
              isHit = this.riskChangeHelper.hitBeforeContentField(beforeContentField, newItem);
            }
            // 时间周期
            const timePeriodField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.timePeriod);
            const periodRes: HitDetailsBaseResponse = new HitDetailsBaseResponse();
            if (timePeriodField && isHit) {
              // 获取所有的
              const periodRes = await this.riskChangeHelper.commonCivilRiskChange(
                [params.keyNo],
                [RiskChangeCategoryEnum.category17],
                timePeriodField.fieldValue[0],
                'month',
                10000,
              );
              if (periodRes.Paging.TotalRecords > 10000) {
                // 先记录，超过10000 ，代码需要优化改成游标查询
                this.logger.error(`RiskChange category17 Max WindowSize CompanyId ${params.keyNo}`);
              }
              if (periodRes?.Result?.length) {
                isHit = true;
              } else {
                isHit = false;
              }
            }
            // 变更阀值
            const thresholdCountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.thresholdCount);
            if (thresholdCountField && isHit) {
              if (periodRes?.Result?.length) {
                const allPeriodList = periodRes.Result;
                isHit = this.riskChangeHelper.hitTimePeriodThresholdCountField(thresholdCountField, allPeriodList);
              } else {
                isHit = false;
              }
            }
            break;
          }
          //受益人变更 ,没有数据
          /*case RiskChangeCategoryEnum.category21: {
              const beneficiaryTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beneficiaryType);
              if (beneficiaryTypeField && isHit) isHit = this.riskChangeHelper.hitBeneficiaryTypeField(beneficiaryTypeField, newItem);
              break;
            }*/
          //注册资本变更
          case RiskChangeCategoryEnum.category37: {
            const currencyChangeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.currencyChange);
            if (currencyChangeField && isHit) {
              isHit = this.riskChangeHelper.hitCurrencyChangeField(currencyChangeField, newItem);
            }
            const regisCapitalTrendField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.regisCapitalTrend);
            if (regisCapitalTrendField && isHit) {
              isHit = this.riskChangeHelper.hitRegisCapitalTrendField(regisCapitalTrendField, newItem);
            }
            const regisCapitalChangeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.regisCapitalChangeRatio);
            if (regisCapitalChangeRatioField && isHit) {
              isHit = this.riskChangeHelper.hitRegisCapitalChangeRatioField(regisCapitalChangeRatioField, newItem);
            }
            const periodRegisCapitalField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodRegisCapital);
            if (periodRegisCapitalField && isHit) {
              const valuePeriodBaseLine = periodRegisCapitalField.fieldValue[0]?.valuePeriodBaseLine;
              if (valuePeriodBaseLine) {
                const periodRes = await this.riskChangeHelper.commonCivilRiskChange(
                  [params.keyNo],
                  [RiskChangeCategoryEnum.category37],
                  valuePeriodBaseLine,
                  'year',
                );
                isHit = this.riskChangeHelper.hitPeriodRegisCapitalField(periodRegisCapitalField, newItem, periodRes.Result);
              }
            }
            break;
          }
          //经营状态变更
          case RiskChangeCategoryEnum.category38: {
            const businessStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessStatus);
            if (businessStatusField && isHit) {
              isHit = this.riskChangeHelper.category38(businessStatusField, newItem);
            }
            break;
          }
          //被限制高消费
          case RiskChangeCategoryEnum.category55: {
            const restrictTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.restricterType);
            if (restrictTypeField && isHit) {
              isHit = this.riskChangeHelper.restricterTypeField(restrictTypeField, newItem);
            }
            break;
          }
          //破产重整
          case RiskChangeCategoryEnum.category58: {
            isHit = this.riskChangeHelper.category58Field(newItem);
            break;
          }
          //裁判文书
          case RiskChangeCategoryEnum.category4: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.riskChangeHelper.category4(judicialRoleTypeField, newItem);
            }
            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.riskChangeHelper.caseReasonTypeField(caseReasonTypeField, newItem);
            }
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.riskChangeHelper.checkCaseTypeField(caseTypeField, newItem);
            }
            const lawsuitAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.lawsuitAmount);
            if (lawsuitAmountField && isHit) {
              isHit = this.riskChangeHelper.checkAmountField(lawsuitAmountField, processAmountString(newItem?.ChangeExtend?.I), 1);
            }
            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.riskChangeHelper.checkContractDisputeField(isContractDisputeField, newItem);
            }
            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.riskChangeHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
            }
            const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
            if (isBankOrFlField && isHit) {
              isHit = this.riskChangeHelper.checkBankOrFinancialLeasingField4(isBankOrFlField, newItem);
            }
            break;
          }
          //立案信息
          case RiskChangeCategoryEnum.category49: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.riskChangeHelper.category49(judicialRoleTypeField, newItem);
            }
            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.riskChangeHelper.caseReasonTypeField(caseReasonTypeField, newItem);
            }
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.riskChangeHelper.checkCaseTypeField(caseTypeField, newItem);
            }
            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.riskChangeHelper.checkContractDisputeField(isContractDisputeField, newItem);
            }
            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.riskChangeHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
            }
            const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
            if (isBankOrFlField && isHit) {
              isHit = this.riskChangeHelper.checkBankOrFinancialLeasingField49(isBankOrFlField, newItem);
            }
            break;
          }
          //开庭公告
          case RiskChangeCategoryEnum.category18: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.riskChangeHelper.category18(judicialRoleTypeField, newItem);
            }
            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.riskChangeHelper.caseReasonTypeField(caseReasonTypeField, newItem);
            }
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.riskChangeHelper.checkCaseTypeField(caseTypeField, newItem);
            }
            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.riskChangeHelper.checkContractDisputeField(isContractDisputeField, newItem);
            }
            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.riskChangeHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
            }
            const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
            if (isBankOrFlField && isHit) {
              isHit = this.riskChangeHelper.checkBankOrFinancialLeasingField18(isBankOrFlField, newItem);
            }
            break;
          }
          //法院公告
          case RiskChangeCategoryEnum.category7: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.riskChangeHelper.category7(judicialRoleTypeField, newItem);
            }
            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.riskChangeHelper.caseReasonTypeField(caseReasonTypeField, newItem);
            }
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.riskChangeHelper.checkCaseTypeField(caseTypeField, newItem);
            }
            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.riskChangeHelper.checkContractDisputeField(isContractDisputeField, newItem);
            }
            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.riskChangeHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
            }
            const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
            if (isBankOrFlField && isHit) {
              isHit = this.riskChangeHelper.checkBankOrFinancialLeasingField7(isBankOrFlField, newItem);
            }
            break;
          }
          //送达公告
          case RiskChangeCategoryEnum.category27: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.riskChangeHelper.category27(judicialRoleTypeField, newItem);
            }
            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.riskChangeHelper.caseReasonTypeField(caseReasonTypeField, newItem);
            }
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.riskChangeHelper.checkCaseTypeField(caseTypeField, newItem);
            }
            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.riskChangeHelper.checkContractDisputeField(isContractDisputeField, newItem);
            }
            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.riskChangeHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
            }
            const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
            if (isBankOrFlField && isHit) {
              isHit = this.riskChangeHelper.checkBankOrFinancialLeasingField27(isBankOrFlField, newItem);
            }
            break;
          }
          //诉前调解
          case RiskChangeCategoryEnum.category90: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.riskChangeHelper.category90(judicialRoleTypeField, newItem);
            }
            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.riskChangeHelper.caseReasonTypeField(caseReasonTypeField, newItem);
            }
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.riskChangeHelper.checkCaseTypeField(caseTypeField, newItem);
            }
            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.riskChangeHelper.checkContractDisputeField(isContractDisputeField, newItem);
            }
            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.riskChangeHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
            }
            const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
            if (isBankOrFlField && isHit) {
              isHit = this.riskChangeHelper.checkBankOrFinancialLeasingField90(isBankOrFlField, newItem);
            }
            break;
          }
          // 司法拍卖
          case RiskChangeCategoryEnum.category57: {
            const auctionTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.auctionType);
            if (auctionTypeField && isHit) {
              isHit = this.riskChangeHelper.auctionTypeField(auctionTypeField, newItem);
            }
            // 起拍价
            const limitPriceField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.listingPrice);
            if (limitPriceField && isHit) {
              isHit = this.riskChangeHelper.limitPriceTypeField(limitPriceField, newItem);
            }
            break;
          }
          // 行政处罚
          case RiskChangeCategoryEnum.category107: {
            const penaltyUnitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyUnit);
            if (penaltyUnitField && isHit) {
              isHit = this.riskChangeHelper.penaltyUnitField(penaltyUnitField, newItem);
            }
            const punishTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishType);
            if (punishTypeField && isHit) {
              isHit = this.riskChangeHelper.punishTypeField(punishTypeField, newItem);
            }
            const punishAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishAmount);
            if (punishAmountField && isHit) {
              isHit = this.riskChangeHelper.checkAmountField(punishAmountField, newItem?.ChangeExtend?.F, 1);
            }
            const punishRedCardField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishRedCard);
            if (punishRedCardField && isHit) {
              isHit = this.riskChangeHelper.penaltyRedCardFieldCategory107(punishRedCardField, newItem);
            }
            const penaltyIssuingUnitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyIssuingUnit);
            if (penaltyIssuingUnitField && isHit) {
              isHit = this.riskChangeHelper.penaltyIssuingUnitField(penaltyIssuingUnitField, newItem);
            }
            const isListedField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isListed);
            if (isListedField && isHit) {
              isHit = await this.riskChangeHelper.checkListedField(isListedField, newItem, params.keyNo);
            }
            break;
          }
          // 经营异常
          case RiskChangeCategoryEnum.category11: {
            const businessAbnormalTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessAbnormalType);
            if (businessAbnormalTypeField && isHit) {
              isHit = this.riskChangeHelper.businessAbnormalTypeField(businessAbnormalTypeField, newItem);
            }
            break;
          }
          // 环保处罚
          case RiskChangeCategoryEnum.category22: {
            const punishEnvTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishType);
            if (punishEnvTypeField && isHit) {
              isHit = this.riskChangeHelper.punishEnvTypeField(punishEnvTypeField, newItem);
            }
            const punishAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltiesAmount);
            if (punishAmountField && isHit) {
              isHit = this.riskChangeHelper.amountField(punishAmountField, newItem?.ChangeExtend?.E, 1);
            }
            const punishRedCardField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishRedCard);
            if (punishRedCardField && isHit) {
              isHit = this.riskChangeHelper.penaltyRedCardFieldCategory22(punishRedCardField, newItem);
            }
            break;
          }
          //欠税公告
          case RiskChangeCategoryEnum.category31: {
            const taxOwedAmountTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.taxOwedAmount);
            if (taxOwedAmountTypeField && isHit) {
              // isHit = this.riskChangeHelper.taxOwedAmountTypeField(taxOwedAmountTypeField, newItem);
              isHit = this.riskChangeHelper.amountField(taxOwedAmountTypeField, newItem?.ChangeExtend?.B, 10000);
            }
            const penaltyUnitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyUnit);
            if (penaltyUnitField && isHit) {
              isHit = this.riskChangeHelper.penaltyUnitField31(penaltyUnitField, newItem);
            }
            const punishAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishAmount);
            if (punishAmountField && isHit) {
              isHit = this.riskChangeHelper.checkAmountField(punishAmountField, newItem?.ChangeExtend?.F, 1);
            }
            break;
          }
          //被列入税务非正常户
          case RiskChangeCategoryEnum.category117: {
            const penaltyUnitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyUnit);
            if (penaltyUnitField && isHit) {
              isHit = this.riskChangeHelper.penaltyUnitField117(penaltyUnitField, newItem);
            }
            break;
          }
          //金融监管
          case RiskChangeCategoryEnum.category121: {
            const financialPenaltyCauseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.financialPenaltyCause);
            if (financialPenaltyCauseTypeField && isHit) {
              isHit = this.riskChangeHelper.financialPenaltyCauseTypeField(financialPenaltyCauseTypeField, newItem);
            }
            break;
          }
          //抽查检查
          case RiskChangeCategoryEnum.category14: {
            const inspectionResultTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.inspectionResultType);
            if (inspectionResultTypeField && isHit) {
              isHit = this.riskChangeHelper.inspectionResultTypeField(inspectionResultTypeField, newItem);
            }
            break;
          }
          // 知识产权
          case RiskChangeCategoryEnum.category86: {
            const intellectualRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.intellectualRole);
            if (intellectualRoleField && isHit) {
              isHit = this.riskChangeHelper.category86IntellectualRole(intellectualRoleField, newItem);
            }
            const intellectualTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.intellectualType);
            if (intellectualTypeField && isHit) {
              isHit = this.riskChangeHelper.category86IntellectualType(intellectualTypeField, newItem);
            }
            break;
          }
          // 未准入境
          case RiskChangeCategoryEnum.category98: {
            break;
          }
          // 产品召回
          case RiskChangeCategoryEnum.category78: {
            break;
          }
          // 食品安全
          case RiskChangeCategoryEnum.category79: {
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.productSource);
            if (typeField && isHit) {
              isHit = this.riskChangeHelper.category79Field(typeField, newItem);
            }
            break;
          }
          // 注销备案
          case RiskChangeCategoryEnum.category61: {
            break;
          }
          // 简易注销
          case RiskChangeCategoryEnum.category23: {
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.simpleCancelType);
            if (typeField && isHit) {
              isHit = this.riskChangeHelper.category23Field(typeField, newItem);
            }
            break;
          }
          // 票据违约
          case RiskChangeCategoryEnum.category108: {
            break;
          }
          // 融资动态
          case RiskChangeCategoryEnum.category28: {
            //排除 获得融资的融资动态
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.financingDynamicType);
            if (typeField && isHit) {
              isHit = this.riskChangeHelper.category28Field(typeField, newItem);
            }
            break;
          }
          // 企业公告65&113
          case RiskChangeCategoryEnum.category65: {
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.announcementReportType);
            if (isHit && typeField) {
              isHit = this.riskChangeHelper.categoryAnnouncementReportField(typeField, newItem);
            }
            break;
          }
          case RiskChangeCategoryEnum.category113: {
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.announcementReportType);
            if (isHit && typeField) {
              isHit = this.riskChangeHelper.categoryAnnouncementReportField(typeField, newItem);
            }
            break;
          }
          case RiskChangeCategoryEnum.category114: {
            const shareChangeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeStatus);
            if (shareChangeStatusField && isHit) {
              isHit = await this.riskChangeHelper.category114shareChangeStatusField(shareChangeStatusField, newItem);
            }
            const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            if (holderRoleField && isHit) {
              isHit = await this.riskChangeHelper.category114holderRoleField(holderRoleField, newItem, params.keyNo);
            }
            const beforeContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beforeContent);
            if (beforeContentField && isHit) {
              isHit = await this.riskChangeHelper.category114beforeContentField(beforeContentField, newItem);
            }
            const afterContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.afterContent);
            if (afterContentField && isHit) {
              isHit = await this.riskChangeHelper.category114afterContentField(afterContentField, newItem);
            }
            break;
          }
          // 股权冻结
          case RiskChangeCategoryEnum.category26: {
            const equityFreezeScopeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityFreezeScope);
            if (equityFreezeScopeField && isHit) {
              isHit = this.riskChangeHelper.equityFreezeScopeFieldCategory26(equityFreezeScopeField, newItem);
            }
            // 主要人员
            const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            if (holderRoleField && isHit) {
              isHit = await this.riskChangeHelper.holderRoleFieldCategory26(holderRoleField, newItem, params.keyNo);
            }
            // 股权冻结金额
            const equityFrozenAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityFrozenAmount);
            if (equityFrozenAmountField && isHit) {
              isHit = this.riskChangeHelper.equityFrozenAmountFieldCategory26(equityFrozenAmountField, newItem);
            }
            break;
          }
          // 股权出质
          case RiskChangeCategoryEnum.category12: {
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgedRatioOrHolding);
            if (typeField && isHit) {
              isHit = this.riskChangeHelper.category12Field(typeField, newItem);
            }
            // 主要人员
            const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            if (holderRoleField && isHit) {
              isHit = await this.riskChangeHelper.holderRoleFieldCategory12(holderRoleField, newItem, params.keyNo);
            }
            // 股权出质的状态
            const equityPledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeStatus);
            if (equityPledgeStatusField && isHit) {
              isHit = this.riskChangeHelper.equityPledgeStatusFieldCategory12(equityPledgeStatusField, newItem);
            }
            // 股权出质比例
            const equityPledgeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeRatio);
            if (equityPledgeRatioField && isHit) {
              isHit = this.riskChangeHelper.equityPledgeRatioFieldCategory12(equityPledgeRatioField, newItem);
            }
            // 股权出质-出质股权数额
            const equityPledgeAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeAmount);
            if (equityPledgeAmountField && isHit) {
              isHit = this.riskChangeHelper.equityPledgeAmountFieldCategory12(equityPledgeAmountField, newItem);
            }
            // 股权出质-质押股份数量(股)
            const equityPledgeQuantityField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeQuantity);
            if (equityPledgeQuantityField && isHit) {
              isHit = this.riskChangeHelper.equityPledgeQuantityFieldCategory12(equityPledgeQuantityField, newItem);
            }
            break;
          }
          // 股权质押
          case RiskChangeCategoryEnum.category50: {
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.pledgedRatioOrHolding);
            if (typeField && isHit) {
              isHit = this.riskChangeHelper.category50Field(typeField, newItem);
            }
            // 主要人员
            const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            if (holderRoleField && isHit) {
              isHit = await this.riskChangeHelper.holderRoleFieldCategory50(holderRoleField, newItem, params.keyNo);
            }
            // 股权质押的状态
            const sharePledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.sharePledgeStatus);
            if (sharePledgeStatusField && isHit) {
              isHit = this.riskChangeHelper.sharePledgeStatusFieldCategory50(sharePledgeStatusField, newItem);
            }
            // 股权质押-质押占总股本比例
            const stockPledgeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.stockPledgeRatio);
            if (stockPledgeRatioField && isHit) {
              isHit = this.riskChangeHelper.stockPledgeRatioFieldCategory50(stockPledgeRatioField, newItem);
            }
            // 股权质押-质押股份数量(股)
            const stockPledgeQuantityField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.stockPledgeQuantity);
            if (stockPledgeQuantityField && isHit) {
              isHit = this.riskChangeHelper.stockPledgeQuantityFieldCategory50(stockPledgeQuantityField, newItem);
            }
            break;
          }
          // 询价评估
          case RiskChangeCategoryEnum.category59: {
            if (isHit && dimension.strategyFields) {
              const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.evaluationPrice);
              if (typeField && isHit) {
                isHit = this.riskChangeHelper.category59Field(typeField, newItem);
              }
            }
            break;
          }
          // 询价评估-机构
          case RiskChangeCategoryEnum.category76: {
            break;
          }
          // 资产拍卖
          case RiskChangeCategoryEnum.category75: {
            if (isHit && dimension.strategyFields) {
              const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.quoteResultPrice);
              if (typeField && isHit) {
                isHit = this.riskChangeHelper.category75Field(typeField, newItem);
              }
            }
            break;
          }
          default: {
            break;
          }
        }
        if (isHit) {
          hitData.push(itemRaw);
        }
      } catch (e) {
        this.logger.error(`RiskChange getDimensionDetail request: ${JSON.stringify(itemRaw)}`, e);
      }
    });
    return hitData;
  }
}
