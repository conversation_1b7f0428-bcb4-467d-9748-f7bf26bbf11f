import { Test, TestingModule } from '@nestjs/testing';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { BaseHelper } from '../helper/base.helper';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

const createTestStrategyField = (fieldKey: DimensionFieldKeyEnums, fieldValue: any): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  return field;
};

describe('DimensionHitDetailProcessor 财务相关测试', () => {
  let processor: DimensionHitDetailProcessor;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockBaseHelper: jest.Mocked<BaseHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockRiskChangeHelper = {
      category15Field: jest.fn(),
      category30Field: jest.fn(),
      category101Field: jest.fn(),
      category131Field: jest.fn(),
      hitCategory123CurrencyChangeField: jest.fn(),
      capitalReduceSelectCompareResult: jest.fn(),
      hitPeriodRegisCapitalField123: jest.fn(),
      restricterTypeField: jest.fn(),
      category58Field: jest.fn(),
      equityFreezeScopeFieldCategory26: jest.fn(),
      holderRoleFieldCategory26: jest.fn(),
      equityFrozenAmountFieldCategory26: jest.fn(),
      category12Field: jest.fn(),
      holderRoleFieldCategory12: jest.fn(),
      equityPledgeStatusFieldCategory12: jest.fn(),
      equityPledgeRatioFieldCategory12: jest.fn(),
      equityPledgeAmountFieldCategory12: jest.fn(),
      equityPledgeQuantityFieldCategory12: jest.fn(),
      category50Field: jest.fn(),
      holderRoleFieldCategory50: jest.fn(),
      sharePledgeStatusFieldCategory50: jest.fn(),
      stockPledgeRatioFieldCategory50: jest.fn(),
      stockPledgeQuantityFieldCategory50: jest.fn(),
      category59Field: jest.fn(),
      category75Field: jest.fn(),
    } as any;

    mockPersonHelper = {
      getEmployeeData: jest.fn(),
    } as any;

    mockBaseHelper = {
      filterLastYearData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DimensionHitDetailProcessor,
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: BaseHelper, useValue: mockBaseHelper },
      ],
    }).compile();

    processor = module.get<DimensionHitDetailProcessor>(DimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('动产抵押 (category15)', () => {
    it('应该正确处理动产抵押类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-movable-mortgage',
        Category: RiskChangeCategoryEnum.category15,
        ChangeExtend: JSON.stringify({
          Amount: '5000000',
          MortgageType: '设备抵押',
          Mortgagor: '测试公司',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const guaranteedPrincipalField = createTestStrategyField(DimensionFieldKeyEnums.guaranteedPrincipal, [100, 1000]);
      dimension.strategyFields = [guaranteedPrincipalField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category15Field.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-movable-mortgage');
      expect(mockRiskChangeHelper.category15Field).toHaveBeenCalledWith(
        guaranteedPrincipalField,
        expect.objectContaining({
          Id: 'test-movable-mortgage',
          Category: RiskChangeCategoryEnum.category15,
        }),
      );
    });
  });

  describe('土地抵押 (category30)', () => {
    it('应该正确处理土地抵押类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-land-mortgage',
        Category: RiskChangeCategoryEnum.category30,
        ChangeExtend: JSON.stringify({
          LandArea: '1000',
          MortgageAmount: '10000000',
          Location: '北京市朝阳区',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const landMortgageAmountField = createTestStrategyField(DimensionFieldKeyEnums.landMortgageAmount, [500, 2000]);
      dimension.strategyFields = [landMortgageAmountField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category30Field.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-land-mortgage');
      expect(mockRiskChangeHelper.category30Field).toHaveBeenCalledWith(
        params.keyNo,
        landMortgageAmountField,
        expect.objectContaining({
          Id: 'test-land-mortgage',
          Category: RiskChangeCategoryEnum.category30,
        }),
      );
    });
  });

  describe('担保信息 (category53/category101)', () => {
    it('应该正确处理担保信息 category53', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-guarantee-53',
        Category: RiskChangeCategoryEnum.category53,
        ChangeExtend: JSON.stringify({
          T: 1, // 提供担保
          Amount: '3000000',
          GuaranteeType: '连带责任担保',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const guaranteeAmountField = createTestStrategyField(DimensionFieldKeyEnums.guaranteeAmount, [200, 500]);
      dimension.strategyFields = [guaranteeAmountField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category101Field.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-guarantee-53');
      expect(mockRiskChangeHelper.category101Field).toHaveBeenCalledWith(
        guaranteeAmountField,
        expect.objectContaining({
          Id: 'test-guarantee-53',
          Category: RiskChangeCategoryEnum.category53,
        }),
      );
    });

    it('应该正确处理担保信息 category101', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-guarantee-101',
        Category: RiskChangeCategoryEnum.category101,
        ChangeExtend: JSON.stringify({
          T: 1, // 提供担保
          Amount: '8000000',
          GuaranteeType: '一般担保',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const guaranteeAmountField = createTestStrategyField(DimensionFieldKeyEnums.guaranteeAmount, [500, 1000]);
      dimension.strategyFields = [guaranteeAmountField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category101Field.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-guarantee-101');
      expect(mockRiskChangeHelper.category101Field).toHaveBeenCalled();
    });
  });

  describe('税务催缴 (category131)', () => {
    it('应该正确处理税务催缴类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-tax-collection',
        Category: RiskChangeCategoryEnum.category131,
        ChangeExtend: JSON.stringify({
          TaxAmount: '500000',
          TaxType: '企业所得税',
          DueDate: '2024-03-31',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const amountOwedField = createTestStrategyField(DimensionFieldKeyEnums.AmountOwed, [30, 100]);
      dimension.strategyFields = [amountOwedField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category131Field.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-tax-collection');
      expect(mockRiskChangeHelper.category131Field).toHaveBeenCalledWith(
        amountOwedField,
        expect.objectContaining({
          Id: 'test-tax-collection',
          Category: RiskChangeCategoryEnum.category131,
        }),
      );
    });
  });

  describe('减资公告 (category123)', () => {
    it('应该正确处理减资公告类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-capital-reduction',
        Category: RiskChangeCategoryEnum.category123,
        ChangeExtend: JSON.stringify({
          OriginalCapital: '10000000',
          ReducedCapital: '8000000',
          Currency: 'CNY',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const currencyChangeField = createTestStrategyField(DimensionFieldKeyEnums.currencyChange, ['CNY']);
      const capitalReductionRateField = createTestStrategyField(DimensionFieldKeyEnums.capitalReductionRate, [10, 30]);
      dimension.strategyFields = [currencyChangeField, capitalReductionRateField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.hitCategory123CurrencyChangeField.mockReturnValue(true);
      mockRiskChangeHelper.capitalReduceSelectCompareResult.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-capital-reduction');
      expect(mockRiskChangeHelper.hitCategory123CurrencyChangeField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.capitalReduceSelectCompareResult).toHaveBeenCalled();
    });

    it('应该正确处理带有周期注册资本字段的减资公告', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-capital-reduction-period',
        Category: RiskChangeCategoryEnum.category123,
        ChangeExtend: JSON.stringify({
          OriginalCapital: '20000000',
          ReducedCapital: '15000000',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const periodRegisCapitalField = createTestStrategyField(DimensionFieldKeyEnums.periodRegisCapital, [{ valuePeriodBaseLine: 2 }]);
      dimension.strategyFields = [periodRegisCapitalField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Mock ES搜索返回
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 3 },
            hits: [
              { _source: { Id: 'period1', Category: RiskChangeCategoryEnum.category37 } },
              { _source: { Id: 'period2', Category: RiskChangeCategoryEnum.category37 } },
            ],
          },
        },
      });

      mockRiskChangeHelper.hitPeriodRegisCapitalField123.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-capital-reduction-period');
      expect(mockRiskChangeHelper.hitPeriodRegisCapitalField123).toHaveBeenCalled();
    });
  });

  describe('被限制高消费 (category55)', () => {
    it('应该正确处理被限制高消费类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-restricted-consumption',
        Category: RiskChangeCategoryEnum.category55,
        ChangeExtend: JSON.stringify({
          C: JSON.stringify([{ KeyNo: 'company1', Name: '测试公司', Org: 0 }]),
          E: JSON.stringify([{ KeyNo: 'person1', Name: '张三', Org: 0 }]),
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const restricterTypeField = createTestStrategyField(DimensionFieldKeyEnums.restricterType, [1, 2]); // 企业本身和法人代表
      dimension.strategyFields = [restricterTypeField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.restricterTypeField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-restricted-consumption');
      expect(mockRiskChangeHelper.restricterTypeField).toHaveBeenCalledWith(
        restricterTypeField,
        expect.objectContaining({
          Id: 'test-restricted-consumption',
          Category: RiskChangeCategoryEnum.category55,
        }),
      );
    });
  });

  describe('破产重整 (category58)', () => {
    it('应该正确处理破产重整类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-bankruptcy-reorganization',
        Category: RiskChangeCategoryEnum.category58,
        ChangeExtend: JSON.stringify({
          C: 'test-company-id', // 被申请人
          ReorganizationType: '重整',
          Court: '北京市第一中级人民法院',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category58Field.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-bankruptcy-reorganization');
      expect(mockRiskChangeHelper.category58Field).toHaveBeenCalledWith(
        expect.objectContaining({
          Id: 'test-bankruptcy-reorganization',
          Category: RiskChangeCategoryEnum.category58,
        }),
      );
    });
  });

  describe('股权冻结 (category26)', () => {
    it('应该正确处理股权冻结类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-equity-freeze',
        Category: RiskChangeCategoryEnum.category26,
        ChangeExtend: JSON.stringify({
          FreezeScope: '全部股权',
          FreezeAmount: '5000000',
          Freezer: '北京市第一中级人民法院',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const equityFreezeScopeField = createTestStrategyField(DimensionFieldKeyEnums.equityFreezeScope, ['全部股权']);
      const holderRoleField = createTestStrategyField(DimensionFieldKeyEnums.holderRole, [1, 2]);
      const equityFrozenAmountField = createTestStrategyField(DimensionFieldKeyEnums.equityFrozenAmount, [300, 800]);
      dimension.strategyFields = [equityFreezeScopeField, holderRoleField, equityFrozenAmountField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.equityFreezeScopeFieldCategory26.mockReturnValue(true);
      mockRiskChangeHelper.holderRoleFieldCategory26.mockResolvedValue(true);
      mockRiskChangeHelper.equityFrozenAmountFieldCategory26.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-equity-freeze');
      expect(mockRiskChangeHelper.equityFreezeScopeFieldCategory26).toHaveBeenCalled();
      expect(mockRiskChangeHelper.holderRoleFieldCategory26).toHaveBeenCalled();
      expect(mockRiskChangeHelper.equityFrozenAmountFieldCategory26).toHaveBeenCalled();
    });
  });

  describe('股权出质 (category12)', () => {
    it('应该正确处理股权出质类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-equity-pledge',
        Category: RiskChangeCategoryEnum.category12,
        ChangeExtend: JSON.stringify({
          PledgeRatio: '30%',
          PledgeAmount: '3000000',
          PledgeQuantity: '1000000',
          Status: '有效',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const equityPledgedRatioField = createTestStrategyField(DimensionFieldKeyEnums.equityPledgedRatioOrHolding, [20, 50]);
      const holderRoleField = createTestStrategyField(DimensionFieldKeyEnums.holderRole, [1, 2]);
      const equityPledgeStatusField = createTestStrategyField(DimensionFieldKeyEnums.equityPledgeStatus, ['有效']);
      const equityPledgeRatioField = createTestStrategyField(DimensionFieldKeyEnums.equityPledgeRatio, [25, 40]);
      dimension.strategyFields = [equityPledgedRatioField, holderRoleField, equityPledgeStatusField, equityPledgeRatioField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category12Field.mockReturnValue(true);
      mockRiskChangeHelper.holderRoleFieldCategory12.mockResolvedValue(true);
      mockRiskChangeHelper.equityPledgeStatusFieldCategory12.mockReturnValue(true);
      mockRiskChangeHelper.equityPledgeRatioFieldCategory12.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-equity-pledge');
      expect(mockRiskChangeHelper.category12Field).toHaveBeenCalled();
      expect(mockRiskChangeHelper.holderRoleFieldCategory12).toHaveBeenCalled();
      expect(mockRiskChangeHelper.equityPledgeStatusFieldCategory12).toHaveBeenCalled();
      expect(mockRiskChangeHelper.equityPledgeRatioFieldCategory12).toHaveBeenCalled();
    });

    it('应该正确处理带有出质股权数额和质押股份数量的股权出质', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-equity-pledge-amount',
        Category: RiskChangeCategoryEnum.category12,
        ChangeExtend: JSON.stringify({
          PledgeAmount: '8000000',
          PledgeQuantity: '2000000',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const equityPledgeAmountField = createTestStrategyField(DimensionFieldKeyEnums.equityPledgeAmount, [500, 1000]);
      const equityPledgeQuantityField = createTestStrategyField(DimensionFieldKeyEnums.equityPledgeQuantity, [150, 250]);
      dimension.strategyFields = [equityPledgeAmountField, equityPledgeQuantityField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.equityPledgeAmountFieldCategory12.mockReturnValue(true);
      mockRiskChangeHelper.equityPledgeQuantityFieldCategory12.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-equity-pledge-amount');
      expect(mockRiskChangeHelper.equityPledgeAmountFieldCategory12).toHaveBeenCalled();
      expect(mockRiskChangeHelper.equityPledgeQuantityFieldCategory12).toHaveBeenCalled();
    });
  });

  describe('股权质押 (category50)', () => {
    it('应该正确处理股权质押类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-stock-pledge',
        Category: RiskChangeCategoryEnum.category50,
        ChangeExtend: JSON.stringify({
          PledgeRatio: '25%',
          PledgeQuantity: '5000000',
          Status: '质押中',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const pledgedRatioField = createTestStrategyField(DimensionFieldKeyEnums.pledgedRatioOrHolding, [20, 40]);
      const holderRoleField = createTestStrategyField(DimensionFieldKeyEnums.holderRole, [1, 2]);
      const sharePledgeStatusField = createTestStrategyField(DimensionFieldKeyEnums.sharePledgeStatus, ['质押中']);
      dimension.strategyFields = [pledgedRatioField, holderRoleField, sharePledgeStatusField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category50Field.mockReturnValue(true);
      mockRiskChangeHelper.holderRoleFieldCategory50.mockResolvedValue(true);
      mockRiskChangeHelper.sharePledgeStatusFieldCategory50.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-stock-pledge');
      expect(mockRiskChangeHelper.category50Field).toHaveBeenCalled();
      expect(mockRiskChangeHelper.holderRoleFieldCategory50).toHaveBeenCalled();
      expect(mockRiskChangeHelper.sharePledgeStatusFieldCategory50).toHaveBeenCalled();
    });

    it('应该正确处理带有质押占总股本比例和质押股份数量的股权质押', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-stock-pledge-ratio',
        Category: RiskChangeCategoryEnum.category50,
        ChangeExtend: JSON.stringify({
          StockPledgeRatio: '15%',
          StockPledgeQuantity: '3000000',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const stockPledgeRatioField = createTestStrategyField(DimensionFieldKeyEnums.stockPledgeRatio, [10, 20]);
      const stockPledgeQuantityField = createTestStrategyField(DimensionFieldKeyEnums.stockPledgeQuantity, [200, 400]);
      dimension.strategyFields = [stockPledgeRatioField, stockPledgeQuantityField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.stockPledgeRatioFieldCategory50.mockReturnValue(true);
      mockRiskChangeHelper.stockPledgeQuantityFieldCategory50.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-stock-pledge-ratio');
      expect(mockRiskChangeHelper.stockPledgeRatioFieldCategory50).toHaveBeenCalled();
      expect(mockRiskChangeHelper.stockPledgeQuantityFieldCategory50).toHaveBeenCalled();
    });
  });

  describe('询价评估 (category59)', () => {
    it('应该正确处理询价评估类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-price-evaluation',
        Category: RiskChangeCategoryEnum.category59,
        ChangeExtend: JSON.stringify({
          EvaluationPrice: '12000000',
          EvaluationType: '房产评估',
          Evaluator: '北京评估公司',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const evaluationPriceField = createTestStrategyField(DimensionFieldKeyEnums.evaluationPrice, [800, 1500]);
      dimension.strategyFields = [evaluationPriceField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category59Field.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-price-evaluation');
      expect(mockRiskChangeHelper.category59Field).toHaveBeenCalledWith(
        evaluationPriceField,
        expect.objectContaining({
          Id: 'test-price-evaluation',
          Category: RiskChangeCategoryEnum.category59,
        }),
      );
    });
  });

  describe('资产拍卖 (category75)', () => {
    it('应该正确处理资产拍卖类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-asset-auction',
        Category: RiskChangeCategoryEnum.category75,
        ChangeExtend: JSON.stringify({
          QuoteResultPrice: '8500000',
          AssetType: '机器设备',
          AuctionDate: '2024-04-15',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const quoteResultPriceField = createTestStrategyField(DimensionFieldKeyEnums.quoteResultPrice, [600, 1000]);
      dimension.strategyFields = [quoteResultPriceField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category75Field.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-asset-auction');
      expect(mockRiskChangeHelper.category75Field).toHaveBeenCalledWith(
        quoteResultPriceField,
        expect.objectContaining({
          Id: 'test-asset-auction',
          Category: RiskChangeCategoryEnum.category75,
        }),
      );
    });
  });
});
