import { Test, TestingModule } from '@nestjs/testing';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from 'libs/config/config.service';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { RelatedDimensionHitDetailProcessor } from '../related-dimension-hit-detail.processor';
import { CaseReasonHelper } from '../helper/case-reason.helper';
import { RiskChangeEsSource } from '../risk-change-es.source';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { CompanySearchService } from 'apps/company/company-search.service';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';

// 测试辅助函数
const createMockDimensionDefinition = (key: DimensionTypeEnums): DimensionDefinitionEntity => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return def;
};

const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = createMockDimensionDefinition(key);
  const strategy = new DimensionHitStrategyPO(def);

  // Mock getStrategyFieldByKey 方法
  strategy.getStrategyFieldByKey = jest.fn().mockReturnValue({
    fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
    fieldValue: [{ valuePeriodBaseLine: 1 }],
  });

  return strategy;
};

const createTestDimensionResult = (dimensionKey: DimensionTypeEnums, totalHits = 1): DimensionHitResultPO => {
  const result = new DimensionHitResultPO();
  result.dimensionKey = dimensionKey;
  result.dimensionName = `测试维度-${dimensionKey}`;
  result.totalHits = totalHits;
  result.description = '测试结果';
  return result;
};

describe('RiskChangeEsSource 单元测试', () => {
  let service: RiskChangeEsSource;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockDimensionHitDetailProcessor: jest.Mocked<DimensionHitDetailProcessor>;
  let mockRelatedDimensionHitDetailsProcessor: jest.Mocked<RelatedDimensionHitDetailProcessor>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockEsClient: jest.Mocked<Client>;

  beforeEach(async () => {
    // 创建 mock 对象
    mockConfigService = {
      esConfig: {
        riskChangeList: {
          nodes: ['http://localhost:9200'],
          indexName: 'test-risk-change-index',
        },
      },
    } as any;

    mockRiskChangeHelper = {
      hitMainInfoUpdateCapitalChange: jest.fn(),
    } as any;

    mockCompanySearchService = {
      searchCompany: jest.fn(),
    } as any;

    mockDimensionHitDetailProcessor = {
      fetchHits: jest.fn(),
      bindRiskChangeEsSearchFn: jest.fn(),
    } as any;

    mockRelatedDimensionHitDetailsProcessor = {
      processAnalyze: jest.fn(),
      fetchHits: jest.fn(),
      bindRiskChangeEsSearchFn: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      checkCaseTypeField: jest.fn(),
      getCaseTitleDescData: jest.fn(),
    } as any;

    mockEsClient = {
      search: jest.fn().mockResolvedValue({
        body: {
          hits: {
            total: { value: 0 },
            hits: [],
          },
        },
      }),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RiskChangeEsSource,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: DimensionHitDetailProcessor, useValue: mockDimensionHitDetailProcessor },
        { provide: RelatedDimensionHitDetailProcessor, useValue: mockRelatedDimensionHitDetailsProcessor },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
      ],
    }).compile();

    service = module.get<RiskChangeEsSource>(RiskChangeEsSource);

    // 手动设置 ES 客户端，因为它在构造函数中创建
    (service as any).esClient = mockEsClient;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('analyze 方法测试', () => {
    it('应该正确处理空的维度策略列表', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const dimensionHitStrategyPOs: DimensionHitStrategyPO[] = [];

      // Mock 关联方处理器返回空结果
      mockRelatedDimensionHitDetailsProcessor.processAnalyze.mockResolvedValue([]);

      // Act
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // Assert
      expect(result).toEqual([]);
      expect(mockRelatedDimensionHitDetailsProcessor.processAnalyze).toHaveBeenCalledWith([], companyId);
    });

    it('应该正确分离关联方和非关联方维度', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const relatedDimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      const normalDimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);

      const dimensionHitStrategyPOs = [relatedDimension, normalDimension];

      // Mock 关联方处理器返回结果
      const mockRelatedResult = [createTestDimensionResult(DimensionTypeEnums.ActualControllerRiskChange, 1)];
      mockRelatedDimensionHitDetailsProcessor.processAnalyze.mockResolvedValue(mockRelatedResult);

      // Mock 父类 analyze 方法的行为 (BaseEsAnalyzeService.analyze)
      jest
        .spyOn(Object.getPrototypeOf(Object.getPrototypeOf(service)), 'analyze')
        .mockResolvedValue([createTestDimensionResult(DimensionTypeEnums.RiskChange, 1)]);

      // Act
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // Assert
      expect(mockRelatedDimensionHitDetailsProcessor.processAnalyze).toHaveBeenCalledWith([relatedDimension], companyId);
      expect(result).toHaveLength(2);
      expect(result[0].dimensionKey).toBe(DimensionTypeEnums.ActualControllerRiskChange);
      expect(result[1].dimensionKey).toBe(DimensionTypeEnums.RiskChange);
    });

    it('应该只处理关联方维度当没有普通维度时', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const relatedDimension = createTestDimensionStrategy(DimensionTypeEnums.RecentInvestCancellationsRiskChange);

      const dimensionHitStrategyPOs = [relatedDimension];

      const mockRelatedResult = [createTestDimensionResult(DimensionTypeEnums.RecentInvestCancellationsRiskChange, 2)];
      mockRelatedDimensionHitDetailsProcessor.processAnalyze.mockResolvedValue(mockRelatedResult);

      // Act
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // Assert
      expect(mockRelatedDimensionHitDetailsProcessor.processAnalyze).toHaveBeenCalledWith([relatedDimension], companyId);
      expect(result).toEqual(mockRelatedResult);
    });
  });

  describe('getDimensionDetail 方法测试', () => {
    it('应该正确处理关联方维度详情查询', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ListedEntityRiskChange);

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      const mockResponse = new HitDetailsBaseResponse();
      mockResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 5,
      };
      mockResponse.Result = [{ id: 'test-result' }];

      mockRelatedDimensionHitDetailsProcessor.fetchHits.mockResolvedValue(mockResponse);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(mockRelatedDimensionHitDetailsProcessor.fetchHits).toHaveBeenCalledWith(dimension, params);
      expect(result).toEqual(mockResponse);
    });

    it('应该正确处理 RiskChange 维度详情查询', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      // Mock 父类方法返回的基础数据
      const mockBaseResponse = new HitDetailsBaseResponse();
      mockBaseResponse.Result = [
        { id: 'item1', Category: 1 },
        { id: 'item2', Category: 2 },
      ];
      mockBaseResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 2,
      };

      // Mock 父类的 getDimensionDetail 方法
      jest.spyOn(Object.getPrototypeOf(Object.getPrototypeOf(service)), 'getDimensionDetail').mockResolvedValue(mockBaseResponse);

      // Mock 详情处理器返回的过滤结果
      const mockFilteredHits = [{ id: 'item1', Category: 1 }];
      mockDimensionHitDetailProcessor.fetchHits.mockResolvedValue(mockFilteredHits);

      // Mock getCaseTitleDescData 方法
      mockCaseReasonHelper.getCaseTitleDescData.mockResolvedValue(undefined);

      // Mock getRiskListDesc 函数的返回值
      const mockRiskListDescResult = {
        Title: '',
        Subtitle: '',
        ChangeExtend: undefined,
        Content: '',
        ContentArray: [],
        Highlight: [],
        OtherHighlight: [],
        SubtitleHighlight: '',
        RelateChange: '',
        Category: 1,
        Id: undefined,
        Name: undefined,
        ObjectId: undefined,
        ChangeDate: null,
        CreateDate: null,
        UpdateDate: null,
        PublishTime: null,
      };

      // Mock getRiskListDesc 函数
      const getRiskListDescSpy = jest.fn().mockReturnValue(mockRiskListDescResult);
      jest.doMock('apps/data/risk.copy.from.c/risk', () => ({
        getRiskListDesc: getRiskListDescSpy,
      }));

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(mockDimensionHitDetailProcessor.fetchHits).toHaveBeenCalledWith(mockBaseResponse, dimension, params);
      expect(result.Result).toEqual([mockRiskListDescResult]);
      expect(result.Paging.TotalRecords).toBe(1); // 过滤后的数量
      expect(mockCaseReasonHelper.getCaseTitleDescData).toHaveBeenCalled();
    });

    it('应该正确处理 MainInfoUpdateCapitalChange 维度详情查询', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.MainInfoUpdateCapitalChange);

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      // Mock 父类方法返回的基础数据
      const mockBaseResponse = new HitDetailsBaseResponse();
      mockBaseResponse.Result = [
        { id: 'item1', Category: 37 },
        { id: 'item2', Category: 37 },
        { id: 'item3', Category: 37 },
      ];
      mockBaseResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 3,
      };

      // Mock 父类的 getDimensionDetail 方法
      jest.spyOn(Object.getPrototypeOf(Object.getPrototypeOf(service)), 'getDimensionDetail').mockResolvedValue(mockBaseResponse);

      // Mock helper 方法返回命中结果
      mockRiskChangeHelper.hitMainInfoUpdateCapitalChange.mockReturnValue(true);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      // 注意：hitMainInfoUpdateCapitalChange 方法的参数顺序是 (strategyField, results)
      const expectedStrategyField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodRegisCapital);
      expect(mockRiskChangeHelper.hitMainInfoUpdateCapitalChange).toHaveBeenCalledWith(expectedStrategyField, mockBaseResponse.Result);
      expect(result.Result).toHaveLength(1); // 因为只有第一个结果被添加到 hitData
      expect(result.Paging.TotalRecords).toBe(1);
    });

    it('应该正确处理其他维度详情查询', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.SecurityNotice);

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      const mockResponse = new HitDetailsBaseResponse();
      mockResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 1,
      };
      mockResponse.Result = [{ id: 'test-result', Category: 109 }];

      // Mock 父类的 getDimensionDetail 方法
      jest.spyOn(Object.getPrototypeOf(Object.getPrototypeOf(service)), 'getDimensionDetail').mockResolvedValue(mockResponse);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(result).toEqual(mockResponse);
    });
  });

  describe('analyze 方法的分支覆盖测试', () => {
    it('应该正确处理有命中但需要二次判断的维度', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);

      // Mock getStrategyFieldByKey 返回 hitCount 字段
      dimension.getStrategyFieldByKey = jest.fn().mockImplementation((key) => {
        if (key === DimensionFieldKeyEnums.hitCount) {
          return {
            fieldValue: [5],
            compareType: 'GreaterThan',
          };
        }
        return null;
      });

      const dimensionHitStrategyPOs = [dimension];

      // Mock 关联方处理器返回空结果
      mockRelatedDimensionHitDetailsProcessor.processAnalyze.mockResolvedValue([]);

      // Mock 父类 analyze 方法返回有命中的结果
      const mockDimHitRes = [createTestDimensionResult(DimensionTypeEnums.RiskChange, 10)];
      jest.spyOn(Object.getPrototypeOf(Object.getPrototypeOf(service)), 'analyze').mockResolvedValue(mockDimHitRes);

      // Mock getDimensionDetail 返回过滤后的结果
      const mockDetailResponse = new HitDetailsBaseResponse();
      mockDetailResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 8,
      };
      jest.spyOn(service, 'getDimensionDetail').mockResolvedValue(mockDetailResponse);

      // Act
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].totalHits).toBe(8); // 应该使用过滤后的数量
    });

    it('应该正确处理命中记录条数不满足条件的情况', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);

      // Mock getStrategyFieldByKey 返回 hitCount 字段，要求大于10
      dimension.getStrategyFieldByKey = jest.fn().mockImplementation((key) => {
        if (key === DimensionFieldKeyEnums.hitCount) {
          return {
            fieldValue: [10],
            compareType: 'GreaterThan',
          };
        }
        return null;
      });

      const dimensionHitStrategyPOs = [dimension];

      // Mock 关联方处理器返回空结果
      mockRelatedDimensionHitDetailsProcessor.processAnalyze.mockResolvedValue([]);

      // Mock 父类 analyze 方法返回有命中的结果
      const mockDimHitRes = [createTestDimensionResult(DimensionTypeEnums.RiskChange, 5)];
      jest.spyOn(Object.getPrototypeOf(Object.getPrototypeOf(service)), 'analyze').mockResolvedValue(mockDimHitRes);

      // Mock getDimensionDetail 返回命中数量不足的结果
      const mockDetailResponse = new HitDetailsBaseResponse();
      mockDetailResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 5, // 小于要求的10
      };
      jest.spyOn(service, 'getDimensionDetail').mockResolvedValue(mockDetailResponse);

      // Act
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // Assert
      expect(result).toHaveLength(0); // 不满足条件，应该返回空数组
    });

    it('应该正确处理不命中但需要提示的维度', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);

      // Mock getStrategyFieldByKey 返回 isShowTip 字段
      dimension.getStrategyFieldByKey = jest.fn().mockImplementation((key) => {
        if (key === DimensionFieldKeyEnums.isShowTip) {
          return {
            fieldValue: [1], // 需要提示
          };
        }
        return null;
      });

      const dimensionHitStrategyPOs = [dimension];

      // Mock 关联方处理器返回空结果
      mockRelatedDimensionHitDetailsProcessor.processAnalyze.mockResolvedValue([]);

      // Mock 父类 analyze 方法返回空结果（不命中）
      jest.spyOn(Object.getPrototypeOf(Object.getPrototypeOf(service)), 'analyze').mockResolvedValue([]);

      // Act
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].totalHits).toBe(0); // 命中数为0但有提示
    });
  });

  describe('getDimensionDetailItemData 方法测试', () => {
    it('应该正确处理 isScanRisk 参数', async () => {
      // Arrange
      const resp = new HitDetailsBaseResponse();
      resp.Result = [{ id: 'test' }];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const params = new HitDetailsBaseQueryParams();
      const analyzeParams = { isScanRisk: true };

      // Act
      const result = await (service as any).getDimensionDetailItemData(resp, dimension, params, analyzeParams);

      // Assert
      expect(result).toEqual(resp); // 应该直接返回原始响应
    });

    it('应该正确处理 SecurityNotice 维度数据', async () => {
      // Arrange
      const resp = new HitDetailsBaseResponse();
      resp.Result = [
        {
          KeyNo: 'test-keyno',
          Id: 'test-id',
          ChangeExtend: JSON.stringify({
            A: 'test-name',
            H: 'test-reason',
            D: 'test-unit',
            C: 'test-date',
            IsValid: 1,
            J: 'test-j-id',
          }),
        },
      ];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.SecurityNotice);
      const params = new HitDetailsBaseQueryParams();

      // Act
      const result = await (service as any).getDimensionDetailItemData(resp, dimension, params);

      // Assert
      expect(result.Result[0]).toEqual({
        name: 'test-name',
        reason: 'test-reason',
        publishUnit: 'test-unit',
        publishDate: 'test-date',
        isValid: 1,
        keyNo: 'test-keyno',
        riskId: 'test-id',
        id: 'test-j-id',
      });
    });

    it('应该正确处理 CapitalReduction 维度数据', async () => {
      // Arrange
      const resp = new HitDetailsBaseResponse();
      resp.Result = [
        {
          Id: 'test-id',
          Name: 'test-name',
          KeyNo: 'test-keyno',
          ChangeExtend: JSON.stringify({
            B: 'test-decide-date',
            D: 'test-content',
            A: 'test-notice-date',
            C: 'test-notice-period',
            F: 'test-notice-title',
          }),
        },
      ];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.CapitalReduction);
      const params = new HitDetailsBaseQueryParams();

      // Act
      const result = await (service as any).getDimensionDetailItemData(resp, dimension, params);

      // Assert
      expect(result.Result[0]).toEqual({
        Id: 'test-id',
        DecideDate: 'test-decide-date',
        Content: 'test-content',
        NoticeDate: 'test-notice-date',
        NoticePeriod: 'test-notice-period',
        NoticeTitle: 'test-notice-title',
        Name: 'test-name',
        KeyNo: 'test-keyno',
      });
    });

    it('应该正确处理 MainInfoUpdateHolder 维度数据', async () => {
      // Arrange
      const resp = new HitDetailsBaseResponse();
      resp.Result = [
        {
          GroupId: 'group-1',
          Id: 'test-id',
          KeyNo: 'test-keyno',
          Name: 'test-name',
          RiskLevel: 1,
          DataType: 1,
          Category: 24,
          BeforeContent: 'before',
          AfterContent: 'after',
          ChangeExtend: '{}',
          ObjectId: 'obj-1',
          ChangeStatus: 1,
          ChangeDate: 1234567890,
          CreateDate: 1234567890,
          DetailCount: 1,
          DisplayList: [],
          Extend1: 'ext1',
          Extend4: 'ext4',
          MaxLevel: 1,
          Extend3: 'ext3',
          Extend2: 'ext2',
          RKDetailCount: 1,
          IsImportant: 1,
          ImportantCount: 1,
          RelatedInfo: 'related',
          ExtraField: 'should-be-filtered', // 这个字段应该被过滤掉
        },
      ];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.MainInfoUpdateHolder);
      const params = new HitDetailsBaseQueryParams();

      // Act
      const result = await (service as any).getDimensionDetailItemData(resp, dimension, params);

      // Assert
      expect(result.Result[0]).not.toHaveProperty('ExtraField');
      expect(result.Result[0]).toHaveProperty('GroupId', 'group-1');
      expect(result.Result[0]).toHaveProperty('Id', 'test-id');
    });

    it('应该正确处理默认情况（返回 ChangeExtend 解析后的数据）', async () => {
      // Arrange
      const resp = new HitDetailsBaseResponse();
      resp.Result = [
        {
          ChangeExtend: JSON.stringify({
            field1: 'value1',
            field2: 'value2',
          }),
        },
      ];

      const dimension = createTestDimensionStrategy('UnknownDimension' as any);
      const params = new HitDetailsBaseQueryParams();

      // Act
      const result = await (service as any).getDimensionDetailItemData(resp, dimension, params);

      // Assert
      expect(result.Result[0]).toEqual({
        field1: 'value1',
        field2: 'value2',
      });
    });

    it('应该正确处理空的 Result 数组', async () => {
      // Arrange
      const resp = new HitDetailsBaseResponse();
      resp.Result = [];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const params = new HitDetailsBaseQueryParams();

      // Act
      const result = await (service as any).getDimensionDetailItemData(resp, dimension, params);

      // Assert
      expect(result.Result).toEqual([]);
    });
  });

  describe('getDimensionDetail 边界情况测试', () => {
    it('应该正确处理 MainInfoUpdateCapitalChange 维度无策略字段的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.MainInfoUpdateCapitalChange);

      // Mock getStrategyFieldByKey 返回 null
      dimension.getStrategyFieldByKey = jest.fn().mockReturnValue(null);

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      const mockBaseResponse = new HitDetailsBaseResponse();
      mockBaseResponse.Result = [{ id: 'item1', Category: 37 }];
      mockBaseResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 1,
      };

      // Mock 父类的 getDimensionDetail 方法
      jest.spyOn(Object.getPrototypeOf(Object.getPrototypeOf(service)), 'getDimensionDetail').mockResolvedValue(mockBaseResponse);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(result.Result).toHaveLength(0); // 没有策略字段，应该返回空结果
      expect(result.Paging.TotalRecords).toBe(0);
    });

    it('应该正确处理 MainInfoUpdateCapitalChange 维度命中失败的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.MainInfoUpdateCapitalChange);

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      const mockBaseResponse = new HitDetailsBaseResponse();
      mockBaseResponse.Result = [{ id: 'item1', Category: 37 }];
      mockBaseResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 1,
      };

      // Mock 父类的 getDimensionDetail 方法
      jest.spyOn(Object.getPrototypeOf(Object.getPrototypeOf(service)), 'getDimensionDetail').mockResolvedValue(mockBaseResponse);

      // Mock helper 方法返回未命中结果
      mockRiskChangeHelper.hitMainInfoUpdateCapitalChange.mockReturnValue(false);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(result.Result).toHaveLength(0); // 未命中，应该返回空结果
      expect(result.Paging.TotalRecords).toBe(0);
    });

    it('应该正确处理 MainInfoUpdateCapitalChange 维度无结果的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.MainInfoUpdateCapitalChange);

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      const mockBaseResponse = new HitDetailsBaseResponse();
      mockBaseResponse.Result = []; // 空结果
      mockBaseResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 0,
      };

      // Mock 父类的 getDimensionDetail 方法
      jest.spyOn(Object.getPrototypeOf(Object.getPrototypeOf(service)), 'getDimensionDetail').mockResolvedValue(mockBaseResponse);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(result.Result).toHaveLength(0);
      expect(result.Paging.TotalRecords).toBe(0);
    });

    it('应该正确处理 RiskChange 维度空结果的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      const mockBaseResponse = new HitDetailsBaseResponse();
      mockBaseResponse.Result = [];
      mockBaseResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 0,
      };

      // Mock 父类的 getDimensionDetail 方法
      jest.spyOn(Object.getPrototypeOf(Object.getPrototypeOf(service)), 'getDimensionDetail').mockResolvedValue(mockBaseResponse);

      // Mock 详情处理器返回空结果
      mockDimensionHitDetailProcessor.fetchHits.mockResolvedValue([]);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(result.Result).toHaveLength(0);
      expect(result.Paging.TotalRecords).toBe(0);
      // getCaseTitleDescData 不应该被调用，因为没有结果
      expect(mockCaseReasonHelper.getCaseTitleDescData).not.toHaveBeenCalled();
    });
  });

  describe('保护方法测试', () => {
    it('应该正确测试 getDetailFromEs 方法', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);

      // Mock getDimensionQuery 方法
      jest.spyOn(service as any, 'getDimensionQuery').mockResolvedValue({
        bool: {
          filter: [],
        },
      });

      // Mock getSortField 方法
      dimension.getSortField = jest.fn().mockReturnValue({
        field: 'CreateDate',
        order: 'DESC',
      });

      // Mock dimensionFilter
      dimension.dimensionFilter = {
        startTime: 1640995200000, // 2022-01-01
        endTime: 1672531199000, // 2022-12-31
        id: 'test-dynamic-id',
      };

      const params = new HitDetailsBaseQueryParams();
      params.pageIndex = 2;
      params.pageSize = 20;
      params.field = 'UpdateDate';
      params.order = 'ASC';

      // Mock ES 搜索响应
      const mockEsResponse = {
        body: {
          hits: {
            total: { value: 100 },
            hits: [{ _source: { Id: 'test1', Name: 'Test 1' } }, { _source: { Id: 'test2', Name: 'Test 2' } }],
          },
        },
      };

      (mockEsClient.search as jest.Mock).mockResolvedValue(mockEsResponse);

      // Act
      const result = await (service as any).getDetailFromEs(companyId, dimension, params);

      // Assert
      expect(result.total).toBe(100);
      expect(result.data).toHaveLength(2);
      expect(result.data[0]).toEqual({ Id: 'test1', Name: 'Test 1' });

      // 验证 ES 查询参数
      expect(mockEsClient.search).toHaveBeenCalledWith(
        expect.objectContaining({
          body: expect.objectContaining({
            from: 20, // (pageIndex - 1) * pageSize = (2 - 1) * 20
            size: 20,
            sort: { UpdateDate: 'ASC' },
            query: expect.objectContaining({
              bool: expect.objectContaining({
                filter: expect.arrayContaining([
                  { range: { Es_Version: { lt: 999999 } } },
                  { term: { KeyNo: companyId } },
                  { range: { CreateDate: { gte: 1640995200000, lte: 1672531199000 } } },
                  { term: { Id: 'test-dynamic-id' } },
                ]),
              }),
            }),
          }),
        }),
      );
    });

    it('应该正确处理 MainInfoUpdatePerson 维度数据转换', async () => {
      // Arrange
      const resp = new HitDetailsBaseResponse();
      resp.Result = [
        {
          GroupId: 'group-1',
          Id: 'test-id',
          KeyNo: 'test-keyno',
          Name: 'test-name',
          RiskLevel: 1,
          DataType: 1,
          Category: 25,
          BeforeContent: 'before',
          AfterContent: 'after',
          ChangeExtend: '{}',
          ObjectId: 'obj-1',
          ChangeStatus: 1,
          ChangeDate: 1234567890,
          CreateDate: 1234567890,
          DetailCount: 1,
          DisplayList: [],
          Extend1: 'ext1',
          Extend4: 'ext4',
          MaxLevel: 1,
          Extend3: 'ext3',
          Extend2: 'ext2',
          RKDetailCount: 1,
          IsImportant: 1,
          ImportantCount: 1,
          RelatedInfo: 'related',
          ExtraField: 'should-be-filtered',
        },
      ];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.MainInfoUpdatePerson);
      const params = new HitDetailsBaseQueryParams();

      // Act
      const result = await (service as any).getDimensionDetailItemData(resp, dimension, params);

      // Assert
      expect(result.Result[0]).not.toHaveProperty('ExtraField');
      expect(result.Result[0]).toHaveProperty('GroupId', 'group-1');
      expect(result.Result[0]).toHaveProperty('Category', 25);
    });

    it('应该正确处理 MainInfoUpdateBeneficiary 维度数据转换', async () => {
      // Arrange
      const resp = new HitDetailsBaseResponse();
      resp.Result = [
        {
          GroupId: 'group-1',
          Id: 'test-id',
          KeyNo: 'test-keyno',
          Name: 'test-name',
          RiskLevel: 1,
          DataType: 1,
          Category: 114,
          BeforeContent: 'before',
          AfterContent: 'after',
          ChangeExtend: '{}',
          ObjectId: 'obj-1',
          ChangeStatus: 1,
          ChangeDate: 1234567890,
          CreateDate: 1234567890,
          DetailCount: 1,
          DisplayList: [],
          Extend1: 'ext1',
          Extend4: 'ext4',
          MaxLevel: 1,
          Extend3: 'ext3',
          Extend2: 'ext2',
          RKDetailCount: 1,
          IsImportant: 1,
          ImportantCount: 1,
          RelatedInfo: 'related',
          ExtraField: 'should-be-filtered',
        },
      ];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.MainInfoUpdateBeneficiary);
      const params = new HitDetailsBaseQueryParams();

      // Act
      const result = await (service as any).getDimensionDetailItemData(resp, dimension, params);

      // Assert
      expect(result.Result[0]).not.toHaveProperty('ExtraField');
      expect(result.Result[0]).toHaveProperty('GroupId', 'group-1');
      expect(result.Result[0]).toHaveProperty('Category', 114);
    });

    it('应该正确处理无效的 ChangeExtend JSON', async () => {
      // Arrange
      const resp = new HitDetailsBaseResponse();
      resp.Result = [
        {
          ChangeExtend: 'invalid-json',
        },
      ];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.SecurityNotice);
      const params = new HitDetailsBaseQueryParams();

      // Act & Assert
      // 由于 JSON.parse 会抛出错误，我们期望这个方法抛出异常
      await expect((service as any).getDimensionDetailItemData(resp, dimension, params)).rejects.toThrow();
    });
  });
});
