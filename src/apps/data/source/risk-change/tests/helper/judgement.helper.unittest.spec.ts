import { Test, TestingModule } from '@nestjs/testing';
import { JudgementHelper } from '../../helper/judgement.helper';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
// import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user'; // 保留注释，等待手工修复

// Mock CourtRole constants
jest.mock('libs/constants/case.constants', () => ({
  CourtRole: [
    { label: '原告', value: '11' },
    { label: '上诉人', value: '13' },
    { label: '申请执行人', value: '12' },
    { label: '申请人', value: '14' },
    { label: '被告', value: '21' },
    { label: '被上诉人', value: '23' },
    { label: '被执行人', value: '22' },
    { label: '被申请人', value: '24' },
    { label: '第三人', value: '91' },
    { label: '其他', value: '99' },
  ],
}));

// Mock getCompareResultForArray utility
jest.mock('libs/utils/diligence/diligence.utils', () => ({
  getCompareResultForArray: jest.fn(),
}));

import { getCompareResultForArray } from 'libs/utils/diligence/diligence.utils';

describe('JudgementHelper 单元测试', () => {
  let helper: JudgementHelper;
  let mockGetCompareResultForArray: jest.MockedFunction<typeof getCompareResultForArray>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [JudgementHelper],
    }).compile();

    helper = module.get<JudgementHelper>(JudgementHelper);
    mockGetCompareResultForArray = getCompareResultForArray as jest.MockedFunction<typeof getCompareResultForArray>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('category7 方法测试 - 法院公告', () => {
    it('应该正确处理法院公告角色匹配', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11', '21']; // 原告、被告
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          J: [
            { KeyNo: 'test-keyno', RoleType: 11 }, // 原告
            { KeyNo: 'test-keyno', RoleType: 21 }, // 被告
            { KeyNo: 'other-keyno', RoleType: 12 }, // 其他公司的申请执行人
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['11', '21'], // 源值
        ['11', '21'], // 目标值
      );
    });

    it('应该正确处理法院公告角色不匹配', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['12', '22']; // 申请执行人、被执行人
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          J: [
            { KeyNo: 'test-keyno', RoleType: 11 }, // 原告
            { KeyNo: 'test-keyno', RoleType: 21 }, // 被告
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(false);

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['11', '21'], // 源值
        ['12', '22'], // 目标值
      );
    });

    it('应该正确过滤掉不匹配KeyNo的记录', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          J: [
            { KeyNo: 'other-keyno', RoleType: 11 }, // 不匹配的KeyNo
            { KeyNo: 'test-keyno', RoleType: 21 }, // 匹配的KeyNo但角色不匹配
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(false);

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['21'], // 只有匹配KeyNo的角色
        ['11'],
      );
    });

    it('应该正确处理无效的RoleType', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          J: [
            { KeyNo: 'test-keyno', RoleType: 999 }, // 无效的角色类型
            { KeyNo: 'test-keyno', RoleType: null }, // null值
            { KeyNo: 'test-keyno' }, // 缺少RoleType
          ],
        },
      };

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理空的J数组', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          J: [], // 空数组
        },
      };

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理缺少ChangeExtend的情况', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        // 缺少ChangeExtend
      };

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理重复的角色类型', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          J: [
            { KeyNo: 'test-keyno', RoleType: 11 }, // 原告
            { KeyNo: 'test-keyno', RoleType: 11 }, // 重复的原告
            { KeyNo: 'test-keyno', RoleType: 11 }, // 再次重复的原告
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['11'], // 去重后只有一个
        ['11'],
      );
    });

    it('应该正确处理空的fieldValue', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = []; // 空数组
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          J: [{ KeyNo: 'test-keyno', RoleType: 11 }],
        },
      };

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });
  });

  describe('category27 方法测试 - 送达公告', () => {
    it('应该正确处理送达公告角色匹配', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['21', '22']; // 被告、被执行人
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          D: [
            { N: 'test-keyno', RT: 21 }, // 被告
            { N: 'test-keyno', RT: 22 }, // 被执行人
            { N: 'other-keyno', RT: 11 }, // 其他公司的原告
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.category27(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['21', '22'], // 源值
        ['21', '22'], // 目标值
      );
    });

    it('应该正确处理送达公告角色不匹配', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11', '12']; // 原告、申请执行人
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          D: [
            { N: 'test-keyno', RT: 21 }, // 被告
            { N: 'test-keyno', RT: 22 }, // 被执行人
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(false);

      // Act
      const result = helper.category27(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['21', '22'], // 源值
        ['11', '12'], // 目标值
      );
    });

    it('应该正确过滤掉不匹配N字段的记录', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['21'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          D: [
            { N: 'other-keyno', RT: 21 }, // 不匹配的N字段
            { N: 'test-keyno', RT: 11 }, // 匹配的N字段但角色不匹配
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(false);

      // Act
      const result = helper.category27(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['11'], // 只有匹配N字段的角色
        ['21'],
      );
    });

    it('应该正确处理缺少D字段的情况', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['21'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          // 缺少D字段
        },
      };

      // Act
      const result = helper.category27(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });
  });

  describe('category90 方法测试 - 诉前调解', () => {
    it('应该正确处理诉前调解角色匹配', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11', '21']; // 原告、被告
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          I: [
            { KeyNo: 'test-keyno', RoleType: 11 }, // 原告
            { KeyNo: 'test-keyno', RoleType: 21 }, // 被告
            { KeyNo: 'other-keyno', RoleType: 12 }, // 其他公司的申请执行人
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.category90(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['11', '21'], // 源值
        ['11', '21'], // 目标值
      );
    });

    it('应该正确处理诉前调解角色不匹配', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['12', '22']; // 申请执行人、被执行人
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          I: [
            { KeyNo: 'test-keyno', RoleType: 11 }, // 原告
            { KeyNo: 'test-keyno', RoleType: 21 }, // 被告
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(false);

      // Act
      const result = helper.category90(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['11', '21'], // 源值
        ['12', '22'], // 目标值
      );
    });

    it('应该正确处理缺少I字段的情况', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          // 缺少I字段
        },
      };

      // Act
      const result = helper.category90(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });
  });

  describe('category18 方法测试 - 开庭公告', () => {
    it('应该正确处理开庭公告角色匹配', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['21', '22']; // 被告、被执行人
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          D: [
            { N: 'test-keyno', RT: 21 }, // 被告
            { N: 'test-keyno', RT: 22 }, // 被执行人
            { N: 'other-keyno', RT: 11 }, // 其他公司的原告
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.category18(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['21', '22'], // 源值
        ['21', '22'], // 目标值
      );
    });

    it('应该正确处理开庭公告角色不匹配', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11', '12']; // 原告、申请执行人
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          D: [
            { N: 'test-keyno', RT: 21 }, // 被告
            { N: 'test-keyno', RT: 22 }, // 被执行人
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(false);

      // Act
      const result = helper.category18(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['21', '22'], // 源值
        ['11', '12'], // 目标值
      );
    });
  });

  describe('category49 方法测试 - 立案信息', () => {
    it('应该正确处理立案信息角色匹配', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11', '21']; // 原告、被告
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          I: [
            { KeyNo: 'test-keyno', RoleType: 11 }, // 原告
            { KeyNo: 'test-keyno', RoleType: 21 }, // 被告
            { KeyNo: 'other-keyno', RoleType: 12 }, // 其他公司的申请执行人
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.category49(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['11', '21'], // 源值
        ['11', '21'], // 目标值
      );
    });

    it('应该正确处理立案信息角色不匹配', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['12', '22']; // 申请执行人、被执行人
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          I: [
            { KeyNo: 'test-keyno', RoleType: 11 }, // 原告
            { KeyNo: 'test-keyno', RoleType: 21 }, // 被告
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(false);

      // Act
      const result = helper.category49(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['11', '21'], // 源值
        ['12', '22'], // 目标值
      );
    });

    it('应该正确处理缺少I字段的情况', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          // 缺少I字段
        },
      };

      // Act
      const result = helper.category49(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });
  });

  describe('category4 方法测试 - 裁判文书', () => {
    it('应该正确处理裁判文书角色匹配（通过KeyNo）', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['12', '22']; // 申请执行人、被执行人
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          K: [
            { KeyNo: 'test-keyno', RT: 12 }, // 申请执行人
            { KeyNo: 'test-keyno', RT: 22 }, // 被执行人
            { KeyNo: 'other-keyno', RT: 11 }, // 其他公司的原告
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.category4(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['12', '22'], // 源值
        ['12', '22'], // 目标值
      );
    });

    it('应该正确处理裁判文书角色匹配（通过SupNameAndKeyNo.KeyNo）', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['21']; // 被告
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          K: [
            { SupNameAndKeyNo: { KeyNo: 'test-keyno' }, RT: 21 }, // 被告
            { SupNameAndKeyNo: { KeyNo: 'other-keyno' }, RT: 21 }, // 其他公司的被告
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.category4(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['21'], // 源值
        ['21'], // 目标值
      );
    });

    it('应该正确处理裁判文书角色不匹配', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11', '12']; // 原告、申请执行人
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          K: [
            { KeyNo: 'test-keyno', RT: 21 }, // 被告
            { KeyNo: 'test-keyno', RT: 22 }, // 被执行人
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(false);

      // Act
      const result = helper.category4(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['21', '22'], // 源值
        ['11', '12'], // 目标值
      );
    });

    it('应该正确过滤掉不匹配KeyNo的记录', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['21'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          K: [
            { KeyNo: 'other-keyno', RT: 21 }, // 不匹配的KeyNo
            { SupNameAndKeyNo: { KeyNo: 'other-keyno' }, RT: 21 }, // 不匹配的SupNameAndKeyNo.KeyNo
            { KeyNo: 'test-keyno', RT: 11 }, // 匹配的KeyNo但角色不匹配
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(false);

      // Act
      const result = helper.category4(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['11'], // 只有匹配KeyNo的角色
        ['21'],
      );
    });

    it('应该正确处理缺少K字段的情况', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['21'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          // 缺少K字段
        },
      };

      // Act
      const result = helper.category4(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理复杂的数据结构', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['12', '22'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          K: [
            {
              KeyNo: 'test-keyno',
              R: 0,
              RT: 12,
              T: '',
              Org: 0,
              RN: '申请执行人',
              ShowName: '宁波银行股份有限公司杭州城西支行',
              Name: '宁波银行股份有限公司杭州城西支行',
            },
            {
              KeyNo: 'test-keyno',
              R: 1,
              RT: 22,
              T: '',
              Org: 0,
              RN: '被执行人',
              ShowName: '飞耀控股集团有限公司',
              Name: '飞耀控股集团有限公司',
            },
            {
              KeyNo: '',
              R: 1,
              RT: 22,
              T: '',
              Org: -2,
              RN: '被执行人',
              ShowName: '许*',
              Name: '许婷',
            },
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.category4(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['12', '22'], // 只有匹配KeyNo的角色
        ['12', '22'],
      );
    });
  });

  describe('边界情况和综合测试', () => {
    it('应该正确处理 fieldValue 为 null 的情况', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = null; // null值
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          J: [{ KeyNo: 'test-keyno', RoleType: 11 }],
        },
      };

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理 fieldValue 为空数组的情况', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = []; // 空数组
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          J: [{ KeyNo: 'test-keyno', RoleType: 11 }],
        },
      };

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理 item 为空的情况', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {}; // 空对象

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理 item 为 null 的情况', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = null; // null值

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理所有角色类型都无效的情况', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          J: [
            { KeyNo: 'test-keyno', RoleType: 999 }, // 无效角色类型
            { KeyNo: 'test-keyno', RoleType: 888 }, // 无效角色类型
            { KeyNo: 'test-keyno', RoleType: 'invalid' }, // 无效角色类型
          ],
        },
      };

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理混合有效和无效角色类型的情况', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11', '21'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          J: [
            { KeyNo: 'test-keyno', RoleType: 11 }, // 有效角色类型
            { KeyNo: 'test-keyno', RoleType: 999 }, // 无效角色类型
            { KeyNo: 'test-keyno', RoleType: 21 }, // 有效角色类型
            { KeyNo: 'test-keyno', RoleType: null }, // null值
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.category7(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['11', '21'], // 只有有效的角色类型
        ['11', '21'],
      );
    });

    it('应该正确处理不同比较类型的情况', () => {
      // Arrange
      const testCases = [
        {
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
          mockResult: true,
          expected: true,
        },
        {
          compareType: DimensionFieldCompareTypeEnums.ContainsAll,
          mockResult: false,
          expected: false,
        },
        {
          compareType: DimensionFieldCompareTypeEnums.Equal,
          mockResult: true,
          expected: true,
        },
      ];

      testCases.forEach(({ compareType, mockResult, expected }) => {
        const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
        judicialRoleTypeField.fieldValue = ['11'];
        judicialRoleTypeField.compareType = compareType;

        const item = {
          KeyNo: 'test-keyno',
          ChangeExtend: {
            J: [{ KeyNo: 'test-keyno', RoleType: 11 }],
          },
        };

        mockGetCompareResultForArray.mockReturnValue(mockResult);

        // Act
        const result = helper.category7(judicialRoleTypeField, item);

        // Assert
        expect(result).toBe(expected);
        expect(mockGetCompareResultForArray).toHaveBeenCalledWith(compareType, ['11'], ['11']);

        // Reset mock for next iteration
        mockGetCompareResultForArray.mockClear();
      });
    });

    it('应该正确处理所有方法的一致性', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['11', '21'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const testCases = [
        {
          method: 'category7',
          item: {
            KeyNo: 'test-keyno',
            ChangeExtend: {
              J: [
                { KeyNo: 'test-keyno', RoleType: 11 },
                { KeyNo: 'test-keyno', RoleType: 21 },
              ],
            },
          },
        },
        {
          method: 'category27',
          item: {
            KeyNo: 'test-keyno',
            ChangeExtend: {
              D: [
                { N: 'test-keyno', RT: 11 },
                { N: 'test-keyno', RT: 21 },
              ],
            },
          },
        },
        {
          method: 'category90',
          item: {
            KeyNo: 'test-keyno',
            ChangeExtend: {
              I: [
                { KeyNo: 'test-keyno', RoleType: 11 },
                { KeyNo: 'test-keyno', RoleType: 21 },
              ],
            },
          },
        },
        {
          method: 'category18',
          item: {
            KeyNo: 'test-keyno',
            ChangeExtend: {
              D: [
                { N: 'test-keyno', RT: 11 },
                { N: 'test-keyno', RT: 21 },
              ],
            },
          },
        },
        {
          method: 'category49',
          item: {
            KeyNo: 'test-keyno',
            ChangeExtend: {
              I: [
                { KeyNo: 'test-keyno', RoleType: 11 },
                { KeyNo: 'test-keyno', RoleType: 21 },
              ],
            },
          },
        },
        {
          method: 'category4',
          item: {
            KeyNo: 'test-keyno',
            ChangeExtend: {
              K: [
                { KeyNo: 'test-keyno', RT: 11 },
                { KeyNo: 'test-keyno', RT: 21 },
              ],
            },
          },
        },
      ];

      mockGetCompareResultForArray.mockReturnValue(true);

      testCases.forEach(({ method, item }) => {
        // Act
        const result = helper[method](judicialRoleTypeField, item);

        // Assert
        expect(result).toBe(true);
        expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, ['11', '21'], ['11', '21']);

        // Reset mock for next iteration
        mockGetCompareResultForArray.mockClear();
      });
    });

    it('应该正确处理复杂的嵌套数据结构', () => {
      // Arrange
      const judicialRoleTypeField = new DimensionHitStrategyFieldsEntity();
      judicialRoleTypeField.fieldValue = ['12', '22'];
      judicialRoleTypeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          K: [
            {
              KeyNo: 'test-keyno',
              RT: 12,
              SupNameAndKeyNo: { KeyNo: 'test-keyno', Name: '测试公司' },
              R: 0,
              T: '',
              Org: 0,
              RN: '申请执行人',
              ShowName: '测试公司',
              Name: '测试公司',
            },
            {
              SupNameAndKeyNo: { KeyNo: 'test-keyno', Name: '测试公司2' },
              RT: 22,
              R: 1,
              T: '',
              Org: 0,
              RN: '被执行人',
              ShowName: '测试公司2',
              Name: '测试公司2',
            },
            {
              KeyNo: 'other-keyno',
              RT: 11,
              R: 0,
              T: '',
              Org: 0,
              RN: '原告',
              ShowName: '其他公司',
              Name: '其他公司',
            },
          ],
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.category4(judicialRoleTypeField, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(
        DimensionFieldCompareTypeEnums.ContainsAny,
        ['12', '22'], // 只有匹配KeyNo或SupNameAndKeyNo.KeyNo的角色
        ['12', '22'],
      );
    });
  });
});
