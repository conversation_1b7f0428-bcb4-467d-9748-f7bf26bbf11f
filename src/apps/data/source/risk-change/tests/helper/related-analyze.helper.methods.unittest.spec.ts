import { Test, TestingModule } from '@nestjs/testing';
import { RelatedAnalyzeHelper } from '../../helper/related-analyze.helper';
import { PersonHelper } from '../../../../helper/person.helper';
import { CompanySearchService } from '../../../../../company/company-search.service';
import { BankLitigationHelper } from '../../helper/bank-litigation.helper';
import { CaseReasonHelper } from '../../helper/case-reason.helper';
import { CompanyChangeHelper } from '../../helper/company-change.helper';
import { JudgementHelper } from '../../helper/judgement.helper';
import { MainEmployeeHelper } from '../../helper/main-employee.helper';
import { PenaltyHelper } from '../../helper/penalty.helper';
import { CompanyStockHelper } from '../../helper/company-stock.helper';
import { CompanyFinaceHelper } from '../../helper/company-finace.helper';
import { BaseHelper } from '../../helper/base.helper';
import { DimensionHitStrategyPO } from '../../../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionFieldKeyEnums } from '../../../../../../libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyFieldsEntity } from '../../../../../../libs/entities/DimensionHitStrategyFieldsEntity';

describe('RelatedAnalyzeHelper - Helper Methods', () => {
  let helper: RelatedAnalyzeHelper;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockBankLitigationHelper: jest.Mocked<BankLitigationHelper>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockCompanyChangeHelper: jest.Mocked<CompanyChangeHelper>;
  let mockJudgementHelper: jest.Mocked<JudgementHelper>;
  let mockMainEmployeeHelper: jest.Mocked<MainEmployeeHelper>;
  let mockPenaltyHelper: jest.Mocked<PenaltyHelper>;
  let mockCompanyStockHelper: jest.Mocked<CompanyStockHelper>;
  let mockCompanyFinanceHelper: jest.Mocked<CompanyFinaceHelper>;
  let mockBaseHelper: jest.Mocked<BaseHelper>;

  beforeEach(async () => {
    // Create mocks for all dependencies
    mockPersonHelper = {} as any;
    mockCompanySearchService = {
      companyDetailsQcc: jest.fn(),
    } as any;
    mockBankLitigationHelper = {} as any;
    mockCaseReasonHelper = {} as any;
    mockCompanyChangeHelper = {} as any;
    mockJudgementHelper = {} as any;
    mockMainEmployeeHelper = {} as any;
    mockPenaltyHelper = {} as any;
    mockCompanyStockHelper = {} as any;
    mockCompanyFinanceHelper = {} as any;
    mockBaseHelper = {} as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RelatedAnalyzeHelper,
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: BankLitigationHelper, useValue: mockBankLitigationHelper },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
        { provide: CompanyChangeHelper, useValue: mockCompanyChangeHelper },
        { provide: JudgementHelper, useValue: mockJudgementHelper },
        { provide: MainEmployeeHelper, useValue: mockMainEmployeeHelper },
        { provide: PenaltyHelper, useValue: mockPenaltyHelper },
        { provide: CompanyStockHelper, useValue: mockCompanyStockHelper },
        { provide: CompanyFinaceHelper, useValue: mockCompanyFinanceHelper },
        { provide: BaseHelper, useValue: mockBaseHelper },
      ],
    }).compile();

    helper = module.get<RelatedAnalyzeHelper>(RelatedAnalyzeHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getDimesionTopics', () => {
    let mockDimension: jest.Mocked<DimensionHitStrategyPO>;

    beforeEach(() => {
      mockDimension = {
        getStrategyFieldByKey: jest.fn(),
      } as any;
    });

    it('should return empty array when no topics field exists', () => {
      mockDimension.getStrategyFieldByKey.mockReturnValue(null);

      const result = helper['getDimesionTopics'](mockDimension);

      expect(result).toEqual([]);
      expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.topics);
    });

    it('should return empty array when topics field has no values', () => {
      const mockTopicsField = {
        fieldValue: [],
      } as DimensionHitStrategyFieldsEntity;

      mockDimension.getStrategyFieldByKey.mockReturnValue(mockTopicsField);

      const result = helper['getDimesionTopics'](mockDimension);

      expect(result).toEqual([]);
    });

    it('should return all topic values except "all" when "all" is included', () => {
      const mockTopicsField = {
        fieldValue: ['all', 'topic1'],
      } as DimensionHitStrategyFieldsEntity;

      mockDimension.getStrategyFieldByKey.mockReturnValue(mockTopicsField);

      // Since NegativePositiveTopicTypes is read-only, we'll test the logic differently
      // We'll test that when 'all' is included, it processes the fieldValue correctly
      const result = helper['getDimesionTopics'](mockDimension);

      // The method should return an array when 'all' is included
      expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.topics);
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });

    it('should return specific topic values when "all" is not included', () => {
      const mockTopicsField = {
        fieldValue: ['topic1', 'topic2'],
      } as DimensionHitStrategyFieldsEntity;

      mockDimension.getStrategyFieldByKey.mockReturnValue(mockTopicsField);

      const result = helper['getDimesionTopics'](mockDimension);

      expect(result).toEqual(['topic1', 'topic2']);
    });
  });

  describe('negativePositiveNewsField', () => {
    it('should return false when topics array is empty', () => {
      const topics: string[] = [];
      const newItem = {
        ChangeExtend: {
          newTags: ['tag1', 'tag2'],
        },
      };

      const result = helper.negativePositiveNewsField(topics, newItem);

      expect(result).toBe(false);
    });

    it('should return false when newTags is undefined', () => {
      const topics = ['tag1', 'tag2'];
      const newItem = {
        ChangeExtend: {},
      };

      const result = helper.negativePositiveNewsField(topics, newItem);

      expect(result).toBe(false);
    });

    it('should return false when newTags is null', () => {
      const topics = ['tag1', 'tag2'];
      const newItem = {
        ChangeExtend: {
          newTags: null,
        },
      };

      const result = helper.negativePositiveNewsField(topics, newItem);

      expect(result).toBe(false);
    });

    it('should return true when all newTags are included in topics', () => {
      const topics = ['tag1', 'tag2', 'tag3'];
      const newItem = {
        ChangeExtend: {
          newTags: ['tag1', 'tag2'],
        },
      };

      const result = helper.negativePositiveNewsField(topics, newItem);

      expect(result).toBe(true);
    });

    it('should return false when some newTags are not included in topics', () => {
      const topics = ['tag1', 'tag2'];
      const newItem = {
        ChangeExtend: {
          newTags: ['tag1', 'tag3'],
        },
      };

      const result = helper.negativePositiveNewsField(topics, newItem);

      expect(result).toBe(false);
    });

    it('should return false when no newTags are included in topics', () => {
      const topics = ['tag1', 'tag2'];
      const newItem = {
        ChangeExtend: {
          newTags: ['tag3', 'tag4'],
        },
      };

      const result = helper.negativePositiveNewsField(topics, newItem);

      expect(result).toBe(false);
    });

    it('should handle empty newTags array', () => {
      const topics = ['tag1', 'tag2'];
      const newItem = {
        ChangeExtend: {
          newTags: [],
        },
      };

      const result = helper.negativePositiveNewsField(topics, newItem);

      expect(result).toBe(true); // empty array should return true for every()
    });

    it('should handle missing ChangeExtend', () => {
      const topics = ['tag1', 'tag2'];
      const newItem = {};

      const result = helper.negativePositiveNewsField(topics, newItem);

      expect(result).toBe(false);
    });

    it('should handle edge case with topicSourceValue being truthy but not array', () => {
      const topics = ['tag1', 'tag2'];
      const newItem = {
        ChangeExtend: {
          newTags: 'not-an-array',
        },
      };

      // This should throw an error since the method expects an array
      expect(() => {
        helper.negativePositiveNewsField(topics, newItem);
      }).toThrow('topicSourceValue.every is not a function');
    });
  });

  describe('hitCompanyDetail', () => {
    let mockTypeField: DimensionHitStrategyFieldsEntity;
    let mockItem: any;

    beforeEach(() => {
      mockItem = {
        ChangeExtend: {
          A: 'Test Company Name',
          K: 'test-company-key',
        },
      };
    });

    describe('excludeCompanyName field', () => {
      beforeEach(() => {
        mockTypeField = {
          dimensionFieldKey: DimensionFieldKeyEnums.excludeCompanyName,
          fieldValue: ['Exclude', 'BadCompany'],
        } as DimensionHitStrategyFieldsEntity;
      });

      it('should return false when company name contains excluded keyword', async () => {
        mockItem.ChangeExtend.A = 'Test Exclude Company';

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(false);
      });

      it('should return true when company name does not contain excluded keywords', async () => {
        mockItem.ChangeExtend.A = 'Good Company Name';

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(true);
      });

      it('should return false when company name is missing', async () => {
        mockItem.ChangeExtend.A = null;

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(false);
      });

      it('should return false when fieldValue is missing', async () => {
        mockTypeField.fieldValue = null;

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(false);
      });
    });

    describe('companyName field', () => {
      beforeEach(() => {
        mockTypeField = {
          dimensionFieldKey: DimensionFieldKeyEnums.companyName,
          fieldValue: ['Target', 'Specific'],
        } as DimensionHitStrategyFieldsEntity;
      });

      it('should return true when company name contains target keyword', async () => {
        mockItem.ChangeExtend.A = 'Target Company Ltd';

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(true);
      });

      it('should return false when company name does not contain target keywords', async () => {
        mockItem.ChangeExtend.A = 'Other Company Name';

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(false);
      });

      it('should return false when company name is missing', async () => {
        mockItem.ChangeExtend.A = null;

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(false);
      });
    });

    describe('companySocpe field', () => {
      beforeEach(() => {
        mockTypeField = {
          dimensionFieldKey: DimensionFieldKeyEnums.companySocpe,
          fieldValue: ['Technology', 'Software'],
        } as DimensionHitStrategyFieldsEntity;
      });

      it('should return true when company scope contains target keyword', async () => {
        mockCompanySearchService.companyDetailsQcc.mockResolvedValue({
          Scope: 'Technology and Software Development',
          IndustryV3: {},
        } as any);

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(true);
        expect(mockCompanySearchService.companyDetailsQcc).toHaveBeenCalledWith('test-company-key', ['Scope', 'IndustryV3']);
      });

      it('should return false when company scope does not contain target keywords', async () => {
        mockCompanySearchService.companyDetailsQcc.mockResolvedValue({
          Scope: 'Manufacturing and Production',
          IndustryV3: {},
        } as any);

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(false);
      });

      it('should return false when company key is missing', async () => {
        mockItem.ChangeExtend.K = null;

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(false);
        expect(mockCompanySearchService.companyDetailsQcc).not.toHaveBeenCalled();
      });
    });

    describe('qccIndustry field', () => {
      beforeEach(() => {
        mockTypeField = {
          dimensionFieldKey: DimensionFieldKeyEnums.qccIndustry,
          fieldValue: ['32-3203-320302', '32-3203', '32'],
        } as DimensionHitStrategyFieldsEntity;
      });

      it('should return true for three-level industry match', async () => {
        mockCompanySearchService.companyDetailsQcc.mockResolvedValue({
          Scope: '',
          QccIndustry: {
            Ac: '32',
            An: '公用事业',
            Bc: '3203',
            Bn: '水务业',
            Cc: '320302',
            Cn: '污水处理及再生利用',
          },
        } as any);

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(true);
      });

      it('should return true for two-level industry match', async () => {
        mockCompanySearchService.companyDetailsQcc.mockResolvedValue({
          Scope: '',
          QccIndustry: {
            Ac: '32',
            An: '公用事业',
            Bc: '3203',
            Bn: '水务业',
            Cc: '320302',
            Cn: '污水处理及再生利用',
          },
        } as any);

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(true);
      });

      it('should return true for one-level industry match', async () => {
        mockCompanySearchService.companyDetailsQcc.mockResolvedValue({
          Scope: '',
          QccIndustry: {
            Ac: '32',
            An: '公用事业',
            Bc: '3203',
            Bn: '水务业',
            Cc: '320302',
            Cn: '污水处理及再生利用',
          },
        } as any);

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(true);
      });

      it('should return false when industry does not match', async () => {
        mockCompanySearchService.companyDetailsQcc.mockResolvedValue({
          Scope: '',
          QccIndustry: {
            Ac: '33',
            An: '其他行业',
            Bc: '3301',
            Bn: '其他',
            Cc: '330101',
            Cn: '其他',
          },
        } as any);

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(false);
      });

      it('should return false when QccIndustry is null', async () => {
        mockCompanySearchService.companyDetailsQcc.mockResolvedValue({
          Scope: '',
          QccIndustry: null,
        } as any);

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(false);
      });
    });

    describe('companyIndustry field', () => {
      beforeEach(() => {
        mockTypeField = {
          dimensionFieldKey: DimensionFieldKeyEnums.companyIndustry,
          fieldValue: ['K-70-701', 'K-70', 'K'],
        } as DimensionHitStrategyFieldsEntity;
      });

      it('should return true for three-level industry match', async () => {
        mockCompanySearchService.companyDetailsQcc.mockResolvedValue({
          Scope: '',
          IndustryV3: {
            IndustryCode: 'K',
            Industry: '房地产业',
            SubIndustryCode: '70',
            SubIndustry: '房地产业',
            MiddleCategoryCode: '701',
            MiddleCategory: '房地产开发经营',
            SmallCategoryCode: '7010',
            SmallCategory: '房地产开发经营',
          },
        } as any);

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(true);
      });

      it('should return true for two-level industry match', async () => {
        mockCompanySearchService.companyDetailsQcc.mockResolvedValue({
          Scope: '',
          IndustryV3: {
            IndustryCode: 'K',
            Industry: '房地产业',
            SubIndustryCode: '70',
            SubIndustry: '房地产业',
            MiddleCategoryCode: '701',
            MiddleCategory: '房地产开发经营',
          },
        } as any);

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(true);
      });

      it('should return true for one-level industry match', async () => {
        mockCompanySearchService.companyDetailsQcc.mockResolvedValue({
          Scope: '',
          IndustryV3: {
            IndustryCode: 'K',
            Industry: '房地产业',
            SubIndustryCode: '70',
            SubIndustry: '房地产业',
            MiddleCategoryCode: '701',
            MiddleCategory: '房地产开发经营',
          },
        } as any);

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(true);
      });

      it('should return false when industry does not match', async () => {
        mockCompanySearchService.companyDetailsQcc.mockResolvedValue({
          Scope: '',
          IndustryV3: {
            IndustryCode: 'M',
            Industry: '科学研究和技术服务业',
            SubIndustryCode: '71',
            SubIndustry: '专业技术服务业',
            MiddleCategoryCode: '711',
            MiddleCategory: '法律服务',
          },
        } as any);

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(false);
      });
    });

    describe('default case', () => {
      it('should return false for unknown dimension field key', async () => {
        mockTypeField = {
          dimensionFieldKey: 'unknown-field' as any,
          fieldValue: ['test'],
        } as DimensionHitStrategyFieldsEntity;

        const result = await helper['hitCompanyDetail'](mockTypeField, mockItem);

        expect(result).toBe(false);
      });
    });
  });
});
