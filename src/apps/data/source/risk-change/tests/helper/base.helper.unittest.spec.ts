import { Test, TestingModule } from '@nestjs/testing';
import { BaseHelper } from '../../helper/base.helper';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { PersonHelper } from '../../../../helper/person.helper';
// import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user'; // 保留注释，等待手工修复

describe('BaseHelper 单元测试', () => {
  let helper: BaseHelper;
  let mockPersonHelper: jest.Mocked<PersonHelper>;

  beforeEach(async () => {
    mockPersonHelper = {
      getPersonData: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [BaseHelper, { provide: PersonHelper, useValue: mockPersonHelper }],
    }).compile();

    helper = module.get<BaseHelper>(BaseHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('restricterTypeField 方法测试', () => {
    it('应该正确识别企业本身的限高', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 企业本身
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'company123', // 添加KeyNo字段
        ChangeExtend: {
          C: JSON.stringify([
            {
              KeyNo: 'company123',
              Name: '测试公司',
              Org: 0,
            },
          ]),
          E: JSON.stringify([]), // 空的法人代表限高
        },
      };

      // Act
      const result = helper.restricterTypeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别法人代表的限高', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [2]; // 法人代表
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'person123', // 添加KeyNo字段
        ChangeExtend: {
          C: JSON.stringify([]), // 空的企业限高
          E: JSON.stringify([
            {
              KeyNo: 'person123',
              Name: '张三',
              Org: 0,
            },
          ]),
        },
      };

      // Act
      const result = helper.restricterTypeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理同时包含企业和法人限高的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1, 2]; // 企业本身和法人代表
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'company123', // 添加KeyNo字段，匹配企业限高
        ChangeExtend: {
          C: JSON.stringify([
            {
              KeyNo: 'company123',
              Name: '测试公司',
              Org: 0,
            },
          ]),
          E: JSON.stringify([
            {
              KeyNo: 'person123',
              Name: '张三',
              Org: 0,
            },
          ]),
        },
      };

      // Act
      const result = helper.restricterTypeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理无限高记录的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          C: JSON.stringify([]),
          E: JSON.stringify([]),
        },
      };

      // Act
      const result = helper.restricterTypeField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理 JSON 解析错误', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        KeyNo: 'test123',
        ChangeExtend: {
          C: 'invalid-json',
          E: 'invalid-json',
        },
      };

      // Act & Assert
      // 由于源代码中没有对JSON解析错误进行处理，会直接抛出异常
      expect(() => helper.restricterTypeField(field, item)).toThrow();
    });
  });

  describe('amountField 方法测试', () => {
    it('应该正确处理金额在范围内的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [[100, 500]]; // 100万到500万
      field.compareType = DimensionFieldCompareTypeEnums.Between;

      const rawValue = 3000000; // 300万（单位：元）

      // Act
      const result = helper.amountField(field, rawValue);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理金额超出范围的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [[100, 500]]; // 100万到500万
      field.compareType = DimensionFieldCompareTypeEnums.Between;

      const rawValue = 6000000; // 600万（单位：元）

      // Act
      const result = helper.amountField(field, rawValue);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理只有最小值的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [[100, undefined]]; // 大于等于100万
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const rawValue = 1500000; // 150万（单位：元）

      // Act
      const result = helper.amountField(field, rawValue);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理只有最大值的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [[undefined, 200]]; // 小于200万
      field.compareType = DimensionFieldCompareTypeEnums.LessThan;

      const rawValue = 1500000; // 150万（单位：元）

      // Act
      const result = helper.amountField(field, rawValue);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理空值的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [[100, 500]];
      field.compareType = DimensionFieldCompareTypeEnums.Between;

      const rawValue = null;

      // Act
      const result = helper.amountField(field, rawValue);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理自定义转换率', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [[1, 5]]; // 1到5（单位：千元）
      field.compareType = DimensionFieldCompareTypeEnums.Between;

      const rawValue = 3000; // 3000元
      const conversionRate = 1000; // 转换率：千元

      // Act
      const result = helper.amountField(field, rawValue, conversionRate);

      // Assert
      expect(result).toBe(true);
    });
  });

  describe('category28Field 方法测试', () => {
    it('应该正确过滤获得融资的融资动态', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          C: 'A轮', // 获得融资的轮次
        },
      };

      // Act
      const result = helper.category28Field(field, item);

      // Assert
      expect(result).toBe(false); // 应该被过滤掉
    });

    it('应该正确通过非获得融资的融资动态', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          C: '退市', // 非获得融资的类型
        },
      };

      // Act
      const result = helper.category28Field(field, item);

      // Assert
      expect(result).toBe(true); // 应该通过
    });
  });

  describe('filterLastYearData 方法测试', () => {
    it('应该正确过滤上一年的数据', () => {
      // Arrange
      const currentYear = new Date().getFullYear();
      const lastYearTimestamp = new Date(currentYear - 1, 6, 1).getTime() / 1000; // 去年7月1日

      const item = {
        ChangeExtend: {
          D: lastYearTimestamp,
        },
      };

      // Act
      const result = helper.filterLastYearData(item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确过滤更早年份的数据', () => {
      // Arrange
      const currentYear = new Date().getFullYear();
      const olderTimestamp = new Date(currentYear - 2, 6, 1).getTime() / 1000; // 前年7月1日

      const item = {
        ChangeExtend: {
          D: olderTimestamp,
        },
      };

      // Act
      const result = helper.filterLastYearData(item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空日期的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          D: null,
        },
      };

      // Act
      const result = helper.filterLastYearData(item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少日期字段的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {},
      };

      // Act
      const result = helper.filterLastYearData(item);

      // Assert
      expect(result).toBe(false);
    });
  });
});
