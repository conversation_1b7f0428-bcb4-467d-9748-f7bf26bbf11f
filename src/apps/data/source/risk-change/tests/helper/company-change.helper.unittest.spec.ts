import { Test, TestingModule } from '@nestjs/testing';
import { CompanyChangeHelper } from '../../helper/company-change.helper';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { CompanySearchService } from 'apps/company/company-search.service';
// import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user'; // 保留注释，等待手工修复

describe('CompanyChangeHelper 单元测试', () => {
  let helper: CompanyChangeHelper;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;

  beforeEach(async () => {
    mockCompanySearchService = {
      companyDetailsQcc: jest.fn(),
      companyDetailsKys: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [CompanyChangeHelper, { provide: CompanySearchService, useValue: mockCompanySearchService }],
    }).compile();

    helper = module.get<CompanyChangeHelper>(CompanyChangeHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('hitLayTypesField 方法测试', () => {
    it('应该正确命中法人代表变更', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 法人变更类型
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          C: 1, // 法人变更代码
        },
      };

      // Act
      const result = helper.hitLayTypesField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的法人变更类型', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 期望的法人变更类型
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          C: 2, // 实际的法人变更代码不匹配
        },
      };

      // Act
      const result = helper.hitLayTypesField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的fieldValue', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = []; // 空的fieldValue
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          C: 1,
        },
      };

      // Act
      const result = helper.hitLayTypesField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少ChangeExtend的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {}; // 缺少ChangeExtend

      // Act
      const result = helper.hitLayTypesField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少C字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {}, // 缺少C字段
      };

      // Act
      const result = helper.hitLayTypesField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('hitLayTypesField72 方法测试', () => {
    it('应该正确处理法人变更72特殊情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: JSON.stringify({
          OperInfo: {
            C: 1, // 法人变更代码
          },
        }),
      };

      // Act
      const result = helper.hitLayTypesField72(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理OperInfo为null的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.ExceptAny;

      const item = {
        ChangeExtend: JSON.stringify({
          OperInfo: null,
        }),
      };

      // Act
      const result = helper.hitLayTypesField72(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理JSON解析失败的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: 'invalid json', // 无效的JSON字符串
      };

      // Act
      const result = helper.hitLayTypesField72(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category15Field 方法测试', () => {
    it('应该正确处理动产抵押金额匹配', () => {
      // Arrange
      const dimension = new DimensionHitStrategyFieldsEntity();
      dimension.fieldValue = [100]; // 目标金额
      dimension.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          A: '150万元', // 抵押金额
        },
      };

      // Act
      const result = helper.category15Field(dimension, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理动产抵押金额不匹配', () => {
      // Arrange
      const dimension = new DimensionHitStrategyFieldsEntity();
      dimension.fieldValue = [100]; // 目标金额
      dimension.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          A: '50万元', // 抵押金额小于目标
        },
      };

      // Act
      const result = helper.category15Field(dimension, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少A字段的情况', () => {
      // Arrange
      const dimension = new DimensionHitStrategyFieldsEntity();
      dimension.fieldValue = [100];
      dimension.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {}, // 缺少A字段
      };

      // Act
      const result = helper.category15Field(dimension, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category58Field 方法测试', () => {
    it('应该正确识别破产重整被申请人', () => {
      // Arrange
      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          C: JSON.stringify([
            { KeyNo: 'test-keyno' }, // 匹配的KeyNo
            { KeyNo: 'other-keyno' },
          ]),
        },
      };

      // Act
      const result = helper.category58Field(item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的被申请人', () => {
      // Arrange
      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          C: JSON.stringify([{ KeyNo: 'other-keyno-1' }, { KeyNo: 'other-keyno-2' }]),
        },
      };

      // Act
      const result = helper.category58Field(item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的C字段', () => {
      // Arrange
      const item = {
        KeyNo: 'test-keyno',
        ChangeExtend: {
          C: null,
        },
      };

      // Act & Assert
      // 当C为null时，会返回空对象{}，然后尝试调用.some()方法会抛出错误
      expect(() => helper.category58Field(item)).toThrow('CSourceValue.some is not a function');
    });

    it('应该正确处理缺少ChangeExtend的情况', () => {
      // Arrange
      const item = {
        KeyNo: 'test-keyno',
        // 缺少ChangeExtend
      };

      // Act & Assert
      // 当ChangeExtend缺少时，C为undefined，会返回空对象{}，然后尝试调用.some()方法会抛出错误
      expect(() => helper.category58Field(item)).toThrow('CSourceValue.some is not a function');
    });
  });

  describe('category30Field 方法测试', () => {
    it('应该正确处理土地抵押匹配', () => {
      // Arrange
      const companyId = 'test-company-id';
      const dimension = new DimensionHitStrategyFieldsEntity();
      dimension.fieldValue = [100];
      dimension.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          T: 2, // 土地抵押类型
          F: {
            KeyNo: 'test-company-id', // 匹配的公司ID
          },
          B: 150, // 抵押金额
        },
      };

      // Act
      const result = helper.category30Field(companyId, dimension, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理公司ID不匹配的情况', () => {
      // Arrange
      const companyId = 'test-company-id';
      const dimension = new DimensionHitStrategyFieldsEntity();
      dimension.fieldValue = [100];
      dimension.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          T: 2,
          F: {
            KeyNo: 'other-company-id', // 不匹配的公司ID
          },
          B: 150,
        },
      };

      // Act
      const result = helper.category30Field(companyId, dimension, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理T字段不为2的情况', () => {
      // Arrange
      const companyId = 'test-company-id';
      const dimension = new DimensionHitStrategyFieldsEntity();
      dimension.fieldValue = [100];
      dimension.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          T: 1, // 不是土地抵押类型
          F: {
            KeyNo: 'test-company-id',
          },
          B: 150,
        },
      };

      // Act
      const result = helper.category30Field(companyId, dimension, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category101Field 方法测试', () => {
    it('应该正确处理担保信息匹配', () => {
      // Arrange
      const dimension = new DimensionHitStrategyFieldsEntity();
      dimension.fieldValue = [100];
      dimension.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          T: 1, // 担保信息类型
          A: 150, // 担保金额
        },
      };

      // Act
      const result = helper.category101Field(dimension, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理T字段不为1的情况', () => {
      // Arrange
      const dimension = new DimensionHitStrategyFieldsEntity();
      dimension.fieldValue = [100];
      dimension.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          T: 2, // 不是担保信息类型
          A: 150,
        },
      };

      // Act
      const result = helper.category101Field(dimension, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少A字段的情况', () => {
      // Arrange
      const dimension = new DimensionHitStrategyFieldsEntity();
      dimension.fieldValue = [100];
      dimension.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          T: 1,
          // 缺少A字段
        },
      };

      // Act
      const result = helper.category101Field(dimension, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category131Field 方法测试', () => {
    it('应该正确处理税务催缴金额匹配', () => {
      // Arrange
      const dimension = new DimensionHitStrategyFieldsEntity();
      dimension.fieldValue = [10]; // 目标金额（万元）
      dimension.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          B: 150000, // 催缴金额（元），应该转换为15万元
        },
      };

      // Act
      const result = helper.category131Field(dimension, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理税务催缴金额不匹配', () => {
      // Arrange
      const dimension = new DimensionHitStrategyFieldsEntity();
      dimension.fieldValue = [10]; // 目标金额（万元）
      dimension.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          B: 50000, // 催缴金额（元），转换为5万元，小于目标
        },
      };

      // Act
      const result = helper.category131Field(dimension, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少B字段的情况', () => {
      // Arrange
      const dimension = new DimensionHitStrategyFieldsEntity();
      dimension.fieldValue = [10];
      dimension.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          // 缺少B字段
        },
      };

      // Act
      const result = helper.category131Field(dimension, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('hitIsBPField 方法测试', () => {
    it('应该正确识别变更为股东', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 1-股东
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          IsBP: 1, // 变更为股东
        },
      };

      // Act
      const result = helper.hitIsBPField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别变更为非股东', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [2]; // 2-非股东
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          IsBP: 2, // 变更为非股东
        },
      };

      // Act
      const result = helper.hitIsBPField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的股东状态', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 期望股东
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          IsBP: 2, // 实际非股东
        },
      };

      // Act
      const result = helper.hitIsBPField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少IsBP字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          // 缺少IsBP字段
        },
      };

      // Act
      const result = helper.hitIsBPField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('hitBeforeContentField 方法测试', () => {
    it('应该正确解析变更前持股比例', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [50]; // 目标持股比例
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        BeforeContent: '60.5%', // 变更前持股比例
      };

      // Act
      const result = helper.hitBeforeContentField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的持股比例', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [50]; // 目标持股比例
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        BeforeContent: '30.5%', // 变更前持股比例小于目标
      };

      // Act
      const result = helper.hitBeforeContentField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少BeforeContent字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [50];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        // 缺少BeforeContent字段
      };

      // Act
      const result = helper.hitBeforeContentField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理无效的持股比例格式', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [50];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        BeforeContent: 'invalid format', // 无效格式
      };

      // Act
      const result = helper.hitBeforeContentField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('hitShareChangeStatusField 方法测试', () => {
    it('应该正确识别持股比例增加趋势', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 1-增加
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeStatus: 1, // 增加
      };

      // Act
      const result = helper.hitShareChangeStatusField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别持股比例减少趋势', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [0]; // 0-减少
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeStatus: 0, // 减少
      };

      // Act
      const result = helper.hitShareChangeStatusField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的变更趋势', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 期望增加
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeStatus: 0, // 实际减少
      };

      // Act
      const result = helper.hitShareChangeStatusField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理ChangeStatus为null的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeStatus: null,
      };

      // Act
      const result = helper.hitShareChangeStatusField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('hitShareChangeRateField 方法测试', () => {
    it('应该正确计算持股比例变更幅度', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [20]; // 目标变更幅度20%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        BeforeContent: '50', // 变更前50%
        AfterContent: '70', // 变更后70%，变更幅度为40%
      };

      // Act
      const result = helper.hitShareChangeRateField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理变更幅度不满足条件的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [50]; // 目标变更幅度50%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        BeforeContent: '50', // 变更前50%
        AfterContent: '60', // 变更后60%，变更幅度为20%
      };

      // Act
      const result = helper.hitShareChangeRateField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少BeforeContent的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [20];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        AfterContent: '70',
        // 缺少BeforeContent
      };

      // Act
      const result = helper.hitShareChangeRateField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少AfterContent的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [20];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        BeforeContent: '50',
        // 缺少AfterContent
      };

      // Act
      const result = helper.hitShareChangeRateField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('hitTimePeriodThresholdCountField 方法测试', () => {
    it('应该正确统计新增投资数量', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [2]; // 目标数量
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const allPeriodList = [
        { BeforeContent: '', ChangeStatus: 1 }, // 新增
        { BeforeContent: '', ChangeStatus: 1 }, // 新增
        { BeforeContent: '100', ChangeStatus: 1 }, // 不是新增
        { BeforeContent: '', ChangeStatus: 0 }, // 不是增加
      ];

      // Act
      const result = helper.hitTimePeriodThresholdCountField(field, allPeriodList);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理数量不满足条件的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [5]; // 目标数量
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const allPeriodList = [
        { BeforeContent: '', ChangeStatus: 1 }, // 新增
        { BeforeContent: '', ChangeStatus: 1 }, // 新增
      ];

      // Act
      const result = helper.hitTimePeriodThresholdCountField(field, allPeriodList);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的期间列表', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const allPeriodList = [];

      // Act
      const result = helper.hitTimePeriodThresholdCountField(field, allPeriodList);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('hitChangeStatusField 方法测试', () => {
    it('应该正确处理数值比较类型的变更趋势', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 目标变更趋势
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeStatus: 1, // 增加
      };

      // Act
      const result = helper.hitChangeStatusField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理数组比较类型的变更趋势', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1, 2]; // 目标变更趋势数组
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeStatus: 1, // 匹配其中一个
      };

      // Act
      const result = helper.hitChangeStatusField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的变更趋势', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [2]; // 期望减少
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeStatus: 1, // 实际增加
      };

      // Act
      const result = helper.hitChangeStatusField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少ChangeStatus的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        // 缺少ChangeStatus
      };

      // Act
      const result = helper.hitChangeStatusField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('hitAfterContentField 方法测试', () => {
    it('应该正确解析变更后持股比例', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [50]; // 目标持股比例
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        AfterContent: '60.5%', // 变更后持股比例
      };

      // Act
      const result = helper.hitAfterContentField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的持股比例', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [50]; // 目标持股比例
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        AfterContent: '30.5%', // 变更后持股比例小于目标
      };

      // Act
      const result = helper.hitAfterContentField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少AfterContent字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [50];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        // 缺少AfterContent字段
      };

      // Act
      const result = helper.hitAfterContentField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('hitCurrencyChangeField 方法测试', () => {
    it('应该正确识别币种变更', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 1-币种变更
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        Extend1: {
          T: 1, // 币种变更标识
        },
      };

      // Act
      const result = helper.hitCurrencyChangeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别非币种变更', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [0]; // 0-不是币种变更
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        Extend1: {
          T: 0, // 非币种变更标识
        },
      };

      // Act
      const result = helper.hitCurrencyChangeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理缺少Extend1字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        // 缺少Extend1字段
      };

      // Act
      const result = helper.hitCurrencyChangeField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('hitCategory123CurrencyChangeField 方法测试', () => {
    it('应该正确识别币种变更（Bc不等于Ac）', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 1-币种变更
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          E: JSON.stringify({
            Bc: 'USD', // 变更前币种
            Ac: 'CNY', // 变更后币种
          }),
        },
      };

      // Act
      const result = helper.hitCategory123CurrencyChangeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别非币种变更（Bc等于Ac）', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [0]; // 0-不是币种变更
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          E: JSON.stringify({
            Bc: 'CNY', // 变更前币种
            Ac: 'CNY', // 变更后币种相同
          }),
        },
      };

      // Act
      const result = helper.hitCategory123CurrencyChangeField(field, item);

      // Assert
      // 由于源值为0（非币种变更），但条件中有 currencyChangeFieldSourceValue && 检查，所以返回false
      expect(result).toBe(false);
    });

    it('应该正确处理缺少E字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          // 缺少E字段
        },
      };

      // Act
      const result = helper.hitCategory123CurrencyChangeField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('hitRegisCapitalTrendField 方法测试', () => {
    it('应该正确识别注册资本减少趋势', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 1-减少
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          T: 1, // 减少趋势
        },
      };

      // Act
      const result = helper.hitRegisCapitalTrendField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别注册资本增加趋势', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [2]; // 2-增加
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          T: 2, // 增加趋势
        },
      };

      // Act
      const result = helper.hitRegisCapitalTrendField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的趋势', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 期望减少
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          T: 2, // 实际增加
        },
      };

      // Act
      const result = helper.hitRegisCapitalTrendField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少T字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          // 缺少T字段
        },
      };

      // Act
      const result = helper.hitRegisCapitalTrendField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('hitRegisCapitalChangeRatioField 方法测试', () => {
    it('应该正确计算注册资本变更比例', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [20]; // 目标变更比例20%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          A: '1000万元人民币', // 变更前注册资本
          B: '500万元人民币', // 变更后注册资本，减少50%
        },
      };

      // Act
      const result = helper.hitRegisCapitalChangeRatioField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理变更比例不满足条件的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [50]; // 目标变更比例50%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          A: '1000万元人民币', // 变更前注册资本
          B: '800万元人民币', // 变更后注册资本，减少20%
        },
      };

      // Act
      const result = helper.hitRegisCapitalChangeRatioField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少A字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [20];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          B: '500万元人民币',
          // 缺少A字段
        },
      };

      // Act
      const result = helper.hitRegisCapitalChangeRatioField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少B字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [20];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          A: '1000万元人民币',
          // 缺少B字段
        },
      };

      // Act
      const result = helper.hitRegisCapitalChangeRatioField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category38 方法测试', () => {
    it('应该正确识别经营状态变更', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [90]; // 90-吊销
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          B: '企业被吊销营业执照', // 包含吊销关键字
        },
      };

      // Act
      const result = helper.category38(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理多种经营状态', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [90, 99]; // 90-吊销, 99-注销
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          B: '企业注销登记', // 包含注销关键字
        },
      };

      // Act
      const result = helper.category38(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的经营状态', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [90]; // 只要吊销
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          B: '企业正常经营', // 不包含吊销关键字
        },
      };

      // Act
      const result = helper.category38(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少B字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [90];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          // 缺少B字段
        },
      };

      // Act
      const result = helper.category38(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('checkListedField 方法测试', () => {
    it('应该正确识别上市企业', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 1-上市
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock company details response
      mockCompanySearchService.companyDetailsKys.mockResolvedValue({
        result: {
          listingstatuskw: ['F_4'], // 上市状态关键字
          stockinfo: ['股票信息'],
        },
      } as any);

      // Act
      const result = await helper.checkListedField(mockCompanySearchService, field, item, keyNo);

      // Assert
      expect(result).toBe(true);
      expect(mockCompanySearchService.companyDetailsKys).toHaveBeenCalledWith(keyNo);
    });

    it('应该正确识别非上市企业', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [0]; // 0-非上市
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock company details response
      mockCompanySearchService.companyDetailsKys.mockResolvedValue({
        result: {
          listingstatuskw: [], // 无上市状态关键字
          stockinfo: [],
        },
      } as any);

      // Act
      const result = await helper.checkListedField(mockCompanySearchService, field, item, keyNo);

      // Assert
      // 由于源值为0（非上市），但条件中有 listedFieldSourceValue && 检查，所以返回false
      expect(result).toBe(false);
      expect(mockCompanySearchService.companyDetailsKys).toHaveBeenCalledWith(keyNo);
    });

    it('应该正确处理查询失败的情况', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock company details response
      mockCompanySearchService.companyDetailsKys.mockResolvedValue(null);

      // Act
      const result = await helper.checkListedField(mockCompanySearchService, field, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category23Field 方法测试', () => {
    it('应该正确识别简易注销结果', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 假设1对应某种简易注销类型
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          B: '简易注销公告', // 简易注销结果
        },
      };

      // Act
      const result = helper.category23Field(field, item);

      // Assert
      // 这里的结果取决于 SimpleCancelTypeConstant 中的配置
      expect(typeof result).toBe('boolean');
    });

    it('应该正确处理不匹配的简易注销结果', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [999]; // 不存在的类型
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          B: '未知结果',
        },
      };

      // Act
      const result = helper.category23Field(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少B字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          // 缺少B字段
        },
      };

      // Act
      const result = helper.category23Field(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('边界情况测试', () => {
    it('应该正确处理 ChangeExtend 为空的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {},
      };

      // Act
      const result1 = helper.hitLayTypesField(field, item);
      const result2 = helper.hitIsBPField(field, item);
      const result3 = helper.category15Field(field, item);

      // Assert
      expect(result1).toBe(false);
      expect(result2).toBe(false);
      expect(result3).toBe(false);
    });

    it('应该正确处理 item 为空的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {};

      // Act
      const result1 = helper.hitLayTypesField(field, item);
      const result2 = helper.hitShareChangeStatusField(field, item);
      const result3 = helper.hitBeforeContentField(field, item);

      // Assert
      expect(result1).toBe(false);
      expect(result2).toBe(false);
      expect(result3).toBe(false);
    });

    it('应该正确处理 fieldValue 为空数组的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = []; // 空数组
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          C: 1,
        },
      };

      // Act
      const result1 = helper.hitLayTypesField(field, item);
      const result2 = helper.hitIsBPField(field, item);

      // Assert
      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });

    it('应该正确处理 fieldValue 为 null 的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = null; // null值
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          C: 1,
        },
      };

      // Act
      const result1 = helper.hitLayTypesField(field, item);
      const result2 = helper.hitIsBPField(field, item);

      // Assert
      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });
  });
});
