import { Test, TestingModule } from '@nestjs/testing';
import { RelatedAnalyzeHelper } from '../../helper/related-analyze.helper';
import { PersonHelper } from '../../../../helper/person.helper';
import { CompanySearchService } from '../../../../../company/company-search.service';
import { BankLitigationHelper } from '../../helper/bank-litigation.helper';
import { CaseReasonHelper } from '../../helper/case-reason.helper';
import { CompanyChangeHelper } from '../../helper/company-change.helper';
import { JudgementHelper } from '../../helper/judgement.helper';
import { MainEmployeeHelper } from '../../helper/main-employee.helper';
import { PenaltyHelper } from '../../helper/penalty.helper';
import { CompanyStockHelper } from '../../helper/company-stock.helper';
import { CompanyFinaceHelper } from '../../helper/company-finace.helper';
import { BaseHelper } from '../../helper/base.helper';
import { DimensionHitStrategyPO } from '../../../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from '../../../../../../libs/model/diligence/details/request';
import { RiskChangeCategoryEnum } from '../../../../../../libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from '../../../../../../libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyFieldsEntity } from '../../../../../../libs/entities/DimensionHitStrategyFieldsEntity';

describe('RelatedAnalyzeHelper', () => {
  let helper: RelatedAnalyzeHelper;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockBankLitigationHelper: jest.Mocked<BankLitigationHelper>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockCompanyChangeHelper: jest.Mocked<CompanyChangeHelper>;
  let mockJudgementHelper: jest.Mocked<JudgementHelper>;
  let mockMainEmployeeHelper: jest.Mocked<MainEmployeeHelper>;
  let mockPenaltyHelper: jest.Mocked<PenaltyHelper>;
  let mockCompanyStockHelper: jest.Mocked<CompanyStockHelper>;
  let mockCompanyFinanceHelper: jest.Mocked<CompanyFinaceHelper>;
  let mockBaseHelper: jest.Mocked<BaseHelper>;

  beforeEach(async () => {
    // Create comprehensive mocks for all dependencies
    mockPersonHelper = {
      getEmployeeData: jest.fn(),
      getFinalActualController: jest.fn(),
    } as any;

    mockCompanySearchService = {
      companyDetailsQcc: jest.fn(),
    } as any;

    mockBankLitigationHelper = {
      checkBankOrFinancialLeasingField4: jest.fn(),
      checkBankOrFinancialLeasingField49: jest.fn(),
      checkBankOrFinancialLeasingField18: jest.fn(),
      checkBankOrFinancialLeasingField7: jest.fn(),
      checkBankOrFinancialLeasingField27: jest.fn(),
      checkBankOrFinancialLeasingField90: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      caseReasonTypeField: jest.fn(),
      checkCaseTypeField: jest.fn(),
      checkContractDisputeField: jest.fn(),
      checkFinancialReasonField: jest.fn(),
    } as any;

    mockCompanyChangeHelper = {
      hitLayTypesField: jest.fn(),
      category15Field: jest.fn(),
      category30Field: jest.fn(),
      category101Field: jest.fn(),
      category131Field: jest.fn(),
      hitCategory123CurrencyChangeField: jest.fn(),
      capitalReduceSelectCompareResult: jest.fn(),
      hitShareChangeStatusField: jest.fn(),
      hitShareChangeRateField: jest.fn(),
      hitBeforeContentField: jest.fn(),
      hitAfterContentField: jest.fn(),
      hitIsBPField: jest.fn(),
      hitChangeStatusField: jest.fn(),
      hitCurrencyChangeField: jest.fn(),
      hitRegisCapitalTrendField: jest.fn(),
      hitRegisCapitalChangeRatioField: jest.fn(),
      category38: jest.fn(),
      category58Field: jest.fn(),
      category23Field: jest.fn(),
      checkListedField: jest.fn(),
    } as any;

    mockJudgementHelper = {
      category4: jest.fn(),
      category49: jest.fn(),
      category18: jest.fn(),
      category7: jest.fn(),
      category27: jest.fn(),
      category90: jest.fn(),
    } as any;

    mockMainEmployeeHelper = {
      hitCompChangeRoleField: jest.fn(),
    } as any;

    mockPenaltyHelper = {
      penaltyUnitField: jest.fn(),
      punishTypeField: jest.fn(),
      penaltyRedCardFieldCategory107: jest.fn(),
      penaltyIssuingUnitField: jest.fn(),
      businessAbnormalTypeField: jest.fn(),
      punishEnvTypeField: jest.fn(),
      financialPenaltyCauseTypeField: jest.fn(),
      inspectionResultTypeField: jest.fn(),
    } as any;

    mockCompanyStockHelper = {
      hitHolderRoleField: jest.fn(),
      category12Field: jest.fn(),
      holderRoleFieldCategory12: jest.fn(),
      equityPledgeStatusFieldCategory12: jest.fn(),
      equityPledgeRatioFieldCategory12: jest.fn(),
      equityPledgeAmountFieldCategory12: jest.fn(),
      equityPledgeQuantityFieldCategory12: jest.fn(),
      category50Field: jest.fn(),
      holderRoleFieldCategory50: jest.fn(),
      sharePledgeStatusFieldCategory50: jest.fn(),
      stockPledgeRatioFieldCategory50: jest.fn(),
      stockPledgeQuantityFieldCategory50: jest.fn(),
      category59Field: jest.fn(),
      category75Field: jest.fn(),
      limitPriceTypeField: jest.fn(),
      categoryAnnouncementReportField: jest.fn(),
      equityFreezeScopeFieldCategory26: jest.fn(),
      holderRoleFieldCategory26: jest.fn(),
      equityFrozenAmountFieldCategory26: jest.fn(),
    } as any;

    mockCompanyFinanceHelper = {
      categoryAnnualReportField: jest.fn(),
      categoryRetainedProfitField: jest.fn(),
      categoryNetProfitRatioField: jest.fn(),
      categoryRevenueRatioField: jest.fn(),
      categoryAccountsReceivableRatioField: jest.fn(),
      categoryInventoryRatioField: jest.fn(),
      categoryInterestBearingLiabilitiesRatioField: jest.fn(),
      categoryIbdAnnualRevRatioField: jest.fn(),
      categoryCmAndStbRatioField: jest.fn(),
      categoryTotalLiabToAssetsRatioField: jest.fn(),
      categoryCashFlowFromActivitiesAmountField: jest.fn(),
    } as any;

    mockBaseHelper = {
      restricterTypeField: jest.fn(),
      checkAmountField: jest.fn(),
      auctionTypeField: jest.fn(),
      category86IntellectualRole: jest.fn(),
      category86IntellectualType: jest.fn(),
      category79Field: jest.fn(),
      category28Field: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RelatedAnalyzeHelper,
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: BankLitigationHelper, useValue: mockBankLitigationHelper },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
        { provide: CompanyChangeHelper, useValue: mockCompanyChangeHelper },
        { provide: JudgementHelper, useValue: mockJudgementHelper },
        { provide: MainEmployeeHelper, useValue: mockMainEmployeeHelper },
        { provide: PenaltyHelper, useValue: mockPenaltyHelper },
        { provide: CompanyStockHelper, useValue: mockCompanyStockHelper },
        { provide: CompanyFinaceHelper, useValue: mockCompanyFinanceHelper },
        { provide: BaseHelper, useValue: mockBaseHelper },
      ],
    }).compile();

    helper = module.get<RelatedAnalyzeHelper>(RelatedAnalyzeHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Constructor and Initialization', () => {
    it('should be defined', () => {
      expect(helper).toBeDefined();
    });

    it('should have all required dependencies injected', () => {
      expect(helper['personHelper']).toBeDefined();
      expect(helper['companySearchService']).toBeDefined();
      expect(helper['bankLitigationHelper']).toBeDefined();
      expect(helper['caseReasonHelper']).toBeDefined();
      expect(helper['companyChangeHelper']).toBeDefined();
      expect(helper['judgementHelper']).toBeDefined();
      expect(helper['mainEmployeeHelper']).toBeDefined();
      expect(helper['penaltyHelper']).toBeDefined();
      expect(helper['companyStockHelper']).toBeDefined();
      expect(helper['companyFinanceHelper']).toBeDefined();
      expect(helper['baseHelper']).toBeDefined();
    });
  });

  describe('detailAnalyzeForRelated', () => {
    let mockDimension: jest.Mocked<DimensionHitStrategyPO>;
    let mockParams: HitDetailsBaseQueryParams;

    beforeEach(() => {
      mockDimension = {
        getStrategyFieldByKey: jest.fn(),
      } as any;

      mockParams = {
        keyNo: 'test-key-no',
      } as any;
    });

    it('should return empty array when esHitDetails is empty', async () => {
      const result = await helper.detailAnalyzeForRelated([], mockDimension, mockParams);
      expect(result).toEqual([]);
    });

    it('should handle JSON parsing for Extend1 and ChangeExtend fields', async () => {
      const esHitDetails = [
        {
          Category: RiskChangeCategoryEnum.category62,
          Extend1: '{"test": "value"}',
          ChangeExtend: '{"newTags": ["tag1"]}',
        },
      ];

      // For category62, we need to mock the topics field to make isHit = true
      const mockTopicsField = {
        fieldValue: ['tag1'], // This will match the newTags in ChangeExtend
      } as DimensionHitStrategyFieldsEntity;

      mockDimension.getStrategyFieldByKey.mockReturnValue(mockTopicsField);

      const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

      expect(result).toHaveLength(1);
      // The method processes the original item, so we need to check the processed newItem
      // The JSON parsing happens on a cloned item, but the original item is pushed to hitData
      expect(result[0].Extend1).toBe('{"test": "value"}'); // Original string is preserved in result
      expect(result[0].ChangeExtend).toBe('{"newTags": ["tag1"]}'); // Original string is preserved in result
    });

    it('should handle invalid JSON gracefully', async () => {
      const esHitDetails = [
        {
          Category: RiskChangeCategoryEnum.category62,
          Extend1: 'invalid-json',
          ChangeExtend: null,
        },
      ];

      // For category62, we need to mock the topics field, but since ChangeExtend is null,
      // newTags will be undefined, so negativePositiveNewsField will return false
      // Let's use a different category that doesn't require field validation
      esHitDetails[0].Category = RiskChangeCategoryEnum.category58; // bankruptcy reorganization

      mockDimension.getStrategyFieldByKey.mockReturnValue(null);
      mockCompanyChangeHelper.category58Field.mockReturnValue(true); // Mock the category58Field method

      const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

      expect(result).toHaveLength(1);
      expect(result[0].Extend1).toBe('invalid-json');
      expect(result[0].ChangeExtend).toBe(null); // Original null value is preserved
    });

    it('should handle errors in processing individual items', async () => {
      const esHitDetails = [
        {
          Category: RiskChangeCategoryEnum.category62,
        },
        {
          Category: RiskChangeCategoryEnum.category39,
        },
      ];

      // Mock an error for the first item
      mockDimension.getStrategyFieldByKey.mockImplementationOnce(() => {
        throw new Error('Test error');
      });
      mockDimension.getStrategyFieldByKey.mockReturnValue(null);

      const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

      // Should still process the second item despite error in first
      expect(result).toHaveLength(1);
    });

    describe('Category Processing - News Categories', () => {
      it('should process category62 (negative news) correctly', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category62,
            ChangeExtend: '{"newTags": ["tag1", "tag2"]}',
          },
        ];

        const mockTopicsField = {
          fieldValue: ['tag1', 'tag2'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValue(mockTopicsField);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.topics);
        expect(result).toHaveLength(1);
      });

      it('should process category66 and category67 (positive news) correctly', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category66,
            ChangeExtend: '{"newTags": ["tag1"]}',
          },
          {
            Category: RiskChangeCategoryEnum.category67,
            ChangeExtend: '{"newTags": ["tag2"]}',
          },
        ];

        const mockTopicsField = {
          fieldValue: ['tag1', 'tag2'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValue(mockTopicsField);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(result).toHaveLength(2);
      });
    });

    describe('Category Processing - Company Changes', () => {
      it('should process category39 (legal representative change) correctly', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category39,
          },
        ];

        const mockLayTypesField = {
          fieldValue: ['type1'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValue(mockLayTypesField);
        mockCompanyChangeHelper.hitLayTypesField.mockReturnValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.layTypes);
        expect(mockCompanyChangeHelper.hitLayTypesField).toHaveBeenCalledWith(mockLayTypesField, expect.any(Object));
        expect(result).toHaveLength(1);
      });

      it('should process category46 (main employee change) correctly', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category46,
          },
        ];

        const mockCompChangeRoleField = {
          fieldValue: ['role1'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValue(mockCompChangeRoleField);
        mockMainEmployeeHelper.hitCompChangeRoleField.mockReturnValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.compChangeRole);
        expect(mockMainEmployeeHelper.hitCompChangeRoleField).toHaveBeenCalledWith(mockCompChangeRoleField, expect.any(Object));
        expect(result).toHaveLength(1);
      });

      it('should process category15 (movable property mortgage) correctly', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category15,
          },
        ];

        const mockGuaranteedPrincipalField = {
          fieldValue: ['100000'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValue(mockGuaranteedPrincipalField);
        mockCompanyChangeHelper.category15Field.mockReturnValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.guaranteedPrincipal);
        expect(mockCompanyChangeHelper.category15Field).toHaveBeenCalledWith(mockGuaranteedPrincipalField, expect.any(Object));
        expect(result).toHaveLength(1);
      });

      it('should process category30 (land mortgage) correctly', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category30,
          },
        ];

        const mockLandMortgageAmountField = {
          fieldValue: ['500000'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValue(mockLandMortgageAmountField);
        mockCompanyChangeHelper.category30Field.mockReturnValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.landMortgageAmount);
        expect(mockCompanyChangeHelper.category30Field).toHaveBeenCalledWith(mockParams.keyNo, mockLandMortgageAmountField, expect.any(Object));
        expect(result).toHaveLength(1);
      });
    });

    describe('Category Processing - Guarantee and Tax', () => {
      it('should process category53 and category101 (guarantee information) correctly', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category53,
          },
          {
            Category: RiskChangeCategoryEnum.category101,
          },
        ];

        const mockGuaranteeAmountField = {
          fieldValue: ['1000000'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValue(mockGuaranteeAmountField);
        mockCompanyChangeHelper.category101Field.mockReturnValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.guaranteeAmount);
        expect(mockCompanyChangeHelper.category101Field).toHaveBeenCalledTimes(2);
        expect(result).toHaveLength(2);
      });

      it('should process category131 (tax collection) correctly', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category131,
          },
        ];

        const mockAmountOwedField = {
          fieldValue: ['50000'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValue(mockAmountOwedField);
        mockCompanyChangeHelper.category131Field.mockReturnValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.AmountOwed);
        expect(mockCompanyChangeHelper.category131Field).toHaveBeenCalledWith(mockAmountOwedField, expect.any(Object));
        expect(result).toHaveLength(1);
      });
    });

    describe('Category Processing - Capital Changes', () => {
      it('should process category123 (capital reduction announcement) correctly', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category123,
          },
        ];

        const mockCurrencyChangeField = {
          fieldValue: ['CNY'],
        } as DimensionHitStrategyFieldsEntity;

        const mockCapitalReductionRateField = {
          fieldValue: ['0.5'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValueOnce(mockCurrencyChangeField).mockReturnValueOnce(mockCapitalReductionRateField);

        mockCompanyChangeHelper.hitCategory123CurrencyChangeField.mockReturnValue(true);
        mockCompanyChangeHelper.capitalReduceSelectCompareResult.mockReturnValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.currencyChange);
        expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.capitalReductionRate);
        expect(result).toHaveLength(1);
      });

      it('should process category37 (registered capital change) correctly', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category37,
          },
        ];

        const mockCurrencyChangeField = {
          fieldValue: ['CNY'],
        } as DimensionHitStrategyFieldsEntity;

        const mockRegisCapitalTrendField = {
          fieldValue: ['increase'],
        } as DimensionHitStrategyFieldsEntity;

        const mockRegisCapitalChangeRatioField = {
          fieldValue: ['0.2'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey
          .mockReturnValueOnce(mockCurrencyChangeField)
          .mockReturnValueOnce(mockRegisCapitalTrendField)
          .mockReturnValueOnce(mockRegisCapitalChangeRatioField);

        mockCompanyChangeHelper.hitCurrencyChangeField.mockReturnValue(true);
        mockCompanyChangeHelper.hitRegisCapitalTrendField.mockReturnValue(true);
        mockCompanyChangeHelper.hitRegisCapitalChangeRatioField.mockReturnValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockCompanyChangeHelper.hitCurrencyChangeField).toHaveBeenCalled();
        expect(mockCompanyChangeHelper.hitRegisCapitalTrendField).toHaveBeenCalled();
        expect(mockCompanyChangeHelper.hitRegisCapitalChangeRatioField).toHaveBeenCalled();
        expect(result).toHaveLength(1);
      });
    });

    describe('Category Processing - Shareholding Changes', () => {
      it('should process category68 (shareholding ratio change) with multiple fields', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category68,
            ChangeExtend: '{"K": "test-company-key"}',
          },
        ];

        const mockShareChangeStatusField = {
          fieldValue: ['increase'],
        } as DimensionHitStrategyFieldsEntity;

        const mockShareChangeRateField = {
          fieldValue: ['0.1'],
        } as DimensionHitStrategyFieldsEntity;

        const mockBeforeContentField = {
          fieldValue: ['before'],
        } as DimensionHitStrategyFieldsEntity;

        const mockAfterContentField = {
          fieldValue: ['after'],
        } as DimensionHitStrategyFieldsEntity;

        const mockIsBPField = {
          fieldValue: ['true'],
        } as DimensionHitStrategyFieldsEntity;

        const mockHolderRoleField = {
          fieldValue: ['shareholder'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey
          .mockReturnValueOnce(mockShareChangeStatusField)
          .mockReturnValueOnce(mockShareChangeRateField)
          .mockReturnValueOnce(mockBeforeContentField)
          .mockReturnValueOnce(mockAfterContentField)
          .mockReturnValueOnce(mockIsBPField)
          .mockReturnValueOnce(mockHolderRoleField);

        mockCompanyChangeHelper.hitShareChangeStatusField.mockReturnValue(true);
        mockCompanyChangeHelper.hitShareChangeRateField.mockReturnValue(true);
        mockCompanyChangeHelper.hitBeforeContentField.mockReturnValue(true);
        mockCompanyChangeHelper.hitAfterContentField.mockReturnValue(true);
        mockCompanyChangeHelper.hitIsBPField.mockReturnValue(true);
        mockCompanyStockHelper.hitHolderRoleField.mockResolvedValue({ hit: true, hitKeyNos: ['key1', 'key2'] });

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockCompanyChangeHelper.hitShareChangeStatusField).toHaveBeenCalled();
        expect(mockCompanyChangeHelper.hitShareChangeRateField).toHaveBeenCalled();
        expect(mockCompanyChangeHelper.hitBeforeContentField).toHaveBeenCalled();
        expect(mockCompanyChangeHelper.hitAfterContentField).toHaveBeenCalled();
        expect(mockCompanyChangeHelper.hitIsBPField).toHaveBeenCalled();
        expect(mockCompanyStockHelper.hitHolderRoleField).toHaveBeenCalled();
        expect(result).toHaveLength(1);
      });

      it('should process category68 and stop early when field returns false', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category68,
          },
        ];

        const mockShareChangeStatusField = {
          fieldValue: ['decrease'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValue(mockShareChangeStatusField);
        mockCompanyChangeHelper.hitShareChangeStatusField.mockReturnValue(false);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockCompanyChangeHelper.hitShareChangeStatusField).toHaveBeenCalled();
        expect(result).toHaveLength(0); // Should not be included when hit is false
      });
    });

    describe('Category Processing - Investment Changes', () => {
      it('should process category203 and category17 (external investment change)', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category203,
            ChangeExtend: '{"A": "Test Company", "K": "test-key"}',
          },
          {
            Category: RiskChangeCategoryEnum.category17,
            ChangeExtend: '{"A": "Another Company", "K": "another-key"}',
          },
        ];

        const mockChangeStatusField = {
          fieldValue: ['new'],
        } as DimensionHitStrategyFieldsEntity;

        // Mock all the field checks to return null (no additional validation)
        mockDimension.getStrategyFieldByKey.mockImplementation((key) => {
          if (key === DimensionFieldKeyEnums.changeStatus) {
            return mockChangeStatusField;
          }
          return null; // All other fields return null
        });

        mockCompanyChangeHelper.hitChangeStatusField.mockReturnValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockCompanyChangeHelper.hitChangeStatusField).toHaveBeenCalledTimes(2);
        expect(result).toHaveLength(2);
      });

      it('should process category17 with company detail fields', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category17,
            ChangeExtend: '{"A": "Tech Company", "K": "tech-key"}',
          },
        ];

        const mockChangeStatusField = {
          fieldValue: ['new'],
        } as DimensionHitStrategyFieldsEntity;

        const mockCompanyScopeField = {
          dimensionFieldKey: DimensionFieldKeyEnums.companySocpe,
          fieldValue: ['Technology'],
        } as DimensionHitStrategyFieldsEntity;

        const mockCompanyNameField = {
          dimensionFieldKey: DimensionFieldKeyEnums.companyName,
          fieldValue: ['Tech'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey
          .mockReturnValueOnce(mockChangeStatusField)
          .mockReturnValueOnce(null) // afterContentField
          .mockReturnValueOnce(null) // isBPField
          .mockReturnValueOnce(null) // beforeContentField
          .mockReturnValueOnce(mockCompanyScopeField)
          .mockReturnValueOnce(mockCompanyNameField)
          .mockReturnValueOnce(null) // companyIndustryField
          .mockReturnValueOnce(null) // qccIndustryField
          .mockReturnValueOnce(null); // excludeCompanyNameField

        mockCompanyChangeHelper.hitChangeStatusField.mockReturnValue(true);

        // Mock hitCompanyDetail method
        const hitCompanyDetailSpy = jest.spyOn(helper as any, 'hitCompanyDetail');
        hitCompanyDetailSpy.mockResolvedValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(hitCompanyDetailSpy).toHaveBeenCalledTimes(2);
        expect(result).toHaveLength(1);

        hitCompanyDetailSpy.mockRestore();
      });
    });

    describe('Category Processing - Business Status', () => {
      it('should process category38 (business status change)', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category38,
          },
        ];

        const mockBusinessStatusField = {
          fieldValue: ['active'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValue(mockBusinessStatusField);
        mockCompanyChangeHelper.category38.mockReturnValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.businessStatus);
        expect(mockCompanyChangeHelper.category38).toHaveBeenCalledWith(mockBusinessStatusField, expect.any(Object));
        expect(result).toHaveLength(1);
      });

      it('should process category58 (bankruptcy reorganization)', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category58,
          },
        ];

        mockCompanyChangeHelper.category58Field.mockReturnValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockCompanyChangeHelper.category58Field).toHaveBeenCalledWith(expect.any(Object));
        expect(result).toHaveLength(1);
      });
    });

    describe('Category Processing - Restrictions', () => {
      it('should process category208 and category55 (high consumption restriction)', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category208,
          },
          {
            Category: RiskChangeCategoryEnum.category55,
          },
        ];

        const mockRestrictTypeField = {
          fieldValue: ['individual'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValue(mockRestrictTypeField);
        mockBaseHelper.restricterTypeField.mockReturnValue(true);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(mockDimension.getStrategyFieldByKey).toHaveBeenCalledWith(DimensionFieldKeyEnums.restricterType);
        expect(mockBaseHelper.restricterTypeField).toHaveBeenCalledTimes(2);
        expect(result).toHaveLength(2);
      });
    });

    describe('Category Processing - Edge Cases', () => {
      it('should handle category with no matching fields', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category39,
          },
        ];

        mockDimension.getStrategyFieldByKey.mockReturnValue(null);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(result).toHaveLength(1); // Should still include item when no fields to check
      });

      it('should handle unknown category', async () => {
        const esHitDetails = [
          {
            Category: 'unknown-category' as any,
          },
        ];

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(result).toHaveLength(1); // Should include item for unknown categories
      });

      it('should handle field returning false and exclude item', async () => {
        const esHitDetails = [
          {
            Category: RiskChangeCategoryEnum.category39,
          },
        ];

        const mockLayTypesField = {
          fieldValue: ['type1'],
        } as DimensionHitStrategyFieldsEntity;

        mockDimension.getStrategyFieldByKey.mockReturnValue(mockLayTypesField);
        mockCompanyChangeHelper.hitLayTypesField.mockReturnValue(false);

        const result = await helper.detailAnalyzeForRelated(esHitDetails, mockDimension, mockParams);

        expect(result).toHaveLength(0); // Should exclude when hit is false
      });
    });
  });
});
