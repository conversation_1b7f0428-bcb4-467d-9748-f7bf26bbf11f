import { Test, TestingModule } from '@nestjs/testing';
import { CompanyFinaceHelper } from '../../helper/company-finace.helper';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { BaseHelper } from '../../helper/base.helper';
// import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user'; // 保留注释，等待手工修复

describe('CompanyFinaceHelper 单元测试', () => {
  let helper: CompanyFinaceHelper;
  let mockCompanyDetailService: jest.Mocked<CompanyDetailService>;
  let mockBaseHelper: jest.Mocked<BaseHelper>;

  beforeEach(async () => {
    mockCompanyDetailService = {
      getCompanyFinance: jest.fn(),
    } as any;

    mockBaseHelper = {} as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CompanyFinaceHelper,
        { provide: CompanyDetailService, useValue: mockCompanyDetailService },
        { provide: BaseHelper, useValue: mockBaseHelper },
      ],
    }).compile();

    helper = module.get<CompanyFinaceHelper>(CompanyFinaceHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('categoryAnnualReportField 方法测试', () => {
    it('应该正确识别年报类型', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [203]; // 目标年报类型 - 年报
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          TS: 203, // 年报类型标识 - 年报
        },
      };

      // Act
      const result = helper.categoryAnnualReportField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的年报类型', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [203]; // 期望的年报类型 - 年报
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          TS: 204, // 不匹配的年报类型 - 季报
        },
      };

      // Act
      const result = helper.categoryAnnualReportField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少TS字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [203];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          // 缺少TS字段
        },
      };

      // Act
      const result = helper.categoryAnnualReportField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少ChangeExtend的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [203];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        // 缺少ChangeExtend
      };

      // Act
      const result = helper.categoryAnnualReportField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的fieldValue', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = []; // 空的fieldValue
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          TS: 203,
        },
      };

      // Act
      const result = helper.categoryAnnualReportField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('categoryRetainedProfitField 方法测试', () => {
    it('应该正确处理留存收益匹配', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1000000]; // 目标留存收益
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '主要指标',
              AccountType: '利润表',
              AccountNameZh: '净利润',
              FieldList: [
                {
                  OriginalValue: 1500000, // 留存收益值
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryRetainedProfitField(field, item, keyNo);

      // Assert
      expect(result).toBe(true);
      expect(mockCompanyDetailService.getCompanyFinance).toHaveBeenCalledWith(keyNo, [0], 1);
    });

    it('应该正确处理留存收益不匹配', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [2000000]; // 目标留存收益
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '主要指标',
              AccountType: '利润表',
              AccountNameZh: '净利润',
              FieldList: [
                {
                  OriginalValue: 1000000, // 留存收益值小于目标
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryRetainedProfitField(field, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理财务数据查询失败', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1000000];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue(null);

      // Act
      const result = await helper.categoryRetainedProfitField(field, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的ReportFields', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1000000];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [], // 空的ReportFields
        },
      } as any);

      // Act
      const result = await helper.categoryRetainedProfitField(field, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('categoryNetProfitRatioField 方法测试', () => {
    it('应该正确处理净利润同比增长匹配', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [10]; // 目标同比增长率10%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '主要指标',
              AccountType: '利润表',
              AccountNameZh: '净利润',
              FieldList: [
                {
                  YoyGrowth: 15.5, // 净利润同比增长率
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryNetProfitRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(true);
      expect(mockCompanyDetailService.getCompanyFinance).toHaveBeenCalledWith(keyNo, [0], 1);
    });

    it('应该正确处理净利润同比增长不匹配', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [20]; // 目标同比增长率20%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '主要指标',
              AccountType: '利润表',
              AccountNameZh: '净利润',
              FieldList: [
                {
                  YoyGrowth: 10.5, // 净利润同比增长率小于目标
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryNetProfitRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理YoyGrowth为null的情况', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [10];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '主要指标',
              AccountType: '利润表',
              AccountNameZh: '净利润',
              FieldList: [
                {
                  YoyGrowth: null, // YoyGrowth为null
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryNetProfitRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('categoryRevenueRatioField 方法测试', () => {
    it('应该正确处理营业收入同比增长匹配', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [5]; // 目标同比增长率5%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '主要指标',
              AccountType: '利润表',
              AccountNameZh: '营业收入',
              FieldList: [
                {
                  YoyGrowth: 8.2, // 营业收入同比增长率
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryRevenueRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(true);
      expect(mockCompanyDetailService.getCompanyFinance).toHaveBeenCalledWith(keyNo, [0], 1);
    });

    it('应该正确处理营业收入同比增长不匹配', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [15]; // 目标同比增长率15%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '主要指标',
              AccountType: '利润表',
              AccountNameZh: '营业收入',
              FieldList: [
                {
                  YoyGrowth: 8.2, // 营业收入同比增长率小于目标
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryRevenueRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理营业收入数据缺失', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [5];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '主要指标',
              AccountType: '利润表',
              AccountNameZh: '其他收入', // 不匹配的科目
              FieldList: [
                {
                  YoyGrowth: 8.2,
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryRevenueRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('categoryAccountsReceivableRatioField 方法测试', () => {
    it('应该正确计算应收账款同比增长', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [10]; // 目标同比增长率10%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动资产',
              AccountNameZh: '应收账款',
              FieldList: [
                {
                  OriginalValue: 1100000, // 当期应收账款
                },
                {
                  OriginalValue: 1000000, // 上期应收账款
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryAccountsReceivableRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(true); // (1100000 - 1000000) / 1000000 * 100 = 10%
      expect(mockCompanyDetailService.getCompanyFinance).toHaveBeenCalledWith(keyNo, [], 2);
    });

    it('应该正确处理应收账款同比增长不匹配', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [20]; // 目标同比增长率20%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动资产',
              AccountNameZh: '应收账款',
              FieldList: [
                {
                  OriginalValue: 1050000, // 当期应收账款
                },
                {
                  OriginalValue: 1000000, // 上期应收账款
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryAccountsReceivableRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(false); // (1050000 - 1000000) / 1000000 * 100 = 5% < 20%
    });

    it('应该正确处理应收票据及应收账款科目', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [15]; // 目标同比增长率15%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response - 第一次查询应收账款为空，第二次查询应收票据及应收账款
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动资产',
              AccountNameZh: '应收票据及应收账款',
              FieldList: [
                {
                  OriginalValue: 1200000, // 当期应收票据及应收账款
                },
                {
                  OriginalValue: 1000000, // 上期应收票据及应收账款
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryAccountsReceivableRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(true); // (1200000 - 1000000) / 1000000 * 100 = 20% > 15%
    });

    it('应该正确处理缺少历史数据的情况', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [10];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动资产',
              AccountNameZh: '应收账款',
              FieldList: [
                {
                  OriginalValue: 1100000, // 只有当期数据
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryAccountsReceivableRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(false); // 缺少上期数据，无法计算同比
    });
  });

  describe('categoryInventoryRatioField 方法测试', () => {
    it('应该正确计算存货同比增长', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [8]; // 目标同比增长率8%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动资产',
              AccountNameZh: '存货',
              FieldList: [
                {
                  OriginalValue: 540000, // 当期存货
                },
                {
                  OriginalValue: 500000, // 上期存货
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryInventoryRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(true); // (540000 - 500000) / 500000 * 100 = 8%
      expect(mockCompanyDetailService.getCompanyFinance).toHaveBeenCalledWith(keyNo, [], 2);
    });

    it('应该正确处理存货同比增长不匹配', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [15]; // 目标同比增长率15%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动资产',
              AccountNameZh: '存货',
              FieldList: [
                {
                  OriginalValue: 520000, // 当期存货
                },
                {
                  OriginalValue: 500000, // 上期存货
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryInventoryRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(false); // (520000 - 500000) / 500000 * 100 = 4% < 15%
    });

    it('应该正确处理存货数据缺失', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [8];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [], // 空的ReportFields
        },
      } as any);

      // Act
      const result = await helper.categoryInventoryRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('categoryInterestBearingLiabilitiesRatioField 方法测试', () => {
    it('应该正确计算有息负债同比增长', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [12]; // 目标同比增长率12%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动负债',
              AccountNameZh: '短期借款',
              FieldList: [
                {
                  OriginalValue: 500000, // 当期短期借款
                },
                {
                  OriginalValue: 400000, // 上期短期借款
                },
              ],
            },
            {
              ReportType: '资产负债表',
              AccountType: '流动负债',
              AccountNameZh: '应付票据',
              FieldList: [
                {
                  OriginalValue: 200000, // 当期应付票据
                },
                {
                  OriginalValue: 150000, // 上期应付票据
                },
              ],
            },
            {
              ReportType: '资产负债表',
              AccountType: '非流动负债',
              AccountNameZh: '长期借款',
              FieldList: [
                {
                  OriginalValue: 300000, // 当期长期借款
                },
                {
                  OriginalValue: 250000, // 上期长期借款
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryInterestBearingLiabilitiesRatioField(field, item, keyNo);

      // Assert
      // 当期总计: 500000 + 200000 + 300000 = 1000000
      // 上期总计: 400000 + 150000 + 250000 = 800000
      // 同比增长: (1000000 - 800000) / 800000 * 100 = 25% > 12%
      expect(result).toBe(true);
      expect(mockCompanyDetailService.getCompanyFinance).toHaveBeenCalledWith(keyNo, [], 2);
    });

    it('应该正确处理有息负债同比增长不匹配', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [30]; // 目标同比增长率30%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动负债',
              AccountNameZh: '短期借款',
              FieldList: [
                {
                  OriginalValue: 520000, // 当期短期借款
                },
                {
                  OriginalValue: 500000, // 上期短期借款
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryInterestBearingLiabilitiesRatioField(field, item, keyNo);

      // Assert
      // 同比增长: (520000 - 500000) / 500000 * 100 = 4% < 30%
      expect(result).toBe(false);
    });

    it('应该正确处理有息负债数据缺失', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [12];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [], // 空的ReportFields
        },
      } as any);

      // Act
      const result = await helper.categoryInterestBearingLiabilitiesRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('categoryIbdAnnualRevRatioField 方法测试', () => {
    it('应该正确计算有息负债/年度总收入占比', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [50]; // 目标占比50%
      field.compareType = DimensionFieldCompareTypeEnums.LessThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response - 第一次调用获取有息负债，第二次调用获取营业收入
      mockCompanyDetailService.getCompanyFinance
        .mockResolvedValueOnce({
          Result: {
            ReportFields: [
              {
                ReportType: '资产负债表',
                AccountType: '流动负债',
                AccountNameZh: '短期借款',
                FieldList: [
                  {
                    OriginalValue: 300000, // 短期借款
                  },
                ],
              },
              {
                ReportType: '资产负债表',
                AccountType: '非流动负债',
                AccountNameZh: '长期借款',
                FieldList: [
                  {
                    OriginalValue: 200000, // 长期借款
                  },
                ],
              },
            ],
          },
        } as any)
        .mockResolvedValueOnce({
          Result: {
            ReportFields: [
              {
                ReportType: '主要指标',
                AccountType: '利润表',
                AccountNameZh: '营业收入',
                FieldList: [
                  {
                    OriginalValue: 1000000, // 年度总收入
                  },
                ],
              },
            ],
          },
        } as any);

      // Act
      const result = await helper.categoryIbdAnnualRevRatioField(field, item, keyNo);

      // Assert
      // 有息负债总额: 300000 + 200000 = 500000
      // 占比: 500000 / 1000000 * 100 = 50% <= 50%
      expect(result).toBe(true);
      expect(mockCompanyDetailService.getCompanyFinance).toHaveBeenCalledTimes(2);
      expect(mockCompanyDetailService.getCompanyFinance).toHaveBeenNthCalledWith(1, keyNo, [], 2);
      expect(mockCompanyDetailService.getCompanyFinance).toHaveBeenNthCalledWith(2, keyNo, [0], 1);
    });

    it('应该正确处理有息负债/年度总收入占比不匹配', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [30]; // 目标占比30%
      field.compareType = DimensionFieldCompareTypeEnums.LessThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance
        .mockResolvedValueOnce({
          Result: {
            ReportFields: [
              {
                ReportType: '资产负债表',
                AccountType: '流动负债',
                AccountNameZh: '短期借款',
                FieldList: [
                  {
                    OriginalValue: 400000, // 短期借款
                  },
                ],
              },
            ],
          },
        } as any)
        .mockResolvedValueOnce({
          Result: {
            ReportFields: [
              {
                ReportType: '主要指标',
                AccountType: '利润表',
                AccountNameZh: '营业收入',
                FieldList: [
                  {
                    OriginalValue: 1000000, // 年度总收入
                  },
                ],
              },
            ],
          },
        } as any);

      // Act
      const result = await helper.categoryIbdAnnualRevRatioField(field, item, keyNo);

      // Assert
      // 占比: 400000 / 1000000 * 100 = 40% > 30%
      expect(result).toBe(false);
    });

    it('应该正确处理缺少营业收入数据的情况', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [50];
      field.compareType = DimensionFieldCompareTypeEnums.LessThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance
        .mockResolvedValueOnce({
          Result: {
            ReportFields: [
              {
                ReportType: '资产负债表',
                AccountType: '流动负债',
                AccountNameZh: '短期借款',
                FieldList: [
                  {
                    OriginalValue: 300000,
                  },
                ],
              },
            ],
          },
        } as any)
        .mockResolvedValueOnce({
          Result: {
            ReportFields: [], // 空的营业收入数据
          },
        } as any);

      // Act
      const result = await helper.categoryIbdAnnualRevRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('categoryCmAndStbRatioField 方法测试', () => {
    it('应该正确计算（货币资金+交易性金融资产）/（短期借款+应付票据）比率', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [120]; // 目标比率120%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动资产',
              AccountNameZh: '货币资金',
              FieldList: [
                {
                  OriginalValue: 800000, // 货币资金
                },
              ],
            },
            {
              ReportType: '资产负债表',
              AccountType: '流动资产',
              AccountNameZh: '交易性金融资产',
              FieldList: [
                {
                  OriginalValue: 200000, // 交易性金融资产
                },
              ],
            },
            {
              ReportType: '资产负债表',
              AccountType: '流动负债',
              AccountNameZh: '短期借款',
              FieldList: [
                {
                  OriginalValue: 500000, // 短期借款
                },
              ],
            },
            {
              ReportType: '资产负债表',
              AccountType: '流动负债',
              AccountNameZh: '应付票据',
              FieldList: [
                {
                  OriginalValue: 300000, // 应付票据
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryCmAndStbRatioField(field, item, keyNo);

      // Assert
      // 比率: (800000 + 200000) / (500000 + 300000) * 100 = 125% > 120%
      expect(result).toBe(true);
      expect(mockCompanyDetailService.getCompanyFinance).toHaveBeenCalledWith(keyNo, [], 2);
    });

    it('应该正确处理比率不匹配的情况', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [150]; // 目标比率150%
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动资产',
              AccountNameZh: '货币资金',
              FieldList: [
                {
                  OriginalValue: 600000, // 货币资金
                },
              ],
            },
            {
              ReportType: '资产负债表',
              AccountType: '流动负债',
              AccountNameZh: '短期借款',
              FieldList: [
                {
                  OriginalValue: 500000, // 短期借款
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryCmAndStbRatioField(field, item, keyNo);

      // Assert
      // 比率: 600000 / 500000 * 100 = 120% < 150%
      expect(result).toBe(false);
    });

    it('应该正确处理分母为零的情况', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [100];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动资产',
              AccountNameZh: '货币资金',
              FieldList: [
                {
                  OriginalValue: 600000, // 货币资金
                },
              ],
            },
            // 缺少短期借款和应付票据数据，分母为0
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryCmAndStbRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(false); // 分母为0，无法计算比率
    });
  });

  describe('categoryTotalLiabToAssetsRatioField 方法测试', () => {
    it('应该正确计算负债合计/资产合计比率', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [60]; // 目标比率60%
      field.compareType = DimensionFieldCompareTypeEnums.LessThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动负债',
              AccountNameZh: '流动负债合计',
              FieldList: [
                {
                  OriginalValue: 3000000, // 流动负债合计
                },
              ],
            },
            {
              ReportType: '资产负债表',
              AccountType: '非流动负债',
              AccountNameZh: '非流动负债合计',
              FieldList: [
                {
                  OriginalValue: 2000000, // 非流动负债合计
                },
              ],
            },
            {
              ReportType: '资产负债表',
              AccountType: '流动资产',
              AccountNameZh: '流动资产合计',
              FieldList: [
                {
                  OriginalValue: 6000000, // 流动资产合计
                },
              ],
            },
            {
              ReportType: '资产负债表',
              AccountType: '非流动资产',
              AccountNameZh: '非流动资产合计',
              FieldList: [
                {
                  OriginalValue: 4000000, // 非流动资产合计
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryTotalLiabToAssetsRatioField(field, item, keyNo);

      // Assert
      // 负债合计: 3000000 + 2000000 = 5000000
      // 资产合计: 6000000 + 4000000 = ********
      // 比率: 5000000 / ******** * 100 = 50% <= 60%
      expect(result).toBe(true);
      expect(mockCompanyDetailService.getCompanyFinance).toHaveBeenCalledWith(keyNo, [], 2);
    });

    it('应该正确处理负债资产比率不匹配', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [40]; // 目标比率40%
      field.compareType = DimensionFieldCompareTypeEnums.LessThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动负债',
              AccountNameZh: '流动负债合计',
              FieldList: [
                {
                  OriginalValue: 4500000, // 流动负债合计
                },
              ],
            },
            {
              ReportType: '资产负债表',
              AccountType: '非流动负债',
              AccountNameZh: '非流动负债合计',
              FieldList: [
                {
                  OriginalValue: 500000, // 非流动负债合计
                },
              ],
            },
            {
              ReportType: '资产负债表',
              AccountType: '流动资产',
              AccountNameZh: '流动资产合计',
              FieldList: [
                {
                  OriginalValue: 7000000, // 流动资产合计
                },
              ],
            },
            {
              ReportType: '资产负债表',
              AccountType: '非流动资产',
              AccountNameZh: '非流动资产合计',
              FieldList: [
                {
                  OriginalValue: 3000000, // 非流动资产合计
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryTotalLiabToAssetsRatioField(field, item, keyNo);

      // Assert
      // 负债合计: 4500000 + 500000 = 5000000
      // 资产合计: 7000000 + 3000000 = ********
      // 比率: 5000000 / ******** * 100 = 50% > 40%
      expect(result).toBe(false);
    });

    it('应该正确处理资产数据缺失的情况', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [60];
      field.compareType = DimensionFieldCompareTypeEnums.LessThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '资产负债表',
              AccountType: '流动负债',
              AccountNameZh: '流动负债合计',
              FieldList: [
                {
                  OriginalValue: 3000000,
                },
              ],
            },
            // 缺少资产数据
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryTotalLiabToAssetsRatioField(field, item, keyNo);

      // Assert
      expect(result).toBe(false); // 缺少资产数据，无法计算比率
    });
  });

  describe('categoryCashFlowFromActivitiesAmountField 方法测试', () => {
    it('应该正确验证连续X年经营活动现金流量净额', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [
        {
          consecutiveYearCount: 3, // 连续3年
          cashFlowFromActivitiesAmount: 1000000, // 目标现金流量净额
          cashFlowFromActivitiesAmountCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        },
      ];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '现金流量表',
              AccountType: '经营活动产生的现金流量',
              AccountNameZh: '经营活动产生的现金流量净额',
              FieldList: [
                {
                  OriginalValue: 1200000, // 第1年现金流量净额
                },
                {
                  OriginalValue: 1100000, // 第2年现金流量净额
                },
                {
                  OriginalValue: 1050000, // 第3年现金流量净额
                },
                {
                  OriginalValue: 900000, // 第4年现金流量净额（不满足条件）
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryCashFlowFromActivitiesAmountField(field, item, keyNo);

      // Assert
      // 前3年都满足 >= 1000000 的条件
      expect(result).toBe(true);
      expect(mockCompanyDetailService.getCompanyFinance).toHaveBeenCalledWith(keyNo, [], 4);
    });

    it('应该正确处理不满足连续年数要求的情况', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [
        {
          consecutiveYearCount: 3, // 连续3年
          cashFlowFromActivitiesAmount: 1000000, // 目标现金流量净额
          cashFlowFromActivitiesAmountCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        },
      ];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '现金流量表',
              AccountType: '经营活动产生的现金流量',
              AccountNameZh: '经营活动产生的现金流量净额',
              FieldList: [
                {
                  OriginalValue: 1200000, // 第1年现金流量净额
                },
                {
                  OriginalValue: 800000, // 第2年现金流量净额（不满足条件）
                },
                {
                  OriginalValue: 1050000, // 第3年现金流量净额
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryCashFlowFromActivitiesAmountField(field, item, keyNo);

      // Assert
      // 只有2年满足条件，不满足连续3年的要求
      expect(result).toBe(false);
    });

    it('应该正确处理现金流量数据缺失的情况', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [
        {
          consecutiveYearCount: 2,
          cashFlowFromActivitiesAmount: 1000000,
          cashFlowFromActivitiesAmountCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        },
      ];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [], // 空的ReportFields
        },
      } as any);

      // Act
      const result = await helper.categoryCashFlowFromActivitiesAmountField(field, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理部分年份数据缺失的情况', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [
        {
          consecutiveYearCount: 3,
          cashFlowFromActivitiesAmount: 500000,
          cashFlowFromActivitiesAmountCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        },
      ];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '现金流量表',
              AccountType: '经营活动产生的现金流量',
              AccountNameZh: '经营活动产生的现金流量净额',
              FieldList: [
                {
                  OriginalValue: 600000, // 第1年现金流量净额
                },
                {
                  OriginalValue: null, // 第2年数据缺失
                },
                {
                  OriginalValue: 550000, // 第3年现金流量净额
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryCashFlowFromActivitiesAmountField(field, item, keyNo);

      // Assert
      // 第2年数据缺失，按0处理，不满足条件
      expect(result).toBe(false);
    });

    it('应该正确处理不同比较类型的情况', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [
        {
          consecutiveYearCount: 2,
          cashFlowFromActivitiesAmount: 1000000,
          cashFlowFromActivitiesAmountCompareType: DimensionFieldCompareTypeEnums.LessThan, // 小于比较
        },
      ];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue({
        Result: {
          ReportFields: [
            {
              ReportType: '现金流量表',
              AccountType: '经营活动产生的现金流量',
              AccountNameZh: '经营活动产生的现金流量净额',
              FieldList: [
                {
                  OriginalValue: 800000, // 第1年现金流量净额 < 1000000
                },
                {
                  OriginalValue: 900000, // 第2年现金流量净额 < 1000000
                },
              ],
            },
          ],
        },
      } as any);

      // Act
      const result = await helper.categoryCashFlowFromActivitiesAmountField(field, item, keyNo);

      // Assert
      // 连续2年都小于1000000，满足条件
      expect(result).toBe(true);
    });
  });

  describe('边界情况测试', () => {
    it('应该正确处理财务数据查询返回null', async () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1000000];
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const item = {};

      // Mock finance data response
      mockCompanyDetailService.getCompanyFinance.mockResolvedValue(null);

      // Act
      const result1 = await helper.categoryRetainedProfitField(field, item, keyNo);
      const result2 = await helper.categoryNetProfitRatioField(field, item, keyNo);
      const result3 = await helper.categoryRevenueRatioField(field, item, keyNo);

      // Assert
      expect(result1).toBe(false);
      expect(result2).toBe(false);
      expect(result3).toBe(false);
    });

    it('应该正确处理空的fieldValue', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = []; // 空的fieldValue
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          TS: 203,
        },
      };

      // Act
      const result = helper.categoryAnnualReportField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理fieldValue为null的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = null; // null值
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          TS: 203,
        },
      };

      // Act
      const result = helper.categoryAnnualReportField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理item为空的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [203];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {}; // 空的item

      // Act
      const result = helper.categoryAnnualReportField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });
});
