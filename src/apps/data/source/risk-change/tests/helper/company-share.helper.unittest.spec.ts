import { Test, TestingModule } from '@nestjs/testing';
import { CompanyShareHelper } from '../../helper/company-share.helper';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { PersonHelper } from '../../../../helper/person.helper';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonData } from 'libs/model/data/source/PersonData';
// import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user'; // 保留注释，等待手工修复

describe('CompanyShareHelper 单元测试', () => {
  let helper: CompanyShareHelper;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;

  beforeEach(async () => {
    mockPersonHelper = {
      getFinalActualController: jest.fn(),
      getPartnerList: jest.fn(),
    } as any;

    mockCompanySearchService = {
      companySearchForKys: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CompanyShareHelper,
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
      ],
    }).compile();

    helper = module.get<CompanyShareHelper>(CompanyShareHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('calculatePeriodHolderRoleChangeThreshold 方法测试', () => {
    it('应该正确计算大股东变更阈值', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1]; // 大股东
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1]; // 增加
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const changeThresholdField = new DimensionHitStrategyFieldsEntity();
      changeThresholdField.fieldValue = [10]; // 10%阈值
      changeThresholdField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const allPeriodList = [
        {
          ChangeExtend: { IsBP: '1' },
          BeforeContent: '20%',
          AfterContent: '35%', // 增加15%
          ChangeStatus: 1, // 增加
        },
        {
          IsBP: '1',
          BeforeContent: '10%',
          AfterContent: '15%', // 增加5%
          ChangeStatus: 1, // 增加
        },
      ];

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.calculatePeriodHolderRoleChangeThreshold(holderRoleField, shareChangeStatusField, changeThresholdField, allPeriodList, keyNo);

      // Assert
      expect(result).toBe(true); // 总变更比例20%超过阈值10%
    });

    it('应该正确计算实际控制人变更阈值', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2]; // 实际控制人
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [0]; // 减少
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const changeThresholdField = new DimensionHitStrategyFieldsEntity();
      changeThresholdField.fieldValue = [5]; // 5%阈值
      changeThresholdField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const allPeriodList = [
        {
          ChangeExtend: { K: 'ac-keyno-1' },
          BeforeContent: '30%',
          AfterContent: '20%', // 减少10%
          ChangeStatus: 0, // 减少
        },
        {
          K: 'ac-keyno-1',
          BeforeContent: '15%',
          AfterContent: '10%', // 减少5%
          ChangeStatus: 0, // 减少
        },
      ];

      const keyNo = 'test-keyno';

      // Mock实际控制人数据
      const mockActualControllers = [
        Object.assign(new PersonData(), { keyNo: 'ac-keyno-1', name: '张三' }),
        Object.assign(new PersonData(), { keyNo: 'ac-keyno-2', name: '李四' }),
      ];
      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      // Act
      const result = await helper.calculatePeriodHolderRoleChangeThreshold(holderRoleField, shareChangeStatusField, changeThresholdField, allPeriodList, keyNo);

      // Assert
      expect(result).toBe(true); // 总变更比例15%超过阈值5%
      expect(mockPersonHelper.getFinalActualController).toHaveBeenCalledWith(keyNo, false);
    });

    it('应该正确计算公司主体变更阈值', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [3]; // 公司主体
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1]; // 增加
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const changeThresholdField = new DimensionHitStrategyFieldsEntity();
      changeThresholdField.fieldValue = [20]; // 20%阈值
      changeThresholdField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const keyNo = 'test-keyno';
      const allPeriodList = [
        {
          ChangeExtend: { K: keyNo },
          BeforeContent: '10%',
          AfterContent: '25%', // 增加15%
          ChangeStatus: 1, // 增加
        },
        {
          K: keyNo,
          BeforeContent: '5%',
          AfterContent: '15%', // 增加10%
          ChangeStatus: 1, // 增加
        },
      ];

      // Act
      const result = await helper.calculatePeriodHolderRoleChangeThreshold(holderRoleField, shareChangeStatusField, changeThresholdField, allPeriodList, keyNo);

      // Assert
      expect(result).toBe(true); // 总变更比例25%超过阈值20%
    });

    it('应该正确处理变更比例不满足阈值的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1]; // 大股东
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1]; // 增加
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const changeThresholdField = new DimensionHitStrategyFieldsEntity();
      changeThresholdField.fieldValue = [50]; // 50%阈值
      changeThresholdField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const allPeriodList = [
        {
          ChangeExtend: { IsBP: '1' },
          BeforeContent: '20%',
          AfterContent: '25%', // 增加5%
        },
      ];

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.calculatePeriodHolderRoleChangeThreshold(holderRoleField, shareChangeStatusField, changeThresholdField, allPeriodList, keyNo);

      // Assert
      expect(result).toBe(false); // 总变更比例5%小于阈值50%
    });

    it('应该正确处理空的期间列表', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1];
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const changeThresholdField = new DimensionHitStrategyFieldsEntity();
      changeThresholdField.fieldValue = [10];
      changeThresholdField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const allPeriodList = [];
      const keyNo = 'test-keyno';

      // Act
      const result = await helper.calculatePeriodHolderRoleChangeThreshold(holderRoleField, shareChangeStatusField, changeThresholdField, allPeriodList, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的fieldValue', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = []; // 空数组
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1];
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const changeThresholdField = new DimensionHitStrategyFieldsEntity();
      changeThresholdField.fieldValue = [10];
      changeThresholdField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const allPeriodList = [
        {
          ChangeExtend: { IsBP: '1' },
          BeforeContent: '20%',
          AfterContent: '35%',
        },
      ];

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.calculatePeriodHolderRoleChangeThreshold(holderRoleField, shareChangeStatusField, changeThresholdField, allPeriodList, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category72isPEVCField 方法测试', () => {
    it('应该正确识别PEVC机构进来融资', async () => {
      // Arrange
      const isPEVCField = new DimensionHitStrategyFieldsEntity();
      isPEVCField.fieldValue = [1]; // 是PEVC
      isPEVCField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            H: [{ K: 'pevc-keyno-1' }], // 股份上升
            F: [{ K: 'pevc-keyno-2' }], // 股份新增
          },
        },
      };

      const keyNo = 'test-keyno';

      // Mock PEVC查询结果
      mockCompanySearchService.companySearchForKys.mockResolvedValue({
        Result: [
          {
            id: 'pevc-keyno-1',
            name: 'PEVC机构1',
            commonlist: [{ k: '2', v: 'PEVC' }], // PEVC标识
            isHide: false,
            b2bproduct: [],
            b2bproductcategory: [],
          },
          {
            id: 'pevc-keyno-2',
            name: 'PEVC机构2',
            commonlist: [{ k: '2', v: 'PEVC' }], // PEVC标识
            isHide: false,
            b2bproduct: [],
            b2bproductcategory: [],
          },
        ],
        GroupItems: [],
        Paging: { PageIndex: 1, PageSize: 10, TotalRecords: { value: 2, relation: 'eq' } },
        Status: 200,
        scrollId: '',
      });

      // Act
      const result = await helper.category72isPEVCField(isPEVCField, newItem, keyNo);

      // Assert
      expect(result).toBe(true);
      expect(mockCompanySearchService.companySearchForKys).toHaveBeenCalledWith(
        expect.objectContaining({
          includeFields: ['id', 'name', 'commonlist'],
          pageIndex: 1,
          pageSize: 2,
          filter: { ids: ['pevc-keyno-1', 'pevc-keyno-2'] },
        }),
      );
    });

    it('应该正确识别非PEVC机构', async () => {
      // Arrange
      const isPEVCField = new DimensionHitStrategyFieldsEntity();
      isPEVCField.fieldValue = [0]; // 不是PEVC
      isPEVCField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            H: [{ K: 'normal-keyno-1' }],
            F: [{ K: 'normal-keyno-2' }],
          },
        },
      };

      const keyNo = 'test-keyno';

      // Mock非PEVC查询结果
      mockCompanySearchService.companySearchForKys.mockResolvedValue({
        Result: [
          {
            id: 'normal-keyno-1',
            name: '普通公司1',
            commonlist: [{ k: '1', v: '非PEVC' }], // 非PEVC标识
            isHide: false,
            b2bproduct: [],
            b2bproductcategory: [],
          },
          {
            id: 'normal-keyno-2',
            name: '普通公司2',
            commonlist: [],
            isHide: false,
            b2bproduct: [],
            b2bproductcategory: [],
          },
        ],
        GroupItems: [],
        Paging: { PageIndex: 1, PageSize: 10, TotalRecords: { value: 2, relation: 'eq' } },
        Status: 200,
        scrollId: '',
      });

      // Act
      const result = await helper.category72isPEVCField(isPEVCField, newItem, keyNo);

      // Assert
      expect(result).toBe(true); // 源值为0（非PEVC），期望值为0，匹配条件，所以返回true
    });

    it('应该正确处理空的PartInfo', async () => {
      // Arrange
      const isPEVCField = new DimensionHitStrategyFieldsEntity();
      isPEVCField.fieldValue = [1];
      isPEVCField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            H: [],
            F: [],
          },
        },
      };

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.category72isPEVCField(isPEVCField, newItem, keyNo);

      // Assert
      expect(result).toBe(false);
      expect(mockCompanySearchService.companySearchForKys).not.toHaveBeenCalled();
    });

    it('应该正确处理缺少ChangeExtend的情况', async () => {
      // Arrange
      const isPEVCField = new DimensionHitStrategyFieldsEntity();
      isPEVCField.fieldValue = [1];
      isPEVCField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {}; // 缺少ChangeExtend

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.category72isPEVCField(isPEVCField, newItem, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的fieldValue', async () => {
      // Arrange
      const isPEVCField = new DimensionHitStrategyFieldsEntity();
      isPEVCField.fieldValue = []; // 空数组
      isPEVCField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            H: [{ K: 'pevc-keyno-1' }],
            F: [{ K: 'pevc-keyno-2' }],
          },
        },
      };

      const keyNo = 'test-keyno';

      // Mock空的查询结果（因为fieldValue为空，不会进行查询，但为了安全起见还是mock）
      mockCompanySearchService.companySearchForKys.mockResolvedValue({
        Result: [],
        GroupItems: [],
        Paging: { PageIndex: 1, PageSize: 10, TotalRecords: { value: 0, relation: 'eq' } },
        Status: 200,
        scrollId: '',
      });

      // Act
      const result = await helper.category72isPEVCField(isPEVCField, newItem, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category72periodShareRatioChangeField 方法测试', () => {
    it('应该正确计算期间股份比例变更', async () => {
      // Arrange
      const periodShareRatioChangeField = new DimensionHitStrategyFieldsEntity();
      periodShareRatioChangeField.fieldValue = [
        {
          shareChangeRate: 10, // 变更比例阈值10%
          shareChangeRateCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        },
      ];

      const item = {
        ChangeExtend: {
          PartInfo: {
            D: [{ K: 'ac-keyno-1', C: '25%' }], // 当前股份
          },
        },
      };

      const keyNo = 'test-keyno';
      const allPeriodList = [
        {
          ChangeExtend: {
            PartInfo: {
              D: [{ K: 'ac-keyno-1', B: '30%' }], // 历史股份下降
              H: [{ K: 'ac-keyno-1', B: '35%' }], // 历史股份上升
              F: [{ K: 'ac-keyno-1', C: '40%' }], // 历史股份新增
            },
          },
        },
      ];

      const actualControllerKeyNo = 'ac-keyno-1';

      // Act
      const result = await helper.category72periodShareRatioChangeField(periodShareRatioChangeField, item, keyNo, allPeriodList, actualControllerKeyNo);

      // Assert
      expect(result).toBe(true); // 最大股份40% - 当前股份25% = 15% > 10%
    });

    it('应该正确处理变更比例不满足条件的情况', async () => {
      // Arrange
      const periodShareRatioChangeField = new DimensionHitStrategyFieldsEntity();
      periodShareRatioChangeField.fieldValue = [
        {
          shareChangeRate: 20, // 变更比例阈值20%
          shareChangeRateCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        },
      ];

      const item = {
        ChangeExtend: {
          PartInfo: {
            D: [{ K: 'ac-keyno-1', C: '25%' }], // 当前股份
          },
        },
      };

      const keyNo = 'test-keyno';
      const allPeriodList = [
        {
          ChangeExtend: {
            PartInfo: {
              D: [{ K: 'ac-keyno-1', B: '30%' }], // 历史股份
            },
          },
        },
      ];

      const actualControllerKeyNo = 'ac-keyno-1';

      // Act
      const result = await helper.category72periodShareRatioChangeField(periodShareRatioChangeField, item, keyNo, allPeriodList, actualControllerKeyNo);

      // Assert
      expect(result).toBe(false); // 变更比例5%小于阈值20%
    });

    it('应该正确处理找不到实际控制人的情况', async () => {
      // Arrange
      const periodShareRatioChangeField = new DimensionHitStrategyFieldsEntity();
      periodShareRatioChangeField.fieldValue = [
        {
          shareChangeRate: 10,
          shareChangeRateCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        },
      ];

      const item = {
        ChangeExtend: {
          PartInfo: {
            D: [{ K: 'other-keyno', C: '25%' }], // 不是实际控制人
          },
        },
      };

      const keyNo = 'test-keyno';
      const allPeriodList = [
        {
          ChangeExtend: {
            PartInfo: {
              D: [{ K: 'other-keyno', B: '30%' }],
            },
          },
        },
      ];

      const actualControllerKeyNo = 'ac-keyno-1'; // 找不到的keyNo

      // Act
      const result = await helper.category72periodShareRatioChangeField(periodShareRatioChangeField, item, keyNo, allPeriodList, actualControllerKeyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category72ShareChangeStatusField 方法测试', () => {
    it('应该正确识别股份减少趋势', () => {
      // Arrange
      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [0]; // 减少
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            D: [
              {
                K: 'ac-keyno-1',
                B: '30%', // 变更前
                C: '20%', // 变更后，减少了
              },
            ],
          },
        },
      };

      const keyNoHits = ['ac-keyno-1'];

      // Act
      const result = helper.category72ShareChangeStatusField(shareChangeStatusField, newItem, keyNoHits);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别股份增加趋势', () => {
      // Arrange
      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1]; // 增加
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            D: [
              {
                K: 'ac-keyno-1',
                B: '20%', // 变更前
                C: '30%', // 变更后，增加了
              },
            ],
          },
        },
      };

      const keyNoHits = ['ac-keyno-1'];

      // Act
      const result = helper.category72ShareChangeStatusField(shareChangeStatusField, newItem, keyNoHits);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的变更趋势', () => {
      // Arrange
      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1]; // 期望增加
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            D: [
              {
                K: 'ac-keyno-1',
                B: '30%', // 变更前
                C: '20%', // 变更后，实际减少了
              },
            ],
          },
        },
      };

      const keyNoHits = ['ac-keyno-1'];

      // Act
      const result = helper.category72ShareChangeStatusField(shareChangeStatusField, newItem, keyNoHits);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的keyNoHits', () => {
      // Arrange
      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1];
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            D: [
              {
                K: 'ac-keyno-1',
                B: '20%',
                C: '30%',
              },
            ],
          },
        },
      };

      const keyNoHits = []; // 空数组

      // Act
      const result = helper.category72ShareChangeStatusField(shareChangeStatusField, newItem, keyNoHits);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理找不到对应keyNo的情况', () => {
      // Arrange
      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1];
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            D: [
              {
                K: 'other-keyno',
                B: '20%',
                C: '30%',
              },
            ],
          },
        },
      };

      const keyNoHits = ['ac-keyno-1']; // 找不到的keyNo

      // Act
      const result = helper.category72ShareChangeStatusField(shareChangeStatusField, newItem, keyNoHits);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少B或C字段的情况', () => {
      // Arrange
      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1];
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            D: [
              {
                K: 'ac-keyno-1',
                B: '20%',
                // 缺少C字段
              },
            ],
          },
        },
      };

      const keyNoHits = ['ac-keyno-1'];

      // Act
      const result = helper.category72ShareChangeStatusField(shareChangeStatusField, newItem, keyNoHits);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category72holderRoleField 方法测试', () => {
    it('应该正确识别实际控制人变更', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2]; // 实际控制人
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            D: [
              { K: 'ac-keyno-1' }, // 股份下降的实际控制人
              { K: 'other-keyno' },
            ],
          },
        },
      };

      const keyNo = 'test-keyno';

      // Mock实际控制人数据
      const mockActualControllers = [
        Object.assign(new PersonData(), { keyNo: 'ac-keyno-1', name: '张三' }),
        Object.assign(new PersonData(), { keyNo: 'ac-keyno-2', name: '李四' }),
      ];
      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      // Act
      const result = await helper.category72holderRoleField(holderRoleField, newItem, keyNo);

      // Assert
      expect(result.hit).toBe(true);
      expect(result.hitKeyNos).toEqual(['ac-keyno-1', 'ac-keyno-2']);
      expect(mockPersonHelper.getFinalActualController).toHaveBeenCalledWith(keyNo, false);
    });

    it('应该正确处理非实际控制人变更', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2]; // 实际控制人
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            D: [
              { K: 'other-keyno-1' }, // 非实际控制人
              { K: 'other-keyno-2' },
            ],
          },
        },
      };

      const keyNo = 'test-keyno';

      // Mock实际控制人数据
      const mockActualControllers = [
        Object.assign(new PersonData(), { keyNo: 'ac-keyno-1', name: '张三' }),
        Object.assign(new PersonData(), { keyNo: 'ac-keyno-2', name: '李四' }),
      ];
      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      // Act
      const result = await helper.category72holderRoleField(holderRoleField, newItem, keyNo);

      // Assert
      expect(result.hit).toBe(false);
      expect(result.hitKeyNos).toEqual(['ac-keyno-1', 'ac-keyno-2']);
    });

    it('应该正确处理空的fieldValue', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = []; // 空数组
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            D: [{ K: 'ac-keyno-1' }],
          },
        },
      };

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.category72holderRoleField(holderRoleField, newItem, keyNo);

      // Assert
      expect(result.hit).toBe(false);
      expect(result.hitKeyNos).toEqual([]);
    });

    it('应该正确处理空的decreasePartners', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            D: [], // 空数组
          },
        },
      };

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.category72holderRoleField(holderRoleField, newItem, keyNo);

      // Assert
      expect(result.hit).toBe(false);
      expect(result.hitKeyNos).toEqual([]);
    });
  });

  describe('category114holderRoleField 方法测试', () => {
    it('应该正确识别实际控制人变更', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2]; // 实际控制人
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: [
          {
            BeforeContent: JSON.stringify({ KeyNo: 'ac-keyno-1' }),
            AfterContent: JSON.stringify({ KeyNo: 'ac-keyno-2' }),
            ChangeExtend: JSON.stringify({ T: 2 }), // 类型2
          },
        ],
      };

      const keyNo = 'test-keyno';

      // Mock实际控制人数据
      const mockActualControllers = [
        Object.assign(new PersonData(), { keyNo: 'ac-keyno-1', name: '张三' }),
        Object.assign(new PersonData(), { keyNo: 'ac-keyno-2', name: '李四' }),
      ];
      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      // Act
      const result = await helper.category114holderRoleField(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(true);
      expect(mockPersonHelper.getFinalActualController).toHaveBeenCalledWith(keyNo, false);
    });

    it('应该正确处理非实际控制人变更', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2]; // 实际控制人
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: [
          {
            BeforeContent: JSON.stringify({ KeyNo: 'other-keyno' }),
            AfterContent: JSON.stringify({ KeyNo: 'other-keyno-2' }),
            ChangeExtend: JSON.stringify({ T: 2 }), // 类型2
          },
        ],
      };

      const keyNo = 'test-keyno';

      // Mock实际控制人数据
      const mockActualControllers = [
        Object.assign(new PersonData(), { keyNo: 'ac-keyno-1', name: '张三' }),
        Object.assign(new PersonData(), { keyNo: 'ac-keyno-2', name: '李四' }),
      ];
      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      // Act
      const result = await helper.category114holderRoleField(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理T不等于2的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: [
          {
            BeforeContent: JSON.stringify({ KeyNo: 'ac-keyno-1' }),
            AfterContent: JSON.stringify({ KeyNo: 'ac-keyno-2' }),
            ChangeExtend: JSON.stringify({ T: 1 }), // 类型不是2
          },
        ],
      };

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.category114holderRoleField(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的fieldValue', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = []; // 空数组
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: [
          {
            BeforeContent: JSON.stringify({ KeyNo: 'ac-keyno-1' }),
            AfterContent: JSON.stringify({ KeyNo: 'ac-keyno-2' }),
            ChangeExtend: JSON.stringify({ T: 2 }),
          },
        ],
      };

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.category114holderRoleField(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少BeforeContent、AfterContent或ChangeExtend的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: [
          {
            // 缺少必要字段
            ChangeExtend: JSON.stringify({ T: 1 }),
          },
        ],
      };

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.category114holderRoleField(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category114ShareChangeStatusField 方法测试', () => {
    it('应该正确识别股份减少趋势', async () => {
      // Arrange
      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [0]; // 减少
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: [
          {
            BeforeContent: JSON.stringify({ PercentTotal: '30%' }),
            AfterContent: JSON.stringify({ PercentTotal: '20%' }),
            ChangeExtend: JSON.stringify({ T: 2 }),
          },
        ],
      };

      // Act
      const result = await helper.category114shareChangeStatusField(shareChangeStatusField, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别股份增加趋势', async () => {
      // Arrange
      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1]; // 增加
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: [
          {
            BeforeContent: JSON.stringify({ PercentTotal: '20%' }),
            AfterContent: JSON.stringify({ PercentTotal: '30%' }),
            ChangeExtend: JSON.stringify({ T: 2 }),
          },
        ],
      };

      // Act
      const result = await helper.category114shareChangeStatusField(shareChangeStatusField, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的变更趋势', async () => {
      // Arrange
      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1]; // 期望增加
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: [
          {
            BeforeContent: JSON.stringify({ PercentTotal: '30%' }),
            AfterContent: JSON.stringify({ PercentTotal: '20%' }), // 实际减少
            ChangeExtend: JSON.stringify({ T: 2 }),
          },
        ],
      };

      // Act
      const result = await helper.category114shareChangeStatusField(shareChangeStatusField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的fieldValue', async () => {
      // Arrange
      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = []; // 空数组
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: [
          {
            BeforeContent: JSON.stringify({ PercentTotal: '20%' }),
            AfterContent: JSON.stringify({ PercentTotal: '30%' }),
            ChangeExtend: JSON.stringify({ T: 2 }),
          },
        ],
      };

      // Act
      const result = await helper.category114shareChangeStatusField(shareChangeStatusField, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category114DifferenceRatioField 方法测试', () => {
    it('应该正确计算持股比例差值', () => {
      // Arrange
      const differenceRatioField = new DimensionHitStrategyFieldsEntity();
      differenceRatioField.fieldValue = [20]; // 20%阈值
      differenceRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const holders = [
        { K: 'holder-1', B: '50%', C: '30%' }, // 差值40%
        { K: 'holder-2', B: '30%', C: '25%' }, // 差值16.67%
      ];

      const keyNoHits = ['holder-1', 'holder-2'];

      // Act
      const result = helper.hitDifferenceRatioField(differenceRatioField, holders, keyNoHits);

      // Assert
      expect(result).toBe(true); // holder-1的差值40%超过阈值20%
    });

    it('应该正确处理差值不满足条件的情况', () => {
      // Arrange
      const differenceRatioField = new DimensionHitStrategyFieldsEntity();
      differenceRatioField.fieldValue = [50]; // 50%阈值
      differenceRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const holders = [
        { K: 'holder-1', B: '30%', C: '25%' }, // 差值16.67%
        { K: 'holder-2', B: '20%', C: '18%' }, // 差值10%
      ];

      const keyNoHits = ['holder-1', 'holder-2'];

      // Act
      const result = helper.hitDifferenceRatioField(differenceRatioField, holders, keyNoHits);

      // Assert
      expect(result).toBe(false); // 所有差值都小于阈值50%
    });

    it('应该正确处理找不到对应holder的情况', () => {
      // Arrange
      const differenceRatioField = new DimensionHitStrategyFieldsEntity();
      differenceRatioField.fieldValue = [20];
      differenceRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const holders = [{ K: 'holder-1', B: '50%', C: '30%' }];

      const keyNoHits = ['holder-not-found']; // 找不到的keyNo

      // Act
      const result = helper.hitDifferenceRatioField(differenceRatioField, holders, keyNoHits);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少B或C字段的情况', () => {
      // Arrange
      const differenceRatioField = new DimensionHitStrategyFieldsEntity();
      differenceRatioField.fieldValue = [20];
      differenceRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const holders = [
        { K: 'holder-1', B: '50%' }, // 缺少C字段
        { K: 'holder-2', C: '30%' }, // 缺少B字段
      ];

      const keyNoHits = ['holder-1', 'holder-2'];

      // Act
      const result = helper.hitDifferenceRatioField(differenceRatioField, holders, keyNoHits);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('category114AbsRatioField 方法测试', () => {
    it('应该正确计算绝对持股比例差值', () => {
      // Arrange
      const absRatioField = new DimensionHitStrategyFieldsEntity();
      absRatioField.fieldValue = [10]; // 10%阈值
      absRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const holders = [
        { K: 'holder-1', B: '50%', C: '30%' }, // 绝对差值20%
        { K: 'holder-2', B: '30%', C: '25%' }, // 绝对差值5%
      ];

      const keyNoHits = ['holder-1', 'holder-2'];

      // Act
      const result = helper.hitAbsRatioField(absRatioField, holders, keyNoHits);

      // Assert
      expect(result).toBe(true); // holder-1的绝对差值20%超过阈值10%
    });

    it('应该正确处理绝对差值不满足条件的情况', () => {
      // Arrange
      const absRatioField = new DimensionHitStrategyFieldsEntity();
      absRatioField.fieldValue = [25]; // 25%阈值
      absRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const holders = [
        { K: 'holder-1', B: '30%', C: '25%' }, // 绝对差值5%
        { K: 'holder-2', B: '20%', C: '18%' }, // 绝对差值2%
      ];

      const keyNoHits = ['holder-1', 'holder-2'];

      // Act
      const result = helper.hitAbsRatioField(absRatioField, holders, keyNoHits);

      // Assert
      expect(result).toBe(false); // 所有绝对差值都小于阈值25%
    });

    it('应该正确处理找不到对应holder的情况', () => {
      // Arrange
      const absRatioField = new DimensionHitStrategyFieldsEntity();
      absRatioField.fieldValue = [10];
      absRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const holders = [{ K: 'holder-1', B: '50%', C: '30%' }];

      const keyNoHits = ['holder-not-found']; // 找不到的keyNo

      // Act
      const result = helper.hitAbsRatioField(absRatioField, holders, keyNoHits);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少B或C字段的情况', () => {
      // Arrange
      const absRatioField = new DimensionHitStrategyFieldsEntity();
      absRatioField.fieldValue = [10];
      absRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const holders = [
        { K: 'holder-1', B: '50%' }, // 缺少C字段
        { K: 'holder-2', C: '30%' }, // 缺少B字段
      ];

      const keyNoHits = ['holder-1', 'holder-2'];

      // Act
      const result = helper.hitAbsRatioField(absRatioField, holders, keyNoHits);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('边界情况测试', () => {
    it('应该正确处理 ChangeExtend 为空的情况', async () => {
      // Arrange
      const isPEVCField = new DimensionHitStrategyFieldsEntity();
      isPEVCField.fieldValue = [1];
      isPEVCField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {
        ChangeExtend: {}, // 空对象
      };

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.category72isPEVCField(isPEVCField, newItem, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理 item 为空的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {}; // 空对象

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.category72holderRoleField(holderRoleField, item, keyNo);

      // Assert
      expect(result.hit).toBe(false);
      expect(result.hitKeyNos).toEqual([]);
    });

    it('应该正确处理 fieldValue 为 null 的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = null; // null值
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            D: [{ K: 'ac-keyno-1' }],
          },
        },
      };

      const keyNo = 'test-keyno';

      // Act
      const result = await helper.category72holderRoleField(holderRoleField, newItem, keyNo);

      // Assert
      expect(result.hit).toBe(false);
      expect(result.hitKeyNos).toEqual([]);
    });

    it('应该正确处理PersonHelper返回空数组的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const shareChangeStatusField = new DimensionHitStrategyFieldsEntity();
      shareChangeStatusField.fieldValue = [1];
      shareChangeStatusField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const changeThresholdField = new DimensionHitStrategyFieldsEntity();
      changeThresholdField.fieldValue = [10];
      changeThresholdField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const allPeriodList = [
        {
          ChangeExtend: { K: 'ac-keyno-1' },
          BeforeContent: '30%',
          AfterContent: '20%',
        },
      ];

      const keyNo = 'test-keyno';

      // Mock空的实际控制人数据
      mockPersonHelper.getFinalActualController.mockResolvedValue([]);

      // Act
      const result = await helper.calculatePeriodHolderRoleChangeThreshold(holderRoleField, shareChangeStatusField, changeThresholdField, allPeriodList, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理CompanySearchService返回空结果的情况', async () => {
      // Arrange
      const isPEVCField = new DimensionHitStrategyFieldsEntity();
      isPEVCField.fieldValue = [1];
      isPEVCField.compareType = DimensionFieldCompareTypeEnums.Equal;

      const newItem = {
        ChangeExtend: {
          PartInfo: {
            H: [{ K: 'pevc-keyno-1' }],
            F: [{ K: 'pevc-keyno-2' }],
          },
        },
      };

      const keyNo = 'test-keyno';

      // Mock空的查询结果
      mockCompanySearchService.companySearchForKys.mockResolvedValue({
        Result: [], // 空结果
        GroupItems: [],
        Paging: { PageIndex: 1, PageSize: 10, TotalRecords: { value: 0, relation: 'eq' } },
        Status: 200,
        scrollId: '',
      });

      // Act
      const result = await helper.category72isPEVCField(isPEVCField, newItem, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });
});
