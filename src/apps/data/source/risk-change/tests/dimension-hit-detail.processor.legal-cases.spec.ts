import { Test, TestingModule } from '@nestjs/testing';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { BaseHelper } from '../helper/base.helper';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

const createTestStrategyField = (fieldKey: DimensionFieldKeyEnums, fieldValue: any): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  return field;
};

describe('DimensionHitDetailProcessor 法律案件类测试', () => {
  let processor: DimensionHitDetailProcessor;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockBaseHelper: jest.Mocked<BaseHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockRiskChangeHelper = {
      category4: jest.fn(),
      category49: jest.fn(),
      category18: jest.fn(),
      category7: jest.fn(),
      category27: jest.fn(),
      category90: jest.fn(),
      caseReasonTypeField: jest.fn(),
      checkCaseTypeField: jest.fn(),
      checkAmountField: jest.fn(),
      checkContractDisputeField: jest.fn(),
      checkFinancialReasonField: jest.fn(),
      checkBankOrFinancialLeasingField4: jest.fn(),
      checkBankOrFinancialLeasingField49: jest.fn(),
      checkBankOrFinancialLeasingField18: jest.fn(),
      checkBankOrFinancialLeasingField7: jest.fn(),
      checkBankOrFinancialLeasingField27: jest.fn(),
      checkBankOrFinancialLeasingField90: jest.fn(),
      auctionTypeField: jest.fn(),
      limitPriceTypeField: jest.fn(),
    } as any;

    mockPersonHelper = {
      getEmployeeData: jest.fn(),
    } as any;

    mockBaseHelper = {
      filterLastYearData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DimensionHitDetailProcessor,
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: BaseHelper, useValue: mockBaseHelper },
      ],
    }).compile();

    processor = module.get<DimensionHitDetailProcessor>(DimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('裁判文书 (category4)', () => {
    it('应该正确处理裁判文书类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-judgment-doc',
        Category: RiskChangeCategoryEnum.category4,
        ChangeExtend: JSON.stringify({
          I: '1000000', // 诉讼金额
          CaseType: '民事',
          CaseReason: '合同纠纷',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const judicialRoleField = createTestStrategyField(DimensionFieldKeyEnums.judicialRole, [1, 2]);
      const caseReasonTypeField = createTestStrategyField(DimensionFieldKeyEnums.CaseReasonType, ['合同纠纷']);
      const caseTypeField = createTestStrategyField(DimensionFieldKeyEnums.CaseType, ['民事']);
      const lawsuitAmountField = createTestStrategyField(DimensionFieldKeyEnums.lawsuitAmount, [50, 200]);
      dimension.strategyFields = [judicialRoleField, caseReasonTypeField, caseTypeField, lawsuitAmountField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category4.mockReturnValue(true);
      mockRiskChangeHelper.caseReasonTypeField.mockReturnValue(true);
      mockRiskChangeHelper.checkCaseTypeField.mockReturnValue(true);
      mockRiskChangeHelper.checkAmountField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-judgment-doc');
      expect(mockRiskChangeHelper.category4).toHaveBeenCalled();
      expect(mockRiskChangeHelper.caseReasonTypeField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.checkCaseTypeField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.checkAmountField).toHaveBeenCalledWith(
        lawsuitAmountField,
        1000000, // processAmountString 处理后的金额
        1,
      );
    });

    it('应该正确处理带有合同纠纷和金融原因字段的裁判文书', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-judgment-contract',
        Category: RiskChangeCategoryEnum.category4,
        ChangeExtend: JSON.stringify({
          I: '5000000',
          CaseType: '民事',
          CaseReason: '合同纠纷',
          IsContract: true,
          IsFinancial: true,
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const isContractDisputeField = createTestStrategyField(DimensionFieldKeyEnums.isContractDispute, [true]);
      const isFinancialReasonField = createTestStrategyField(DimensionFieldKeyEnums.isFinancialReason, [true]);
      const isBankOrFlField = createTestStrategyField(DimensionFieldKeyEnums.isBankOrFinancialLeasing, [true]);
      dimension.strategyFields = [isContractDisputeField, isFinancialReasonField, isBankOrFlField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.checkContractDisputeField.mockReturnValue(true);
      mockRiskChangeHelper.checkFinancialReasonField.mockReturnValue(true);
      mockRiskChangeHelper.checkBankOrFinancialLeasingField4.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-judgment-contract');
      expect(mockRiskChangeHelper.checkContractDisputeField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.checkFinancialReasonField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.checkBankOrFinancialLeasingField4).toHaveBeenCalled();
    });
  });

  describe('立案信息 (category49)', () => {
    it('应该正确处理立案信息类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-case-filing',
        Category: RiskChangeCategoryEnum.category49,
        ChangeExtend: JSON.stringify({
          CaseType: '刑事',
          CaseReason: '诈骗',
          JudicialRole: '被告',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const judicialRoleField = createTestStrategyField(DimensionFieldKeyEnums.judicialRole, [2]); // 被告
      const caseReasonTypeField = createTestStrategyField(DimensionFieldKeyEnums.CaseReasonType, ['诈骗']);
      const caseTypeField = createTestStrategyField(DimensionFieldKeyEnums.CaseType, ['刑事']);
      dimension.strategyFields = [judicialRoleField, caseReasonTypeField, caseTypeField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category49.mockReturnValue(true);
      mockRiskChangeHelper.caseReasonTypeField.mockReturnValue(true);
      mockRiskChangeHelper.checkCaseTypeField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-case-filing');
      expect(mockRiskChangeHelper.category49).toHaveBeenCalled();
      expect(mockRiskChangeHelper.caseReasonTypeField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.checkCaseTypeField).toHaveBeenCalled();
    });

    it('应该正确处理带有银行或金融租赁字段的立案信息', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-case-filing-bank',
        Category: RiskChangeCategoryEnum.category49,
        ChangeExtend: JSON.stringify({
          CaseType: '民事',
          CaseReason: '金融借款合同纠纷',
          IsBankOrFL: true,
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const isBankOrFlField = createTestStrategyField(DimensionFieldKeyEnums.isBankOrFinancialLeasing, [true]);
      dimension.strategyFields = [isBankOrFlField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.checkBankOrFinancialLeasingField49.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-case-filing-bank');
      expect(mockRiskChangeHelper.checkBankOrFinancialLeasingField49).toHaveBeenCalled();
    });
  });

  describe('开庭公告 (category18)', () => {
    it('应该正确处理开庭公告类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-court-announcement',
        Category: RiskChangeCategoryEnum.category18,
        ChangeExtend: JSON.stringify({
          CaseType: '民事',
          CaseReason: '买卖合同纠纷',
          CourtDate: '2024-01-15',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const judicialRoleField = createTestStrategyField(DimensionFieldKeyEnums.judicialRole, [1, 2]);
      const caseReasonTypeField = createTestStrategyField(DimensionFieldKeyEnums.CaseReasonType, ['买卖合同纠纷']);
      dimension.strategyFields = [judicialRoleField, caseReasonTypeField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category18.mockReturnValue(true);
      mockRiskChangeHelper.caseReasonTypeField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-court-announcement');
      expect(mockRiskChangeHelper.category18).toHaveBeenCalled();
      expect(mockRiskChangeHelper.caseReasonTypeField).toHaveBeenCalled();
    });
  });

  describe('法院公告 (category7)', () => {
    it('应该正确处理法院公告类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-court-notice',
        Category: RiskChangeCategoryEnum.category7,
        ChangeExtend: JSON.stringify({
          CaseType: '执行',
          CaseReason: '申请执行',
          NoticeType: '执行公告',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const judicialRoleField = createTestStrategyField(DimensionFieldKeyEnums.judicialRole, [3]); // 被执行人
      const caseTypeField = createTestStrategyField(DimensionFieldKeyEnums.CaseType, ['执行']);
      dimension.strategyFields = [judicialRoleField, caseTypeField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category7.mockReturnValue(true);
      mockRiskChangeHelper.checkCaseTypeField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-court-notice');
      expect(mockRiskChangeHelper.category7).toHaveBeenCalled();
      expect(mockRiskChangeHelper.checkCaseTypeField).toHaveBeenCalled();
    });
  });

  describe('送达公告 (category27)', () => {
    it('应该正确处理送达公告类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-service-announcement',
        Category: RiskChangeCategoryEnum.category27,
        ChangeExtend: JSON.stringify({
          CaseType: '民事',
          CaseReason: '劳动争议',
          ServiceType: '传票送达',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const judicialRoleField = createTestStrategyField(DimensionFieldKeyEnums.judicialRole, [2]); // 被告
      dimension.strategyFields = [judicialRoleField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category27.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-service-announcement');
      expect(mockRiskChangeHelper.category27).toHaveBeenCalled();
    });
  });

  describe('诉前调解 (category90)', () => {
    it('应该正确处理诉前调解类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-pre-litigation-mediation',
        Category: RiskChangeCategoryEnum.category90,
        ChangeExtend: JSON.stringify({
          CaseType: '民事',
          CaseReason: '债权债务纠纷',
          MediationResult: '调解成功',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const judicialRoleField = createTestStrategyField(DimensionFieldKeyEnums.judicialRole, [1, 2]);
      const caseReasonTypeField = createTestStrategyField(DimensionFieldKeyEnums.CaseReasonType, ['债权债务纠纷']);
      dimension.strategyFields = [judicialRoleField, caseReasonTypeField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category90.mockReturnValue(true);
      mockRiskChangeHelper.caseReasonTypeField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-pre-litigation-mediation');
      expect(mockRiskChangeHelper.category90).toHaveBeenCalled();
      expect(mockRiskChangeHelper.caseReasonTypeField).toHaveBeenCalled();
    });
  });

  describe('司法拍卖 (category57)', () => {
    it('应该正确处理司法拍卖类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-judicial-auction',
        Category: RiskChangeCategoryEnum.category57,
        ChangeExtend: JSON.stringify({
          AuctionType: '房产拍卖',
          StartPrice: '1000000',
          AuctionDate: '2024-02-01',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const auctionTypeField = createTestStrategyField(DimensionFieldKeyEnums.auctionType, ['房产拍卖']);
      const limitPriceField = createTestStrategyField(DimensionFieldKeyEnums.listingPrice, [500000, 2000000]);
      dimension.strategyFields = [auctionTypeField, limitPriceField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.auctionTypeField.mockReturnValue(true);
      mockRiskChangeHelper.limitPriceTypeField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-judicial-auction');
      expect(mockRiskChangeHelper.auctionTypeField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.limitPriceTypeField).toHaveBeenCalled();
    });
  });
});
