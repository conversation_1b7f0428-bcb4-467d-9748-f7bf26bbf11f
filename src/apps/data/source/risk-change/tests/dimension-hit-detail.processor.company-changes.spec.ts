import { Test, TestingModule } from '@nestjs/testing';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { BaseHelper } from '../helper/base.helper';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

const createTestStrategyField = (fieldKey: DimensionFieldKeyEnums, fieldValue: any): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  return field;
};

describe('DimensionHitDetailProcessor 公司变更类测试', () => {
  let processor: DimensionHitDetailProcessor;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockBaseHelper: jest.Mocked<BaseHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockRiskChangeHelper = {
      hitLayTypesField: jest.fn(),
      hitCompChangeRoleField: jest.fn(),
      changeThresholdField: jest.fn(),
      hitCurrencyChangeField: jest.fn(),
      hitRegisCapitalTrendField: jest.fn(),
      hitRegisCapitalChangeRatioField: jest.fn(),
      hitPeriodRegisCapitalField: jest.fn(),
      category38: jest.fn(),
    } as any;

    mockPersonHelper = {
      getEmployeeData: jest.fn(),
    } as any;

    mockBaseHelper = {
      filterLastYearData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DimensionHitDetailProcessor,
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: BaseHelper, useValue: mockBaseHelper },
      ],
    }).compile();

    processor = module.get<DimensionHitDetailProcessor>(DimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('经营地址变更 (category139)', () => {
    it('应该正确处理经营地址变更类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-address-change',
        Category: RiskChangeCategoryEnum.category139,
        ChangeExtend: JSON.stringify({
          D: Math.floor(Date.now() / 1000), // 当前时间戳
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockBaseHelper.filterLastYearData.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-address-change');
      expect(mockBaseHelper.filterLastYearData).toHaveBeenCalledWith(
        expect.objectContaining({
          Id: 'test-address-change',
          Category: RiskChangeCategoryEnum.category139,
        }),
      );
    });

    it('应该在filterLastYearData返回false时不命中', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-address-change-no-hit',
        Category: RiskChangeCategoryEnum.category139,
        ChangeExtend: JSON.stringify({
          D: Math.floor(Date.now() / 1000) - 365 * 24 * 60 * 60 * 2, // 2年前
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockBaseHelper.filterLastYearData.mockReturnValue(false);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(0);
      expect(mockBaseHelper.filterLastYearData).toHaveBeenCalled();
    });
  });

  describe('法定代表人变更 (category39)', () => {
    it('应该正确处理法定代表人变更类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-legal-rep-change',
        Category: RiskChangeCategoryEnum.category39,
        ChangeExtend: JSON.stringify({
          A: '张三',
          B: '李四',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-legal-rep-change');
    });

    it('应该正确处理带有策略字段的法定代表人变更', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-legal-rep-with-strategy',
        Category: RiskChangeCategoryEnum.category39,
        ChangeExtend: JSON.stringify({
          A: '原法人',
          B: '新法人',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const layTypesField = createTestStrategyField(DimensionFieldKeyEnums.layTypes, [1, 2]);
      dimension.strategyFields = [layTypesField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.hitLayTypesField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-legal-rep-with-strategy');
      expect(mockRiskChangeHelper.hitLayTypesField).toHaveBeenCalledWith(
        layTypesField,
        expect.objectContaining({
          Id: 'test-legal-rep-with-strategy',
          Category: RiskChangeCategoryEnum.category39,
          ChangeExtend: {
            A: '原法人',
            B: '新法人',
          },
        }),
      );
    });
  });

  describe('主要人员变更 (category46)', () => {
    it('应该正确处理主要人员变更', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-personnel-change',
        Category: RiskChangeCategoryEnum.category46,
        ChangeExtend: JSON.stringify({
          PersonName: '张三',
          Role: '董事',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const compChangeRoleField = createTestStrategyField(DimensionFieldKeyEnums.compChangeRole, [1, 2]);
      dimension.strategyFields = [compChangeRoleField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.hitCompChangeRoleField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-personnel-change');
      expect(mockRiskChangeHelper.hitCompChangeRoleField).toHaveBeenCalled();
    });

    it('应该正确处理带有基准日期和变更阈值的主要人员变更', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-personnel-with-threshold',
        Category: RiskChangeCategoryEnum.category46,
        ChangeExtend: JSON.stringify({
          PersonName: '张三',
          Role: '董事',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const compChangeRoleField = createTestStrategyField(DimensionFieldKeyEnums.compChangeRole, [1, 2]);
      const baselineDateField = createTestStrategyField(DimensionFieldKeyEnums.baselineDate, [1]);
      const changeThresholdField = createTestStrategyField(DimensionFieldKeyEnums.changeThreshold, [0.5]);
      dimension.strategyFields = [compChangeRoleField, baselineDateField, changeThresholdField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Mock ES搜索返回
      const mockPeriodResponse = {
        Paging: { TotalRecords: 5 },
        Result: [
          { Id: 'period1', Category: RiskChangeCategoryEnum.category46 },
          { Id: 'period2', Category: RiskChangeCategoryEnum.category46 },
        ],
      };
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 5 },
            hits: mockPeriodResponse.Result.map((r) => ({ _source: r })),
          },
        },
      });

      // Mock 人员数据
      const mockPersonData = [
        { keyNo: 'person1', name: '张三' },
        { keyNo: 'person2', name: '李四' },
      ];
      mockPersonHelper.getEmployeeData.mockResolvedValue(mockPersonData);

      mockRiskChangeHelper.hitCompChangeRoleField.mockReturnValue(true);
      mockRiskChangeHelper.changeThresholdField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-personnel-with-threshold');
      expect(mockPersonHelper.getEmployeeData).toHaveBeenCalledTimes(2); // IpoEmployees 和 Employees
      expect(mockRiskChangeHelper.changeThresholdField).toHaveBeenCalled();
    });
  });

  describe('注册资本变更 (category37)', () => {
    it('应该正确处理注册资本变更', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-capital-change',
        Category: RiskChangeCategoryEnum.category37,
        ChangeExtend: JSON.stringify({
          A: '1000万',
          B: '2000万',
          Currency: 'CNY',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const currencyChangeField = createTestStrategyField(DimensionFieldKeyEnums.currencyChange, ['CNY']);
      const regisCapitalTrendField = createTestStrategyField(DimensionFieldKeyEnums.regisCapitalTrend, [1]); // 上升
      dimension.strategyFields = [currencyChangeField, regisCapitalTrendField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.hitCurrencyChangeField.mockReturnValue(true);
      mockRiskChangeHelper.hitRegisCapitalTrendField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-capital-change');
      expect(mockRiskChangeHelper.hitCurrencyChangeField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.hitRegisCapitalTrendField).toHaveBeenCalled();
    });

    it('应该正确处理带有周期注册资本字段的注册资本变更', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-capital-with-period',
        Category: RiskChangeCategoryEnum.category37,
        ChangeExtend: JSON.stringify({
          A: '1000万',
          B: '2000万',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const periodRegisCapitalField = createTestStrategyField(DimensionFieldKeyEnums.periodRegisCapital, [{ valuePeriodBaseLine: 1 }]);
      dimension.strategyFields = [periodRegisCapitalField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Mock ES搜索返回
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 3 },
            hits: [
              { _source: { Id: 'period1', Category: RiskChangeCategoryEnum.category37 } },
              { _source: { Id: 'period2', Category: RiskChangeCategoryEnum.category37 } },
            ],
          },
        },
      });

      mockRiskChangeHelper.hitPeriodRegisCapitalField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-capital-with-period');
      expect(mockRiskChangeHelper.hitPeriodRegisCapitalField).toHaveBeenCalled();
    });
  });

  describe('经营状态变更 (category38)', () => {
    it('应该正确处理经营状态变更', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-business-status-change',
        Category: RiskChangeCategoryEnum.category38,
        ChangeExtend: JSON.stringify({
          A: '正常',
          B: '注销',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const businessStatusField = createTestStrategyField(DimensionFieldKeyEnums.businessStatus, ['注销']);
      dimension.strategyFields = [businessStatusField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category38.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-business-status-change');
      expect(mockRiskChangeHelper.category38).toHaveBeenCalledWith(
        businessStatusField,
        expect.objectContaining({
          Id: 'test-business-status-change',
          Category: RiskChangeCategoryEnum.category38,
        }),
      );
    });
  });
});
