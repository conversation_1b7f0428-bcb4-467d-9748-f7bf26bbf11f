import { Test, TestingModule } from '@nestjs/testing';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { CaseReasonHelper } from '../helper/case-reason.helper';
import { RelatedDimensionHitDetailProcessor } from '../related-dimension-hit-detail.processor';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { Risk<PERSON>hangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

const createMockStrategyField = (
  fieldKey: DimensionFieldKeyEnums,
  fieldValue: any[],
  compareType?: DimensionFieldCompareTypeEnums,
): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  field.compareType = compareType;
  return field;
};

describe('RelatedDimensionHitDetailProcessor fetchHits 基础逻辑测试', () => {
  let processor: RelatedDimensionHitDetailProcessor;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockCompanyDetailService: jest.Mocked<CompanyDetailService>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockRiskChangeHitDetailAnalyzer: jest.Mocked<DimensionHitDetailProcessor>;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockPersonHelper = {
      getFinalActualController: jest.fn(),
      getPartnerList: jest.fn(),
    } as any;

    mockCompanyDetailService = {
      getInvestCompany: jest.fn(),
    } as any;

    mockCompanySearchService = {
      companyDetailsQcc: jest.fn(),
    } as any;

    mockRiskChangeHitDetailAnalyzer = {
      commonCivilRiskChange: jest.fn(),
    } as any;

    mockRiskChangeHelper = {
      detailAnalyzeForRelated: jest.fn(),
      hitIndustryThresholdField: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      getCaseTitleDescData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RelatedDimensionHitDetailProcessor,
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: CompanyDetailService, useValue: mockCompanyDetailService },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: DimensionHitDetailProcessor, useValue: mockRiskChangeHitDetailAnalyzer },
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
      ],
    }).compile();

    processor = module.get<RelatedDimensionHitDetailProcessor>(RelatedDimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchHits 基础功能测试', () => {
    it('应该返回正确的初始响应结构', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result).toBeInstanceOf(HitDetailsBaseResponse);
      expect(result.Paging).toEqual({
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 0,
      });
      expect(result.Result).toEqual([]); // 修改：无数据时返回空数组，不是undefined
    });

    it('应该在没有关联方角色类型时返回空结果', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      // 不设置 relatedRoleType 字段
      dimension.strategyFields = [];

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(result.Result).toEqual([]); // 修改：无数据时返回空数组，不是undefined
    });

    it('应该在没有风险类别时返回空结果', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, ['ActualController']),
        // 不设置 riskCategories 字段
      ];

      // Mock 相关服务返回数据
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(result.Result).toEqual([]); // 修改：无数据时返回空数组，不是undefined
    });

    it('应该正确构建ES查询条件', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, ['ActualController']),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1', 'category2']),
        createMockStrategyField(DimensionFieldKeyEnums.isValid, [1]),
      ];

      // Mock 相关服务返回数据
      mockPersonHelper.getFinalActualController.mockResolvedValue([
        { keyNo: 'person1', name: '实控人1' },
        { keyNo: 'person2', name: '实控人2' },
      ]);

      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 0 },
            hits: [],
          },
        },
      });

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      await processor.fetchHits(dimension, params);

      // Assert
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          from: 0,
          size: 1000,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                { range: { Es_Version: { lt: 999999 } } },
                { terms: { KeyNo: ['person1', 'person2'] } },
                { term: { IsValid: 1 } },
                { terms: { Category: ['category1', 'category2'] } },
              ]),
            }),
          }),
        }),
        'test-company-id',
      );
    });

    it('应该正确处理维度过滤器的时间范围', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, ['ActualController']),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];
      dimension.dimensionFilter = {
        startTime: 1640995200000, // 2022-01-01
        endTime: 1672531199000, // 2022-12-31
      };

      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 0 },
            hits: [],
          },
        },
      });

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      await processor.fetchHits(dimension, params);

      // Assert
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                {
                  range: {
                    CreateDate: {
                      gte: Math.ceil(1640995200000),
                      lte: Math.ceil(1672531199000),
                    },
                  },
                },
              ]),
            }),
          }),
        }),
        'test-company-id',
      );
    });

    it('应该正确处理维度过滤器的ID', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, ['ActualController']),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];
      dimension.dimensionFilter = {
        id: 'specific-dynamic-id',
      };

      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 0 },
            hits: [],
          },
        },
      });

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      await processor.fetchHits(dimension, params);

      // Assert
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([{ term: { Id: 'specific-dynamic-id' } }]),
            }),
          }),
        }),
        'test-company-id',
      );
    });

    it('应该正确处理排序字段', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, ['ActualController']),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.sortField, [['CreateDate', 'asc']]),
      ];

      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 0 },
            hits: [],
          },
        },
      });

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
        field: 'UpdateDate',
        order: 'DESC',
      };

      // Act
      await processor.fetchHits(dimension, params);

      // Assert
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          sort: { UpdateDate: 'DESC' },
        }),
        'test-company-id',
      );
    });
  });
});
