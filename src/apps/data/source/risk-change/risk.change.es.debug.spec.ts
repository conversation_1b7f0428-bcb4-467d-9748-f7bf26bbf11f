import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON>tyManager, getManager } from 'typeorm';
import { AppTestModule } from '../../../app/app.test.module';
import { DataModule } from '../../data.module';
import { RiskChangeEsSource } from './risk-change-es.source';
import { DimensionTypeEnums } from '../../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { HitDetailsBaseQueryParams } from '../../../../libs/model/diligence/details/request';
import { getDimensionHitStrategyPO } from '../../../test_utils_module/dimension.test.utils';
import { RelatedTypeEnums } from '../../../../libs/enums/dimension/RelatedTypeEnums';
import {
  ChangeStatusMap,
  CurrencyChangeMap,
  HolderRoleType,
  IsBPMap,
  IsPEVCMap,
  LayTypeMap,
  RealRegistrationErrorMap,
  RegisCapitalTrendMap,
  ShareChangeStatusMap,
  SimpleCancelTypeConstant,
} from '../../../../libs/constants/risk.change.constants';
import { NegativePositiveTopicTypes } from '../../../../libs/constants/news.constants';
import { ProcessingAgencyLevelOneMap } from '../../../../libs/constants/punish.constants';
import { DimensionFieldInputTypeEnums } from '../../../../libs/enums/dimension/DimensionFieldInputTypeEnums';
import { RelatedCompanySource } from '../related-company.source';
import { CompanyApiSource } from '../company-api.source';
import { AssertESSource } from '../asset-es.source';
import { IsHistoryPatentConstant, PatentStatusConstant, PatentTypeConstant } from '../../../../libs/constants/company.constants';
import { EnterpriseLibApiSource } from '../enterprise-lib-api.source';
import { PledgeMergerEsSource } from '../pledge-merger-es.source';

jest.setTimeout(60 * 1000);
describe('test risk change service', () => {
  let riskChangeService: RiskChangeEsSource;
  let nebulaGraphService: RelatedCompanySource;
  let assertESService: AssertESSource;
  let pledgeMergerEsService: PledgeMergerEsSource;
  let companyService: CompanyApiSource;
  let entityManager: EntityManager;
  let enterpriseLibService: EnterpriseLibApiSource;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    riskChangeService = module.get<RiskChangeEsSource>(RiskChangeEsSource);
    nebulaGraphService = module.get<RelatedCompanySource>(RelatedCompanySource);
    companyService = module.get<CompanyApiSource>(CompanyApiSource);
    assertESService = module.get<AssertESSource>(AssertESSource);
    pledgeMergerEsService = module.get<PledgeMergerEsSource>(PledgeMergerEsSource);
    enterpriseLibService = module.get<EnterpriseLibApiSource>(EnterpriseLibApiSource);
    entityManager = getManager();
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  it('【注册资本-发生减资】', async () => {
    const id = 'c6ccf9a7cc25213187131a77f327c6d6';
    const companyId = 'f1fa0fc445c7af80acb2fa6474b7a3f8';
    const companyName = '深圳市南网传媒有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [37],
        options: [{ value: 37, label: '注册资本' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.currencyChange,
        fieldValue: [0],
        options: CurrencyChangeMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.regisCapitalTrend,
        fieldValue: [1],
        options: RegisCapitalTrendMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【注册资本-工商状态变动(吊销)】', async () => {
    const id = 'ec928d97ec8a92f7edc260855c9f6ef7';
    const companyId = 'b6fede74358fc8364648161dbdc0fe2e';
    const companyName = '深圳市恒森旅行社有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [38],
        options: [{ value: 38, label: '经营状态' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.businessStatus,
        fieldValue: [90],
        options: [{ label: '吊销', value: 90 }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【工商状态变动】-简易注销', async () => {
    const id = '2421c031ddd0e496661eb1474e934f6e';
    const companyId = '6283adc9c8729b25217b2f24db8e217c';
    const companyName = '义乌市金鹁鸪醋业股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [23],
        options: [{ value: 23, label: '简易注销' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.simpleCancelType,
        fieldValue: [1, 2, 3, 4, 5, 6],
        options: SimpleCancelTypeConstant,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【工商状态变动】-注销备案', async () => {
    const id = '846ba375989f27a04ebf67f631d6ed6e';
    const companyId = '24077b1a38d591fd28d3a88f5ad24a5f';
    const companyName = '深圳市伽呈新能源有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [61],
        options: [{ value: 61, label: '注销备案' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【第一大股东持股比例变化幅度超过20%】', async () => {
    const id = '73e9c63e2569ca90136f3b295b1a9fe4';
    const companyId = 'fe2da491d1d4365ab1d822f4907608f7';
    const companyName = '金天地（深圳）贵金属数字科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [44],
        options: [{ value: 44, label: '股东股比变更' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
        fieldValue: [0],
        options: ShareChangeStatusMap,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.differenceRatio,
        fieldValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1],
        options: HolderRoleType,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【第一大股东持股比例变化绝对值减少超过10%】', async () => {
    const id = '73e9c63e2569ca90136f3b295b1a9fe4';
    const companyId = 'fe2da491d1d4365ab1d822f4907608f7';
    const companyName = '金天地（深圳）贵金属数字科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [44],
        options: [{ value: 44, label: '股东股比变更' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
        fieldValue: [0],
        options: ShareChangeStatusMap,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.absRatio,
        fieldValue: [10],
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1],
        options: HolderRoleType,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控人持股比例变化幅度超过20%】', async () => {
    const id = 'bb864a67087c3a778ee9da077deb1543';
    const companyId = '64fcaaaeeb2e1eb916032efbdcf70dc3';
    const companyName = '北京前程合信人力资源有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [44],
        options: [{ value: 44, label: '股东股比变更' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
        fieldValue: [0],
        options: ShareChangeStatusMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.differenceRatio,
        fieldValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [2],
        options: HolderRoleType,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控人持股比例变化绝对值减少超过10%】', async () => {
    const id = 'bb864a67087c3a778ee9da077deb1543';
    const companyId = '64fcaaaeeb2e1eb916032efbdcf70dc3';
    const companyName = '北京前程合信人力资源有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [44],
        options: [{ value: 44, label: '股东股比变更' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
        fieldValue: [0],
        options: ShareChangeStatusMap,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.absRatio,
        fieldValue: [10],
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [2],
        options: HolderRoleType,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【控股股东股比从大于50% 降低至小于50%】', async () => {
    const id = 'bb864a67087c3a778ee9da077deb1543';
    const companyId = '64fcaaaeeb2e1eb916032efbdcf70dc3';
    const companyName = '北京前程合信人力资源有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [44],
        options: [{ value: 44, label: '股东股比变更' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
        fieldValue: [0],
        options: ShareChangeStatusMap,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1],
        options: HolderRoleType,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.beforeContent,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.afterContent,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【第一大股东变更】', async () => {
    const id = 'b24ef93ae9d498b363a9b9abb691831e';
    const companyId = 'd721571c612945f30db2e8339630f708';
    const companyName = '深圳市容航科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [24],
        options: [{ value: 24, label: '大股东变动' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【第一大股东变更-成员变更】', async () => {
    const id = '39355b8009438febea2ad5809139af68';
    const companyId = 'd721571c612945f30db2e8339630f708';
    const companyName = '深圳市容航科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [72],
        options: [{ value: 72, label: '成员变更' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [1],
        options: IsBPMap,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【股权变动-新增股权冻结】', async () => {
    const id = '7d026ec39a7a2c94cbaf08808c63986e';
    const companyId = '6bc7e7ccdb755391651316a0227c059b';
    const companyName = '万科企业股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [26],
        options: [{ value: 26, label: '股权冻结' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityFreezeScope,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '企业股权被冻结' },
          { value: 2, label: '持有股权被冻结' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it(
    '投资变动-近12个月内公司对外投资企业大量注销或吊销',
    async () => {
      const companyId = '013bf839e4e1c85ba19e6206f66e0bb2';
      const companyName = '新农创美丽乡村（枣庄市山亭区）产业发展有限公司';
      const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RecentInvestCancellationsRiskChange, [
        {
          fieldKey: DimensionFieldKeyEnums.isValid,
          fieldValue: [1],
          accessScope: 1,
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.timePeriod,
          fieldValue: [12],
          options: [{ unit: '月', min: 1, max: 12 }],
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.Equal,
        },
        {
          fieldKey: DimensionFieldKeyEnums.hitCount,
          fieldValue: [1],
          compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        },
        {
          fieldKey: DimensionFieldKeyEnums.relatedRoleType,
          fieldValue: [RelatedTypeEnums.InvestCompany],
          options: [{ value: RelatedTypeEnums.InvestCompany, label: '对外投资' }],
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.fundedRatioLevel,
          fieldValue: [0],
          options: [
            { value: 0, label: '不限' },
            { value: 1, label: '<=5%' },
            { value: 2, label: '>5%' },
            { value: 3, label: '>20%' },
            { value: 4, label: '>50%' },
            { value: 5, label: '>66.66%' },
            { value: 6, label: '=100%' },
          ],
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.riskCategories,
          fieldValue: [38],
          options: [{ value: 38, label: '经营状态' }],
          accessScope: 2,
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.businessStatus,
          fieldValue: [90, 99],
          options: [
            { label: '吊销', value: 90 },
            { label: '注销', value: 99 },
          ],
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
      ]);
      const detail = await riskChangeService.analyze(companyId, [dimension]);
      expect(detail[0]?.totalHits).toEqual(1);
      const result = await riskChangeService.getDimensionDetail(
        dimension,
        Object.assign(
          new HitDetailsBaseQueryParams(),
          {
            keyNo: companyId,
            pageIndex: 1,
            pageSize: 10,
          },
          { keyNo: companyId, companyName },
          '',
        ),
      );
      expect(result).not.toBeNull();
      expect(result.Paging.TotalRecords).toEqual(1);
    },
    3 * 60 * 1000,
  );

  it('【实控变动-实际控制人变更】', async () => {
    const id = '1f268729ef05426578fe3e9e5d275961';
    const companyId = '6583579d4d007d7daab586c94d592e93';
    const companyName = '优合集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [25],
        options: [{ value: 25, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控变动】- 近2年内实控人总持股（非直接持股）合计减持超过10%，排除VC、PE等节点', async () => {
    const id = 'c898e2e91421bdb9c15058cc75bb7d63';
    const companyId = '1e5ae70583905068b60d6c7b07bc6d1c';
    const companyName = '上海畅德医疗科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [72],
        options: [{ value: 72, label: '成员变更' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [2],
        options: HolderRoleType,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
        fieldValue: [0],
        options: ShareChangeStatusMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isPEVC,
        fieldValue: [0],
        options: IsPEVCMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.periodShareRatioChange,
        fieldValue: [
          {
            timePeriod: 24,
            shareChangeStatus: 0,
            shareChangeRate: 10,
            shareChangeRateCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
          },
        ],
        options: [
          {
            timePeriod: {
              label: '时间周期',
              unit: '月',
              min: 1,
              max: 12,
              inputType: DimensionFieldInputTypeEnums.Text,
            },
            shareChangeRate: {
              label: '变更比例>',
              unit: '%',
              min: 0,
              max: 100,
              inputType: DimensionFieldInputTypeEnums.Text,
            },
            // shareChangeRateCompareType: {
            //   label: '占比比较(大于/小于)',
            //   value: DimensionFieldCompareTypeEnums.GreaterThan,
            // },
          },
        ],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控变动-实控人近12个月内新增控股子公司数量超过xx个则触发动态预警】', async () => {
    const id = 'f2a787c13fec94acf7200884f3011021';
    const companyId = 'f85e524f63f38f01a8b56656e24bcdf1';
    const companyName = '重庆机电控股（集团）公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17, 203],
        options: [
          { value: 17, label: '对外投资' },
          { value: 203, label: '对外投资(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [10],
        options: [{ unit: '月', min: 1, max: 12 }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.thresholdCount,
        fieldValue: [1],
        options: [{ unit: '个', min: 1, max: 1000 }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [1],
        options: ChangeStatusMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [1],
        options: IsBPMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(2);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(2);
  });

  it('【实控变动-实控人近12个月内减少控股子公司数量超过xx个则触发动态预警】', async () => {
    const id = '10b5b7c94f75a0e2b0960d4c3dd86453';
    const companyId = 'f37bbffa58c81ff76ac86aefa056e8be';
    const companyName = '深圳市创新投资集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17, 203],
        options: [
          { value: 17, label: '对外投资' },
          { value: 203, label: '对外投资(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [12],
        options: [{ unit: '月', min: 1, max: 12 }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.thresholdCount,
        fieldValue: [1],
        options: [{ unit: '个', min: 1, max: 1000 }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [2],
        options: ChangeStatusMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [2],
        options: IsBPMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控变动-实际控制人近期新增涉房控股公司】', async () => {
    //const id = 'e737c89451b3e745e9db4df5f32d86a8';
    const companyId = '26f32964f1a49ee10aff6bd74636ad77';
    const companyName = '唐山冀东水泥股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17, 203],
        options: [
          { value: 17, label: '对外投资' },
          { value: 203, label: '对外投资(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [1],
        options: ChangeStatusMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [1],
        options: IsBPMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companySocpe,
        fieldValue: ['土地开发', '地产开发', '商品房销售', '房地产项目投资'],
        options: [
          { value: '土地开发', label: '土地开发' },
          { value: '地产开发', label: '地产开发' },
          { value: '商品房销售', label: '商品房销售' },
          { value: '房地产项目投资', label: '房地产项目投资' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    /* dimension.dimensionFilter = {
       id,
     };*/
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控变动-实际控制人近期新增涉房控股公司-国标行业】', async () => {
    const companyId = '26f32964f1a49ee10aff6bd74636ad77';
    const companyName = '唐山冀东水泥股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17, 203],
        options: [
          { value: 17, label: '对外投资' },
          { value: 203, label: '对外投资(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [1],
        options: ChangeStatusMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [1],
        options: IsBPMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyIndustry,
        fieldValue: ['K-70-701'],
        options: [{ value: 'K-70-701', label: '房地产开发经营' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    /*dimension.dimensionFilter = {
      id,
    };*/
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实际控制人近期新增涉房控股公司-企查查行业】', async () => {
    const id = 'e737c89451b3e745e9db4df5f32d86a8';
    const companyId = '26f32964f1a49ee10aff6bd74636ad77';
    const companyName = '唐山冀东水泥股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17, 203],
        options: [
          { value: 17, label: '对外投资' },
          { value: 203, label: '对外投资(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [1],
        options: ChangeStatusMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [1],
        options: IsBPMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.qccIndustry,
        fieldValue: ['28-2801'],
        options: [{ value: '28-2801', label: '房地产开发' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实际控制人近期新增控股公司涉金融】', async () => {
    const companyId = 'f0cccdcb738fa76577b3507f218748a2';
    const companyName = '南通天沃科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17, 203],
        options: [
          { value: 17, label: '对外投资' },
          { value: 203, label: '对外投资(人员)' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [1],
        options: ChangeStatusMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.afterContent,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyName,
        fieldValue: ['小额贷款', '互联网金融', '典当', '保理', '担保', '融资租赁'],
        options: [
          { value: '小额贷款', label: '小额贷款' },
          { value: '互联网金融', label: '互联网金融' },
          { value: '典当', label: '典当' },
          { value: '保理', label: '保理' },
          { value: '担保', label: '担保' },
          { value: '融资租赁', label: '融资租赁' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实际控制人近期新增控股公司涉金融-企查查行业】', async () => {
    const companyId = '2ee57c0916a085efd831f63fd2b2e12b';
    const companyName = '厦门集美市政集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17, 203],
        options: [
          { value: 17, label: '对外投资' },
          { value: 203, label: '对外投资(人员)' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [1],
        options: ChangeStatusMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.afterContent,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.qccIndustry,
        fieldValue: ['29'],
        options: [{ value: '29', label: '金融业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    /* dimension.dimensionFilter = {
       id,
     };*/
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实际控制人近期新增控股公司涉金融】', async () => {
    const companyId = '096e8a1d034d6eb96e3bc20a542ee4ad';
    const companyName = '深圳市飞马国际供应链股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17, 203],
        options: [
          { value: 17, label: '对外投资' },
          { value: 203, label: '对外投资(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [1],
        options: ChangeStatusMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [1],
        options: IsBPMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.excludeCompanyName,
        fieldValue: ['有限合伙'],
        options: [{ value: '有限合伙', label: '有限合伙' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyIndustry,
        fieldValue: ['J'],
        options: [{ value: 'J', label: '金融业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    /* dimension.dimensionFilter = {
       id,
     };*/
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【新增控股子公司中，大多数的行业与本公司行业不一致（国标行业）】', async () => {
    const companyId = '26f32964f1a49ee10aff6bd74636ad77';
    const companyName = '唐山冀东水泥股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17, 203],
        options: [
          { value: 17, label: '对外投资' },
          { value: 203, label: '对外投资(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [1],
        options: ChangeStatusMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [1],
        options: IsBPMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.industryThreshold,
        fieldValue: [1],
        options: [
          { label: '新增控股子公司中，大多数的行业与本公司行业不一致(国标行业)', value: 1 },
          { label: '新增控股子公司中，大多数的行业与本公司行业一致(国标行业)', value: 0 },
          { label: '新增控股子公司中，大多数的行业与本公司行业不一致(企查查行业)', value: 2 },
          { label: '新增控股子公司中，大多数的行业与本公司行业一致(企查查行业)', value: 3 },
        ],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    /* dimension.dimensionFilter = {
       id,
     };*/
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(3);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(3);
  });

  it('【新增控股子公司中，大多数的行业与本公司行业不一致（企查查行业）】', async () => {
    const companyId = '26f32964f1a49ee10aff6bd74636ad77';
    const companyName = '唐山冀东水泥股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17, 203],
        options: [
          { value: 17, label: '对外投资' },
          { value: 203, label: '对外投资(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [1],
        options: ChangeStatusMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [1],
        options: IsBPMap,
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.industryThreshold,
        fieldValue: [2],
        options: [
          { label: '新增控股子公司中，大多数的行业与本公司行业不一致(国标行业)', value: 1 },
          { label: '新增控股子公司中，大多数的行业与本公司行业一致(国标行业)', value: 0 },
          { label: '新增控股子公司中，大多数的行业与本公司行业不一致(企查查行业)', value: 2 },
          { label: '新增控股子公司中，大多数的行业与本公司行业一致(企查查行业)', value: 3 },
        ],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    /*dimension.dimensionFilter = {
      id,
    };*/
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(3);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(3);
  });

  it('【实际控制人的控制企业集中注册且无实缴资本】', async () => {
    //const id = 'c6ede3dce5b897825d6977738432d87d';
    const companyId = '8009a1ce20842af7a42d3589d7781ea7';
    const companyName = '华夏发展建设（北京）有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ControllerCompany, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.registerDate,
        fieldValue: [90],
        options: [{ unit: '天', min: 1, max: 365 }],
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.percentTotal,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.realRegistrationError,
        fieldValue: [1],
        options: RealRegistrationErrorMap,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [2],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【实际控制人的控制企业集中注册且当前企业的注册资本大于母公司(向上一层)的实缴资本】', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ControllerCompany, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.registerDate,
        fieldValue: [90],
        options: [{ unit: '天', min: 1, max: 365 }],
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.percentTotal,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.realRegistrationError,
        fieldValue: [2],
        options: RealRegistrationErrorMap,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [2],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(0);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  // 股权质押-企业本身
  it('【股权质押/出质】-公司本身新增股权质押', async () => {
    const id = 'dbd597240ef2e8c9012388b77d251edc';
    const companyId = '2a2e7be57770b32766e2a5e9b79a473c';
    const companyName = '深圳市德方纳米科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [50],
        options: [{ value: 50, label: '股权质押' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  // 股权质押-大股东
  it('【股权质押/出质】-大股东新增股权质押', async () => {
    const id = '7a7d836e03f8f7ec3989777a47f0001f';
    const companyId = 'c47db0b786d46d3ceebd3dcbf5a82ea4';
    const companyName = '合纵科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [50, 214],
        options: [
          { value: 50, label: '股权质押' },
          { value: 214, label: '股权质押（人）' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.MajorShareholder],
        options: [{ value: RelatedTypeEnums.MajorShareholder, label: '大股东' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  // 股权质押-实际控制人
  it('【股权质押/出质】-实控人新增股权质押', async () => {
    const id = 'ef782352c222eb1fbb6ed5e91ba5b463';
    const companyId = '49942e8e6bcef6cb6cb41c2ffd239d48';
    const companyName = '江苏龙蟠科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [50, 214],
        options: [
          { value: 50, label: '股权质押' },
          { value: 214, label: '股权质押（人）' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  // 股权出质-企业本身

  it('【股权质押/出质】-公司本身新增股权出质', async () => {
    const id = 'fee526d5e1c51bb4af9bd721bc7e5a8c';
    const companyId = 'df676ec76aa9b8f3395e1048290e19f4';
    const companyName = '淄博齐鲁化学工业区金银谷投资发展有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [12],
        options: [{ value: 12, label: '股权出质' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  // 股权出质-大股东 // 临沂市嘉朗生态农业发展有限公司
  it('【股权质押/出质】-大股东新增股权出质', async () => {
    const id = '32e26ac1befcb0c53a5767252be5f693';
    const companyId = '5f4250d25737c5bee9da0c5ce0d99457';
    const companyName = '日照嘉安置业有限责任公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [12, 213],
        options: [
          { value: 12, label: '股权出质' },
          { value: 213, label: '股权出质（人）' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.MajorShareholder],
        options: [{ value: RelatedTypeEnums.MajorShareholder, label: '大股东' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  // 股权出质-实际控制人
  it('【股权质押/出质】-实控人新增股权出质', async () => {
    const id = 'b5460b7105838f4754b34d00b51bf740';
    const companyId = '158cc0b1a5ebae91703354dc0459ddeb';
    const companyName = '杭州临安三景萤石有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [12, 213],
        options: [
          { value: 12, label: '股权出质' },
          { value: 213, label: '股权出质（人）' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  // ----------------
  it('【港股上市企业-财务变动】-近一期的净利润大于0', async () => {
    const id = 'e034e098c4c4f0b3b78105265d02918f';
    const companyId = '9cce0780ab7644008b73bc2120479d31';
    const companyName = '小米科技有限责任公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ListedEntityRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [113],
        options: [{ value: 113, label: '企业公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.StockControlCompany],
        options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.annualReportType,
        fieldValue: [203, 204, 202],
        options: [
          { value: 203, label: '年报' },
          { value: 204, label: '季报' },
          { value: 202, label: '半年报' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.netProfitAmount,
        fieldValue: [0],
        options: [{ unit: '元', min: 0, max: ******** }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【财务变动】-近一期的净利润小于0', async () => {
    const id = 'e4dc2f1ac053c3d8fa013b6c57c894dc';
    const companyId = '82e1b4b99e2162823af509134cb8c866';
    const companyName = '石大胜华新材料集团股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ListedEntityRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [113],
        options: [{ value: 113, label: '企业公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.StockControlCompany],
        options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.annualReportType,
        fieldValue: [203, 204, 202],
        options: [
          { value: 203, label: '年报' },
          { value: 204, label: '季报' },
          { value: 202, label: '半年报' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.netProfitAmount,
        fieldValue: [0],
        options: [{ unit: '元', min: 0, max: ******** }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【财务变动】-近一期的营业收入同比 < 30%', async () => {
    const id = '2ef11f6847505d60faa477b85b0fdfb3';
    const companyId = 'ced87249b98de2c98bb7efbe29674c15';
    const companyName = '杭州立方控股股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ListedEntityRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [113],
        options: [{ value: 113, label: '企业公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.StockControlCompany],
        options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.annualReportType,
        fieldValue: [203, 204, 202],
        options: [
          { value: 203, label: '年报' },
          { value: 204, label: '季报' },
          { value: 202, label: '半年报' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.revenueRatio,
        fieldValue: [30],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【财务变动】-近一期的净利润同比 < 30%', async () => {
    const id = '83b178cdc4cfc899c573bebcddf3f656';
    const companyId = 'c29fe97155f1c25c2561bc46e75c285a';
    const companyName = '常州时创能源股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ListedEntityRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [113],
        options: [{ value: 113, label: '企业公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.StockControlCompany],
        options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.annualReportType,
        fieldValue: [203, 204, 202],
        options: [
          { value: 203, label: '年报' },
          { value: 204, label: '季报' },
          { value: 202, label: '半年报' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.netProfitRatio,
        fieldValue: [30],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【财务变动】-近一期的应收账款同比>=20%', async () => {
    const id = 'ebc81637ea344431f2870a5aad14f3b9';
    const companyId = '8a301811b31332870901557f345b36c8';
    const companyName = '安徽安孚电池科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ListedEntityRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [113],
        options: [{ value: 113, label: '企业公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.StockControlCompany],
        options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.annualReportType,
        fieldValue: [203, 204, 202],
        options: [
          { value: 203, label: '年报' },
          { value: 204, label: '季报' },
          { value: 202, label: '半年报' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.AccountsReceivableRatio,
        fieldValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【财务变动】-近一期的存货同比>=20%', async () => {
    const id = '6d479abe5b48dee4a659fd817742a73c';
    const companyId = 'cd9ac3fca06ed5e25e2a4bcd481216ad';
    const companyName = '共达电声股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ListedEntityRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [113],
        options: [{ value: 113, label: '企业公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.StockControlCompany],
        options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.annualReportType,
        fieldValue: [203, 204, 202],
        options: [
          { value: 203, label: '年报' },
          { value: 204, label: '季报' },
          { value: 202, label: '半年报' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.inventoryRatio,
        fieldValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【财务变动】-近一期的有息负债同比>=20%', async () => {
    const id = 'bada4c49e4a049afa455ee30b71dd6cb';
    const companyId = '0258efd6150e9b9afeaf3024ab2fe064';
    const companyName = '广东领益智造股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ListedEntityRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [113],
        options: [{ value: 113, label: '企业公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.StockControlCompany],
        options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.annualReportType,
        fieldValue: [203, 204, 202],
        options: [
          { value: 203, label: '年报' },
          { value: 204, label: '季报' },
          { value: 202, label: '半年报' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.interestBearingLiabilitiesRatio,
        fieldValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【财务变动】-近一期的有息负债/年度收入（或近期报表收入/12*近期报表月份）>=40%', async () => {
    const id = 'c11891648efc5770404140da1a4e8812';
    const companyId = '84c908b72570aea64b7226733d64deaa';
    const companyName = '河南安彩高科股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ListedEntityRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [113],
        options: [{ value: 113, label: '企业公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.StockControlCompany],
        options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.annualReportType,
        fieldValue: [203, 204, 202],
        options: [
          { value: 203, label: '年报' },
          { value: 204, label: '季报' },
          { value: 202, label: '半年报' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.ibdAnnualRevRatio,
        fieldValue: [40],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【财务变动】-近一期的（货币资金+交易性金融资产）/（短期借款+应付票据）<1', async () => {
    const id = '4656b6761a60204744067fb929e0896e';
    const companyId = 'a028a511654d3855711899a2a970ca0e';
    const companyName = '安徽英力电子科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ListedEntityRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [113],
        options: [{ value: 113, label: '企业公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.StockControlCompany],
        options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.annualReportType,
        fieldValue: [203, 204, 202],
        options: [
          { value: 203, label: '年报' },
          { value: 204, label: '季报' },
          { value: 202, label: '半年报' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cmAndStbRatio,
        fieldValue: [100],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【财务变动】-近一期的负债合计/资产合计>=80%', async () => {
    const id = '88b42dee051ec057bab9c59a037b7541';
    const companyId = '110c2aa81b7b530709b1c1e58f3ea36d';
    const companyName = '苏州昀冢电子科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ListedEntityRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [113],
        options: [{ value: 113, label: '企业公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.StockControlCompany],
        options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.annualReportType,
        fieldValue: [203, 204, 202],
        options: [
          { value: 203, label: '年报' },
          { value: 204, label: '季报' },
          { value: 202, label: '半年报' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.totalLiabToAssetsRatio,
        fieldValue: [80],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【财务变动】-连续三年经营性净现金流均<0', async () => {
    const id = 'd80847b47a68c2ec957fed22b8d23966';
    const companyId = '72e30ba18ce7209839998a521c6fdf14';
    const companyName = '上海张江高科技园区开发股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ListedEntityRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [113],
        options: [{ value: 113, label: '企业公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.StockControlCompany],
        options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.annualReportType,
        fieldValue: [203, 204, 202],
        options: [
          { value: 203, label: '年报' },
          { value: 204, label: '季报' },
          { value: 202, label: '半年报' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cashFlowFromActivitiesAmount,
        fieldValue: [
          {
            consecutiveYearCount: 3,
            cashFlowFromActivitiesAmount: 0,
            cashFlowFromActivitiesAmountCompareType: DimensionFieldCompareTypeEnums.LessThan,
          },
        ],
        options: [
          {
            yearScope: { label: '连续X年', value: 3 },
            cashFlowFromActivitiesAmount: { unit: '元', min: 0, max: ******** },
            cashFlowFromActivitiesAmountCompareType: {
              label: '占比比较(大于/小于)',
              value: DimensionFieldCompareTypeEnums.LessThan,
            },
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【上市情况】-主体企业上市进程', async () => {
    const id = '374c064409614821b2982bf6170fc5eb';
    const companyId = 'df0ffce13d3105dd6f2296ce288e6db2';
    const companyName = '技源集团股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [129],
        options: [{ value: 129, label: '上市进程' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  /**
   * 关联方企业上市动态
   */
  it('【上市情况】-关联公司上市进程', async () => {
    const companyName = '泰安铭泉投资集团有限公司';
    const companyId = 'f5bf2596c5bee7b1c9333fe5e0a322a1';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [129],
        options: [{ value: 129, label: '上市进程' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.MajorityInvestment],
        options: [{ value: RelatedTypeEnums.MajorityInvestment, label: '子公司（对外投资(>50%的企业)）' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(3);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
        '',
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(3);
  });

  it('负面/正面新闻', async () => {
    const companyId = '9cce0780ab7644008b73bc2120479d31';
    const companyName = '小米科技有限责任公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [66, 67, 62],
        options: [
          { value: 66, label: '中立' },
          { value: 67, label: '消极' },
          { value: 62, label: '积极' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        // 新闻主体类型： 负面/正面新闻
        fieldKey: DimensionFieldKeyEnums.topics,
        fieldValue: ['all'],
        options: NegativePositiveTopicTypes,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('负面/正面新闻', async () => {
    const companyId = '9cce0780ab7644008b73bc2120479d31';
    const companyName = '小米科技有限责任公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [66, 67, 62],
        options: [
          { value: 66, label: '中立' },
          { value: 67, label: '消极' },
          { value: 62, label: '积极' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        // 新闻主体类型： 负面/正面新闻
        fieldKey: DimensionFieldKeyEnums.topics,
        fieldValue: ['all'],
        options: NegativePositiveTopicTypes,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('土地抵押', async () => {
    const companyId = 'c6db1fe2c18af0490c8d6eac25d96fc9';
    const companyName = '汕头市澄海区龙晖房地产开发有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [30],
        options: [{ value: 30, label: '土地抵押' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.landMortgageAmount,
        fieldValue: [1999],
        options: [{ unit: '万元', min: 0, max: ******** }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('民事案件', async () => {
    const companyId = 'efb291711ee662dde667076b51f2da54';
    const companyName = '沈阳军超物业管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [49],
        options: [{ value: 49, label: '立案信息' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-立案信息-金融涉诉', async () => {
    const companyId = '3a63e76ac710f019e5df84926f8c5c5b';
    const companyName = '中信银行股份有限公司沈阳分行';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [49],
        options: [{ value: 49, label: '立案信息' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [1],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
        fieldValue: [1],
        options: [
          { label: '非金融租赁或者银行', value: 0 },
          { label: '是金融租赁或者银行', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-立案信息-金融涉诉-实控人', async () => {
    const companyId = 'ff071425ae0d1caac567232747437fe8';
    const companyName = '大连天盛时代装饰装修工程有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [49, 220],
        options: [
          { value: 49, label: '立案信息' },
          { value: 220, label: '立案信息(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [1],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
        fieldValue: [1],
        options: [
          { label: '原告非金融租赁或者银行', value: 0 },
          { label: '原告是金融租赁或者银行', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-送达公告-金融涉诉', async () => {
    const companyId = 'ff53f132a58f4b76e08d3d31b8797d8b';
    const companyName = '中国农业银行股份有限公司深圳市分行';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [27],
        options: [{ value: 27, label: '送达公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [1],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
        fieldValue: [1],
        options: [
          { label: '原告非金融租赁或者银行', value: 0 },
          { label: '原告是金融租赁或者银行', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-送达公告-金融涉诉-实控人', async () => {
    const companyId = '22c0472f35d08fc5c9eba8f0312c2753';
    const companyName = '深圳市多瑙河艺术发展有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [27, 217],
        options: [
          { value: 27, label: '送达公告' },
          { value: 217, label: '送达公告(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [1],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
        fieldValue: [1],
        options: [
          { label: '原告非金融租赁或者银行', value: 0 },
          { label: '原告是金融租赁或者银行', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-判决文书-金融涉诉', async () => {
    const companyId = '234b1bfa4366b3826645eae5ce1322c7';
    const companyName = '云浮市洪宝石材有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [4],
        options: [{ value: 4, label: '判决文书' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [1],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
        fieldValue: [1],
        options: [
          { label: '原告非金融租赁或者银行', value: 0 },
          { label: '原告是金融租赁或者银行', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.lawsuitAmount,
        fieldValue: [0],
        options: [{ unit: '万元', min: 0, max: ******** }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-判决文书-金融涉诉-实控人', async () => {
    const companyId = 'da55a5594cad17f7ee24a4324faf11b2';
    const companyName = '安徽博麦环保科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [4, 221],
        options: [
          { value: 4, label: '裁判文书' },
          { value: 221, label: '裁判文书(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [1],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
        fieldValue: [1],
        options: [
          { label: '原告非金融租赁或者银行', value: 0 },
          { label: '原告是金融租赁或者银行', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.lawsuitAmount,
        fieldValue: [0],
        options: [{ unit: '万元', min: 0, max: ******** }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-开庭公告-金融涉诉', async () => {
    const companyId = '2e251a6914dd7aff0bac491f2d6dc23f';
    const companyName = '交通银行股份有限公司常熟分行';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [18],
        options: [{ value: 18, label: '开庭公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [1],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
        fieldValue: [1],
        options: [
          { label: '原告非金融租赁或者银行', value: 0 },
          { label: '原告是金融租赁或者银行', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-开庭公告-金融涉诉-实控人', async () => {
    const companyId = '9bf93bce9cd6228078599f0def0d7e51';
    const companyName = '阳光城集团上海置业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [18, 219],
        options: [
          { value: 18, label: '开庭公告' },
          { value: 219, label: '开庭公告(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [1],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
        fieldValue: [1],
        options: [
          { label: '原告非金融租赁或者银行', value: 0 },
          { label: '原告是金融租赁或者银行', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-法院公告-金融涉诉', async () => {
    const companyId = '2b96e046701907514650ac7aa99be51f';
    const companyName = '中国工商银行股份有限公司葫芦岛连山支行';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [7],
        options: [{ value: 7, label: '法院公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [1],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
        fieldValue: [1],
        options: [
          { label: '原告非金融租赁或者银行', value: 0 },
          { label: '原告是金融租赁或者银行', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-法院公告-金融涉诉-实控人', async () => {
    const companyId = '286c67ff1f24b59d27cb20c179d138e0';
    const companyName = '海南盛鼎华投资有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [7, 218],
        options: [
          { value: 7, label: '法院公告' },
          { value: 218, label: '法院公告(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [1],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
        fieldValue: [1],
        options: [
          { label: '原告非金融租赁或者银行', value: 0 },
          { label: '原告是金融租赁或者银行', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-诉前调解-金融涉诉', async () => {
    const companyId = '7610b0c69307fea891a7505f6f15dc08';
    const companyName = '中国银行股份有限公司鞍山道西支行';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [90],
        options: [{ value: 90, label: '诉前调解' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [1],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
        fieldValue: [1],
        options: [
          { label: '原告非金融租赁或者银行', value: 0 },
          { label: '原告是金融租赁或者银行', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-诉前调解-金融涉诉-实控人', async () => {
    const companyId = 'a757d28e7a800fb67dd73e457f005d05';
    const companyName = '宜宾市易珂苗木有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [90, 232],
        options: [
          { value: 90, label: '诉前调解' },
          { value: 232, label: '诉前调解(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [1],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
        fieldValue: [1],
        options: [
          { label: '原告非金融租赁或者银行', value: 0 },
          { label: '原告是金融租赁或者银行', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-立案信息-非金融涉诉', async () => {
    const companyId = '91cae1cdca46c589881b9626ecde3a08';
    const companyName = '南京盛达品牌管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [49],
        options: [{ value: 49, label: '立案信息' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [0],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-立案信息-非金融涉诉-实控人', async () => {
    const companyId = 'acf17198d08613702f842f5c83f24c00';
    const companyName = '仙桃市郭河镇鑫亿木粉厂';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [49, 220],
        options: [
          { value: 49, label: '立案信息' },
          { value: 220, label: '立案信息(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [0],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-送达公告-非金融涉诉', async () => {
    const companyId = 'bcf6bb8fff3cb7bd4366266e2769de0a';
    const companyName = '珠海奥飞动漫品牌管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [27],
        options: [{ value: 27, label: '送达公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [0],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-送达公告-非金融涉诉-实控人', async () => {
    const companyId = '4bee0a319ba5ec673b1ddfc562eac13f';
    const companyName = '安徽卢记餐饮管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [27, 217],
        options: [
          { value: 27, label: '送达公告' },
          { value: 217, label: '送达公告(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [0],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-判决文书-非金融涉诉', async () => {
    const companyId = 'a644616f8707350c0f8d26a52c6fbf86';
    const companyName = '中国人民财产保险股份有限公司南平市分公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [4],
        options: [{ value: 4, label: '判决文书' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [0],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.lawsuitAmount,
        fieldValue: [0],
        options: [{ unit: '万元', min: 0, max: ******** }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-判决文书-非金融涉诉-实控人', async () => {
    const companyId = 'c9d7e4cc8e13a54bd113cc6b31c45ae7';
    const companyName = '乌审旗爱沙博林苗木繁育有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [4, 221],
        options: [
          { value: 4, label: '裁判文书' },
          { value: 221, label: '裁判文书(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [0],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.lawsuitAmount,
        fieldValue: [0],
        options: [{ unit: '万元', min: 0, max: ******** }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-开庭公告-非金融涉诉', async () => {
    const companyId = '4b18682393278616f57d052f060265f1';
    const companyName = '咏勤机械（昆山）有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [18],
        options: [{ value: 18, label: '开庭公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [0],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-开庭公告-非金融涉诉-实控人', async () => {
    const companyId = '4adcb06cbc9bab51ee979ed6072bd67e';
    const companyName = '滨海恩满汽车销售有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [0],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [18, 219],
        options: [
          { value: 18, label: '开庭公告' },
          { value: 219, label: '开庭公告(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-法院公告-非金融涉诉', async () => {
    const companyId = '12067c41e2a32d32c63075dda9c59ccb';
    const companyName = '贵州沃方企业管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [7],
        options: [{ value: 7, label: '法院公告' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [0],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-法院公告-非金融涉诉-实控人', async () => {
    const companyId = 'd5a3b09a54a925c4f30918b559d10716';
    const companyName = '佛山市直爽智能科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [7, 218],
        options: [
          { value: 7, label: '法院公告' },
          { value: 218, label: '法院公告(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [0],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-诉前调解-非金融涉诉', async () => {
    const companyId = 'd990a9531783de9d523357b0bef4688e';
    const companyName = '武汉诺启思智能教育科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [90],
        options: [{ value: 90, label: '诉前调解' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [0],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('民事案件-诉前调解-非金融涉诉-实控人', async () => {
    const companyId = '59ed4c1bce53dbe77f2e7afb301ef34c';
    const companyName = '广元市红城品正装饰装修有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [90, 232],
        options: [
          { value: 90, label: '诉前调解' },
          { value: 232, label: '诉前调解(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isFinancialReason,
        fieldValue: [0],
        options: [
          { label: '非金融涉诉', value: 0 },
          { label: '是金融涉诉', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isContractDispute,
        fieldValue: [1],
        options: [
          { value: 1, label: '是合同纠纷' },
          { value: 0, label: '非合同纠纷' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['ms'],
        options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('刑事案件-立案信息', async () => {
    const companyId = 'c43032d87e59efca7308db84ea435dd1';
    const companyName = '月亮湖国际旅游度假投资开发有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [49],
        options: [{ value: 49, label: '立案信息' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'ms', caseReasonValue: 'A' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('刑事案件-立案信息-实控人', async () => {
    const companyId = '9fd5a931a6a5787fe79f826390f5a824';
    const companyName = '深圳市米谷智能有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [49, 220],
        options: [
          { value: 49, label: '立案信息' },
          { value: 220, label: '立案信息(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'xs', caseReasonValue: 'A' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('刑事案件-判决文书', async () => {
    const companyId = '261fca4a9c6ab5d90a33fb9c3eeb87ff';
    const companyName = '莱州市三旭机械有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [4],
        options: [{ value: 4, label: '裁判文书' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'ms', caseReasonValue: 'A' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('刑事案件-判决文书-实控人', async () => {
    const companyId = '142c225f9c556d414c296ffa8dd687f6';
    const companyName = '云南安双科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [4, 221],
        options: [
          { value: 4, label: '裁判文书' },
          { value: 221, label: '裁判文书(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'xs', caseReasonValue: 'A' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('刑事案件-开庭公告', async () => {
    const companyId = 'a644616f8707350c0f8d26a52c6fbf86';
    const companyName = '中国人民财产保险股份有限公司南平市分公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [18],
        options: [{ value: 18, label: '开庭公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: ['A0248', 'A0249'],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'ms', caseReasonValue: 'A' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('刑事案件-开庭公告-实控人', async () => {
    const companyId = 'c870198a22e19af60507c08f2c42dbc9';
    const companyName = '哈尔滨巨合运输有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [18, 219],
        options: [
          { value: 18, label: '开庭公告' },
          { value: 219, label: '开庭公告(人员)' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: ['A0248', 'A0249'],
        options: [
          { label: '交通肇事罪', value: 'A0248' },
          { label: '危险驾驶罪', value: 'A0249' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'xs', caseReasonValue: 'A' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('刑事案件-法院公告', async () => {
    const companyId = 'a55ace74cb4cfd40593e7e40ae3c2c30';
    const companyName = '湖南中安大宗商品现货交易市场有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [7],
        options: [{ value: 7, label: '法院公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'ms', caseReasonValue: 'A' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('刑事案件-法院公告-实控人', async () => {
    const companyId = '';
    const companyName = '';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [7, 218],
        options: [
          { value: 7, label: '法院公告' },
          { value: 218, label: '法院公告(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'xs', caseReasonValue: 'A' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('执行案件-被执行人', async () => {
    const companyId = '21d8e5b1281c122b96ad79df4d31e570';
    const companyName = '泉州正元印刷有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [3],
        options: [{ value: 3, label: '被执行人' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('执行案件-被执行人-实控人', async () => {
    const companyId = '6fd27f7a871a9052510d16fb0f3fc670';
    const companyName = '深圳市时代华娱电影电视节目制作有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [3, 206],
        options: [
          { value: 3, label: '被执行人' },
          { value: 206, label: '被执行人(人员)' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('执行案件-失信被执行人', async () => {
    const companyId = '395132ba07a954884ee7d6d46dff0735';
    const companyName = '上饶市悦盛房地产开发有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [2],
        options: [{ value: 2, label: '失信被执行人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('执行案件-失信被执行人-实控人', async () => {
    const companyId = '3f25ab4191af465b1d5d07f65f3e0667';
    const companyName = '北京盛世双木恒鑫置业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [2, 205],
        options: [
          { value: 2, label: '失信被执行人' },
          { value: 205, label: '失信被执行人(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('执行案件-限制高消费', async () => {
    const companyId = '0a245ae55818139702ed7210f379e2a7';
    const companyName = '四川领力装饰工程有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [55],
        options: [{ value: 55, label: '限制高消费' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.restricterType,
        fieldValue: [1],
        options: [
          { value: 1, label: '企业本身' },
          //{ value: 2, label: '法人代表' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('执行案件-限制高消费-实控人', async () => {
    const companyId = 'b94fc37ea3b40b276d842c922e0a845e';
    const companyName = '广西金永旺投资有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [55, 208],
        options: [
          { value: 55, label: '限制高消费' },
          { value: 208, label: '限制高消费(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.restricterType,
        fieldValue: [1],
        options: [
          { value: 1, label: '企业本身' },
          //{ value: 2, label: '法人代表' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('新增重要高管变更', async () => {
    const companyId = '5dc9ee43a3b6251d99e069dce7c72bb3';
    const companyName = '无锡中炫大至科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [46],
        options: [{ value: 46, label: '主要成员' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('列入税收非正常户', async () => {
    const companyId = 'ba83c43ab4c2c340c111bac39ed1ce80';
    const companyName = '深圳市拓星者文化传媒有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltyUnit,
        accessScope: 0,
        fieldValue: [5],
        options: [...ProcessingAgencyLevelOneMap, { label: '农业农村部', value: '9907' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [117],
        options: [{ value: 117, label: '被列入税务非正常户' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('新增欠税公告', async () => {
    const companyId = '90a57ca4f168fa5cb6d161bfff842a9e';
    const companyName = '西双版纳金橡树酒店管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltyUnit,
        accessScope: 0,
        fieldValue: ['5'],
        options: [...ProcessingAgencyLevelOneMap, { label: '农业农村部', value: '9907' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [31],
        options: [{ value: 31, label: '欠税公告' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishAmount,
        fieldValue: [0],
        options: [{ unit: '元', min: 0, max: ********999 }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('新增行政处罚', async () => {
    const companyId = 'c29f74984dbf0d43010e20692027465e';
    const companyName = '广州芬七商贸有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
        accessScope: 0,
        fieldValue: ['1', '3', '5', '6', '7', '8', '12', '13', '14', '9907'],
        options: [...ProcessingAgencyLevelOneMap, { label: '农业农村部', value: '9907' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [107],
        options: [{ value: 107, label: '行政处罚' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishType,
        fieldValue: ['0908', '0911', '0915', '0910', '0914', '0913', '0906', '0907', '0909', '0912'],
        options: [
          { label: '吊销许可证/执照', value: '0908', esCode: 'A008' },
          { label: '责令关闭', value: '0911', esCode: 'A011' },
          { label: '移送司法机关', value: '0915', esCode: 'A015' },
          { label: '责令停产停业', value: '0910', esCode: 'A010' },
          { label: '限制从业', value: '0913', esCode: 'A013' },
          { label: '行政拘留', value: '0914', esCode: 'A014' },
          { label: '暂扣许可证件', value: '0906', esCode: 'A006' },
          { label: '降低资质等级', value: '0907', esCode: 'A007' },
          { label: '限制开展生产经营活动', value: '0909', esCode: 'A009' },
          { label: '经营异常名录', value: '0912', esCode: 'A012' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('新增行政处罚-税务-罚款xxx以上', async () => {
    const companyId = 'c8fd564beae3f9dda41b94a8a6dc987a';
    const companyName = '淮安久誉餐饮管理服务有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
        accessScope: 0,
        fieldValue: ['5'],
        options: [...ProcessingAgencyLevelOneMap],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [107],
        options: [{ value: 107, label: '行政处罚' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishType,
        fieldValue: ['0903'],
        options: [{ label: '罚款', value: '0903', esCode: 'A003' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishAmount,
        fieldValue: [0],
        options: [{ unit: '元', min: 0, max: ********999 }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('新增行政处罚-上市-交易所处罚', async () => {
    const companyId = '917720a16d08bdde171681983391aed0';
    const companyName = '恒大地产集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
        accessScope: 0,
        fieldValue: ['4'],
        options: [...ProcessingAgencyLevelOneMap, { label: '农业农村部', value: '9907' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [107],
        options: [{ value: 107, label: '行政处罚' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('新增行政处罚-上市-交易所处罚-实控人', async () => {
    const companyId = '35710dfd5ed7d2046270a643246e97f1';
    const companyName = '宁夏中科生物科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
        accessScope: 0,
        fieldValue: ['4'],
        options: [...ProcessingAgencyLevelOneMap, { label: '农业农村部', value: '9907' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [107, 238],
        options: [
          { value: 107, label: '行政处罚' },
          { value: 238, label: '行政处罚(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('新增行政处罚-证监会处罚', async () => {
    const companyId = '35710dfd5ed7d2046270a643246e97f1';
    const companyName = '宁夏中科生物科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
        accessScope: 0,
        fieldValue: ['2'],
        options: [
          ...ProcessingAgencyLevelOneMap,
          { label: '农业农村部', value: '9907' },
          { label: '中国证券监督管理委员会', value: '16' },
          { label: '证监局', value: '17' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [107],
        options: [{ value: 107, label: '行政处罚' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('新增行政处罚-证监会处罚-实控人', async () => {
    const companyId = 'dba00576dabf2b4dd0394dfa67c74234';
    const companyName = '同创九鼎投资控股有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
        accessScope: 0,
        fieldValue: ['2'],
        options: [
          ...ProcessingAgencyLevelOneMap,
          { label: '农业农村部', value: '9907' },
          { label: '中国证券监督管理委员会', value: '16' },
          { label: '证监局', value: '17' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [107, 238],
        options: [
          { value: 107, label: '行政处罚' },
          { value: 238, label: '行政处罚(人员)' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('红牌处罚-行政处罚', async () => {
    const companyId = '460f7bcb9d124a513d1d550b988ec0f2';
    const companyName = '中山市鸽大妈餐饮管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [107],
        options: [{ value: 107, label: '行政处罚' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishRedCard,
        fieldValue: [1],
        options: [
          { label: '非红牌处罚', value: 0 },
          { label: '是红牌处罚', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('红牌环保处罚', async () => {
    // 处罚事由 未取得排污许可证
    // const companyId = '97c7341d42dcfe45e1480b2a0a0ab23b';
    // const companyName = '江苏久嘉工程建设有限公司';
    // 处罚事由 伪造排污许可证
    // const companyId = '8bf4e979ebc2324dd9358530a7e711fb';
    // const companyName = '重庆峰海再生资源开发有限公司';
    // 处罚结果 吊销排污许可证
    const companyId = '8bf4e979ebc2324dd9358530a7e711fb';
    const companyName = '重庆峰海再生资源开发有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [22],
        options: [{ value: 22, label: '环保处罚' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishRedCard,
        fieldValue: [1],
        options: [
          { label: '非红牌处罚', value: 0 },
          { label: '是红牌处罚', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('新增司法拍卖', async () => {
    const companyId = 'f85a2a7f8ceb8e8ebe88377cb3004b5a';
    const companyName = '深圳市万商产业投资集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [57],
        options: [{ value: 57, label: '司法拍卖' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.auctionType,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '破产拍卖' },
          { value: 2, label: '司法拍卖' },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.listingPrice,
        fieldValue: [],
        options: [{ unit: '元', min: 0, max: ********, label: '起拍价（元）' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('重要高管变更-法人变更', async () => {
    const companyId = '3f9aafffc19676e1e4676ee87c8d85ee';
    const companyName = '邵阳市东石科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [72],
        options: [{ value: 72, label: '成员变更' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.layTypes,
        fieldValue: [1],
        options: LayTypeMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('负面新闻', async () => {
    // 处罚事由 未取得排污许可证
    // const companyId = '97c7341d42dcfe45e1480b2a0a0ab23b';
    // const companyName = '江苏久嘉工程建设有限公司';
    // 处罚事由 伪造排污许可证
    // const companyId = '8bf4e979ebc2324dd9358530a7e711fb';
    // const companyName = '重庆峰海再生资源开发有限公司';
    // 处罚结果 吊销排污许可证
    const companyId = 'eac35f7cb2922037a2f7e0525d8cf0cb';
    const companyName = '美的集团股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [66, 67, 62],
        options: [
          { value: 66, label: '中立' },
          { value: 67, label: '消极' },
          { value: 62, label: '积极' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        // 新闻主体类型： 负面/正面新闻
        fieldKey: DimensionFieldKeyEnums.topics,
        fieldValue: ['all'],
        options: NegativePositiveTopicTypes,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('负面新闻-实控人', async () => {
    // 处罚事由 未取得排污许可证
    // const companyId = '97c7341d42dcfe45e1480b2a0a0ab23b';
    // const companyName = '江苏久嘉工程建设有限公司';
    // 处罚事由 伪造排污许可证
    // const companyId = '8bf4e979ebc2324dd9358530a7e711fb';
    // const companyName = '重庆峰海再生资源开发有限公司';
    // 处罚结果 吊销排污许可证
    const companyId = '9cce0780ab7644008b73bc2120479d31';
    const companyName = '小米科技有限责任公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [66, 67, 62],
        options: [
          { value: 66, label: '中立' },
          { value: 67, label: '消极' },
          { value: 62, label: '积极' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [3],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        // 新闻主体类型： 负面/正面新闻
        fieldKey: DimensionFieldKeyEnums.topics,
        fieldValue: ['all'],
        options: NegativePositiveTopicTypes,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('新增担保信息', async () => {
    const companyId = '6cf52a1cca85fc1a7ed02f452189dced';
    const companyName = '深圳中青宝互动网络股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [53, 101],
        options: [
          { value: 53, label: '担保信息' },
          { value: 101, label: '担保信息' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('新增担保信息-实控人', async () => {
    const companyId = '02ac26e9e945441bf435ab425d8bb90c';
    const companyName = '东方世纪科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActualControllerRiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [243],
        options: [{ value: 243, label: '担保信息' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [36],
        options: [{ unit: '月', min: 1, max: 12 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('新增破产重整', async () => {
    const companyId = 'ad3dae74583e489a2e4c27a05ec9d4bf';
    const companyName = '内蒙古长明机械股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [58],
        options: [{ value: 58, label: '破产重整' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('新增融资动态', async () => {
    const companyId = '1e5ae70583905068b60d6c7b07bc6d1c';
    const companyName = '上海畅德医疗科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [28],
        options: [{ value: 28, label: '融资动态' }],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('纳税人资质', async () => {
    const companyId = '5ca76185522b585c98c0e82177ebd655';
    const companyName = '上海保集贸易集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.TaxpayerCertificationChange, []);
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageIndex: 1,
      pageSize: 10,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('资产查冻', async () => {
    const companyId = 'a3bd2cfe461c442b41f933574922066b';
    const companyName = '莘县华祥盐化有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.AssetInvestigationAndFreezing, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 统计周期 近1年
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [1],
        options: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await assertESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await assertESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('动产抵押', async () => {
    const companyId = 'a85daaee29dbf7c0aac083b6a989d63b';
    const companyName = '众能联合数字技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PledgeMerger, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 统计周期 近1年
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [1],
        options: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await pledgeMergerEsService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await pledgeMergerEsService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('授权发明专利转出', async () => {
    const companyId = '6b242b475738f45a4dd180564d029aa9';
    const companyName = '华为技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentInfo, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [1],
        options: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isHistoryPatent,
        fieldValue: [1],
        options: IsHistoryPatentConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentStatus,
        fieldValue: ['ZT002001'],
        options: PatentStatusConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
});
