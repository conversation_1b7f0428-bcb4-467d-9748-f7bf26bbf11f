import { Injectable } from '@nestjs/common';
import { ConfigService } from 'libs/config/config.service';
import { Client } from '@elastic/elasticsearch';
import { BaseEsAnalyzeService } from './base-es-analyze.service';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { find, pick } from 'lodash';
import * as Bluebird from 'bluebird';
import { DimensionRiskLevelEnum } from '../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { AggBucketItemPO, CreditAggBucketItemPO } from '../../../libs/model/data/source/credit.analyze/CreditAggBucketItemPO';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import * as moment from 'moment';
import { CompOvsSanctionsFieldNameMapping, ForeignExportControlsCodeTranslation } from '../../../libs/constants/blacklist.outer.constants';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { getCompareResult, getStartTimeByCycle } from '../../../libs/utils/diligence/diligence.utils';
import { EsOperator } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { SanctionsListCode } from '../../../libs/constants/ovs.sanctions.constants';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';

/**
 * 外部黑名单-出口管制合规风险企业清单数据源
 */
@Injectable()
export class OvsSanctionsBlacklistSource extends BaseEsAnalyzeService {
  constructor(private readonly configService: ConfigService) {
    super(
      OvsSanctionsBlacklistSource.name,
      new Client({
        nodes: configService.esConfig.ovsSanctions.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.ovsSanctions.indexName,
    );
  }

  async analyze(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    return Bluebird.map(DimensionHitStrategyPOs, async (d: DimensionHitStrategyPO) => {
      /**
       * 处理命中描述信息需要的参数
       */
      const desData = {
        isHidden: '',
        isHiddenY: '',
      };
      let hitCount = 0;
      switch (d.key) {
        case DimensionTypeEnums.OvsSanction: {
          hitCount = await this.OvsSanctionCompareResult(d, companyId);
          break;
        }
        default:
          break;
      }
      // 命中记录条数 规则设置
      const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
      if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
        // 不满足 命中记录条数规则 标记未命中
        hitCount = 0;
      }
      if (hitCount > 0) {
        return processDimHitResPO(d, hitCount, desData);
      }
      return null;
    }).then((item) => item.filter((t) => t));
  }

  /**
   * 出口管制合规风险企业清单数据源
   */
  private async OvsSanctionCompareResult(dimension: DimensionHitStrategyPO, companyId: string) {
    const params = new HitDetailsBaseQueryParams();
    params.keyNo = companyId;
    const response = await this.getDimensionDetail(
      dimension,
      params,
      Object.assign(new DimensionAnalyzeParamsPO(), {
        isScanRisk: true,
        keyNo: companyId,
      }),
    );
    return response?.Result?.length ? response.Paging.TotalRecords : 0;
  }

  /**
   * 获取出口管制合规风险企业清单数据源
   * @param dimension
   * @param params
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const data = await super.getDimensionDetail(dimension, Object.assign(params, { field: 'designateddate' }), analyzeParams);
    return data;
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    // 数据处理
    if (resp?.Result?.length) {
      //优化下面的逻辑，减少循环次数
      resp.Result = resp.Result.map((source) => {
        const publishTypeStr = String(source.sanctionslistcode);
        const detailConfig = ForeignExportControlsCodeTranslation[publishTypeStr];
        const keyNoList = source?.keynolist ? JSON.parse(source.keynolist) : [];
        const nameAndKeyNo = keyNoList.map((item) => {
          return { KeyNo: item?.CompKeyno, Name: item?.CompNameCn ?? item?.CompNameEn };
        });
        const Details = detailConfig.fields.reduce((arr, key) => {
          arr.push({ dataKey: CompOvsSanctionsFieldNameMapping[key], dataValue: source[key], key });
          return arr;
        }, []);
        return Object.assign(pick(source, detailConfig.fields), {
          NameAndKeyNo: nameAndKeyNo,
          Name: nameAndKeyNo[0]?.Name ?? '',
          KeyNo: nameAndKeyNo[0]?.KeyNo ?? '',
          CaseReasonType: detailConfig.name, //命中黑名单类型
          CaseReason: source.sanctionsreason, //命中黑名单原因
          Court: '', //列入机关
          Id: source.id,
          Publishdate: source?.designateddate ? moment(source.designateddate, 'YYYYMMDD').unix() : undefined,
          dimensionKey: dimension.key,
          dimensionName: dimension.strategyName,
          Details: JSON.stringify(Details),
          isdetails: 1,
        });
      });
    }
    return resp;
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionHitStrategyPO): Promise<object> {
    const subBool = {
      filter: [],
      must: [],
    };
    subBool.filter.push({ term: { datastatus: 1 } });
    subBool.filter.push({ terms: { keynoes: [companyId] } });

    const cycleField = dimension.getCycleField();
    const cycle = cycleField?.fieldValue ? (cycleField.fieldValue?.[0] as number) : 0;
    const rangeOperator = cycleField?.compareType ? EsOperator[cycleField.compareType] : 'gte';
    switch (dimension.key) {
      case DimensionTypeEnums.OvsSanction: {
        //  出口管制合规风险企业清单
        const sanctionListCodeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.sanctionListCodes);
        if (sanctionListCodeField?.fieldValue?.length) {
          const esCodeList = SanctionsListCode.filter((item) => sanctionListCodeField?.fieldValue.includes(item.value)).flatMap((item) => item.esCode);
          subBool.filter.push({ terms: { sanctionslistcode: esCodeList } });
        }
        break;
      }
    }
    // 统计周期
    if (cycle > 0) {
      const timestamp = getStartTimeByCycle(cycle);
      subBool.filter.push({ range: { designateddate: { [rangeOperator]: Math.ceil(timestamp / 1000) } } });
    }
    return { bool: subBool };
  }

  protected async createAggs(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]) {
    const aggs: any = {};
    await Bluebird?.map(DimensionHitStrategyPOs, async (po) => {
      const dimQuery = await this.getDimensionQuery(companyId, po);
      const aggsName = `${this.bucketNamePrefix}${po.strategyId}`;
      aggs[aggsName] = {
        filter: dimQuery,
        aggs: {},
      };
    });
    return aggs;
  }

  protected processAggs(aggObj: any): CreditAggBucketItemPO[] {
    const bucketData: CreditAggBucketItemPO[] = [];
    Object.keys(aggObj).forEach((bucketName) => {
      const dimensionType = bucketName.replace(this.bucketNamePrefix, '');
      const bucket = aggObj[bucketName];
      // const esMappingModel: CreditEsMappingModel = getCreditMappingByDimension(dimensionType);
      // if (esMappingModel) {
      const hitCount = bucket['doc_count'];
      if (hitCount > 0) {
        const res: CreditAggBucketItemPO = {
          dimensionType,
          hitCount,
        };
        Object.keys(DimensionRiskLevelEnum)
          .filter((level) => isNaN(Number(level)))
          .sort((a, b) => DimensionRiskLevelEnum[a] - DimensionRiskLevelEnum[b])
          .forEach((level) => {
            if (bucket[`agg_${level}`] && bucket[`agg_${level}`]['doc_count'] > 0) {
              res.level = DimensionRiskLevelEnum[level];
            }
          });
        bucketData.push(res);
      }
      // }
    });
    return bucketData;
  }

  protected processBucketData(bucketData: AggBucketItemPO[], DimensionHitStrategyPOs: DimensionHitStrategyPO[]): DimensionHitResultPO[] {
    return bucketData
      .map((item) => {
        const d: DimensionHitStrategyPO = find(DimensionHitStrategyPOs, { strategyId: +item.dimensionType });
        const desData = {
          isHidden: '',
          isHiddenY: '',
        };
        const { hitCount } = item as AggBucketItemPO;

        let hit = true;
        // 命中记录条数 规则设置
        const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
        if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
          // 不满足 命中记录条数规则 标记未命中
          hit = false;
        }

        if (hit) {
          return processDimHitResPO(d, hitCount, desData);
        }
        return null;
      })
      .filter((t) => t);
  }
}
