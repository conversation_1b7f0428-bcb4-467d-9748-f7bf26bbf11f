import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { AxiosRequestConfig, Method } from 'axios';
import { Cacheable } from '@type-cacheable/core';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { ConfigService } from 'libs/config/config.service';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import * as Bluebird from 'bluebird';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { getCompareResult } from '../../../libs/utils/diligence/diligence.utils';
import { IAnalyzeService } from './analyze.interface';

/**
 * 标讯数据源接口
 */
@Injectable()
export class TenderApiSource implements IAnalyzeService<HitDetailsBaseQueryParams, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  private readonly logger: Logger = QccLogger.getLogger(TenderApiSource.name);

  constructor(private readonly configService: ConfigService, private readonly httpUtils: HttpUtilsService) {}

  async analyze(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    return Bluebird.map(
      DimensionHitStrategyPOs,
      async (d: DimensionHitStrategyPO) => {
        let hitCount = 0;
        switch (d.key) {
          case DimensionTypeEnums.NoTender: {
            // 无中标项目
            // 找下公司的中标项目
            const tenderResult = await this.getDimensionDetail(d, {
              keyNo: companyId,
              pageIndex: 1,
              pageSize: 1,
            });
            hitCount = tenderResult?.Paging?.TotalRecords || 0;
            break;
          }
        }

        // 命中记录条数 规则设置
        const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
        if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
          // 不满足 命中记录条数规则 标记未命中
          hitCount = 0;
        }

        if (hitCount > 0) {
          return processDimHitResPO(d, hitCount);
        }
        return null;
      },
      { concurrency: 3 },
    ).then((results) => results.filter((t) => t));
  }

  @Cacheable({ ttlSeconds: 300 })
  async getDimensionDetail(dimension: DimensionHitStrategyPO, data: HitDetailsBaseQueryParams): Promise<HitDetailsBaseResponse> {
    // this.logger.info(`get hit details for dimension: ${dimension.key}`);
    const { keyNo, pageIndex, pageSize } = data;
    try {
      if (dimension.source !== DimensionSourceEnums.Tender) {
        return new HitDetailsBaseResponse();
      }

      switch (dimension?.key) {
        // 无招投标项目
        case DimensionTypeEnums.NoTender: {
          const filter = {
            publishdate: [],
            allkeynos: [keyNo],
          };
          const cycle = dimension.getCycle();
          if (cycle > 0) {
            filter.publishdate.push({
              currently: true,
              flag: 1,
              number: (cycle || 1) * 365,
              unit: 'day',
            });
          } else {
            delete filter.publishdate;
          }

          const tenderListResult = await this.httpRequest('POST', `${this.configService.server.tender.baseUrl}api/search`, {
            sortField: 'publishdate',
            sortOrder: 'DESC',
            type: '100',
            filter,
            pageIndex,
            pageSize,
          });

          if (tenderListResult?.Paging?.TotalRecords == 0) {
            return Object.assign(new HitDetailsBaseResponse(), {
              Result: [{ description: '该企业无招投标记录' }],
              Paging: {
                PageSize: pageSize,
                PageIndex: pageIndex,
                TotalRecords: 1,
              },
            });
          }
          break;
        }
        default:
          break;
      }
    } catch (error) {
      this.logger.error('tender getDimensionDetail error!');
    }
    return new HitDetailsBaseResponse();
  }

  // 调用标讯接口
  public async httpRequest(method: Method, url: string, data: Record<string, any>) {
    try {
      const requestParams: AxiosRequestConfig = {
        method,
        url,
        data,
        headers: {
          'X-API-TOKEN': this.configService.server.tender.token,
        },
      };

      return await this.httpUtils.sendResquest(requestParams);
    } catch (error) {
      this.logger.error(' tender interface error:', error);
      return null;
    }
  }
}
