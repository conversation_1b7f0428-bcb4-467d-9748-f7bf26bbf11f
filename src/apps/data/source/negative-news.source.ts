/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { ConfigService } from 'libs/config/config.service';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { Client } from '@elastic/elasticsearch';
import { BaseEsAnalyzeService } from './base-es-analyze.service';
import { flatMap } from 'lodash';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { getCompareResult, getStartTimeByCycle } from '../../../libs/utils/diligence/diligence.utils';
import { AllTopicTypes } from '../../../libs/constants/news.constants';
import { EsOperator } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import * as Bluebird from 'bluebird';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';

/**
 * 负面舆情数据源接口
 */
@Injectable()
export class NegativeNewsSource extends BaseEsAnalyzeService {
  constructor(private readonly configService: ConfigService) {
    super(
      NegativeNewsSource.name,
      new Client({
        nodes: configService.esConfig.negativeNews.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.negativeNews.indexName,
    );
  }

  async analyze(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    return Bluebird.map(DimensionHitStrategyPOs, async (d: DimensionHitStrategyPO) => {
      /**
       * 处理命中描述信息需要的参数
       */
      const desData = {
        isHidden: '',
        isHiddenY: '',
      };
      let hitCount = 0;
      switch (d.key) {
        case DimensionTypeEnums.NegativeNews: {
          hitCount = await this.negativeNewsCompareResult(d, companyId);
          break;
        }
      }
      // 命中记录条数 规则设置
      const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
      if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
        // 不满足 命中记录条数规则 标记未命中
        hitCount = 0;
      }
      if (hitCount > 0) {
        return processDimHitResPO(d, hitCount, desData);
      }
      return null;
    }).then((item) => item.filter((t) => t));
  }

  /**
   * 获取风险维度的详情
   * @param dimension
   * @param params
   * @returns
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const resp: HitDetailsBaseResponse = await super.getDimensionDetail(dimension, params, analyzeParams);
    return resp;
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    return resp;
  }

  /**
   * 负面新闻
   */
  private async negativeNewsCompareResult(dimension: DimensionHitStrategyPO, companyId: string) {
    const params = new HitDetailsBaseQueryParams();
    params.keyNo = companyId;
    const response = await this.getDimensionDetail(
      dimension,
      params,
      Object.assign(new DimensionAnalyzeParamsPO(), {
        isScanRisk: true,
        keyNo: companyId,
      }),
    );
    return response?.Result?.length ? response.Paging.TotalRecords : 0;
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionHitStrategyPO): Promise<object> {
    const ids = [companyId];

    const subBool = {
      filter: [],
      should: [],
      must: [{ term: { isvalid: 1 } }, { terms: { isadd: [0, 1] } }],
    };

    subBool.filter.push({ terms: { companykeyno: ids } });

    const cycleField = dimension.getCycleField();
    const cycle = cycleField?.fieldValue ? (cycleField.fieldValue?.[0] as number) : 0;
    const rangeOperator = cycleField?.compareType ? EsOperator[cycleField.compareType] : 'gte';

    switch (dimension.key) {
      case DimensionTypeEnums.NegativeNews: {
        // 新闻类型
        const topicIds = this.getDimesionTopics(dimension);
        // if (topicIds.length) {
        //   subBool.filter.push({ terms: { topicid: this.FormatTopicIds(topicIds) } });
        // }
        break;
      }
    }
    // 统计周期
    if (cycle > 0) {
      const timestamp = getStartTimeByCycle(cycle);
      subBool.filter.push({ range: { publishtime: { [rangeOperator]: Math.ceil(timestamp / 1000) } } });
    }
    return { bool: subBool };
  }

  private getDimesionTopics(dimension: DimensionHitStrategyPO) {
    const topicsFields = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.topics);
    if (topicsFields?.fieldValue.length) {
      if (topicsFields.fieldValue.includes('all')) {
        return AllTopicTypes.filter((t) => t.value !== 'all').map((t) => t.value);
      } else {
        return topicsFields?.fieldValue;
      }
    }
    return [];
  }

  /**
   * 12000: 经营相关 需要剔除（  '12016': '税务注销登记',）
   */
  private FormatTopicIds(topicIds: string[]): string[] {
    let newTopicIds = topicIds;
    if (topicIds.includes('12000')) {
      newTopicIds = flatMap(topicIds, (topicId) => {
        if (topicId == '12000') {
          return [
            '12001',
            '12002',
            '12003',
            '12004',
            '12005',
            '12006',
            '12007',
            '12008',
            '12010',
            '12011',
            '12012',
            '12013',
            '12014',
            '12015',
            '12017',
            '12018',
            '12019',
          ];
        }
        return topicId;
      });
    }
    return newTopicIds;
  }
}
