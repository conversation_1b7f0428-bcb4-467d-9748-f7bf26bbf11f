import { Injectable } from '@nestjs/common';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from 'libs/config/config.service';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { BaseEsAnalyzeService } from './base-es-analyze.service';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import * as Bluebird from 'bluebird';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { getCompareResult, getStartTimeByCycle } from '../../../libs/utils/diligence/diligence.utils';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import { BlackTypeItems } from '../../../libs/constants/blacklist.outer.constants';

@Injectable()
export class OuterBlacklistSource extends BaseEsAnalyzeService {
  constructor(private readonly configService: ConfigService) {
    super(
      OuterBlacklistSource.name,
      new Client({
        nodes: configService.esConfig.blacklist.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.blacklist.indexName,
    );
  }

  async analyze(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    return Bluebird.map(DimensionHitStrategyPOs, async (d: DimensionHitStrategyPO) => {
      /**
       * 处理命中描述信息需要的参数
       */
      const desData = {
        isHidden: '',
        isHiddenY: '',
      };
      let hitCount = 0;
      switch (d.key) {
        case DimensionTypeEnums.HitOuterBlackList: {
          const params = new HitDetailsBaseQueryParams();
          params.keyNo = companyId;
          const res = await this.getDimensionDetail(
            d,
            params,
            Object.assign(new DimensionAnalyzeParamsPO(), {
              isScanRisk: true,
              keyNo: companyId,
            }),
          );
          if (res) {
            hitCount = res?.Paging?.TotalRecords || 0;
          }
          break;
        }
        default:
          break;
      }
      // 命中记录条数 规则设置
      const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
      if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
        // 不满足 命中记录条数规则 标记未命中
        hitCount = 0;
      }
      if (hitCount > 0) {
        return processDimHitResPO(d, hitCount, desData);
      }
      return null;
    }).then((item) => item.filter((t) => t));
  }

  /**
   * 获取黑名维度详情
   * @param dimension
   * @param params
   * @returns
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const blacklistResp: HitDetailsBaseResponse = await super.getDimensionDetail(dimension, params, analyzeParams);
    return blacklistResp;
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    // 数据处理
    if (resp?.Result?.length) {
      resp.Result.forEach((source) => {
        Object.assign(source, {
          NameAndKeyNo: source?.nameandkeyno ? JSON.parse(source.nameandkeyno) : [],
          Name: source.compkeyno ? JSON.parse(source.compkeyno).Name : '',
          KeyNo: source.compkeyno ? JSON.parse(source.compkeyno).KeyNo : '',
          CaseReasonType: source.listtype, //命中黑名单类型
          CaseReason: source.decisionreason, //命中黑名单原因
          Court: source.decisionoffice, //列入机关
          Id: source.id,
          Publishdate: source?.decisiondate, //在kys_blacklist_query 索引内，decisiondate字段表示列入黑名单的时间，与发布时间不同，需要单独处理
        });
      });
    }
    return resp;
  }

  protected async getDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ) {
    const subBool = {
      filter: [],
    };
    subBool.filter.push({ term: { 'compname.keyword': companyId } });
    // 时间
    const cycle = dimension.getCycle();
    if (cycle && cycle > 0) {
      const timestamp = getStartTimeByCycle(cycle);
      subBool.filter.push({ range: { decisiondate: { gte: Math.ceil(timestamp / 1000) } } });
    }
    // datastatus
    const isValidParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
    if (isValidParams && Number(isValidParams.fieldValue[0]) >= 0) {
      subBool.filter.push({ term: { datastatus: Number(isValidParams.fieldValue[0]) } });
    }
    switch (dimension.key) {
      // 黑名单类型
      case DimensionTypeEnums.HitOuterBlackList: {
        const blackTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.blackType);
        if (!blackTypeField?.fieldValue?.length || blackTypeField?.fieldValue.includes('all')) {
          // TODO: BlackTypeItems 改成从blackTypeField的option中取
          // 黑名单-类型 未配置 或者 选择all 查询目前可配置的全部黑名单
          const esCodeList = BlackTypeItems.filter((item) => item.value !== 'all').flatMap((item) => item.esCode);
          subBool.filter.push({ terms: { listtypecode: esCodeList } });
        } else {
          const esCodeList = BlackTypeItems.filter((item) => blackTypeField?.fieldValue.includes(item.value)).flatMap((item) => item.esCode);
          subBool.filter.push({ terms: { listtypecode: esCodeList } });
        }
      }
    }
    return { bool: subBool };
  }
}
