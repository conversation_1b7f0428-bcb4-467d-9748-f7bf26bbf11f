import { Injectable } from '@nestjs/common';
import { BaseRiskChangeStrategy, DimensionCategoryMap } from './base-risk-change.strategy';
import { DimensionHitStrategyPO } from '../../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from '../../../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from '../../../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../../../libs/model/diligence/details/response';
import { DimensionFieldKeyEnums } from '../../../../../libs/enums/dimension/dimension.filter.params';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import * as _ from 'lodash';
import * as Bluebird from 'bluebird';
import { BusinessAbnormalType } from '../../../../../libs/constants/punish.constants';
import { DimensionHitStrategyFieldsEntity } from '../../../../../libs/entities/DimensionHitStrategyFieldsEntity';
import { getCompareResultForArray } from '../../../../../libs/utils/diligence/diligence.utils';
import { RiskChangeCategoryEnum } from '../../../../../libs/enums/riskchange/RiskChangeCategoryEnum';

/**
 * 业务异常策略类
 * 处理经营异常相关维度
 */
@Injectable()
export class BusinessAbnormalStrategy extends BaseRiskChangeStrategy {
  /**
   * 业务异常相关维度列表
   */
  private readonly businessAbnormalDimensions = [
    DimensionTypeEnums.BusinessAbnormal,
    DimensionTypeEnums.BusinessAbnormal1,
    DimensionTypeEnums.BusinessAbnormal2,
    DimensionTypeEnums.BusinessAbnormal3,
  ];

  /**
   * 业务异常维度与风险变更类别映射
   */
  private readonly businessAbnormalDimensionCategoryMap: DimensionCategoryMap = {
    [DimensionTypeEnums.BusinessAbnormal]: [
      RiskChangeCategoryEnum.category38, // 经营异常
    ],
    [DimensionTypeEnums.BusinessAbnormal1]: [
      RiskChangeCategoryEnum.category38, // 经营异常
    ],
    [DimensionTypeEnums.BusinessAbnormal2]: [
      RiskChangeCategoryEnum.category38, // 经营异常
    ],
    [DimensionTypeEnums.BusinessAbnormal3]: [
      RiskChangeCategoryEnum.category38, // 经营异常
    ],
  };

  /**
   * 构造函数
   */
  constructor() {
    super(BusinessAbnormalStrategy.name);
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return this.businessAbnormalDimensions;
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): DimensionCategoryMap {
    return this.businessAbnormalDimensionCategoryMap;
  }

  /**
   * 检查维度类型是否由该策略处理
   * @param dimension 维度策略
   */
  supportsDimension(dimension: DimensionHitStrategyPO): boolean {
    return this.getSupportedDimensions().includes(dimension?.key);
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    if (!this.supportsDimension(dimension)) {
      return null;
    }

    const baseQuery = this.createBaseQuery(companyId);
    const dimensionType = dimension?.key as DimensionTypeEnums;

    // 获取该维度对应的风险变更类别
    const categories = this.getDimensionCategoryMap()[dimensionType];

    if (!categories?.length) {
      return baseQuery;
    }

    // 添加类别过滤条件
    (baseQuery as any).bool.must.push({
      terms: {
        Category: categories,
      },
    });

    // 添加有效性过滤
    (baseQuery as any).bool.must.push({
      term: {
        IsValid: 1,
      },
    });

    // 添加风险快讯标识过滤
    (baseQuery as any).bool.must.push({
      term: {
        IsRK: 1,
      },
    });

    // 添加经营异常类型过滤
    const businessAbnormalTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessAbnormalType);
    if (businessAbnormalTypeField?.fieldValue?.length) {
      const abnormalTypes = businessAbnormalTypeField.fieldValue;
      const esCodeList = [];

      // 将业务异常类型值转换为ES代码
      abnormalTypes.forEach((type) => {
        const abnormalType = BusinessAbnormalType.find((t) => t.value === type);
        if (abnormalType) {
          esCodeList.push(abnormalType.esCode);
        }
      });

      if (esCodeList.length) {
        (baseQuery as any).bool.must.push({
          terms: {
            'ChangeExtend.D.key': esCodeList,
          },
        });
      }
    }

    // 添加维度过滤器中的时间范围
    const dimensionFilter = dimension?.dimensionFilter;
    if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
      (baseQuery as any).bool.must.push({
        range: {
          CreateDate: {
            gte: Math.ceil(dimensionFilter.startTime),
            lte: Math.ceil(dimensionFilter.endTime),
          },
        },
      });
    }

    return baseQuery;
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk || !response?.Result?.length) {
      return response;
    }

    // 处理业务异常维度详情数据
    response.Result = await Bluebird.map(response.Result, async (item) => {
      try {
        // 确保ChangeExtend是对象
        if (typeof item.ChangeExtend === 'string') {
          try {
            item.ChangeExtend = JSON.parse(item.ChangeExtend);
          } catch (error) {
            this.logger.error(`解析ChangeExtend失败: ${error?.message || error}`);
          }
        }

        // 处理业务异常详情
        this.processBusinessAbnormalDetail(item, dimension);
        return item;
      } catch (error) {
        this.logger.error(`处理业务异常维度详情数据失败: ${error?.message || error}`);
        return item;
      }
    });

    return response;
  }

  /**
   * 处理业务异常详情
   * @param item 数据项
   * @param dimension 维度策略
   * @private
   */
  private processBusinessAbnormalDetail(item: any, dimension: DimensionHitStrategyPO): void {
    // 获取维度字段
    const businessAbnormalTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessAbnormalType);

    // 处理经营异常类型字段
    if (businessAbnormalTypeField) {
      item.HitBusinessAbnormalType = this.businessAbnormalTypeField(businessAbnormalTypeField, item);
    }

    // 提取业务异常信息
    this.extractBusinessAbnormalInfo(item);
  }

  /**
   * 提取业务异常信息
   * @param item 数据项
   * @private
   */
  private extractBusinessAbnormalInfo(item: any): void {
    try {
      const changeExtend = item.ChangeExtend || {};

      // 提取异常类型和描述
      if (changeExtend.D?.key) {
        const abnormalType = BusinessAbnormalType.find((t) => t.esCode === changeExtend.D.key);
        if (abnormalType) {
          item.AbnormalTypeCode = abnormalType.value;
          item.AbnormalTypeName = abnormalType.label;
        }
      }

      // 提取异常原因和日期
      item.AbnormalReason = changeExtend.A || '';
      item.AbnormalDate = changeExtend.B || item.CreateDate || '';
      item.AbnormalDepartment = changeExtend.C || '';
      item.RecoveryDate = changeExtend.E || '';
      item.RecoveryReason = changeExtend.F || '';
    } catch (error) {
      this.logger.error(`提取业务异常信息失败: ${error?.message || error}`);
    }
  }

  /**
   * 经营异常类型匹配
   * @param businessAbnormalTypeField 经营异常类型字段
   * @param item 数据项
   */
  private businessAbnormalTypeField(businessAbnormalTypeField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const businessAbnormalTypeFieldTargetValues = businessAbnormalTypeField?.fieldValue as string[];
    const businessAbnormalTypeFieldSourceValues: string[] = [];

    // 确保ChangeExtend是对象
    if (typeof item.ChangeExtend === 'string') {
      try {
        item.ChangeExtend = JSON.parse(item.ChangeExtend);
      } catch (error) {
        this.logger.error(`解析ChangeExtend失败: ${error?.message || error}`);
      }
    }

    if (item?.ChangeExtend?.D?.key) {
      BusinessAbnormalType.forEach((t) => {
        if (item?.ChangeExtend?.D.key?.includes(t.esCode)) {
          businessAbnormalTypeFieldSourceValues.push(t.value);
        }
      });
    }

    if (
      businessAbnormalTypeFieldTargetValues?.length &&
      businessAbnormalTypeFieldSourceValues?.length &&
      getCompareResultForArray(businessAbnormalTypeField.compareType, businessAbnormalTypeFieldSourceValues, businessAbnormalTypeFieldTargetValues)
    ) {
      hit = true;
    }

    return hit;
  }
}
