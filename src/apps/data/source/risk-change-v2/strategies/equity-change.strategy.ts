import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { cloneDeep, orderBy } from 'lodash';
import { CompanyChangeHelper } from '../../risk-change/helper/company-change.helper';
import { CompanyStockHelper } from '../../risk-change/helper/company-stock.helper';
import { BaseRiskChangeStrategy } from './base-risk-change.strategy';

/**
 * 股权变更策略
 * 处理股权变更相关的风险类型
 */
@Injectable()
export class EquityChangeStrategy extends BaseRiskChangeStrategy {
  /**
   * 构造函数
   * @param companyStockHelper 企业股票辅助服务
   * @param companyChangeHelper 企业变更辅助服务
   */
  constructor(private readonly companyStockHelper: CompanyStockHelper, private readonly companyChangeHelper: CompanyChangeHelper) {
    super('EquityChangeStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.MainInfoUpdateHolder, DimensionTypeEnums.EquityPledge, DimensionTypeEnums.FreezeEquity, DimensionTypeEnums.StockPledge];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.MainInfoUpdateHolder]: [RiskChangeCategoryEnum.category12],
      [DimensionTypeEnums.EquityPledge]: [RiskChangeCategoryEnum.category12],
      [DimensionTypeEnums.FreezeEquity]: [RiskChangeCategoryEnum.category12],
      [DimensionTypeEnums.StockPledge]: [RiskChangeCategoryEnum.category25],
    };
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 创建基础查询对象
      const query: any = this.createBaseQuery(companyId);

      // 添加风险类别过滤
      const categories = this.getDimensionCategoryMap()[dimension.key];
      if (categories?.length) {
        query.bool.must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加维度过滤条件
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.must.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加有效性过滤
      const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidField && Number(isValidField.fieldValue[0]) >= 0) {
        query.bool.must.push({
          term: {
            IsValid: Number(isValidField.fieldValue[0]),
          },
        });
      } else {
        // 默认只查询有效记录
        query.bool.must.push({
          term: {
            IsValid: 1,
          },
        });
      }

      // 根据维度类型添加特定条件
      if (dimension.key === DimensionTypeEnums.MainInfoUpdateHolder) {
        // 股东变更特定条件
        const holderChangeTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeStatus);
        if (holderChangeTypeField && holderChangeTypeField.fieldValue?.length) {
          query.bool.must.push({
            terms: {
              'ChangeExtend.ChangeType': holderChangeTypeField.fieldValue,
            },
          });
        }
      } else if (dimension.key === DimensionTypeEnums.EquityPledge) {
        // 股权质押特定条件
        const equityPledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeStatus);
        if (equityPledgeStatusField && equityPledgeStatusField.fieldValue?.length) {
          query.bool.must.push({
            terms: {
              'ChangeExtend.Status': equityPledgeStatusField.fieldValue,
            },
          });
        }
      } else if (dimension.key === DimensionTypeEnums.FreezeEquity) {
        // 股权冻结特定条件
        query.bool.must.push({
          term: {
            'ChangeExtend.Type': '股权冻结',
          },
        });
      } else if (dimension.key === DimensionTypeEnums.StockPledge) {
        // 股票质押特定条件
        const stockPledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.pledgeStatus);
        if (stockPledgeStatusField && stockPledgeStatusField.fieldValue?.length) {
          query.bool.must.push({
            terms: {
              'ChangeExtend.Status': stockPledgeStatusField.fieldValue,
            },
          });
        }
      }

      return query;
    } catch (error) {
      this.logError('生成股权变更查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];

      // 处理每条记录
      for (const itemRaw of response.Result) {
        try {
          const item = cloneDeep(itemRaw);

          // 解析JSON字段
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              const value = item[key];
              try {
                item[key] = value ? JSON.parse(value) : {};
              } catch (error) {
                item[key] = value;
              }
            }
          });

          let isHit = true;

          // 处理股权变更
          if (item.Category === RiskChangeCategoryEnum.category12) {
            // 处理股东变更
            if (dimension.key === DimensionTypeEnums.MainInfoUpdateHolder) {
              // 股东角色处理
              const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
              if (holderRoleField && isHit) {
                isHit = await this.companyStockHelper.holderRoleFieldCategory12(holderRoleField, item, params.keyNo);
              }

              // 股东变更类型处理
              const holderChangeTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeStatus);
              if (holderChangeTypeField && isHit) {
                isHit = this.companyChangeHelper.hitShareChangeStatusField(holderChangeTypeField, item);
              }

              // 股东变更比例处理
              const shareChangeRateField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeRate);
              if (shareChangeRateField && isHit) {
                isHit = this.companyChangeHelper.hitShareChangeRateField(shareChangeRateField, item);
              }
            }
            // 处理股权质押
            else if (dimension.key === DimensionTypeEnums.EquityPledge) {
              // 质押状态处理
              const equityPledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeStatus);
              if (equityPledgeStatusField && isHit) {
                isHit = this.companyStockHelper.equityPledgeStatusFieldCategory12(equityPledgeStatusField, item);
              }

              // 质押比例处理
              const equityPledgeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeRatio);
              if (equityPledgeRatioField && isHit) {
                isHit = this.companyStockHelper.equityPledgeRatioFieldCategory12(equityPledgeRatioField, item);
              }
            }
            // 处理股权冻结
            else if (dimension.key === DimensionTypeEnums.FreezeEquity) {
              // 确认是股权冻结类型
              isHit = item.ChangeExtend?.Type === '股权冻结';
            }
          }
          // 处理股票质押
          else if (item.Category === RiskChangeCategoryEnum.category25 && dimension.key === DimensionTypeEnums.StockPledge) {
            // 股票质押状态处理
            const stockPledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.pledgeStatus);
            if (stockPledgeStatusField && isHit) {
              isHit = this.companyStockHelper.sharePledgeStatusFieldCategory50(stockPledgeStatusField, item);
            }

            // 股票质押比例处理
            const stockPledgeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.stockPledgeRatio);
            if (stockPledgeRatioField && isHit) {
              isHit = this.companyStockHelper.stockPledgeRatioFieldCategory50(stockPledgeRatioField, item);
            }
          }

          if (isHit) {
            hitData.push(item);
          }
        } catch (error) {
          this.logError('处理股权变更详情项失败', error);
        }
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理股权变更详情失败', error);
      return response;
    }
  }
}
