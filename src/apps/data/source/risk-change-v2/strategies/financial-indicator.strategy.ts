import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRiskChangeStrategy } from './base-risk-change.strategy';
import { RiskChangeHelper } from '../../../helper/risk.change.helper';
import { cloneDeep, orderBy } from 'lodash';
import { CompanyFinaceHelper } from '../../risk-change/helper/company-finace.helper';

/**
 * 财务指标策略
 * 处理财务指标相关的风险类型
 */
@Injectable()
export class FinancialIndicatorStrategy extends BaseRiskChangeStrategy {
  /**
   * 构造函数
   * @param riskChangeHelper 风险变更辅助服务
   * @param companyFinaceHelper 企业财务辅助服务
   */
  constructor(private readonly riskChangeHelper: RiskChangeHelper, private readonly companyFinaceHelper: CompanyFinaceHelper) {
    super('FinancialIndicatorStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.FinancialHealth];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.FinancialHealth]: [RiskChangeCategoryEnum.category49],
    };
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 创建基础查询对象
      const query = this.createBaseQuery(companyId);

      // 添加风险类别过滤
      const categories = this.getDimensionCategoryMap()[dimension.key];
      if (categories?.length) {
        query['bool'] = query['bool'] || { must: [] };
        query['bool'].must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加维度过滤条件
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query['bool'] = query['bool'] || { must: [] };
        query['bool'].must.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加有效性过滤
      const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidField && Number(isValidField.fieldValue[0]) >= 0) {
        query['bool'] = query['bool'] || { must: [] };
        query['bool'].must.push({
          term: {
            IsValid: Number(isValidField.fieldValue[0]),
          },
        });
      } else {
        // 默认只查询有效记录
        query['bool'] = query['bool'] || { must: [] };
        query['bool'].must.push({
          term: {
            IsValid: 1,
          },
        });
      }

      return query;
    } catch (error) {
      this.logError('生成财务指标查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];

      // 处理每条记录
      for (const itemRaw of response.Result) {
        try {
          const item = cloneDeep(itemRaw);

          // 解析JSON字段
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              const value = item[key];
              try {
                item[key] = value ? JSON.parse(value) : {};
              } catch (error) {
                item[key] = value;
              }
            }
          });

          let isHit = true;

          // 处理财务指标
          if (item.Category === RiskChangeCategoryEnum.category49) {
            // 根据不同维度类型处理
            switch (dimension.key) {
              case DimensionTypeEnums.FinancialHealth:
                // 财务健康度处理
                const assetLiabilityRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.assetLiabilityRatio);
                if (assetLiabilityRatioField && isHit) {
                  isHit = await this.companyFinaceHelper.categoryTotalLiabToAssetsRatioField(assetLiabilityRatioField, item, item.KeyNo);
                }
                break;
            }
          }

          if (isHit) {
            hitData.push(item);
          }
        } catch (error) {
          this.logError('处理财务指标详情项失败', error);
        }
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理财务指标详情失败', error);
      return response;
    }
  }
}
