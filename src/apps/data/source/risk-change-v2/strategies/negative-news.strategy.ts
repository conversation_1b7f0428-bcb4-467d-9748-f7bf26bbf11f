import { Injectable } from '@nestjs/common';
import { BaseRiskChangeStrategy, DimensionCategoryMap } from './base-risk-change.strategy';
import { DimensionHitStrategyPO } from '../../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from '../../../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from '../../../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../../../libs/model/diligence/details/response';
import { DimensionFieldKeyEnums } from '../../../../../libs/enums/dimension/dimension.filter.params';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import * as _ from 'lodash';
import * as Bluebird from 'bluebird';
import * as moment from 'moment';
import { DATE_TIME_FORMAT } from '../../../../../libs/constants/common';
import { NegativePositiveTopicTypes } from '../../../../../libs/constants/news.constants';
import { getCompareResultForArray } from '../../../../../libs/utils/diligence/diligence.utils';
import { DimensionHitStrategyFieldsEntity } from '../../../../../libs/entities/DimensionHitStrategyFieldsEntity';
import { RiskChangeCategoryEnum } from '../../../../../libs/enums/riskchange/RiskChangeCategoryEnum';

/**
 * 负面新闻策略类
 * 处理负面新闻相关维度
 */
@Injectable()
export class NegativeNewsStrategy extends BaseRiskChangeStrategy {
  /**
   * 负面新闻相关维度列表
   */
  private readonly negativeNewsDimensions = [DimensionTypeEnums.NegativeNews, DimensionTypeEnums.NegativeNewsRecent, DimensionTypeEnums.NegativeNewsHistory];

  /**
   * 负面新闻维度与风险变更类别映射
   */
  private readonly negativeNewsDimensionCategoryMap: DimensionCategoryMap = {
    [DimensionTypeEnums.NegativeNews]: [
      RiskChangeCategoryEnum.category62, // 负面新闻
      RiskChangeCategoryEnum.category66, // 负面新闻
      RiskChangeCategoryEnum.category67, // 负面新闻
    ],
    [DimensionTypeEnums.NegativeNewsRecent]: [
      RiskChangeCategoryEnum.category62, // 负面新闻
      RiskChangeCategoryEnum.category66, // 负面新闻
      RiskChangeCategoryEnum.category67, // 负面新闻
    ],
    [DimensionTypeEnums.NegativeNewsHistory]: [
      RiskChangeCategoryEnum.category62, // 负面新闻
      RiskChangeCategoryEnum.category66, // 负面新闻
      RiskChangeCategoryEnum.category67, // 负面新闻
    ],
  };

  /**
   * 构造函数
   */
  constructor() {
    super(NegativeNewsStrategy.name);
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return this.negativeNewsDimensions;
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): DimensionCategoryMap {
    return this.negativeNewsDimensionCategoryMap;
  }

  /**
   * 检查维度类型是否由该策略处理
   * @param dimension 维度策略
   */
  supportsDimension(dimension: DimensionHitStrategyPO): boolean {
    return this.getSupportedDimensions().includes(dimension?.key);
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    if (!this.supportsDimension(dimension)) {
      return null;
    }

    const baseQuery = this.createBaseQuery(companyId);
    const dimensionType = dimension?.key as DimensionTypeEnums;

    // 获取该维度对应的风险变更类别
    const categories = this.getDimensionCategoryMap()[dimensionType];

    if (!categories?.length) {
      return baseQuery;
    }

    // 添加类别过滤条件
    (baseQuery as any).bool.must.push({
      terms: {
        Category: categories,
      },
    });

    // 添加有效性过滤
    (baseQuery as any).bool.must.push({
      term: {
        IsValid: 1,
      },
    });

    // 添加风险快讯标识过滤
    (baseQuery as any).bool.must.push({
      term: {
        IsRK: 1,
      },
    });

    // 根据不同的维度类型添加特定的查询条件
    switch (dimensionType) {
      case DimensionTypeEnums.NegativeNewsRecent:
        // 近期负面新闻，添加时间范围过滤
        (baseQuery as any).bool.must.push({
          range: {
            CreateDate: {
              gte: moment().subtract(3, 'month').format(DATE_TIME_FORMAT),
            },
          },
        });
        break;

      case DimensionTypeEnums.NegativeNewsHistory:
        // 历史负面新闻，添加时间范围过滤（3个月以前）
        (baseQuery as any).bool.must.push({
          range: {
            CreateDate: {
              lt: moment().subtract(3, 'month').format(DATE_TIME_FORMAT),
            },
          },
        });
        break;
    }

    // 添加主题过滤
    const topicsField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.topics);
    if (topicsField?.fieldValue?.length) {
      const topics = this.getDimensionTopics(topicsField.fieldValue);
      if (topics?.length) {
        (baseQuery as any).bool.must.push({
          terms: {
            'ChangeExtend.newTags': topics,
          },
        });
      }
    }

    // 添加维度过滤器中的时间范围
    const dimensionFilter = dimension?.dimensionFilter;
    if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
      (baseQuery as any).bool.must.push({
        range: {
          CreateDate: {
            gte: Math.ceil(dimensionFilter.startTime),
            lte: Math.ceil(dimensionFilter.endTime),
          },
        },
      });
    }

    return baseQuery;
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk || !response?.Result?.length) {
      return response;
    }

    // 处理负面新闻维度详情数据
    response.Result = await Bluebird.map(response.Result, async (item) => {
      try {
        // 确保ChangeExtend是对象
        if (typeof item.ChangeExtend === 'string') {
          try {
            item.ChangeExtend = JSON.parse(item.ChangeExtend);
          } catch (error) {
            this.logger.error(`解析ChangeExtend失败: ${error?.message || error}`);
          }
        }

        // 处理负面新闻详情
        this.processNegativeNewsDetail(item, dimension);
        return item;
      } catch (error) {
        this.logger.error(`处理负面新闻维度详情数据失败: ${error?.message || error}`);
        return item;
      }
    });

    return response;
  }

  /**
   * 处理负面新闻详情
   * @param item 数据项
   * @param dimension 维度策略
   * @private
   */
  private processNegativeNewsDetail(item: any, dimension: DimensionHitStrategyPO): void {
    // 获取维度字段
    const topicsField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.topics);

    // 处理主题字段
    if (topicsField) {
      const topics = this.getDimensionTopics(topicsField.fieldValue);
      item.HitTopics = this.negativePositiveNewsField(topics, item);

      // 提取命中的主题
      if (item.HitTopics && item.ChangeExtend?.newTags) {
        const hitTags = Array.isArray(item.ChangeExtend.newTags)
          ? item.ChangeExtend.newTags.filter((tag) => topics.includes(tag))
          : [item.ChangeExtend.newTags];

        item.HitTopicTags = hitTags;
      }
    }

    // 提取新闻信息
    this.extractNewsInfo(item);
  }

  /**
   * 提取新闻信息
   * @param item 数据项
   * @private
   */
  private extractNewsInfo(item: any): void {
    try {
      const changeExtend = item.ChangeExtend || {};

      // 提取新闻标题和内容
      item.NewsTitle = changeExtend.title || '';
      item.NewsContent = changeExtend.content || '';
      item.NewsSource = changeExtend.source || '';
      item.NewsUrl = changeExtend.url || '';
      item.NewsTime = changeExtend.time || item.CreateDate || '';

      // 提取新闻主题标签
      item.NewsTopics = changeExtend.newTags || [];
      if (!Array.isArray(item.NewsTopics)) {
        item.NewsTopics = [item.NewsTopics];
      }
    } catch (error) {
      this.logger.error(`提取新闻信息失败: ${error?.message || error}`);
    }
  }

  /**
   * 负面新闻主题匹配
   * @param topics 主题列表
   * @param item 数据项
   */
  private negativePositiveNewsField(topics: string[], item: any): boolean {
    let hit = false;
    // 判断主题是否匹配
    const topicSourceValue = item?.ChangeExtend?.newTags;

    // 如果主题为空或topics为空，则不命中
    if (!topicSourceValue || !topics?.length) {
      return false;
    }

    // 判断新闻主题是否在策略定义的主题中
    // 如果item中的主题是数组，则判断是否有交集
    if (Array.isArray(topicSourceValue)) {
      hit = topicSourceValue.some((tag) => topics.includes(tag));
    } else {
      // 如果是单个值，直接判断是否包含
      hit = topics.includes(topicSourceValue);
    }

    return hit;
  }

  /**
   * 获取维度主题列表
   * @param dimensionFieldValue 维度字段值
   */
  private getDimensionTopics(dimensionFieldValue: string[]): string[] {
    if (!dimensionFieldValue?.length) {
      return [];
    }

    // 如果包含'all'，则返回所有主题
    if (dimensionFieldValue.includes('all')) {
      return NegativePositiveTopicTypes.filter((t) => t.value !== 'all').map((t) => t.value);
    }

    return dimensionFieldValue;
  }
}
