import { RiskChangeStrategy } from '../interfaces/risk-change-strategy.interface';
import { DimensionHitStrategyPO } from '../../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from '../../../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from '../../../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../../../libs/model/diligence/details/response';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from '../../../../../libs/enums/riskchange/RiskChangeCategoryEnum';

/**
 * 维度类型与风险变更类别映射类型
 */
export type DimensionCategoryMap = {
  [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[];
};

/**
 * 风险变更策略基类
 * 实现RiskChangeStrategy接口的抽象基类
 */
export abstract class BaseRiskChangeStrategy implements RiskChangeStrategy {
  protected readonly logger: Logger;

  /**
   * 构造函数
   * @param strategyName 策略名称
   */
  constructor(private readonly strategyName: string) {
    this.logger = QccLogger.getLogger(strategyName);
  }

  /**
   * 获取支持的维度类型列表
   * 子类应该重写此方法以提供其支持的维度类型列表
   */
  abstract getSupportedDimensions(): DimensionTypeEnums[];

  /**
   * 获取维度类型与风险变更类别的映射
   * 子类应该重写此方法以提供其维度类型与类别的映射关系
   */
  abstract getDimensionCategoryMap(): DimensionCategoryMap;

  /**
   * 检查维度类型是否由该策略处理
   * @param dimension 维度策略
   */
  supportsDimension(dimension: DimensionHitStrategyPO): boolean {
    return this.getSupportedDimensions().includes(dimension.key);
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  abstract generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object>;

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  abstract processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse>;

  /**
   * 创建基础查询对象
   * @param companyId 企业ID
   */
  protected createBaseQuery(companyId: string): object {
    return {
      bool: {
        must: [
          {
            term: {
              KeyNo: companyId,
            },
          },
        ],
      },
    } as any;
  }

  /**
   * 记录错误日志
   * @param message 错误信息
   * @param error 错误对象
   */
  protected logError(message: string, error: any): void {
    this.logger.error(`${message}: ${error?.message || error}`, error);
  }
}
