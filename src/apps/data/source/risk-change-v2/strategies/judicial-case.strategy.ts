import { Injectable } from '@nestjs/common';
import { BaseRiskChangeStrategy, DimensionCategoryMap } from './base-risk-change.strategy';
import { DimensionHitStrategyPO } from '../../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from '../../../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import { HitDetailsBaseQueryParams } from '../../../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../../../libs/model/diligence/details/response';
import { DimensionFieldKeyEnums } from '../../../../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionHitStrategyFieldsEntity } from '../../../../../libs/entities/DimensionHitStrategyFieldsEntity';
import * as _ from 'lodash';
import { RiskChangeCategoryEnum } from '../../../../../libs/enums/riskchange/RiskChangeCategoryEnum';

/**
 * 司法案件相关常量
 */
const CourtRole = [
  { value: '22', label: '被执行人' },
  { value: '12', label: '申请执行人' },
];

const keyCauseActionMap = [
  { value: '9020', label: '金融不良债权' },
  { value: '9021', label: '票据追索' },
  { value: '9022', label: '信用卡纠纷' },
  { value: '9023', label: '借款合同纠纷' },
  { value: '9024', label: '融资租赁合同纠纷' },
  { value: '9025', label: '金融委托理财合同纠纷' },
  { value: '9026', label: '信托纠纷' },
  { value: '9027', label: '期货交易纠纷' },
  { value: '9028', label: '证券纠纷' },
  { value: '9029', label: '信用证纠纷' },
  { value: '9030', label: '保险纠纷' },
  { value: '9031', label: '其他金融纠纷' },
];

const caseReasonContractDisputeMap = [
  { value: '9001', label: '买卖合同纠纷', isFullMatch: false },
  { value: '9002', label: '招标投标买卖合同纠纷', isFullMatch: true },
  { value: '9003', label: '建设工程合同纠纷', isFullMatch: false },
  { value: '9004', label: '运输合同纠纷', isFullMatch: false },
  { value: '9005', label: '技术合同纠纷', isFullMatch: false },
  { value: '9006', label: '租赁合同纠纷', isFullMatch: false },
  { value: '9007', label: '承揽合同纠纷', isFullMatch: false },
  { value: '9008', label: '加工合同纠纷', isFullMatch: false },
  { value: '9009', label: '供用电、水、气、热力合同纠纷', isFullMatch: false },
  { value: '9010', label: '保管合同纠纷', isFullMatch: false },
  { value: '9011', label: '仓储合同纠纷', isFullMatch: false },
  { value: '9012', label: '委托合同纠纷', isFullMatch: false },
  { value: '9013', label: '行纪合同纠纷', isFullMatch: false },
  { value: '9014', label: '居间合同纠纷', isFullMatch: false },
  { value: '9015', label: '借款合同纠纷', isFullMatch: false },
  { value: '9016', label: '借用合同纠纷', isFullMatch: false },
  { value: '9017', label: '保证合同纠纷', isFullMatch: false },
  { value: '9018', label: '抵押合同纠纷', isFullMatch: false },
  { value: '9019', label: '质押合同纠纷', isFullMatch: false },
  { value: '9032', label: '劳务合同纠纷', isFullMatch: false },
  { value: '9033', label: '劳动争议', isFullMatch: false },
  { value: '9034', label: '承包合同纠纷', isFullMatch: false },
  { value: '9035', label: '其他合同纠纷', isFullMatch: false },
];

/**
 * 司法案件策略类
 * 处理司法案件相关维度的风险变更
 */
@Injectable()
export class JudicialCaseStrategy extends BaseRiskChangeStrategy {
  /**
   * 司法案件相关维度列表
   */
  private readonly judicialCaseDimensions = [
    DimensionTypeEnums.PersonExecution,
    DimensionTypeEnums.PersonExecutionHistory,
    DimensionTypeEnums.ContractBreach,
    DimensionTypeEnums.SalesContractDispute,
    DimensionTypeEnums.LaborContractDispute,
    DimensionTypeEnums.MajorDispute,
  ];

  /**
   * 司法案件维度与风险变更类别映射
   */
  private readonly judicialCaseDimensionCategoryMap: DimensionCategoryMap = {
    [DimensionTypeEnums.PersonExecution]: [
      RiskChangeCategoryEnum.category4, // 被执行人
    ],
    [DimensionTypeEnums.PersonExecutionHistory]: [
      RiskChangeCategoryEnum.category4, // 被执行人
    ],
    [DimensionTypeEnums.ContractBreach]: [
      RiskChangeCategoryEnum.category4, // 合同违约
    ],
    [DimensionTypeEnums.SalesContractDispute]: [
      RiskChangeCategoryEnum.category4, // 买卖合同纠纷
    ],
    [DimensionTypeEnums.LaborContractDispute]: [
      RiskChangeCategoryEnum.category4, // 劳动合同纠纷
    ],
    [DimensionTypeEnums.MajorDispute]: [
      RiskChangeCategoryEnum.category4, // 重大纠纷
    ],
  };

  /**
   * 构造函数
   */
  constructor() {
    super('JudicialCaseStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return this.judicialCaseDimensions;
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): DimensionCategoryMap {
    return this.judicialCaseDimensionCategoryMap;
  }

  /**
   * 检查维度类型是否由该策略处理
   * @param dimension 维度策略
   */
  supportsDimension(dimension: DimensionHitStrategyPO): boolean {
    const supportedDimensions = [
      DimensionTypeEnums.PersonExecution,
      DimensionTypeEnums.PersonExecutionHistory,
      DimensionTypeEnums.ContractBreach,
      DimensionTypeEnums.SalesContractDispute,
      DimensionTypeEnums.LaborContractDispute,
      DimensionTypeEnums.MajorDispute,
    ];
    return supportedDimensions.includes(dimension.key);
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    // 创建基础查询对象
    const baseQuery = this.createBaseQuery(companyId);
    const query = baseQuery as any;

    // 添加有效性过滤
    const isValidParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
    if (isValidParams && Number(isValidParams.fieldValue[0]) >= 0) {
      query.bool.must.push({ term: { IsValid: Number(isValidParams.fieldValue[0]) } });
    } else {
      // 默认只查询有效记录
      query.bool.must.push({ term: { IsValid: 1 } });
    }

    // 添加风险快讯标识
    query.bool.must.push({ term: { IsRK: 1 } });

    // 根据维度类型设置不同的查询条件
    switch (dimension.key) {
      case DimensionTypeEnums.PersonExecution:
      case DimensionTypeEnums.PersonExecutionHistory:
        // 被执行人相关查询
        query.bool.must.push({ term: { Category: 4 } });
        break;
      case DimensionTypeEnums.ContractBreach:
        // 合同违约相关查询
        query.bool.must.push({ term: { Category: 4 } });
        break;
      case DimensionTypeEnums.SalesContractDispute:
        // 买卖合同纠纷相关查询
        query.bool.must.push({ term: { Category: 4 } });
        break;
      case DimensionTypeEnums.LaborContractDispute:
        // 劳动合同纠纷相关查询
        query.bool.must.push({ term: { Category: 4 } });
        break;
      case DimensionTypeEnums.MajorDispute:
        // 重大纠纷相关查询
        query.bool.must.push({ term: { Category: 4 } });
        break;
      default:
        break;
    }

    // 添加时间范围过滤
    const dimensionFilter = dimension?.dimensionFilter;
    if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
      const range = {
        CreateDate: {
          gte: Math.ceil(dimensionFilter?.startTime),
          lte: Math.ceil(dimensionFilter?.endTime),
        },
      };
      query.bool.must.push({ range });
    }

    return query;
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      // 如果没有数据，直接返回
      if (!response?.Result?.length) {
        return response;
      }

      // 处理每条数据
      const processedData = response.Result.filter((item) => {
        try {
          // 获取司法角色类型字段
          const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
          if (!judicialRoleTypeField) {
            return true;
          }

          // 检查司法角色类型
          let isHit = this.category4(judicialRoleTypeField, item);

          // 检查案由类型
          const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
          if (caseReasonTypeField && isHit) {
            isHit = this.caseReasonTypeField(caseReasonTypeField, item);
          }

          // 检查案件类型
          const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
          if (caseTypeField && isHit) {
            isHit = this.checkCaseTypeField(caseTypeField, item);
          }

          // 检查是否是合同纠纷
          const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
          if (isContractDisputeField && isHit) {
            isHit = this.checkContractDisputeField(isContractDisputeField, item);
          }

          return isHit;
        } catch (error) {
          this.logError(`处理司法案件详情数据失败`, error);
          return false;
        }
      });

      // 更新响应数据
      response.Result = processedData;
      if (response.Paging) {
        response.Paging.TotalRecords = processedData.length;
      }

      return response;
    } catch (error) {
      this.logError(`处理司法案件维度详情数据失败`, error);
      return response;
    }
  }

  /**
   * 检查司法角色类型
   * @param judicialRoleTypeField 司法角色类型字段
   * @param item 数据项
   */
  private category4(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const judicialRoleTargetValues = judicialRoleTypeField?.fieldValue as string[];
    let judicialRoleSourceValues: string[] = [];
    const RNList = item?.ChangeExtend?.K?.filter((t) => t?.KeyNo === item?.KeyNo || t?.SupNameAndKeyNo?.KeyNo === item?.KeyNo)
      .map((r) => r?.RT)
      .filter((t) => !!t);

    if (RNList?.length) {
      const courtRoleTypes = CourtRole.map((t) => Number(t.value));
      RNList.forEach((t) => {
        if (courtRoleTypes.includes(t)) {
          judicialRoleSourceValues.push(String(t));
        }
      });
    }

    judicialRoleSourceValues = _.uniq(judicialRoleSourceValues);

    if (
      judicialRoleTargetValues?.length &&
      judicialRoleSourceValues?.length &&
      this.getCompareResultForArray(judicialRoleTypeField.compareType, judicialRoleSourceValues, judicialRoleTargetValues)
    ) {
      hit = true;
    }

    return hit;
  }

  /**
   * 判断当前案由是否是目标案由子类或者目标案由
   * @param caseReasonField 案由类型字段
   * @param item 数据项
   */
  private caseReasonTypeField(caseReasonField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const caseReasonTargetList = caseReasonField.fieldValue as string[];

    if (!caseReasonTargetList.length) {
      // 如果filedValue为空则默认为关键案由
      return this.caseReasonTypeFieldByKeyCauseAction(caseReasonField, item);
    }

    let caseReasonList: string[] = [];
    const RC = item?.ChangeExtend?.RC;

    if (RC) {
      if (caseReasonTargetList.some((t) => this.isCodePrefixMatch(t, RC))) {
        caseReasonList.push(String(RC));
      }
    }

    caseReasonList = _.uniq(caseReasonList);

    if (
      (caseReasonList?.length || caseReasonField.compareType === DimensionFieldCompareTypeEnums.ExceptAny) &&
      this.getCompareResultForArray(caseReasonField.compareType, caseReasonList, caseReasonTargetList)
    ) {
      hit = true;
    }

    return hit;
  }

  /**
   * 关键案由判断
   * @param judicialRoleTypeField 司法角色类型字段
   * @param item 数据项
   */
  private caseReasonTypeFieldByKeyCauseAction(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    // 取关键案由map中的value集合
    const judicialRoleTargetValues = keyCauseActionMap.map((item) => item.value) as string[];
    let judicialRoleSourceValues: string[] = [];
    const RC = item?.ChangeExtend?.RC;

    if (RC) {
      const keyCauseActionList = keyCauseActionMap.map((t) => t.value);
      if (keyCauseActionList.includes(RC)) {
        judicialRoleSourceValues.push(String(RC));
      }
    }

    judicialRoleSourceValues = _.uniq(judicialRoleSourceValues);

    if (
      judicialRoleSourceValues?.length &&
      this.getCompareResultForArray(judicialRoleTypeField.compareType, judicialRoleSourceValues, judicialRoleTargetValues)
    ) {
      hit = true;
    }

    return hit;
  }

  /**
   * 检查案件类型
   * @param caseTypeField 案件类型字段
   * @param item 数据项
   */
  private checkCaseTypeField(caseTypeField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const caseTypeFieldTargetValues = caseTypeField.fieldValue as number[];
    const caseTypeFieldSourceValue = item?.ChangeExtend?.T || 0;

    if (caseTypeFieldTargetValues?.length && this.getCompareResult(caseTypeFieldSourceValue, caseTypeFieldTargetValues[0], caseTypeField.compareType)) {
      hit = true;
    }

    return hit;
  }

  /**
   * 是否是合同纠纷
   * @param contractDisputeField 合同纠纷字段
   * @param item 数据项
   */
  private checkContractDisputeField(contractDisputeField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const contractDisputeFieldTargetValues = contractDisputeField.fieldValue as number[];
    const contractDisputeList: string[] = [];
    const RC = item?.ChangeExtend?.RC;

    if (RC) {
      if (caseReasonContractDisputeMap.some((t) => (t.isFullMatch ? this.isCodeExactMatch(t.value, RC) : this.isCodePrefixMatch(t.value, RC)))) {
        contractDisputeList.push(String(RC));
      }
    }

    const penaltyRedCardFieldSourceValue = contractDisputeList.length > 0 ? 1 : 0;

    if (
      contractDisputeFieldTargetValues?.length &&
      penaltyRedCardFieldSourceValue &&
      this.getCompareResult(penaltyRedCardFieldSourceValue, contractDisputeFieldTargetValues[0], contractDisputeField.compareType)
    ) {
      hit = true;
    }

    return hit;
  }

  /**
   * 比较单个值
   * @param sourceValue 源值
   * @param targetValue 目标值
   * @param compareType 比较类型
   */
  private getCompareResult(sourceValue: any, targetValue: any, compareType: DimensionFieldCompareTypeEnums): boolean {
    switch (compareType) {
      case DimensionFieldCompareTypeEnums.Equal:
        return sourceValue === targetValue;
      case DimensionFieldCompareTypeEnums.NotEqual:
        return sourceValue !== targetValue;
      case DimensionFieldCompareTypeEnums.GreaterThan:
        return sourceValue > targetValue;
      case DimensionFieldCompareTypeEnums.LessThan:
        return sourceValue < targetValue;
      case DimensionFieldCompareTypeEnums.GreaterThanOrEqual:
        return sourceValue >= targetValue;
      case DimensionFieldCompareTypeEnums.LessThanOrEqual:
        return sourceValue <= targetValue;
      default:
        return false;
    }
  }

  /**
   * 比较数组值
   * @param compareType 比较类型
   * @param sourceValues 源值数组
   * @param targetValues 目标值数组
   */
  private getCompareResultForArray(compareType: DimensionFieldCompareTypeEnums, sourceValues: any[], targetValues: any[]): boolean {
    switch (compareType) {
      case DimensionFieldCompareTypeEnums.ContainsAny:
        return sourceValues.some((sourceValue) => targetValues.includes(sourceValue));
      case DimensionFieldCompareTypeEnums.ContainsAll:
        return targetValues.every((targetValue) => sourceValues.includes(targetValue));
      case DimensionFieldCompareTypeEnums.ExceptAny:
        return !sourceValues.some((sourceValue) => targetValues.includes(sourceValue));
      case DimensionFieldCompareTypeEnums.ExceptAll:
        return !targetValues.every((targetValue) => sourceValues.includes(targetValue));
      default:
        return false;
    }
  }

  /**
   * 检查代码是否完全匹配
   * @param code1 代码1
   * @param code2 代码2
   */
  private isCodeExactMatch(code1: string, code2: string): boolean {
    return code1 === code2;
  }

  /**
   * 检查代码是否前缀匹配
   * @param prefix 前缀
   * @param code 代码
   */
  private isCodePrefixMatch(prefix: string, code: string): boolean {
    return code.startsWith(prefix);
  }
}
