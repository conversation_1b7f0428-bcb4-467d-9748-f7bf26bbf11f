import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from '../../../../../libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from '../../../../../libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from '../../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from '../../../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from '../../../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../../../libs/model/diligence/details/response';
import { BaseRiskChangeStrategy, DimensionCategoryMap } from './base-risk-change.strategy';
import { RiskChangeHelper } from '../../../../data/helper/risk.change.helper';
import { cloneDeep, orderBy } from 'lodash';
import { RiskChangeUtils } from '../utils/risk-change.utils';

/**
 * 法定代表人变更策略
 * 处理法定代表人变更相关的风险类型
 */
@Injectable()
export class LegalChangeStrategy extends BaseRiskChangeStrategy {
  /**
   * 维度类型与风险变更类别的映射
   */
  private readonly legalChangeDimensionCategoryMap: DimensionCategoryMap = {
    [DimensionTypeEnums.MainInfoUpdateLegalPerson]: [RiskChangeCategoryEnum.category39],
    [DimensionTypeEnums.MainInfoUpdateCapitalChange]: [RiskChangeCategoryEnum.category39],
  };

  /**
   * 构造函数
   * @param riskChangeHelper 风险变更辅助服务
   */
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super('LegalChangeStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return Object.keys(this.legalChangeDimensionCategoryMap) as DimensionTypeEnums[];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): DimensionCategoryMap {
    return this.legalChangeDimensionCategoryMap;
  }

  /**
   * 检查维度类型是否由该策略处理
   * @param dimension 维度策略
   */
  supportsDimension(dimension: DimensionHitStrategyPO): boolean {
    return this.getSupportedDimensions().includes(dimension.key);
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 使用工具类创建基础查询对象
      const query = RiskChangeUtils.createBaseQuery(companyId) as any;

      // 添加风险类别过滤
      const categories = this.getDimensionCategoryMap()[dimension.key];
      if (categories?.length) {
        query.bool.must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加维度过滤条件
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.must.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加有效性过滤
      const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidField && Number(isValidField.fieldValue[0]) >= 0) {
        query.bool.must.push({
          term: {
            IsValid: Number(isValidField.fieldValue[0]),
          },
        });
      } else {
        // 默认只查询有效记录
        query.bool.must.push({
          term: {
            IsValid: 1,
          },
        });
      }

      return query;
    } catch (error) {
      this.logError('生成法定代表人变更查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];

      // 处理每条记录
      for (const itemRaw of response.Result) {
        try {
          const item = cloneDeep(itemRaw);

          // 使用工具类解析JSON字段
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              item[key] = RiskChangeUtils.parseJsonField(item[key]);
            }
          });

          let isHit = true;

          // 处理法定代表人变更
          if (item.Category === RiskChangeCategoryEnum.category39) {
            const layTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.layTypes);
            if (layTypesField && isHit) {
              isHit = this.riskChangeHelper.hitLayTypesField(layTypesField, item);
            }
          }

          if (isHit) {
            // 增强数据，添加易于理解的信息
            this.enrichLegalPersonChangeInfo(item);
            hitData.push(item);
          }
        } catch (error) {
          this.logError('处理法定代表人变更详情项失败', error);
        }
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理法定代表人变更详情失败', error);
      return response;
    }
  }

  /**
   * 增强法定代表人变更信息，添加易于理解的字段
   * @param item 数据项
   */
  private enrichLegalPersonChangeInfo(item: any): void {
    try {
      const changeExtend = item.ChangeExtend || {};

      // 提取变更前后的法人信息
      item.BeforeLegalPerson = changeExtend.A || '';
      item.AfterLegalPerson = changeExtend.B || '';
      item.ChangeDate = item.ChangeDate || item.CreateDate;

      // 添加变更描述
      if (item.BeforeLegalPerson && item.AfterLegalPerson) {
        item.ChangeDescription = `法定代表人由"${item.BeforeLegalPerson}"变更为"${item.AfterLegalPerson}"`;
      }
    } catch (error) {
      this.logger.error(`增强法定代表人变更信息失败: ${error?.message || error}`);
    }
  }
}
