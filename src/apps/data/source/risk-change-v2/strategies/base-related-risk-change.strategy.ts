import { Injectable } from '@nestjs/common';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RelatedTypeEnums } from 'libs/enums/dimension/RelatedTypeEnums';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { PersonHelper } from '../../../helper/person.helper';
import { BaseRiskChangeStrategy } from './base-risk-change.strategy';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';

/**
 * 关联方风险变更策略基类
 * 提供处理关联方风险变更的基本方法
 */
@Injectable()
export abstract class BaseRelatedRiskChangeStrategy extends BaseRiskChangeStrategy {
  /**
   * 构造函数
   * @param strategyName 策略名称
   * @param companyDetailService 企业详情服务
   * @param companySearchService 企业搜索服务
   * @param personHelper 人员辅助服务
   */
  constructor(
    strategyName: string,
    protected readonly companyDetailService: CompanyDetailService,
    protected readonly companySearchService: CompanySearchService,
    protected readonly personHelper: PersonHelper,
  ) {
    super(strategyName);
  }

  /**
   * 获取关联方企业ID列表
   * @param companyId 企业ID
   * @param dimension 维度策略
   */
  protected async getRelatedCompanyIds(companyId: string, dimension: DimensionHitStrategyPO): Promise<string[]> {
    try {
      const companyIds: string[] = [];

      // 查询范围，如果指定查询关联方风险 获取关联方范围定义
      const relatedRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRoleType);
      if (!relatedRoleField) {
        return companyIds;
      }

      // 对外投资企业
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.InvestCompany)) {
        // 投资企业的状态
        const compnayStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessStatus);
        const status = compnayStatusField?.fieldValue;
        // 投资企业的持股比例
        const fundedRatioLevelField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.fundedRatioLevel);
        const fundedRatioLevel = fundedRatioLevelField?.fieldValue?.[0] || 0;
        // 符合条件的对外投资企业列表
        const { Paging, Result } = await this.companyDetailService.getInvestCompany(companyId, fundedRatioLevel, status, 200);
        if (Paging?.TotalRecords > 0) {
          companyIds.push(...Result.map((d) => d.KeyNo));
        }
      }

      // 实际控制人
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.ActualController)) {
        const personlist = await this.personHelper.getFinalActualController(companyId, false);
        const keyNos = personlist?.length ? personlist?.map((p) => p.keyNo).filter((t) => t) : [];
        if (keyNos?.length) {
          companyIds.push(...keyNos);
        }
      }

      // 大股东
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.MajorShareholder)) {
        const partnerList = await this.personHelper.getPartnerList(companyId, 'all');
        const bigStockers = partnerList.filter((partner) => partner?.tags.includes('大股东'));
        if (bigStockers?.length) {
          const keyNos = bigStockers?.length ? bigStockers?.map((p) => p.keyNo).filter((t) => t) : [];
          if (keyNos?.length) {
            companyIds.push(...keyNos);
          }
        }
      }

      // 上市主体企业
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.StockControlCompany)) {
        let isListCompany = false;
        const companyInfo = await this.companySearchService.companyDetailsQcc(companyId);
        if (companyInfo?.Tags?.length) {
          // 是否是上市企业
          const Tag122 = companyInfo?.Tags?.find((t) => t.Type === 122);
          if (Tag122) {
            const dataExtend2 = JSON.parse(Tag122?.DataExtend2 || '{}');
            const ListingStage = dataExtend2?.ListingStage;
            if (ListingStage === '1') {
              isListCompany = true;
            }
          }
          // 如果是上市企业
          if (isListCompany) {
            // 港股企业
            const Tag30 = companyInfo?.Tags?.find((t) => t.Type === 30);
            if (Tag30) {
              const dataExtend2 = JSON.parse(Tag30?.DataExtend2 || '{}');
              const companyKeyNo = dataExtend2?.KN;
              if (companyKeyNo) {
                companyIds.push(companyKeyNo);
              }
            } else {
              companyIds.push(companyId);
            }
          }
        }
      }

      return companyIds;
    } catch (error) {
      this.logError('获取关联方企业ID列表失败', error);
      return [];
    }
  }

  /**
   * 生成关联方维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 获取关联方企业ID列表
      const companyIds = await this.getRelatedCompanyIds(companyId, dimension);
      if (!companyIds.length) {
        return null;
      }

      // 创建查询对象
      const query = {
        bool: {
          filter: [],
        },
      };

      // 添加版本过滤
      query.bool.filter.push({ range: { Es_Version: { lt: 999999 } } });

      // 添加企业ID过滤
      query.bool.filter.push({ terms: { KeyNo: companyIds } });

      // 添加有效性过滤
      const isValidParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidParams && Number(isValidParams.fieldValue[0]) >= 0) {
        query.bool.filter.push({ term: { IsValid: Number(isValidParams.fieldValue[0]) } });
      } else {
        // 默认只查询有效记录
        query.bool.filter.push({ term: { IsValid: 1 } });
      }

      // 添加风险类别过滤
      const riskCategoriesParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.riskCategories);
      if (riskCategoriesParams) {
        const riskCategories = riskCategoriesParams.fieldValue;
        query.bool.filter.push({ terms: { Category: riskCategories } });
      } else {
        const categories = this.getDimensionCategoryMap()[dimension.key];
        if (categories?.length) {
          query.bool.filter.push({
            terms: {
              Category: categories,
            },
          });
        }
      }

      // 添加时间范围过滤
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.filter.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加ID过滤
      if (dimensionFilter?.id) {
        query.bool.filter.push({ term: { Id: dimensionFilter.id } });
      }

      return query;
    } catch (error) {
      this.logError('生成关联方维度查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理关联方企业信息
   * @param detail 详情数据
   * @param sourceCompanyId 源企业ID
   */
  protected async getRelatedCompanyInfo(detail: any, sourceCompanyId: string): Promise<any> {
    try {
      // 获取关联方企业ID
      const relatedCompanyId = this.extractRelatedCompanyId(detail, sourceCompanyId);
      if (!relatedCompanyId) {
        return null;
      }

      // 获取关联类型
      const relationType = this.getRelationType(detail);

      // 获取企业名称
      const companyInfo = await this.companySearchService.companyDetailsQcc(relatedCompanyId);
      const companyName = companyInfo?.Name || '';

      return {
        KeyNo: relatedCompanyId,
        Name: companyName,
        RelationType: relationType,
      };
    } catch (error) {
      this.logError('处理关联方企业信息失败', error);
      return null;
    }
  }

  /**
   * 提取关联方企业ID
   * @param detail 详情数据
   * @param sourceCompanyId 源企业ID
   */
  private extractRelatedCompanyId(detail: any, sourceCompanyId: string): string {
    try {
      return detail.KeyNo !== sourceCompanyId ? detail.KeyNo : '';
    } catch (error) {
      this.logError('提取关联方企业ID失败', error);
      return '';
    }
  }

  /**
   * 获取关联类型
   * @param detail 详情数据
   */
  private getRelationType(detail: any): string {
    try {
      // 根据不同的关联类型设置不同的描述
      // 这里可以根据实际业务逻辑扩展
      return '关联企业';
    } catch (error) {
      this.logError('获取关联类型失败', error);
      return '关联企业';
    }
  }

  /**
   * 处理关联方维度的详细信息
   * 该方法提供了与旧版本 detailAnalyzeForRelated 类似的功能
   * @param esHitDetails ES命中详情数据数组
   * @param dimension 维度策略
   * @param params 查询参数
   */
  protected async processRelatedDetails(esHitDetails: any[], dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<any[]> {
    if (!esHitDetails?.length) {
      return [];
    }

    const hitData = [];
    // 克隆数据，避免修改原始数据
    const clonedDetails = esHitDetails.map((item) => {
      const clonedItem = { ...item };
      // 解析JSON字段
      Object.keys(clonedItem).forEach((key) => {
        if (['Extend1', 'ChangeExtend'].includes(key)) {
          const value = clonedItem[key];
          try {
            clonedItem[key] = typeof value === 'string' ? JSON.parse(value) : value;
          } catch (error) {
            clonedItem[key] = value;
          }
        }
      });
      return clonedItem;
    });

    // 处理每一条详情数据
    for (const newItem of clonedDetails) {
      try {
        let isHit = true; // 默认命中

        // 关联方企业信息处理
        const relatedCompanyInfo = await this.getRelatedCompanyInfo(newItem, params.keyNo);
        if (relatedCompanyInfo) {
          newItem.RelatedCompanyInfo = relatedCompanyInfo;
        }

        // 根据风险变更类别进行处理
        const category = newItem.Category;

        switch (category) {
          // 负面/正面新闻
          case RiskChangeCategoryEnum.category62:
          case RiskChangeCategoryEnum.category66:
          case RiskChangeCategoryEnum.category67: {
            const topicsField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.topics);
            if (topicsField && isHit) {
              isHit = this.checkNewsTopic(topicsField.fieldValue, newItem);
            }
            break;
          }

          // 法定代表人变更
          case RiskChangeCategoryEnum.category39: {
            const layTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.layTypes);
            if (layTypesField && isHit) {
              isHit = this.checkLayTypes(layTypesField, newItem);
            }
            break;
          }

          // 主要人员变更
          case RiskChangeCategoryEnum.category46: {
            const compChangeRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.compChangeRole);
            if (compChangeRoleField && isHit) {
              isHit = this.checkCompChangeRole(compChangeRoleField, newItem);
            }
            break;
          }

          // 动产抵押
          case RiskChangeCategoryEnum.category15: {
            const guaranteedPrincipalField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.guaranteedPrincipal);
            if (guaranteedPrincipalField && isHit) {
              // 动产抵押金额检查
              isHit = this.checkMovableMortgageAmount(guaranteedPrincipalField, newItem);
            }
            break;
          }

          // 土地抵押
          case RiskChangeCategoryEnum.category30: {
            const landMortgageAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.landMortgageAmount);
            if (landMortgageAmountField && isHit) {
              // 土地抵押金额检查
              isHit = this.checkLandMortgageAmount(params.keyNo, landMortgageAmountField, newItem);
            }
            break;
          }

          // 担保信息
          case RiskChangeCategoryEnum.category53:
          case RiskChangeCategoryEnum.category101: {
            const guaranteeAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.guaranteeAmount);
            if (guaranteeAmountField && isHit) {
              // 担保金额检查
              isHit = this.checkGuaranteeAmount(guaranteeAmountField, newItem);
            }
            break;
          }

          // 税务催缴
          case RiskChangeCategoryEnum.category131: {
            const amountOwedField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.AmountOwed);
            if (amountOwedField && isHit) {
              // 欠税金额检查
              isHit = this.checkAmountOwed(amountOwedField, newItem);
            }
            break;
          }

          // 减资公告
          case RiskChangeCategoryEnum.category123: {
            const currencyChangeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.currencyChange);
            if (currencyChangeField && isHit) {
              // 币种变更检查
              isHit = this.checkCurrencyChange(currencyChangeField, newItem);
            }

            const capitalReductionRateField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.capitalReductionRate);
            if (capitalReductionRateField && isHit) {
              // 资本减少率检查
              isHit = this.checkCapitalReductionRate(capitalReductionRateField, newItem);
            }
            break;
          }

          // 持股比例变更
          case RiskChangeCategoryEnum.category68: {
            const shareChangeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeStatus);
            if (shareChangeStatusField && isHit) {
              // 持股变更状态检查
              isHit = this.checkShareChangeStatus(shareChangeStatusField, newItem);
            }

            const shareChangeRateField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeRate);
            if (shareChangeRateField && isHit) {
              // 持股变更率检查
              isHit = this.checkShareChangeRate(shareChangeRateField, newItem);
            }

            const beforeContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beforeContent);
            if (beforeContentField && isHit) {
              // 变更前内容检查
              isHit = this.checkBeforeContent(beforeContentField, newItem);
            }

            const afterContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.afterContent);
            if (afterContentField && isHit) {
              // 变更后内容检查
              isHit = this.checkAfterContent(afterContentField, newItem);
            }

            const isBPField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBP);
            if (isBPField && isHit) {
              // 是否BP检查
              isHit = this.checkIsBP(isBPField, newItem);
            }

            // 持有人角色检查
            const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            if (holderRoleField && isHit) {
              // 此处通常需要访问外部服务，子类应覆盖此方法
              isHit = await this.checkHolderRole(holderRoleField, newItem, params.keyNo);
            }
            break;
          }

          // 对外投资变更
          case RiskChangeCategoryEnum.category203:
          case RiskChangeCategoryEnum.category17: {
            const changeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.changeStatus);
            if (changeStatusField && isHit) {
              isHit = this.checkChangeStatus(changeStatusField, newItem);
            }

            const afterContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.afterContent);
            if (afterContentField && isHit) {
              isHit = this.checkAfterContent(afterContentField, newItem);
            }

            // 检查是否BP
            const isBPField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBP);
            if (isBPField && isHit) {
              isHit = this.checkIsBP(isBPField, newItem);
            }

            // 检查变更前内容
            const beforeContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beforeContent);
            if (beforeContentField && isHit) {
              isHit = this.checkBeforeContent(beforeContentField, newItem);
            }

            // 检查公司经营范围
            const companySocpeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companySocpe);
            if (companySocpeField && isHit) {
              isHit = await this.checkCompanyDetail(companySocpeField, newItem);
            }

            // 检查公司名称
            const companyNameField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyName);
            if (companyNameField && isHit) {
              isHit = await this.checkCompanyDetail(companyNameField, newItem);
            }

            // 检查公司行业
            const companyIndustryField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyIndustry);
            if (companyIndustryField && isHit) {
              isHit = await this.checkCompanyDetail(companyIndustryField, newItem);
            }

            // 检查企查查行业
            const qccIndustryField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.qccIndustry);
            if (qccIndustryField && isHit) {
              isHit = await this.checkCompanyDetail(qccIndustryField, newItem);
            }

            // 排除公司名称
            const excludeCompanyNameField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.excludeCompanyName);
            if (excludeCompanyNameField && isHit) {
              isHit = await this.checkCompanyDetail(excludeCompanyNameField, newItem);
            }
            break;
          }

          // 注册资本变更
          case RiskChangeCategoryEnum.category37: {
            const currencyChangeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.currencyChange);
            if (currencyChangeField && isHit) {
              isHit = this.checkCurrencyChange(currencyChangeField, newItem);
            }

            const regisCapitalTrendField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.regisCapitalTrend);
            if (regisCapitalTrendField && isHit) {
              isHit = this.checkRegisCapitalTrend(regisCapitalTrendField, newItem);
            }

            const regisCapitalChangeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.regisCapitalChangeRatio);
            if (regisCapitalChangeRatioField && isHit) {
              isHit = this.checkRegisCapitalChangeRatio(regisCapitalChangeRatioField, newItem);
            }
            break;
          }

          // 经营状态变更
          case RiskChangeCategoryEnum.category38: {
            const businessStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessStatus);
            if (businessStatusField && isHit) {
              isHit = this.checkBusinessStatus(businessStatusField, newItem);
            }
            break;
          }

          // 被限制高消费
          case RiskChangeCategoryEnum.category208:
          case RiskChangeCategoryEnum.category55: {
            const restricterTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.restricterType);
            if (restricterTypeField && isHit) {
              isHit = this.checkRestricterType(restricterTypeField, newItem);
            }
            break;
          }

          // 破产重整
          case RiskChangeCategoryEnum.category58: {
            // 破产重整一般直接命中
            break;
          }

          // 裁判文书
          case RiskChangeCategoryEnum.category221:
          case RiskChangeCategoryEnum.category4: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.checkJudicialRole(judicialRoleTypeField, newItem);
            }

            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.checkCaseReasonType(caseReasonTypeField, newItem);
            }

            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.checkCaseType(caseTypeField, newItem);
            }

            const lawsuitAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.lawsuitAmount);
            if (lawsuitAmountField && isHit) {
              isHit = this.checkLawsuitAmount(lawsuitAmountField, newItem);
            }

            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.checkIsContractDispute(isContractDisputeField, newItem);
            }

            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.checkIsFinancialReason(isFinancialReasonField, newItem);
            }
            break;
          }
        }

        // 如果命中，则添加到结果中
        if (isHit) {
          hitData.push(newItem);
        }
      } catch (error) {
        this.logError(`处理关联方详情数据失败: ${error?.message || String(error)}`, error);
      }
    }

    return hitData;
  }

  /**
   * 以下是各种检查方法，子类应根据业务需求覆盖这些方法提供实际实现
   */

  /**
   * 检查新闻话题是否匹配
   * 子类应根据业务需求覆盖此方法提供实际实现
   * @param topics 话题列表
   * @param item 详情数据
   */
  protected checkNewsTopic(topics: string[], item: any): boolean {
    try {
      // 基本实现 - 检查标题和内容是否包含话题关键词
      const title = item.ChangeExtend?.T || '';
      const content = item.ChangeExtend?.C || '';
      const combinedText = `${title} ${content}`;

      // 只要包含任一话题关键词即为匹配
      return topics.some((topic) => combinedText.includes(topic));
    } catch (error) {
      this.logError('检查新闻话题失败', error);
      return false;
    }
  }

  /**
   * 检查法定代表人变更类型
   * 子类应根据业务需求覆盖此方法提供实际实现
   * @param layTypesField 法定代表人变更类型字段
   * @param item 详情数据
   */
  protected checkLayTypes(layTypesField: any, item: any): boolean {
    try {
      const beforeContent = item.BeforeContent || '';
      const afterContent = item.AfterContent || '';
      const layTypes = layTypesField.fieldValue || [];

      // 检查变更前后的法定代表人是否为空
      if (!beforeContent && afterContent) {
        // 新增法定代表人
        return layTypes.includes('新增');
      } else if (beforeContent && !afterContent) {
        // 删除法定代表人
        return layTypes.includes('删除');
      } else if (beforeContent !== afterContent) {
        // 变更法定代表人
        return layTypes.includes('变更');
      }

      // 默认返回false
      return false;
    } catch (error) {
      this.logError('检查法定代表人变更类型失败', error);
      return false;
    }
  }

  /**
   * 检查主要人员变更角色
   * 子类应根据业务需求覆盖此方法提供实际实现
   * @param compChangeRoleField 主要人员变更角色字段
   * @param item 详情数据
   */
  protected checkCompChangeRole(compChangeRoleField: any, item: any): boolean {
    try {
      const roleTypes = compChangeRoleField.fieldValue || [];
      const changeInfo = item.ChangeExtend?.ChangeInfo || {};
      const personRole = changeInfo.R || ''; // 人员角色

      // 检查人员角色是否在指定角色列表中
      return roleTypes.some((role) => personRole.includes(role));
    } catch (error) {
      this.logError('检查主要人员变更角色失败', error);
      return false;
    }
  }

  /**
   * 检查变更状态
   * 子类应根据业务需求覆盖此方法提供实际实现
   * @param changeStatusField 变更状态字段
   * @param item 详情数据
   */
  protected checkChangeStatus(changeStatusField: any, item: any): boolean {
    try {
      const allowedStatuses = changeStatusField.fieldValue || [];
      const changeStatus = item.ChangeStatus || '';

      // 检查变更状态是否在允许的状态列表中
      return allowedStatuses.includes(changeStatus);
    } catch (error) {
      this.logError('检查变更状态失败', error);
      return false;
    }
  }

  /**
   * 检查变更后内容
   * 子类应根据业务需求覆盖此方法提供实际实现
   * @param afterContentField 变更后内容字段
   * @param item 详情数据
   */
  protected checkAfterContent(afterContentField: any, item: any): boolean {
    try {
      const afterContent = item.AfterContent || '';
      const matchPatterns = afterContentField.fieldValue || [];

      // 检查变更后内容是否匹配指定模式
      // 简单实现为检查是否包含指定字符串
      return matchPatterns.some((pattern) => afterContent.includes(pattern));
    } catch (error) {
      this.logError('检查变更后内容失败', error);
      return false;
    }
  }

  /**
   * 检查注册资本变更趋势
   * 子类应根据业务需求覆盖此方法提供实际实现
   * @param regisCapitalTrendField 注册资本变更趋势字段
   * @param item 详情数据
   */
  protected checkRegisCapitalTrend(regisCapitalTrendField: any, item: any): boolean {
    try {
      const allowedTrends = regisCapitalTrendField.fieldValue || [];

      // 从变更前后内容中计算变更趋势
      const beforeContent = parseFloat(item.BeforeContent || '0');
      const afterContent = parseFloat(item.AfterContent || '0');

      let trend = '';
      if (afterContent > beforeContent) {
        trend = 'up'; // 上升
      } else if (afterContent < beforeContent) {
        trend = 'down'; // 下降
      } else {
        trend = 'unchanged'; // 不变
      }

      // 检查变更趋势是否在允许的趋势列表中
      return allowedTrends.includes(trend);
    } catch (error) {
      this.logError('检查注册资本变更趋势失败', error);
      return false;
    }
  }

  /**
   * 检查注册资本变更比例
   * 子类应根据业务需求覆盖此方法提供实际实现
   * @param regisCapitalChangeRatioField 注册资本变更比例字段
   * @param item 详情数据
   */
  protected checkRegisCapitalChangeRatio(regisCapitalChangeRatioField: any, item: any): boolean {
    try {
      const minRatio = regisCapitalChangeRatioField.fieldValue?.[0] || 0;
      const maxRatio = regisCapitalChangeRatioField.fieldValue?.[1] || Number.MAX_SAFE_INTEGER;

      // 从变更前后内容中计算变更比例
      const beforeContent = parseFloat(item.BeforeContent || '0');
      const afterContent = parseFloat(item.AfterContent || '0');

      if (beforeContent === 0) {
        return afterContent > 0; // 无法计算比例，但有增加
      }

      // 计算变更比例 (%)
      const changeRatio = Math.abs(((afterContent - beforeContent) / beforeContent) * 100);

      // 检查变更比例是否在指定范围内
      return changeRatio >= minRatio && changeRatio <= maxRatio;
    } catch (error) {
      this.logError('检查注册资本变更比例失败', error);
      return false;
    }
  }

  /**
   * 检查动产抵押金额
   */
  protected checkMovableMortgageAmount(guaranteedPrincipalField: any, item: any): boolean {
    try {
      const amount = item.ChangeExtend?.A || 0;
      const min = parseFloat(guaranteedPrincipalField.fieldValue?.[0] || '0');
      const max = parseFloat(guaranteedPrincipalField.fieldValue?.[1] || 'Infinity');

      return amount >= min && amount <= max;
    } catch (error) {
      this.logError('检查动产抵押金额失败', error);
      return false;
    }
  }

  /**
   * 检查土地抵押金额
   */
  protected checkLandMortgageAmount(companyId: string, landMortgageAmountField: any, item: any): boolean {
    try {
      // 检查是否向企业抵押以及抵押权人
      // 实际实现需要根据业务逻辑补充
      return true;
    } catch (error) {
      this.logError('检查土地抵押金额失败', error);
      return false;
    }
  }

  /**
   * 检查担保金额
   */
  protected checkGuaranteeAmount(guaranteeAmountField: any, item: any): boolean {
    try {
      // 检查是否提供担保
      const changeInfo = item.ChangeExtend?.ChangeInfo || {};
      if (changeInfo.T !== 1) {
        // T===1表示提供担保
        return false;
      }

      // 检查担保金额
      const amount = parseFloat(changeInfo.G || '0');
      const min = parseFloat(guaranteeAmountField.fieldValue?.[0] || '0');
      const max = parseFloat(guaranteeAmountField.fieldValue?.[1] || 'Infinity');

      return amount >= min && amount <= max;
    } catch (error) {
      this.logError('检查担保金额失败', error);
      return false;
    }
  }

  /**
   * 检查欠税金额
   */
  protected checkAmountOwed(amountOwedField: any, item: any): boolean {
    try {
      const amount = parseFloat(item.ChangeExtend?.A || '0');
      const min = parseFloat(amountOwedField.fieldValue?.[0] || '0');
      const max = parseFloat(amountOwedField.fieldValue?.[1] || 'Infinity');

      return amount >= min && amount <= max;
    } catch (error) {
      this.logError('检查欠税金额失败', error);
      return false;
    }
  }

  /**
   * 检查币种变更
   */
  protected checkCurrencyChange(currencyChangeField: any, item: any): boolean {
    try {
      const beforeCurrency = item.ChangeExtend?.BC || '';
      const afterCurrency = item.ChangeExtend?.AC || '';
      const currencyValues = currencyChangeField.fieldValue || [];

      // 币种变更类型可能是：不变、变更、指定币种
      if (currencyValues.includes('不变')) {
        return beforeCurrency === afterCurrency;
      } else if (currencyValues.includes('变更')) {
        return beforeCurrency !== afterCurrency;
      } else {
        // 检查是否包含指定币种
        return currencyValues.includes(afterCurrency);
      }
    } catch (error) {
      this.logError('检查币种变更失败', error);
      return false;
    }
  }

  /**
   * 检查资本减少率
   */
  protected checkCapitalReductionRate(capitalReductionRateField: any, item: any): boolean {
    try {
      const beforeAmount = parseFloat(item.BeforeContent || '0');
      const afterAmount = parseFloat(item.AfterContent || '0');

      if (beforeAmount <= 0 || afterAmount >= beforeAmount) {
        return false; // 不是减资
      }

      // 计算减资比率
      const reductionRate = ((beforeAmount - afterAmount) / beforeAmount) * 100;
      const min = parseFloat(capitalReductionRateField.fieldValue?.[0] || '0');
      const max = parseFloat(capitalReductionRateField.fieldValue?.[1] || '100');

      return reductionRate >= min && reductionRate <= max;
    } catch (error) {
      this.logError('检查资本减少率失败', error);
      return false;
    }
  }

  /**
   * 检查持股变更状态
   */
  protected checkShareChangeStatus(shareChangeStatusField: any, item: any): boolean {
    try {
      const changeStatus = item.ChangeStatus || '';
      const allowedStatuses = shareChangeStatusField.fieldValue || [];

      return allowedStatuses.includes(changeStatus);
    } catch (error) {
      this.logError('检查持股变更状态失败', error);
      return false;
    }
  }

  /**
   * 检查持股变更率
   */
  protected checkShareChangeRate(shareChangeRateField: any, item: any): boolean {
    try {
      const beforeRate = parseFloat(item.BeforeContent || '0');
      const afterRate = parseFloat(item.AfterContent || '0');

      // 计算变更率
      let changeRate = 0;
      if (beforeRate > 0) {
        changeRate = Math.abs(((afterRate - beforeRate) / beforeRate) * 100);
      } else if (afterRate > 0) {
        changeRate = 100; // 从0到有
      }

      const min = parseFloat(shareChangeRateField.fieldValue?.[0] || '0');
      const max = parseFloat(shareChangeRateField.fieldValue?.[1] || '100');

      return changeRate >= min && changeRate <= max;
    } catch (error) {
      this.logError('检查持股变更率失败', error);
      return false;
    }
  }

  /**
   * 检查变更前内容
   */
  protected checkBeforeContent(beforeContentField: any, item: any): boolean {
    try {
      const beforeContent = item.BeforeContent || '';
      const patterns = beforeContentField.fieldValue || [];

      return patterns.some((pattern) => beforeContent.includes(pattern));
    } catch (error) {
      this.logError('检查变更前内容失败', error);
      return false;
    }
  }

  /**
   * 检查是否BP
   */
  protected checkIsBP(isBPField: any, item: any): boolean {
    try {
      const isBP = isBPField.fieldValue?.[0] === '1';
      const changeInfo = item.ChangeExtend?.ChangeInfo || {};
      const actualIsBP = changeInfo.IsBP === 1;

      return isBP === actualIsBP;
    } catch (error) {
      this.logError('检查是否BP失败', error);
      return false;
    }
  }

  /**
   * 检查持有人角色
   * 由于这可能需要外部服务调用，子类应覆盖此方法
   */
  protected async checkHolderRole(holderRoleField: any, item: any, companyId: string): Promise<boolean> {
    try {
      // 默认实现
      return true;
    } catch (error) {
      this.logError('检查持有人角色失败', error);
      return false;
    }
  }

  /**
   * 检查公司详情
   * 由于这可能需要外部服务调用，子类应覆盖此方法
   */
  protected async checkCompanyDetail(typeField: any, item: any): Promise<boolean> {
    try {
      // 默认实现
      return true;
    } catch (error) {
      this.logError('检查公司详情失败', error);
      return false;
    }
  }

  /**
   * 检查经营状态
   */
  protected checkBusinessStatus(businessStatusField: any, item: any): boolean {
    try {
      const afterStatus = item.AfterContent || '';
      const allowedStatuses = businessStatusField.fieldValue || [];

      return allowedStatuses.includes(afterStatus);
    } catch (error) {
      this.logError('检查经营状态失败', error);
      return false;
    }
  }

  /**
   * 检查限制类型
   */
  protected checkRestricterType(restricterTypeField: any, item: any): boolean {
    try {
      const restricterType = item.ChangeExtend?.RT || '';
      const allowedTypes = restricterTypeField.fieldValue || [];

      return allowedTypes.some((type) => restricterType.includes(type));
    } catch (error) {
      this.logError('检查限制类型失败', error);
      return false;
    }
  }

  /**
   * 检查司法角色
   */
  protected checkJudicialRole(judicialRoleTypeField: any, item: any): boolean {
    try {
      const judicialRole = item.ChangeExtend?.JR || '';
      const allowedRoles = judicialRoleTypeField.fieldValue || [];

      return allowedRoles.includes(judicialRole);
    } catch (error) {
      this.logError('检查司法角色失败', error);
      return false;
    }
  }

  /**
   * 检查案由类型
   */
  protected checkCaseReasonType(caseReasonTypeField: any, item: any): boolean {
    try {
      const caseReason = item.ChangeExtend?.CR || '';
      const allowedTypes = caseReasonTypeField.fieldValue || [];

      return allowedTypes.some((type) => caseReason.includes(type));
    } catch (error) {
      this.logError('检查案由类型失败', error);
      return false;
    }
  }

  /**
   * 检查案件类型
   */
  protected checkCaseType(caseTypeField: any, item: any): boolean {
    try {
      const caseType = item.ChangeExtend?.CT || '';
      const allowedTypes = caseTypeField.fieldValue || [];

      return allowedTypes.includes(caseType);
    } catch (error) {
      this.logError('检查案件类型失败', error);
      return false;
    }
  }

  /**
   * 检查诉讼金额
   */
  protected checkLawsuitAmount(lawsuitAmountField: any, item: any): boolean {
    try {
      const amount = parseFloat(item.ChangeExtend?.I || '0');
      const min = parseFloat(lawsuitAmountField.fieldValue?.[0] || '0');
      const max = parseFloat(lawsuitAmountField.fieldValue?.[1] || 'Infinity');

      return amount >= min && amount <= max;
    } catch (error) {
      this.logError('检查诉讼金额失败', error);
      return false;
    }
  }

  /**
   * 检查是否合同纠纷
   */
  protected checkIsContractDispute(isContractDisputeField: any, item: any): boolean {
    try {
      const isContractDispute = isContractDisputeField.fieldValue?.[0] === '1';
      const caseReason = item.ChangeExtend?.CR || '';
      const actualIsContractDispute = caseReason.includes('合同') || caseReason.includes('合同纠纷');

      return isContractDispute === actualIsContractDispute;
    } catch (error) {
      this.logError('检查是否合同纠纷失败', error);
      return false;
    }
  }

  /**
   * 检查是否金融原因
   */
  protected checkIsFinancialReason(isFinancialReasonField: any, item: any): boolean {
    try {
      const isFinancialReason = isFinancialReasonField.fieldValue?.[0] === '1';
      const caseReason = item.ChangeExtend?.CR || '';
      const financialKeywords = ['借款', '金融', '贷款', '融资', '信用证', '票据'];
      const actualIsFinancialReason = financialKeywords.some((keyword) => caseReason.includes(keyword));

      return isFinancialReason === actualIsFinancialReason;
    } catch (error) {
      this.logError('检查是否金融原因失败', error);
      return false;
    }
  }
}
