import { Injectable } from '@nestjs/common';
import { BaseRiskChangeStrategy, DimensionCategoryMap } from './base-risk-change.strategy';
import { DimensionHitStrategyPO } from '../../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from '../../../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import { HitDetailsBaseQueryParams } from '../../../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../../../libs/model/diligence/details/response';
import { DimensionFieldKeyEnums } from '../../../../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionHitStrategyFieldsEntity } from '../../../../../libs/entities/DimensionHitStrategyFieldsEntity';
import * as _ from 'lodash';
import { RiskChangeCategoryEnum } from '../../../../../libs/enums/riskchange/RiskChangeCategoryEnum';

/**
 * 行政处罚相关常量
 */
// 行政处罚原因
const penaltyReason107Map = [
  { value: '1', label: '商业贿赂' },
  { value: '2', label: '垄断行为' },
  { value: '3', label: '政府采购活动违法行为' },
];

// 行政处罚结果
const penaltyResult107Map = [
  { value: '1', label: '没收违法所得' },
  { value: '2', label: '没收非法财物' },
  { value: '3', label: '罚款' },
  { value: '4', label: '责令停产停业' },
  { value: '5', label: '暂扣或者吊销许可证' },
  { value: '6', label: '暂扣或者吊销执照' },
];

// 排除的行政处罚结果
const excludePenaltyResult107Map = [
  { value: '7', label: '警告' },
  { value: '8', label: '通报批评' },
];

// 环保处罚原因
const penaltyReason22Map = [
  { value: '1', label: '环境污染' },
  { value: '2', label: '生态破坏' },
];

// 环保处罚结果
const penaltyResult22Map = [
  { value: '1', label: '没收违法所得' },
  { value: '2', label: '没收非法财物' },
  { value: '3', label: '罚款' },
  { value: '4', label: '责令停产停业' },
  { value: '5', label: '暂扣或者吊销许可证' },
  { value: '6', label: '暂扣或者吊销执照' },
];

// 排除的环保处罚结果
const excludePenaltyResult22Map = [
  { value: '7', label: '警告' },
  { value: '8', label: '通报批评' },
];

// 处罚单位类型
const PenaltyUnitType = [
  { value: 1, label: '市场监督管理局' },
  { value: 2, label: '工商行政管理局' },
  { value: 3, label: '质量技术监督局' },
  { value: 4, label: '食品药品监督管理局' },
  { value: 5, label: '安全生产监督管理局' },
  { value: 6, label: '环境保护局' },
  { value: 7, label: '海关总署' },
  { value: 8, label: '税务局' },
];

/**
 * 行政处罚策略类
 * 处理行政处罚相关维度的风险变更
 */
@Injectable()
export class AdministrativePenaltyStrategy extends BaseRiskChangeStrategy {
  /**
   * 行政处罚相关维度列表
   */
  private readonly administrativePenaltyDimensions = [
    DimensionTypeEnums.AdministrativePenalties,
    DimensionTypeEnums.AdministrativePenalties2,
    DimensionTypeEnums.AdministrativePenalties3,
    DimensionTypeEnums.TaxPenalties,
    DimensionTypeEnums.EnvironmentalPenalties,
  ];

  /**
   * 行政处罚维度与风险变更类别映射
   */
  private readonly administrativePenaltyDimensionCategoryMap: DimensionCategoryMap = {
    [DimensionTypeEnums.AdministrativePenalties]: [
      RiskChangeCategoryEnum.category107, // 行政处罚
    ],
    [DimensionTypeEnums.AdministrativePenalties2]: [
      RiskChangeCategoryEnum.category107, // 行政处罚
    ],
    [DimensionTypeEnums.AdministrativePenalties3]: [
      RiskChangeCategoryEnum.category107, // 行政处罚
    ],
    [DimensionTypeEnums.TaxPenalties]: [
      RiskChangeCategoryEnum.category31, // 税务处罚
    ],
    [DimensionTypeEnums.EnvironmentalPenalties]: [
      RiskChangeCategoryEnum.category22, // 环保处罚
    ],
  };

  /**
   * 构造函数
   */
  constructor() {
    super('AdministrativePenaltyStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return this.administrativePenaltyDimensions;
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): DimensionCategoryMap {
    return this.administrativePenaltyDimensionCategoryMap;
  }

  /**
   * 检查维度类型是否由该策略处理
   * @param dimension 维度策略
   */
  supportsDimension(dimension: DimensionHitStrategyPO): boolean {
    const supportedDimensions = [
      DimensionTypeEnums.AdministrativePenalties,
      DimensionTypeEnums.AdministrativePenalties2,
      DimensionTypeEnums.AdministrativePenalties3,
      DimensionTypeEnums.TaxPenalties,
      DimensionTypeEnums.EnvironmentalPenalties,
    ];
    return supportedDimensions.includes(dimension.key);
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    // 创建基础查询对象
    const baseQuery = this.createBaseQuery(companyId);
    const query = baseQuery as any;

    // 添加有效性过滤
    const isValidParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
    if (isValidParams && Number(isValidParams.fieldValue[0]) >= 0) {
      query.bool.must.push({ term: { IsValid: Number(isValidParams.fieldValue[0]) } });
    } else {
      // 默认只查询有效记录
      query.bool.must.push({ term: { IsValid: 1 } });
    }

    // 添加风险快讯标识
    query.bool.must.push({ term: { IsRK: 1 } });

    // 根据维度类型设置不同的查询条件
    switch (dimension.key) {
      case DimensionTypeEnums.AdministrativePenalties:
      case DimensionTypeEnums.AdministrativePenalties2:
      case DimensionTypeEnums.AdministrativePenalties3:
        // 行政处罚相关查询
        query.bool.must.push({ term: { Category: 107 } });
        break;
      case DimensionTypeEnums.TaxPenalties:
        // 税务处罚相关查询
        query.bool.must.push({ term: { Category: 31 } });
        break;
      case DimensionTypeEnums.EnvironmentalPenalties:
        // 环保处罚相关查询
        query.bool.must.push({ term: { Category: 22 } });
        break;
      default:
        break;
    }

    // 添加时间范围过滤
    const dimensionFilter = dimension?.dimensionFilter;
    if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
      const range = {
        CreateDate: {
          gte: Math.ceil(dimensionFilter?.startTime),
          lte: Math.ceil(dimensionFilter?.endTime),
        },
      };
      query.bool.must.push({ range });
    }

    return query;
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      // 如果没有数据，直接返回
      if (!response?.Result?.length) {
        return response;
      }

      // 处理每条数据
      const processedData = response.Result.filter((item) => {
        try {
          let isHit = true;

          // 根据维度类型进行不同的处理
          switch (dimension.key) {
            case DimensionTypeEnums.AdministrativePenalties:
            case DimensionTypeEnums.AdministrativePenalties2:
            case DimensionTypeEnums.AdministrativePenalties3:
              // 处理行政处罚
              const punishRedCardField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishRedCard);
              if (punishRedCardField) {
                isHit = this.penaltyRedCardFieldCategory107(punishRedCardField, item);
              }

              // 处理处罚发布单位
              const penaltyIssuingUnitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyIssuingUnit);
              if (penaltyIssuingUnitField && isHit) {
                isHit = this.penaltyIssuingUnitField(penaltyIssuingUnitField, item);
              }
              break;

            case DimensionTypeEnums.TaxPenalties:
              // 处理税务处罚
              const penaltyUnitField31 = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyUnit);
              if (penaltyUnitField31 && isHit) {
                isHit = this.penaltyUnitField31(penaltyUnitField31, item);
              }
              break;

            case DimensionTypeEnums.EnvironmentalPenalties:
              // 处理环保处罚
              const punishRedCardFieldEnv = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishRedCard);
              if (punishRedCardFieldEnv) {
                isHit = this.penaltyRedCardFieldCategory22(punishRedCardFieldEnv, item);
              }

              // 处理处罚单位
              const penaltyUnitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyUnit);
              if (penaltyUnitField && isHit) {
                isHit = this.penaltyUnitField(penaltyUnitField, item);
              }
              break;

            default:
              break;
          }

          return isHit;
        } catch (error) {
          this.logError(`处理行政处罚详情数据失败`, error);
          return false;
        }
      });

      // 更新响应数据
      response.Result = processedData;
      if (response.Paging) {
        response.Paging.TotalRecords = processedData.length;
      }

      return response;
    } catch (error) {
      this.logError(`处理行政处罚维度详情数据失败`, error);
      return response;
    }
  }

  /**
   * 行政处罚，红牌处罚
   * @param penaltyRedCardField 处罚红牌字段
   * @param item 数据项
   */
  private penaltyRedCardFieldCategory107(penaltyRedCardField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const penFieldTargetValues = penaltyRedCardField.fieldValue as number[];
    const penaltyRedCardList: string[] = [];

    // 检查处罚原因
    const B = item?.ChangeExtend?.B;
    if (B) {
      if (
        this.containsKeywords(
          B,
          penaltyReason107Map.map((t) => t.label),
        )
      ) {
        penaltyRedCardList.push(String(B));
      }
    }

    // 检查处罚结果
    const C = item?.ChangeExtend?.C;
    if (C) {
      if (
        this.containsKeywords(
          C,
          penaltyResult107Map.map((t) => t.label),
          excludePenaltyResult107Map.map((t) => t.label),
        )
      ) {
        penaltyRedCardList.push(String(C));
      }
    }

    const penaltyRedCardFieldSourceValue = penaltyRedCardList.length > 0 ? 1 : 0;

    if (
      penFieldTargetValues?.length &&
      penaltyRedCardFieldSourceValue &&
      this.getCompareResult(penaltyRedCardFieldSourceValue, penFieldTargetValues[0], penaltyRedCardField.compareType)
    ) {
      hit = true;
    }

    return hit;
  }

  /**
   * 环保处罚，红牌处罚
   * @param penaltyRedCardField 处罚红牌字段
   * @param item 数据项
   */
  private penaltyRedCardFieldCategory22(penaltyRedCardField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const penFieldTargetValues = penaltyRedCardField?.fieldValue as number[];
    const penaltyRedCardList: string[] = [];

    // 检查处罚原因
    const A = item?.ChangeExtend?.A;
    if (A) {
      if (
        this.containsKeywords(
          A,
          penaltyReason22Map.map((t) => t.label),
        )
      ) {
        penaltyRedCardList.push(String(A));
      }
    }

    // 检查处罚结果
    const B = item?.ChangeExtend?.B;
    if (B) {
      if (
        this.containsKeywords(
          B,
          penaltyResult22Map.map((t) => t.label),
          excludePenaltyResult22Map.map((t) => t.label),
        )
      ) {
        penaltyRedCardList.push(String(B));
      }
    }

    const penaltyRedCardFieldSourceValue = penaltyRedCardList.length > 0 ? 1 : 0;

    if (
      penFieldTargetValues?.length &&
      penaltyRedCardFieldSourceValue &&
      this.getCompareResult(penaltyRedCardFieldSourceValue, penFieldTargetValues[0], penaltyRedCardField.compareType)
    ) {
      hit = true;
    }

    return hit;
  }

  /**
   * 行政处罚，处罚单位
   * @param penaltyUnitField 处罚单位字段
   * @param item 数据项
   */
  private penaltyUnitField(penaltyUnitField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const penaltyUnitFieldTargetValues = penaltyUnitField?.fieldValue as number[];
    const penaltyUnitFieldSourceValues: number[] = [];

    if (item?.ChangeExtend?.A) {
      PenaltyUnitType.forEach((t) => {
        if (item?.ChangeExtend?.A?.includes(t.label)) {
          penaltyUnitFieldSourceValues.push(t.value);
        }
      });
    }

    // 如果为空的
    if (
      [DimensionFieldCompareTypeEnums.ExceptAny, DimensionFieldCompareTypeEnums.ExceptAll].includes(penaltyUnitField.compareType) &&
      penaltyUnitFieldSourceValues?.length === 0
    ) {
      hit = true;
    }

    if (
      penaltyUnitFieldTargetValues?.length &&
      penaltyUnitFieldSourceValues?.length &&
      this.getCompareResultForArray(penaltyUnitField.compareType, penaltyUnitFieldSourceValues, penaltyUnitFieldTargetValues)
    ) {
      hit = true;
    }

    return hit;
  }

  /**
   * 行政处罚，处罚发布单位
   * @param penaltyIssuingUnitField 处罚发布单位字段
   * @param item 数据项
   */
  private penaltyIssuingUnitField(penaltyIssuingUnitField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    // 历史模型配置的number 做兼容强转string
    const penaltyUnitFieldTargetValues = penaltyIssuingUnitField?.fieldValue?.map(String) || [];
    const penaltyUnitFieldSourceValues: any[] = [];

    if (item?.ChangeExtend?.G1) {
      const g1 = item?.ChangeExtend?.G1?.toString() ?? '';
      const values = penaltyIssuingUnitField.options.filter((t) => t.value === g1).map((t) => t.value);
      penaltyUnitFieldSourceValues.push(...values);
    }

    if (penaltyUnitFieldTargetValues.includes('9907') && item?.ChangeExtend?.G2 === '9907') {
      penaltyUnitFieldSourceValues.push(item?.ChangeExtend?.G2);
    }

    // 如果为空的
    if (
      [DimensionFieldCompareTypeEnums.ExceptAny, DimensionFieldCompareTypeEnums.ExceptAll].includes(penaltyIssuingUnitField.compareType) &&
      penaltyUnitFieldSourceValues?.length === 0
    ) {
      hit = true;
    }

    if (
      penaltyUnitFieldTargetValues?.length &&
      penaltyUnitFieldSourceValues?.length &&
      this.getCompareResultForArray(penaltyIssuingUnitField.compareType, penaltyUnitFieldSourceValues, penaltyUnitFieldTargetValues)
    ) {
      hit = true;
    }

    return hit;
  }

  /**
   * 行政处罚，处罚单位（税务）
   * @param penaltyUnitField 处罚单位字段
   * @param item 数据项
   */
  private penaltyUnitField31(penaltyUnitField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    let hit = false;
    const penaltyUnitFieldTargetValues = penaltyUnitField?.fieldValue as number[];
    const penaltyUnitFieldSourceValues: number[] = [];

    if (item?.ChangeExtend?.D) {
      penaltyUnitField.options.forEach((t) => {
        if (item?.ChangeExtend?.D?.includes(t.label)) {
          penaltyUnitFieldSourceValues.push(t.value);
        }
      });
    }

    // 如果为空的
    if (
      [DimensionFieldCompareTypeEnums.ExceptAny, DimensionFieldCompareTypeEnums.ExceptAll].includes(penaltyUnitField.compareType) &&
      penaltyUnitFieldSourceValues?.length === 0
    ) {
      hit = true;
    }

    if (
      penaltyUnitFieldTargetValues?.length &&
      penaltyUnitFieldSourceValues?.length &&
      this.getCompareResultForArray(penaltyUnitField.compareType, penaltyUnitFieldSourceValues, penaltyUnitFieldTargetValues)
    ) {
      hit = true;
    }

    return hit;
  }

  /**
   * 检查文本是否包含指定关键词
   * @param text 文本
   * @param keywords 关键词列表
   * @param excludeKeywords 排除的关键词列表
   */
  private containsKeywords(text: string, keywords: string[], excludeKeywords: string[] = []): boolean {
    if (!text) {
      return false;
    }

    // 检查是否包含任一关键词
    const containsIncludeKeyword = keywords.some((keyword) => text.includes(keyword));

    // 检查是否包含任一排除关键词
    const containsExcludeKeyword = excludeKeywords.length > 0 && excludeKeywords.some((keyword) => text.includes(keyword));

    // 包含关键词且不包含排除关键词
    return containsIncludeKeyword && !containsExcludeKeyword;
  }

  /**
   * 比较单个值
   * @param sourceValue 源值
   * @param targetValue 目标值
   * @param compareType 比较类型
   */
  private getCompareResult(sourceValue: any, targetValue: any, compareType: DimensionFieldCompareTypeEnums): boolean {
    switch (compareType) {
      case DimensionFieldCompareTypeEnums.Equal:
        return sourceValue === targetValue;
      case DimensionFieldCompareTypeEnums.NotEqual:
        return sourceValue !== targetValue;
      case DimensionFieldCompareTypeEnums.GreaterThan:
        return sourceValue > targetValue;
      case DimensionFieldCompareTypeEnums.LessThan:
        return sourceValue < targetValue;
      case DimensionFieldCompareTypeEnums.GreaterThanOrEqual:
        return sourceValue >= targetValue;
      case DimensionFieldCompareTypeEnums.LessThanOrEqual:
        return sourceValue <= targetValue;
      default:
        return false;
    }
  }

  /**
   * 比较数组值
   * @param compareType 比较类型
   * @param sourceValues 源值数组
   * @param targetValues 目标值数组
   */
  private getCompareResultForArray(compareType: DimensionFieldCompareTypeEnums, sourceValues: any[], targetValues: any[]): boolean {
    switch (compareType) {
      case DimensionFieldCompareTypeEnums.ContainsAny:
        return sourceValues.some((sourceValue) => targetValues.includes(sourceValue));
      case DimensionFieldCompareTypeEnums.ContainsAll:
        return targetValues.every((targetValue) => sourceValues.includes(targetValue));
      case DimensionFieldCompareTypeEnums.ExceptAny:
        return !sourceValues.some((sourceValue) => targetValues.includes(sourceValue));
      case DimensionFieldCompareTypeEnums.ExceptAll:
        return !targetValues.every((targetValue) => sourceValues.includes(targetValue));
      default:
        return false;
    }
  }
}
