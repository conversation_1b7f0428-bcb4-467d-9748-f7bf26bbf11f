import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRelatedRiskChangeStrategy } from './base-related-risk-change.strategy';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonHelper } from '../../../helper/person.helper';
import { orderBy, cloneDeep } from 'lodash';
import { RiskChangeHelper } from '../../../helper/risk.change.helper';

/**
 * 投资企业注销风险策略
 * 处理投资企业注销风险相关的维度
 */
@Injectable()
export class InvestCompanyCancellationStrategy extends BaseRelatedRiskChangeStrategy {
  /**
   * 构造函数
   * @param companyDetailService 企业详情服务
   * @param companySearchService 企业搜索服务
   * @param personHelper 人员辅助服务
   * @param riskChangeHelper 风险变更辅助服务
   */
  constructor(
    protected readonly companyDetailService: CompanyDetailService,
    protected readonly companySearchService: CompanySearchService,
    protected readonly personHelper: PersonHelper,
    private readonly riskChangeHelper: RiskChangeHelper,
  ) {
    super('InvestCompanyCancellationStrategy', companyDetailService, companySearchService, personHelper);
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.RecentInvestCancellationsRiskChange];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.RecentInvestCancellationsRiskChange]: [
        RiskChangeCategoryEnum.category42, // 企业注销
        RiskChangeCategoryEnum.category3, // 被列入被执行人
        RiskChangeCategoryEnum.category38, // 经营状态变更
        RiskChangeCategoryEnum.category58, // 破产重整
      ],
    };
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      // 1. 首先使用基类的 processRelatedDetails 方法处理详情数据
      const processedDetails = await this.processRelatedDetails(response.Result, dimension, params);

      // 2. 执行对外投资企业特定的处理逻辑
      let hitData = processedDetails;

      // 2.1 检查投资企业数量阈值条件
      let investCompanyCount = null;
      const fundedRatioLevelField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.fundedRatioLevel);
      if (fundedRatioLevelField) {
        // 投资企业的持股比例
        const fundedRatioLevel = fundedRatioLevelField?.fieldValue?.[0] || 0;
        // 验证是否满足命中阈值
        const { Paging } = await this.companyDetailService.getInvestCompany(params.keyNo, fundedRatioLevel);
        investCompanyCount = Paging.TotalRecords;

        this.logger.debug(`投资企业总数: ${investCompanyCount}, 注销/吊销数量: ${hitData.length}`);
      }

      // 2.2 检查阈值规则条件
      const thresholdRuleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.thresholdRule);
      if (thresholdRuleField && investCompanyCount !== null) {
        let isHit = false;

        for (const rule of thresholdRuleField.fieldValue) {
          // 检查投资企业数量是否在阈值范围内
          if (this.checkValueInRange(investCompanyCount, rule.investCount?.[0], rule.investCount?.[1])) {
            // 计算比例
            const ratio = hitData.length / investCompanyCount;
            const threshold = rule.threshold / 100;

            this.logger.debug(`检查阈值规则: 投资企业注销/吊销比例 ${ratio.toFixed(4)}，阈值 ${threshold.toFixed(4)}`);

            // 近期对外投资企业大量注销或吊销的数量 / 对外投资企业数量 >= 阈值%
            if (ratio >= threshold) {
              isHit = true;
              this.logger.debug(
                `满足阈值规则: 投资企业数量在 [${rule.investCount?.[0] || '0'}, ${rule.investCount?.[1] || '∞'}] 范围内，比例 ${ratio.toFixed(
                  4,
                )} >= ${threshold.toFixed(4)}`,
              );
              break;
            }
          }
        }

        // 如果不满足阈值规则，清空命中数据
        if (!isHit) {
          this.logger.debug(`不满足任何阈值规则，清空命中数据`);
          hitData = [];
        }
      }

      // 2.3 检查命中数量条件
      const hitCountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
      if (hitCountField && investCompanyCount !== null) {
        const compareType = hitCountField?.compareType;
        const thresholdValue = hitCountField?.fieldValue?.[0];

        this.logger.debug(`检查命中数量条件: 投资企业数量 ${investCompanyCount}，阈值 ${thresholdValue}，比较类型 ${compareType}`);

        // 如果投资企业数量不满足条件，清空命中数据
        if (!this.compareValues(investCompanyCount, thresholdValue, compareType)) {
          this.logger.debug(`投资企业数量不满足条件，清空命中数据`);
          hitData = [];
        }
      }

      // 3. 增强命中数据，添加投资关系信息
      if (hitData.length > 0) {
        await this.enrichHitDataWithInvestmentInfo(hitData, params.keyNo);
      }

      // 4. 创建响应结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理投资企业注销风险详情失败', error);
      return response;
    }
  }

  /**
   * 增强命中数据，添加投资关系信息
   * @param hitData 命中数据
   * @param companyId 企业ID
   */
  private async enrichHitDataWithInvestmentInfo(hitData: any[], companyId: string): Promise<void> {
    try {
      // 获取所有投资企业详情
      const { Result } = await this.companyDetailService.getInvestCompany(companyId, 0, null, 1000);
      if (!Result?.length) {
        return;
      }

      // 创建投资企业映射，以便快速查找
      const investCompanyMap = {};
      Result.forEach((company) => {
        investCompanyMap[company.KeyNo] = company;
      });

      // 为每条命中数据添加投资信息
      for (const item of hitData) {
        const keyNo = item.KeyNo;
        const investInfo = investCompanyMap[keyNo];

        if (investInfo) {
          item.InvestmentInfo = {
            InvestAmount: investInfo.Amount || '未知',
            InvestDate: investInfo.ShortStatus || '未知',
            Ratio: investInfo.StockPercent || '未知',
            Industry: investInfo.Industry || '未知',
          };
        }
      }
    } catch (error) {
      this.logError('增强命中数据失败', error);
    }
  }

  /**
   * 检查值是否在指定范围内
   * @param value 要检查的值
   * @param min 最小值（可选）
   * @param max 最大值（可选）
   */
  private checkValueInRange(value: number, min?: number, max?: number): boolean {
    if (min !== undefined && value < min) {
      return false;
    }
    if (max !== undefined && value > max) {
      return false;
    }
    return true;
  }

  /**
   * 根据比较类型比较两个值
   * @param value1 第一个值
   * @param value2 第二个值
   * @param compareType 比较类型
   */
  private compareValues(value1: number, value2: number, compareType: string): boolean {
    switch (compareType) {
      case 'Equal':
        return value1 === value2;
      case 'NotEqual':
        return value1 !== value2;
      case 'GreaterThan':
        return value1 > value2;
      case 'GreaterThanOrEqual':
        return value1 >= value2;
      case 'LessThan':
        return value1 < value2;
      case 'LessThanOrEqual':
        return value1 <= value2;
      case 'Between':
        // 对于 Between 类型，value2 应该是一个数组 [min, max]
        // 由于我们不能直接处理这种情况，这里简化处理
        return true;
      default:
        return false;
    }
  }

  /**
   * 检查业务状态
   * 对投资企业注销风险有特殊处理
   */
  protected checkBusinessStatus(businessStatusField: any, item: any): boolean {
    try {
      const afterStatus = item.AfterContent || '';
      const allowedStatuses = businessStatusField.fieldValue || [];

      // 对于投资企业注销风险，我们特别关注"注销"和"吊销"状态
      if (afterStatus.includes('注销') || afterStatus.includes('吊销')) {
        return true;
      }

      return allowedStatuses.includes(afterStatus);
    } catch (error) {
      this.logError('检查经营状态失败', error);
      return false;
    }
  }
}
