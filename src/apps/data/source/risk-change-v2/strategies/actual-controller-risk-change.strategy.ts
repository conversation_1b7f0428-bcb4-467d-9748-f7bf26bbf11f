import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRelatedRiskChangeStrategy } from './base-related-risk-change.strategy';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonHelper } from '../../../helper/person.helper';
import { orderBy, cloneDeep } from 'lodash';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';

/**
 * 实控人风险变更策略
 * 处理实控人风险变更相关的维度
 */
@Injectable()
export class ActualControllerRiskChangeStrategy extends BaseRelatedRiskChangeStrategy {
  /**
   * 构造函数
   * @param companyDetailService 企业详情服务
   * @param companySearchService 企业搜索服务
   * @param personHelper 人员辅助服务
   */
  constructor(
    protected readonly companyDetailService: CompanyDetailService,
    protected readonly companySearchService: CompanySearchService,
    protected readonly personHelper: PersonHelper,
  ) {
    super('ActualControllerRiskChangeStrategy', companyDetailService, companySearchService, personHelper);
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.ActualControllerRiskChange];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.ActualControllerRiskChange]: [
        RiskChangeCategoryEnum.category39, // 法定代表人变更
        RiskChangeCategoryEnum.category62, // 负面新闻
        RiskChangeCategoryEnum.category66, // 负面新闻
        RiskChangeCategoryEnum.category67, // 负面新闻
        RiskChangeCategoryEnum.category4, // 裁判文书
        RiskChangeCategoryEnum.category221, // 裁判文书
        RiskChangeCategoryEnum.category203, // 对外投资变更
        RiskChangeCategoryEnum.category17, // 对外投资变更
      ],
    };
  }

  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      // 1. 使用基类的 processRelatedDetails 方法处理详情数据
      const processedDetails = await this.processRelatedDetails(response.Result, dimension, params);

      // 2. 执行实控人风险特定的处理逻辑
      let hitData = processedDetails;

      // 2.1 检查时间周期条件
      const timePeriodField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.timePeriod);
      if (timePeriodField) {
        // 已在查询条件中处理过时间周期，这里不需要额外处理
        this.logger.debug(`实控人风险变更使用时间周期: ${timePeriodField.fieldValue[0]} 个月`);
      }

      // 2.2 检查阈值数量条件
      const thresholdCountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.thresholdCount);
      if (thresholdCountField && hitData.length > 0) {
        const thresholdCount = thresholdCountField?.fieldValue?.[0] || 0;
        const compareType = thresholdCountField?.compareType || 'GreaterThan';

        this.logger.debug(`实控人风险变更数量: ${hitData.length}, 阈值: ${thresholdCount}, 比较类型: ${compareType}`);

        // 如果命中数量不满足条件，清空命中数据
        if (!this.compareValues(hitData.length, thresholdCount, compareType)) {
          this.logger.debug(`实控人风险变更数量不满足条件，清空命中数据`);
          hitData = [];
        }
      }

      // 2.3 检查行业阈值条件
      const industryThresholdField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.industryThreshold);
      if (industryThresholdField && hitData.length > 0) {
        const isHitIndustryThreshold = await this.checkIndustryThreshold(industryThresholdField, hitData, params.keyNo);
        this.logger.debug(`实控人风险变更行业阈值检查结果: ${isHitIndustryThreshold}`);

        if (!isHitIndustryThreshold) {
          hitData = [];
        }
      }

      // 3. 创建响应结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      // 4. 为结果添加额外信息
      if (result.Result.length > 0) {
        await this.enrichResultWithActualControllerInfo(result.Result, params.keyNo);
      }

      return result;
    } catch (error) {
      this.logError('处理实控人风险变更详情失败', error);
      return response;
    }
  }

  /**
   * 增强结果数据，添加实际控制人信息
   * @param results 结果数据
   * @param companyId 企业ID
   */
  private async enrichResultWithActualControllerInfo(results: any[], companyId: string): Promise<void> {
    try {
      // 获取企业的实际控制人列表
      const actualControllers = await this.personHelper.getFinalActualController(companyId, false);
      if (!actualControllers?.length) {
        return;
      }

      // 为每条结果添加实际控制人信息
      for (const result of results) {
        const relatedKeyNo = result.KeyNo;
        const matchedController = actualControllers.find((controller) => controller.keyNo === relatedKeyNo);

        if (matchedController) {
          result.ActualControllerInfo = {
            Name: matchedController.name,
            KeyNo: matchedController.keyNo,
            ControlRatio: '未知',
            Type: '自然人',
          };
        }
      }
    } catch (error) {
      this.logError('增强结果数据失败', error);
    }
  }

  /**
   * 根据比较类型比较两个值
   * @param value1 第一个值
   * @param value2 第二个值
   * @param compareType 比较类型
   */
  private compareValues(value1: number, value2: number, compareType: string): boolean {
    switch (compareType) {
      case 'Equal':
        return value1 === value2;
      case 'NotEqual':
        return value1 !== value2;
      case 'GreaterThan':
        return value1 > value2;
      case 'GreaterThanOrEqual':
        return value1 >= value2;
      case 'LessThan':
        return value1 < value2;
      case 'LessThanOrEqual':
        return value1 <= value2;
      case 'Between':
        // 对于 Between 类型，value2 应该是一个数组 [min, max]
        // 由于我们不能直接处理这种情况，这里简化处理
        return true;
      default:
        return false;
    }
  }

  /**
   * 检查是否满足行业阈值条件
   * @param industryThresholdField 行业阈值字段
   * @param hitItems 命中项目列表
   * @param companyId 企业ID
   */
  private async checkIndustryThreshold(industryThresholdField: any, hitItems: any[], companyId: string): Promise<boolean> {
    try {
      // 获取企业行业信息
      const companyInfo = await this.companySearchService.companyDetailsQcc(companyId);
      if (!companyInfo) {
        return false;
      }

      // 获取行业信息
      const industry = companyInfo.IndustryV3 || '';
      if (!industry) {
        return false;
      }

      // 检查行业阈值
      const thresholds = industryThresholdField.fieldValue || [];
      for (const threshold of thresholds) {
        // 检查行业是否匹配
        if (industry.includes(threshold.industry)) {
          // 检查命中数量是否超过阈值
          return hitItems.length >= threshold.count;
        }
      }

      // 如果没有匹配的行业阈值，使用默认阈值
      const defaultThreshold = thresholds.find((t) => t.industry === '默认') || { count: 3 };
      return hitItems.length >= defaultThreshold.count;
    } catch (error) {
      this.logError('检查行业阈值失败', error);
      return false;
    }
  }

  /**
   * 检查新闻话题是否匹配
   * 实控人风险变更对新闻话题有特殊处理
   */
  protected checkNewsTopic(topics: string[], item: any): boolean {
    try {
      // 获取新闻标题和内容
      const title = item.ChangeExtend?.T || '';
      const content = item.ChangeExtend?.C || '';
      const combinedText = `${title} ${content}`;

      // 实控人相关关键词
      const controllerKeywords = ['实际控制人', '控股股东', '大股东', '法定代表人'];

      // 检查是否包含实控人相关关键词
      const hasControllerKeyword = controllerKeywords.some((keyword) => combinedText.includes(keyword));

      // 如果不包含实控人相关关键词，则不命中
      if (!hasControllerKeyword) {
        return false;
      }

      // 只要包含任一话题关键词即为匹配
      return topics.some((topic) => combinedText.includes(topic));
    } catch (error) {
      this.logError('检查新闻话题失败', error);
      return false;
    }
  }

  /**
   * 检查法定代表人变更类型
   * 实控人风险变更对法定代表人变更有特殊处理
   */
  protected checkLayTypes(layTypesField: any, item: any): boolean {
    // 先调用基类方法检查法定代表人变更类型
    const baseResult = super.checkLayTypes(layTypesField, item);

    if (!baseResult) {
      return false;
    }

    try {
      // 检查变更人是否为实控人
      const beforeContent = item.BeforeContent || '';
      const afterContent = item.AfterContent || '';

      // 获取变更的法定代表人
      let changedPerson = '';
      if (!beforeContent && afterContent) {
        changedPerson = afterContent; // 新增
      } else if (beforeContent && !afterContent) {
        changedPerson = beforeContent; // 删除
      } else if (beforeContent !== afterContent) {
        changedPerson = `${beforeContent},${afterContent}`; // 变更
      }

      // 检查变更的法定代表人是否为实控人
      // 实际实现需要通过personHelper.getFinalActualController来验证
      // 这里简化处理，只要有变更就认为命中
      return !!changedPerson;
    } catch (error) {
      this.logError('检查法定代表人变更类型失败', error);
      return baseResult; // 出错时返回基类结果
    }
  }
}
