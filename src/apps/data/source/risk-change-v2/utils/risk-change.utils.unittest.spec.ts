import { RiskChangeUtils } from './risk-change.utils';
import * as moment from 'moment';
import { DATE_TIME_FORMAT } from '../../../../../libs/constants/common';

describe('RiskChangeUtils', () => {
  describe('parseCapital', () => {
    it('应该正确解析包含单位的资本值', () => {
      const testCases = [
        { input: '1000万元', expected: 1000 },
        { input: '2000万人民币', expected: 2000 },
        { input: '500万美元', expected: 500 },
        { input: '1.5亿元', expected: 1.5 },
        { input: '100万港币', expected: 100 },
        { input: '200万欧元', expected: 200 },
      ];

      testCases.forEach(({ input, expected }) => {
        const result = RiskChangeUtils.parseCapital(input);
        expect(result).toBe(expected);
      });
    });

    it('应该处理无效的资本值输入', () => {
      const testCases = [
        { input: '不是数字', expected: 0 },
        { input: '', expected: 0 },
        { input: null, expected: 0 },
        { input: undefined, expected: 0 },
      ];

      testCases.forEach(({ input, expected }) => {
        const result = RiskChangeUtils.parseCapital(input as any);
        expect(result).toBe(expected);
      });
    });
  });

  describe('excludeAmountUnits', () => {
    it('应该移除金额单位', () => {
      const testCases = [
        { input: '1000万元', expected: '1000' },
        { input: '2000万人民币', expected: '2000' },
        { input: '500万美元', expected: '500' },
        { input: '1.5亿元', expected: '1.5' },
        { input: '壹佰万元整', expected: '100' },
      ];

      testCases.forEach(({ input, expected }) => {
        const result = RiskChangeUtils.excludeAmountUnits(input);
        expect(result).toBe(expected);
      });
    });

    it('应该处理无效的输入', () => {
      const testCases = [
        { input: '', expected: '0' },
        { input: null, expected: '0' },
        { input: undefined, expected: '0' },
      ];

      testCases.forEach(({ input, expected }) => {
        const result = RiskChangeUtils.excludeAmountUnits(input as any);
        expect(result).toBe(expected);
      });
    });
  });

  describe('calculateCapitalChangeRatio', () => {
    it('应该正确计算资本减少比例', () => {
      const beforeCapital = 1000;
      const afterCapital = 500;
      const changeType = 1; // 减少

      const result = RiskChangeUtils.calculateCapitalChangeRatio(beforeCapital, afterCapital, changeType);
      expect(result).toBe(50); // (1000-500)/1000*100 = 50%
    });

    it('应该正确计算资本增加比例', () => {
      const beforeCapital = 1000;
      const afterCapital = 1500;
      const changeType = 2; // 增加

      const result = RiskChangeUtils.calculateCapitalChangeRatio(beforeCapital, afterCapital, changeType);
      expect(result).toBe(50); // (1500-1000)/1000*100 = 50%
    });

    it('应该处理字符串类型的资本值', () => {
      const beforeCapital = '1000万元';
      const afterCapital = '500万元';
      const changeType = 1; // 减少

      const result = RiskChangeUtils.calculateCapitalChangeRatio(beforeCapital, afterCapital, changeType);
      expect(result).toBe(50);
    });

    it('应该处理无效的输入', () => {
      const testCases = [
        { before: 0, after: 500, type: 1, expected: 0 },
        { before: 1000, after: 0, type: 1, expected: 0 },
        { before: '无效', after: 500, type: 1, expected: 0 },
      ];

      testCases.forEach(({ before, after, type, expected }) => {
        const result = RiskChangeUtils.calculateCapitalChangeRatio(before, after, type);
        expect(result).toBe(expected);
      });
    });
  });

  describe('getTimePeriodDate', () => {
    it('应该返回正确的年份时间周期', () => {
      const now = moment();
      const periodTime = 2; // 2年
      const periodUnit = 'year';

      const result = RiskChangeUtils.getTimePeriodDate(periodTime, periodUnit);
      const expected = moment()
        .subtract(periodTime - 1, 'year')
        .startOf('year')
        .format(DATE_TIME_FORMAT);

      expect(result).toBe(expected);
    });

    it('应该返回正确的月份时间周期', () => {
      const periodTime = 3; // 3个月
      const periodUnit = 'month';

      const result = RiskChangeUtils.getTimePeriodDate(periodTime, periodUnit);
      const expected = moment().subtract(periodTime, 'month').format(DATE_TIME_FORMAT);

      expect(result).toBe(expected);
    });

    it('应该处理中文单位', () => {
      const periodTime = 2;
      const yearResult = RiskChangeUtils.getTimePeriodDate(periodTime, '年');
      const monthResult = RiskChangeUtils.getTimePeriodDate(periodTime, '月');
      const dayResult = RiskChangeUtils.getTimePeriodDate(periodTime, '日');

      const yearExpected = moment()
        .subtract(periodTime - 1, 'year')
        .startOf('year')
        .format(DATE_TIME_FORMAT);
      const monthExpected = moment().subtract(periodTime, 'month').format(DATE_TIME_FORMAT);
      const dayExpected = moment().subtract(periodTime, 'day').format(DATE_TIME_FORMAT);

      expect(yearResult).toBe(yearExpected);
      expect(monthResult).toBe(monthExpected);
      expect(dayResult).toBe(dayExpected);
    });

    it('应该使用默认月份单位处理未知单位', () => {
      const periodTime = 2;
      const result = RiskChangeUtils.getTimePeriodDate(periodTime, '未知单位');
      const expected = moment().subtract(periodTime, 'month').format(DATE_TIME_FORMAT);

      expect(result).toBe(expected);
    });
  });

  describe('parseJsonField', () => {
    it('应该正确解析JSON字符串', () => {
      const jsonStr = '{"name":"张三","age":30}';
      const result = RiskChangeUtils.parseJsonField(jsonStr);
      expect(result).toEqual({ name: '张三', age: 30 });
    });

    it('应该直接返回已经是对象的值', () => {
      const obj = { name: '张三', age: 30 };
      const result = RiskChangeUtils.parseJsonField(obj);
      expect(result).toBe(obj);
    });

    it('应该处理无效的JSON字符串', () => {
      const invalidJson = '{name:"张三"'; // 缺少右括号
      const result = RiskChangeUtils.parseJsonField(invalidJson);
      expect(result).toBe(invalidJson); // 保持原样返回
    });

    it('应该处理空值', () => {
      const testCases = [
        { input: '', expected: {} },
        { input: null, expected: {} },
        { input: undefined, expected: {} },
      ];

      testCases.forEach(({ input, expected }) => {
        const result = RiskChangeUtils.parseJsonField(input as any);
        expect(result).toEqual(expected);
      });
    });
  });

  describe('createBaseQuery', () => {
    it('应该创建正确的基础查询对象', () => {
      const companyId = 'test123';
      const result = RiskChangeUtils.createBaseQuery(companyId);

      expect(result).toEqual({
        bool: {
          must: [
            {
              term: {
                KeyNo: companyId,
              },
            },
          ],
        },
      });
    });
  });
});
