import * as moment from 'moment';
import { DATE_TIME_FORMAT } from '../../../../../libs/constants/common';

/**
 * 风险变更工具类
 * 提供风险变更相关的常用工具方法
 */
export class RiskChangeUtils {
  /**
   * 解析资本值，移除单位和非数字字符
   * @param capitalStr 资本字符串
   * @returns 解析后的数值
   */
  public static parseCapital(capitalStr: string): number {
    if (!capitalStr) {
      return 0;
    }

    try {
      // 移除单位字符，如"万元"、"万人民币"等
      const cleanedStr = this.excludeAmountUnits(capitalStr.trim());
      return Number(cleanedStr) || 0;
    } catch (error) {
      console.error('parseCapital error', error);
      return 0;
    }
  }

  /**
   * 移除金额单位
   * @param str 原始字符串
   * @returns 移除单位后的字符串
   */
  public static excludeAmountUnits(str: string): string {
    if (!str) {
      return '0';
    }

    // 匹配常见的金额单位
    const unitRegexps = [
      /万元/g,
      /万人民币/g,
      /万港币/g,
      /万美元/g,
      /万欧元/g,
      /万日元/g,
      /万/g,
      /元/g,
      /人民币/g,
      /港币/g,
      /美元/g,
      /欧元/g,
      /日元/g,
      /百万/g,
      /亿/g,
    ];

    // 移除所有单位
    let result = str;
    unitRegexps.forEach((regexp) => {
      result = result.replace(regexp, '');
    });

    // 移除非数字字符
    result = result.replace(/[^\d.]/g, '');

    return result || '0';
  }

  /**
   * 计算资本变更比例
   * @param beforeCapital 变更前资本
   * @param afterCapital 变更后资本
   * @param changeType 变更类型：1-减少，2-增加
   * @returns 变更比例（百分比）
   */
  public static calculateCapitalChangeRatio(beforeCapital: string | number, afterCapital: string | number, changeType: number): number {
    try {
      // 解析资本值
      const before = typeof beforeCapital === 'string' ? this.parseCapital(beforeCapital) : beforeCapital;
      const after = typeof afterCapital === 'string' ? this.parseCapital(afterCapital) : afterCapital;

      if (before <= 0 || after <= 0) {
        return 0;
      }

      let ratio = 0;
      if (changeType === 1) {
        // 减少
        ratio = ((before - after) / before) * 100;
      } else {
        // 增加或其他
        ratio = ((after - before) / before) * 100;
      }

      // 保留两位小数
      return parseFloat(ratio.toFixed(2));
    } catch (error) {
      console.error('calculateCapitalChangeRatio error', error);
      return 0;
    }
  }

  /**
   * 获取时间周期日期
   * @param periodTime 周期时间
   * @param periodUnit 周期单位：year/month/day
   * @returns 格式化的日期字符串
   */
  public static getTimePeriodDate(periodTime: number, periodUnit: string): string {
    switch (periodUnit) {
      case '年':
      case 'year': {
        return moment()
          .subtract(periodTime - 1, 'year')
          .startOf('year')
          .format(DATE_TIME_FORMAT);
      }
      case '月':
      case 'month': {
        return moment().subtract(periodTime, 'month').format(DATE_TIME_FORMAT);
      }
      case '日':
      case 'day': {
        return moment().subtract(periodTime, 'day').format(DATE_TIME_FORMAT);
      }
      default:
        return moment().subtract(periodTime, 'month').format(DATE_TIME_FORMAT);
    }
  }

  /**
   * 解析JSON字段
   * @param value 需要解析的值
   * @returns 解析后的对象
   */
  public static parseJsonField(value: any): any {
    if (!value) {
      return {};
    }

    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch (error) {
      console.error('parseJsonField error', error);
      return value;
    }
  }

  /**
   * 创建基础查询对象
   * @param companyId 企业ID
   * @returns 基础查询对象
   */
  public static createBaseQuery(companyId: string): object {
    return {
      bool: {
        must: [
          {
            term: {
              KeyNo: companyId,
            },
          },
        ],
      },
    };
  }
}
