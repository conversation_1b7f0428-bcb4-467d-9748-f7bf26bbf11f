# 风险动态模块工作流程

## 1. 总体架构

风险动态模块主要用于处理企业风险变更数据，支持以下两种维度的风险分析：

1. **普通维度**：直接查询企业本身的风险动态
2. **关联方维度**：查询与企业相关联的实体（如实控人、对外投资企业等）的风险动态

主要文件结构如下：

- `risk-change-es.source.ts`：核心文件，处理 ES 查询和数据分析
- `dimension-hit-detail.processor.ts`：处理非关联方数据维度命中详情
- `related-dimension-hit-detail.processor.ts`：处理关联方数据维度命中详情
- `helper/`：辅助方法集合，用于处理不同数据维度和 category 的处理逻辑

## 2. 核心调用流程

### 2.1 analyze 方法流程

`analyze`方法是主要的对外接口，用于分析企业风险动态的命中情况：

1. **区分维度类型**：

   - 将传入的维度按关联方和非关联方分类
   - 关联方维度包括：`RecentInvestCancellationsRiskChange`, `ActualControllerRiskChange`, `ListedEntityRiskChange`

2. **处理关联方维度**：

   - 调用`relatedDimensionHitDetailsProcessor.processAnalyze`方法处理关联方维度
   - 处理流程：获取关联方企业列表 → 构建查询条件 → 查询风险动态 → 分析命中情况

3. **处理非关联方维度**：

   - 调用父类`BaseEsAnalyzeService`的`analyze`方法进行初步分析
   - 对初步命中的维度进行二次判断，如判断命中记录条数是否符合要求
   - 对于特殊维度（如`RiskChange`和`MainInfoUpdateCapitalChange`），会进一步调用`getDimensionDetail`方法获取详情再判断

4. **生成结果**：
   - 汇总关联方和非关联方的分析结果
   - 为命中的维度生成描述信息

### 2.2 getDimensionDetail 方法流程

`getDimensionDetail`方法用于获取风险动态的详细信息：

1. **区分维度类型**：

   - 关联方维度：调用`relatedDimensionHitDetailsProcessor.fetchHits`方法
   - 非关联方维度：根据维度类型进一步处理

2. **处理 RiskChange 维度**：

   - 调用父类方法获取基础数据
   - 调用`dimensionHitDetailProcessor.fetchHits`方法进行详情分析
   - 对结果进行内存分页和数据格式处理

3. **处理 MainInfoUpdateCapitalChange 维度**：

   - 调用父类方法获取基础数据
   - 调用`riskChangeHelper.hitMainInfoUpdateCapitalChange`方法判断是否命中
   - 对结果进行内存分页处理

4. **处理其他维度**：
   - 直接调用父类方法处理

## 3. 详情处理器

### 3.1 DimensionHitDetailProcessor

`dimension-hit-detail.processor.ts`文件中的`DimensionHitDetailProcessor`类负责处理非关联方维度的详情分析：

1. **fetchHits 方法**：

   - 接收风险动态详情数据、维度定义和查询参数
   - 对每条风险动态详情进行分析，判断是否命中
   - 根据风险动态的 Category（类别）采用不同的处理逻辑
   - 调用各个 helper 类的方法进行具体判断

2. **处理逻辑**：
   - 法定代表人变更、股东股份变更、主要人员变更等多种风险类型处理
   - 负面/正面新闻匹配
   - 经营异常、行政处罚等风险处理
   - 各类财务相关风险变更处理

### 3.2 RelatedDimensionHitDetailProcessor

`related-dimension-hit-detail.processor.ts`文件中的`RelatedDimensionHitDetailProcessor`类负责处理关联方维度的详情分析：

1. **processAnalyze 方法**：

   - 接收关联方维度定义和企业 ID
   - 调用`fetchHits`方法获取详情
   - 根据命中记录条数判断是否符合要求
   - 生成描述信息

2. **fetchHits 方法**：
   - 根据维度定义获取关联方企业列表
   - 构建 ES 查询条件
   - 查询风险动态数据
   - 根据维度类型进行特定处理
   - 生成结果并进行分页处理

## 4. 辅助类

在`helper/`目录下有多个辅助类，用于处理不同类型的风险动态：

1. **base.helper.ts**：基础辅助方法，包括通用的过滤和判断逻辑
2. **penalty.helper.ts**：处理行政处罚相关逻辑
3. **judgement.helper.ts**：处理司法判决相关逻辑
4. **case-reason.helper.ts**：处理案由相关逻辑
5. **company-share.helper.ts**：处理企业股份变更相关逻辑
6. **company-stock.helper.ts**：处理企业股票相关逻辑
7. **main-employee.helper.ts**：处理主要人员变更相关逻辑
8. **company-change.helper.ts**：处理企业变更相关逻辑
9. **company-finace.helper.ts**：处理企业财务相关逻辑
10. **bank-litigation.helper.ts**：处理银行诉讼相关逻辑

## 5. 文件之间的调用关系

```
┌─────────────────────────────┐
│     RiskChangeEsSource      │
└─────────┬───────────────────┘
          │
          ├─────────────────────┐
          ▼                     ▼
┌─────────────────────┐ ┌──────────────────────────────┐
│ DimensionHitDetail  │ │ RelatedDimensionHitDetail    │
│    Processor        │ │      Processor               │
└─────────┬───────────┘ └────────────┬─────────────────┘
          │                          │
          ▼                          ▼
┌─────────────────────┐     ┌──────────────────────────┐
│     Helper类        │     │      各种查询服务        │
│ (针对不同category)  │     │(CompanyDetail/Search等)  │
└─────────────────────┘     └──────────────────────────┘
```

## 6. 主要方法说明

### 6.1 RiskChangeEsSource 类

- **analyze**: 分析企业风险动态命中情况
- **getDimensionDetail**: 获取风险动态详情
- **getDimensionQuery**: 生成维度查询条件
- **getDetailFromEs**: 从 ES 获取详情数据
- **createAggs**: 创建聚合查询
- **processAggs**: 处理聚合结果
- **processBucketData**: 处理桶数据

### 6.2 DimensionHitDetailProcessor 类

- **fetchHits**: 分析风险动态详情是否命中
- **commonCivilRiskChange**: 通用风险动态查询

### 6.3 RelatedDimensionHitDetailProcessor 类

- **processAnalyze**: 处理关联方维度分析
- **fetchHits**: 获取关联方风险动态命中情况

## 7. 数据流转过程

1. 外部调用`analyze`方法，传入企业 ID 和维度定义列表
2. 分离关联方维度和非关联方维度
3. 关联方维度：获取关联方企业列表 → 查询风险动态 → 判断命中情况
4. 非关联方维度：直接查询企业风险动态 → 判断命中情况
5. 命中的维度会进一步调用`getDimensionDetail`获取详情
6. 根据详情数据进行二次判断，过滤不符合条件的记录
7. 汇总结果并返回
