帮我阅读下 source/risk-change/下的方法结构：

1. risk.change.es.source.ts 是用来处理对 企业动态索引 risk_change_index 的数据的访问
   这个文件里面主要只有两个方法 analyze 和 getDimensionDetail 是用来供外部调用的，其他方法主要是内部调用
2. risk-change/helper/ 下面主要都是一些 helper 方法，主要用于对不同数据维度和 category 时候的一些处理逻辑，我已经大致给他们分类到了不同的文件
3. dimension-hit-detail.processor.ts 主要用于对非关联方数据维度的前提下后去到数据之后，判断是否命中指标，这个文件比较大，里面都是针对不同数据维度的特殊处理逻辑
4. related-dimension-hit-detail.processor.ts 主要用于对关联方数据维度的时候， 的处理逻辑，大致就是先拼接 query 条件， 然后使用 risk-change-es.source.ts 的 es 查询方法查询数据，然后 再分析是否命中

以上是文件的一些介绍，你结合这个介绍帮我好好理解一下这两个文件， 简单整理出他们的工作流程及文件之间的调用关系，保存一个 rish-change-workflow.md 文件 , 然后我再根据这个文件制定一个优化计划

我是想做以下优化：

1. 首先把 risk-change-es.source.ts 拆分为两个文件
   1. risk-change-es.source.v2.ts 处理普通的维度
   2. risk-change-related-es.source.ts 处理关联方维度 (这个文件之前我手工创建了一部分，你可以参考下)
2. 使用策略模式来拆分 risk-change-es.source.v2.ts 和 risk-change-re-es.source.ts 里面对每个维度的处理
   还保持 analyze 和 getDimensionDetail 这两个方法对外提供使用， 但是其他对不同 category 数据的使用都转移到不同的策略里面去(主要是 risk-change/helper/ 下面的文件里面的各种方法)
3. strategies/ 下面之前已经手工创建了一些，可以参考，但是如果你这次有更好的计划，也可以不参考
4. 新文件都保存 到 risk-change-v3 目录下

以上是我的一个优化思路， 结合以上内容帮我输出两个东西：

1. risk-change-workflow.md 文件， 详细呈现他们的调用逻辑，方便对现有流程梳理清晰也方便后续的开发人员了解整个流程
2. risk-change-v2-optimization-plan.md 文件， 详细呈现我的优化思路，方便后续根据这个文档拆分成多个可独立执行的优化计划(需要时 prompt 内容的 md 文档，主要是确保每一个单独给到你之后你都能快速了解上下文并帮我开始执行优化)
