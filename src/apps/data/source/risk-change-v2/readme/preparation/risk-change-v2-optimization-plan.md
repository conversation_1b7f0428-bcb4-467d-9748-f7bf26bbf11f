# 风险动态模块优化计划

## 1. 优化目标

当前的风险动态模块存在以下问题需要优化：

1. **代码耦合度高**：`risk-change-es.source.ts`文件中包含了太多不同类型的风险处理逻辑
2. **文件过大**：处理器文件（如`dimension-hit-detail.processor.ts`）超过 1000 行，维护困难
3. **逻辑分散**：不同类别的风险处理逻辑散布在多个 helper 文件中，缺乏统一管理
4. **扩展性差**：添加新的风险类型需要修改多个文件

本优化计划旨在通过策略模式重构代码，提高代码的可维护性、可扩展性和可测试性。

## 2. 总体架构设计

优化后的风险动态模块将采用以下架构：

```
risk-change-v2/
├── interfaces/
│   └── risk-change-strategy.interface.ts  // 策略接口定义
├── strategies/
│   ├── base-risk-change.strategy.ts       // 基础策略类
│   ├── legal-change.strategy.ts           // 法定代表人变更策略
│   ├── capital-change.strategy.ts         // 资本变更策略
│   ├── negative-news.strategy.ts          // 负面新闻策略
│   ├── business-abnormal.strategy.ts      // 经营异常策略
│   ├── judicial-case.strategy.ts          // 司法案例策略
│   ├── administrative-penalty.strategy.ts // 行政处罚策略
│   └── ...                                // 其他风险类型策略
├── risk-change-es.source.v2.ts            // 处理普通维度的主类
├── risk-change-related-es.source.ts       // 处理关联方维度的主类
└── ...
```

## 3. 详细优化步骤

### 3.1 拆分源文件

将现有的`risk-change-es.source.ts`拆分为两个文件：

1. **risk-change-es.source.v2.ts**：

   - 处理普通的维度查询和分析
   - 使用策略模式委托具体的风险类型处理
   - 保留`analyze`和`getDimensionDetail`方法作为主要接口

2. **risk-change-related-es.source.ts**：
   - 专门处理关联方维度
   - 使用策略模式委托具体的关联方风险处理
   - 与`risk-change-es.source.v2.ts`共享策略类

### 3.2 创建策略接口和基类

1. **风险变更策略接口（RiskChangeStrategy）**：

   ```typescript
   export interface RiskChangeStrategy {
     getSupportedDimensions(): DimensionTypeEnums[];
     getDimensionCategoryMap(): DimensionCategoryMap;
     supportsDimension(dimension: DimensionHitStrategyPO): boolean;
     generateDimensionQuery(companyId: string, dimension: DimensionHitStrategyPO, ...): Promise<object>;
     processDimensionDetail(response: HitDetailsBaseResponse, dimension: DimensionHitStrategyPO, ...): Promise<HitDetailsBaseResponse>;
   }
   ```

2. **基础策略类（BaseRiskChangeStrategy）**：
   - 实现接口的通用方法
   - 提供共享的工具方法
   - 让具体策略类继承以减少重复代码

### 3.3 实现具体策略类

根据不同的风险类型和处理逻辑，实现多个具体的策略类：

1. **法定代表人变更策略（LegalChangeStrategy）**：

   - 处理法定代表人变更相关的风险
   - 从`dimension-hit-detail.processor.ts`提取相关逻辑

2. **资本变更策略（CapitalChangeStrategy）**：

   - 处理资本变更、减资公告等相关的风险
   - 从`dimension-hit-detail.processor.ts`提取相关逻辑

3. **负面新闻策略（NegativeNewsStrategy）**：

   - 处理负面/正面新闻相关的风险
   - 从`dimension-hit-detail.processor.ts`提取相关逻辑

4. **经营异常策略（BusinessAbnormalStrategy）**：

   - 处理经营异常、工商变更等相关的风险
   - 从`dimension-hit-detail.processor.ts`提取相关逻辑

5. **司法案例策略（JudicialCaseStrategy）**：

   - 处理司法案例、判决等相关的风险
   - 从`dimension-hit-detail.processor.ts`提取相关逻辑

6. **行政处罚策略（AdministrativePenaltyStrategy）**：
   - 处理行政处罚相关的风险
   - 从`dimension-hit-detail.processor.ts`提取相关逻辑

### 3.4 重构主类

1. **risk-change-es.source.v2.ts**：

   - 依赖注入所有策略类
   - 在`analyze`和`getDimensionDetail`方法中，根据维度类型选择合适的策略
   - 将具体的处理逻辑委托给策略类

2. **risk-change-related-es.source.ts**：
   - 依赖注入所有关联方策略类
   - 在`analyze`和`getDimensionDetail`方法中，根据维度类型选择合适的策略
   - 将具体的处理逻辑委托给策略类

### 3.5 重构 Helper 类

1. 将各个 helper 类中的方法根据风险类型迁移到对应的策略类中
2. 对于通用的 helper 方法，可以保留在基础策略类中
3. 逐步淘汰现有的 helper 类，减少不必要的依赖

## 4. 实现细节

### 4.1 策略接口设计

```typescript
/**
 * 风险变更策略接口
 * 定义处理风险变更的核心方法
 */
export interface RiskChangeStrategy {
  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[];

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): DimensionCategoryMap;

  /**
   * 检查维度类型是否由该策略处理
   * @param dimension 维度策略
   */
  supportsDimension(dimension: DimensionHitStrategyPO): boolean;

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object>;

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse>;
}
```

### 4.2 策略基类设计

```typescript
/**
 * 风险变更策略基类
 * 实现RiskChangeStrategy接口的抽象基类
 */
export abstract class BaseRiskChangeStrategy implements RiskChangeStrategy {
  protected readonly logger: Logger;

  /**
   * 构造函数
   * @param strategyName 策略名称
   */
  constructor(private readonly strategyName: string) {
    this.logger = QccLogger.getLogger(strategyName);
  }

  /**
   * 获取支持的维度类型列表
   * 子类应该重写此方法以提供其支持的维度类型列表
   */
  abstract getSupportedDimensions(): DimensionTypeEnums[];

  /**
   * 获取维度类型与风险变更类别的映射
   * 子类应该重写此方法以提供其维度类型与类别的映射关系
   */
  abstract getDimensionCategoryMap(): DimensionCategoryMap;

  /**
   * 检查维度类型是否由该策略处理
   * @param dimension 维度策略
   */
  supportsDimension(dimension: DimensionHitStrategyPO): boolean {
    return this.getSupportedDimensions().includes(dimension.key);
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  abstract generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object>;

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  abstract processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse>;

  /**
   * 创建基础查询对象
   * @param companyId 企业ID
   */
  protected createBaseQuery(companyId: string): object {
    return {
      bool: {
        must: [
          {
            term: {
              KeyNo: companyId,
            },
          },
        ],
      },
    };
  }

  /**
   * 记录错误日志
   * @param message 错误信息
   * @param error 错误对象
   */
  protected logError(message: string, error: any): void {
    this.logger.error(`${message}: ${error?.message || error}`, error);
  }
}
```

### 4.3 主类实现示例

```typescript
@Injectable()
export class RiskChangeEsSourceV2 extends BaseEsAnalyzeService {
  private strategies: RiskChangeStrategy[] = [];

  constructor(
    readonly configService: ConfigService,
    private readonly moduleRef: ModuleRef, // ... 其他依赖
  ) {
    super(
      'RiskChangeEsSourceV2',
      new Client({
        nodes: configService.esConfig.riskChange.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.riskChange.indexName,
    );
    // 初始化策略类列表
    this.initStrategies();
  }

  private initStrategies(): void {
    try {
      // 获取所有策略类实例
      this.strategies = [
        this.moduleRef.get(LegalChangeStrategy),
        this.moduleRef.get(CapitalChangeStrategy),
        this.moduleRef.get(NegativeNewsStrategy),
        this.moduleRef.get(BusinessAbnormalStrategy),
        this.moduleRef.get(JudicialCaseStrategy),
        this.moduleRef.get(AdministrativePenaltyStrategy),
        // ... 其他策略类
      ];
    } catch (error) {
      this.logger.error(`初始化策略类列表失败: ${error?.message || error}`, error);
    }
  }

  async analyze(companyId: string, dimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    // 过滤出普通维度（非关联方维度）
    const normalDimensions = dimensionHitStrategyPOs.filter((d) => !this.isRelatedDimension(d));

    if (!normalDimensions.length) {
      return [];
    }

    // 调用父类方法进行基础分析
    const dimHitRes = await super.analyze(companyId, normalDimensions);

    // 使用策略模式处理命中的维度
    // ... 具体实现

    return dimHitRes;
  }

  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    // 使用策略模式处理维度详情
    const strategy = this.findStrategyForDimension(dimension);
    if (strategy) {
      // 使用策略处理详情
      const response = await super.getDimensionDetail(dimension, params, analyzeParams);
      return strategy.processDimensionDetail(response, dimension, params, analyzeParams);
    }

    // 没有找到对应策略，使用默认处理
    return super.getDimensionDetail(dimension, params, analyzeParams);
  }

  private findStrategyForDimension(dimension: DimensionHitStrategyPO): RiskChangeStrategy | null {
    return this.strategies.find((strategy) => strategy.supportsDimension(dimension)) || null;
  }

  private isRelatedDimension(dimension: DimensionHitStrategyPO): boolean {
    return [
      DimensionTypeEnums.RecentInvestCancellationsRiskChange,
      DimensionTypeEnums.ActualControllerRiskChange,
      DimensionTypeEnums.ListedEntityRiskChange,
    ].includes(dimension.key);
  }
}
```

## 5. 优化计划拆分

为了方便逐步实施优化，可以将计划拆分为多个独立执行的步骤：

### 5.1 基础架构搭建（第一阶段）

1. 创建策略接口和基类
2. 创建主要的策略类框架
3. 初步实现主类的框架代码

### 5.2 重构普通维度处理（第二阶段）

1. 实现法定代表人变更策略
2. 实现资本变更策略
3. 更新主类以使用这些策略

### 5.3 重构关联方维度处理（第三阶段）

1. 完善关联方维度的策略类
2. 更新关联方主类以使用这些策略

### 5.4 优化剩余风险类型（第四阶段）

1. 实现负面新闻、经营异常等策略
2. 将相关的 helper 方法迁移到策略类中

### 5.5 测试和文档（第五阶段）

1. 编写单元测试
2. 编写文档
3. 性能测试和优化

## 6. 预期收益

通过此次重构，我们预期获得以下收益：

1. **提高代码可维护性**：

   - 代码结构更清晰，单个文件代码量减少
   - 职责更明确，每个策略类只负责一种风险类型

2. **提高代码可扩展性**：

   - 添加新的风险类型只需要添加新的策略类
   - 无需修改现有代码，符合开闭原则

3. **提高代码可测试性**：

   - 策略类可以独立测试
   - 主类的依赖可以更容易地模拟

4. **性能优化**：
   - 更精准的查询条件
   - 更高效的处理逻辑

## 7. 注意事项和风险

1. **兼容性**：确保重构后的代码与现有系统的其他部分保持兼容
2. **性能**：监控重构前后的性能变化，确保不会引入性能退化
3. **测试覆盖**：确保测试覆盖所有重构的代码路径
4. **渐进式实施**：采用渐进式重构策略，确保系统的稳定性
