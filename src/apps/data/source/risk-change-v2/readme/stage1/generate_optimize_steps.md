1. 通过 risk-change-workflow.md 文件，了解当前的动态生成流程
2. 通过 risk-change-v2-optimization-plan.md 文件，了解当前的优化方案
3. 结合这两个内容帮我开始做优化，需要注意以下几点
   1. 不要修改任务原有业务逻辑，即使逻辑暂时有问题，也先不要优化逻辑，确保这次只调整代码解构
   2. 新增代码都放在 src/apps/data/source/risk-change-v2/ 文件夹
   3. 原来的 文件不要动
   4. 起文件名字时候尽量保持一定的含义，避免随意使用名字，同时注意体现 RiskChange 处理相关的含义，避免过于简单的名字跟项目中其他类的名字重复了
   5. 代码风格要统一，尽量和现有代码保持一致
   6. 优化代码时候，注意使用 typescript 类型，不要使用 any 类型
   7. 目前已经有 rules_step_1.md, rules_step_2.md, rules_step_3.md, 可以接着继续输出
   8. 读取 rules_step_1.md, rules_step_2.md, rules_step_3.md 或者其他源代码的时候，尽量有提取关键信息，如步骤，简介，方法声明等内容传输给你的 AI， 不要把所有内容都传输，太多了

这是我制定的优化阶段，应该会需要调整很多代码，所以你可以根据我上面的要求，帮我拆分成若干个你认为合理的粒度, 然后帮我分别生成 prompt ，方便后续根据这个 prompt 来帮我逐步进行优化, 名字可以采用 rules*step*<number>.md, 这样命名
