# 风险动态模块优化计划 - 第四阶段：实现负面新闻和经营异常策略

## 背景说明

前三个阶段已完成了基础架构搭建、法定代表人变更、资本变更策略以及关联方维度策略的实现。在第四阶段，我们将专注于实现负面新闻和经营异常策略，这两类风险是企业风险动态中的重要组成部分。

## 主要目标

1. 实现负面新闻策略（NegativeNewsStrategy）
2. 实现经营异常策略（BusinessAbnormalStrategy）
3. 更新主类以集成这两个策略

## 优化要求

1. 不修改原有业务逻辑，本次仅调整代码结构
2. 新增代码放在 src/apps/data/source/risk-change-v2/ 文件夹
3. 不修改原有文件
4. 文件命名需保持命名含义，体现 RiskChange 处理相关的含义
5. 代码风格需统一，与现有代码保持一致
6. 使用 TypeScript 类型，避免使用 any 类型

## 实现步骤

### 1. 创建负面新闻策略类

在`src/apps/data/source/risk-change-v2/strategies/negative-news.strategy.ts`文件中实现负面新闻策略：

```typescript
import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRiskChangeStrategy } from './base-risk-change.strategy';
import { RiskChangeHelper } from '../../../helper/risk.change.helper';
import { cloneDeep, orderBy } from 'lodash';

/**
 * 负面新闻策略
 * 处理负面/正面新闻相关的风险类型
 */
@Injectable()
export class NegativeNewsStrategy extends BaseRiskChangeStrategy {
  /**
   * 构造函数
   * @param riskChangeHelper 风险变更辅助服务
   */
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super('NegativeNewsStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.NegativeNews, DimensionTypeEnums.PositiveNews];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.NegativeNews]: [RiskChangeCategoryEnum.category101],
      [DimensionTypeEnums.PositiveNews]: [RiskChangeCategoryEnum.category101],
    };
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 创建基础查询对象
      const query = this.createBaseQuery(companyId);

      // 添加风险类别过滤
      const categories = this.getDimensionCategoryMap()[dimension.key];
      if (categories?.length) {
        query.bool.must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加维度过滤条件
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.must.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加有效性过滤
      const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidField && Number(isValidField.fieldValue[0]) >= 0) {
        query.bool.must.push({
          term: {
            IsValid: Number(isValidField.fieldValue[0]),
          },
        });
      } else {
        // 默认只查询有效记录
        query.bool.must.push({
          term: {
            IsValid: 1,
          },
        });
      }

      // 根据正面/负面新闻类型添加额外条件
      if (dimension.key === DimensionTypeEnums.NegativeNews) {
        // 负面新闻筛选
        query.bool.must.push({
          term: {
            NewsType: 0, // 0表示负面新闻
          },
        });
      } else if (dimension.key === DimensionTypeEnums.PositiveNews) {
        // 正面新闻筛选
        query.bool.must.push({
          term: {
            NewsType: 1, // 1表示正面新闻
          },
        });
      }

      return query;
    } catch (error) {
      this.logError('生成负面新闻查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];

      // 处理每条记录
      for (const itemRaw of response.Result) {
        try {
          const item = cloneDeep(itemRaw);

          // 解析JSON字段
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              const value = item[key];
              try {
                item[key] = value ? JSON.parse(value) : {};
              } catch (error) {
                item[key] = value;
              }
            }
          });

          let isHit = true;

          // 处理新闻类型
          if (item.Category === RiskChangeCategoryEnum.category101) {
            // 根据维度类型检查是否命中
            if (dimension.key === DimensionTypeEnums.NegativeNews) {
              isHit = item.NewsType === 0; // 负面新闻
            } else if (dimension.key === DimensionTypeEnums.PositiveNews) {
              isHit = item.NewsType === 1; // 正面新闻
            }

            // 检查新闻关键词
            const newsKeywordsField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.newsKeywords);
            if (newsKeywordsField && isHit) {
              isHit = this.riskChangeHelper.hitNewsKeywords(newsKeywordsField, item);
            }
          }

          if (isHit) {
            hitData.push(item);
          }
        } catch (error) {
          this.logError('处理负面新闻详情项失败', error);
        }
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理负面新闻详情失败', error);
      return response;
    }
  }
}
```

### 2. 创建经营异常策略类

在`src/apps/data/source/risk-change-v2/strategies/business-abnormal.strategy.ts`文件中实现经营异常策略：

```typescript
import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRiskChangeStrategy } from './base-risk-change.strategy';
import { RiskChangeHelper } from '../../../helper/risk.change.helper';
import { cloneDeep, orderBy } from 'lodash';

/**
 * 经营异常策略
 * 处理经营异常相关的风险类型
 */
@Injectable()
export class BusinessAbnormalStrategy extends BaseRiskChangeStrategy {
  /**
   * 构造函数
   * @param riskChangeHelper 风险变更辅助服务
   */
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super('BusinessAbnormalStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.OperatingAbnormal, DimensionTypeEnums.BusinessScope];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.OperatingAbnormal]: [RiskChangeCategoryEnum.category4],
      [DimensionTypeEnums.BusinessScope]: [RiskChangeCategoryEnum.category14],
    };
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 创建基础查询对象
      const query = this.createBaseQuery(companyId);

      // 添加风险类别过滤
      const categories = this.getDimensionCategoryMap()[dimension.key];
      if (categories?.length) {
        query.bool.must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加维度过滤条件
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.must.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加有效性过滤
      const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidField && Number(isValidField.fieldValue[0]) >= 0) {
        query.bool.must.push({
          term: {
            IsValid: Number(isValidField.fieldValue[0]),
          },
        });
      } else {
        // 默认只查询有效记录
        query.bool.must.push({
          term: {
            IsValid: 1,
          },
        });
      }

      return query;
    } catch (error) {
      this.logError('生成经营异常查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];

      // 处理每条记录
      for (const itemRaw of response.Result) {
        try {
          const item = cloneDeep(itemRaw);

          // 解析JSON字段
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              const value = item[key];
              try {
                item[key] = value ? JSON.parse(value) : {};
              } catch (error) {
                item[key] = value;
              }
            }
          });

          let isHit = true;

          // 根据不同类型处理
          if (dimension.key === DimensionTypeEnums.OperatingAbnormal) {
            // 经营异常类型处理
            const abnormalTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.abnormalTypes);
            if (abnormalTypesField && isHit) {
              isHit = this.riskChangeHelper.hitAbnormalTypes(abnormalTypesField, item);
            }
          } else if (dimension.key === DimensionTypeEnums.BusinessScope) {
            // 经营范围变更处理
            const businessScopeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessScope);
            if (businessScopeField && isHit) {
              isHit = this.riskChangeHelper.hitBusinessScope(businessScopeField, item);
            }
          }

          if (isHit) {
            hitData.push(item);
          }
        } catch (error) {
          this.logError('处理经营异常详情项失败', error);
        }
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理经营异常详情失败', error);
      return response;
    }
  }
}
```

### 3. 更新主类以集成新策略

更新`src/apps/data/source/risk-change-v2/risk-change-es.source.v2.ts`文件，增加对新策略的依赖注入和使用：

```typescript
// 在构造函数中增加依赖注入
constructor(
  readonly configService: ConfigService,
  private readonly moduleRef: ModuleRef,
  private readonly legalChangeStrategy: LegalChangeStrategy,
  private readonly capitalChangeStrategy: CapitalChangeStrategy,
  private readonly negativeNewsStrategy: NegativeNewsStrategy,
  private readonly businessAbnormalStrategy: BusinessAbnormalStrategy,
  // 其他策略依赖...
) {
  super(
    'RiskChangeEsSourceV2',
    new Client({
      nodes: configService.esConfig.riskChangeList.nodes,
      ssl: { rejectUnauthorized: false },
    }),
    configService.esConfig.riskChangeList.indexName,
  );

  // 初始化策略类列表
  this.initStrategies();
}

/**
 * 初始化策略列表
 */
private initStrategies(): void {
  this.strategies = [
    this.legalChangeStrategy,
    this.capitalChangeStrategy,
    this.negativeNewsStrategy,
    this.businessAbnormalStrategy,
    // 添加其他策略...
  ];
}
```

## 预期成果

完成第四阶段后，我们将实现两个重要的风险变更策略类：

1. **负面新闻策略**：处理企业的负面和正面新闻相关风险
2. **经营异常策略**：处理企业的经营异常和经营范围变更相关风险

这两个策略将与之前实现的策略一起，通过策略模式进行统一管理和调用，使代码结构更加清晰，易于维护和扩展。
