# 风险动态模块优化计划概述

## 背景

当前的风险动态模块存在代码耦合度高、文件过大、逻辑分散和扩展性差等问题。本优化计划旨在通过策略模式重构代码，提高代码的可维护性、可扩展性和可测试性。

## 优化原则

1. **不修改原有业务逻辑**：本次优化仅调整代码结构，保持业务逻辑不变
2. **新增代码放在专用目录**：所有新代码放在 src/apps/data/source/risk-change-v2/ 文件夹
3. **保持原有文件不动**：不修改原有文件，确保平滑迁移
4. **命名规范**：文件命名需保持命名含义，体现 RiskChange 处理相关的含义
5. **代码风格统一**：与现有代码保持一致
6. **类型安全**：使用 TypeScript 类型，避免使用 any 类型

## 优化分阶段计划

### 第一阶段：基础架构搭建

- 创建策略模式的基础结构，包括接口和基类
- 实现`RiskChangeStrategy`接口
- 实现`BaseRiskChangeStrategy`基类
- 创建主类`RiskChangeEsSourceV2`框架

### 第二阶段：实现法定代表人变更和资本变更策略

- 实现法定代表人变更策略`LegalChangeStrategy`
- 实现资本变更策略`CapitalChangeStrategy`
- 在主类中使用这些策略

### 第三阶段：实现关联方维度策略

- 创建关联方维度的基础策略类`BaseRelatedRiskChangeStrategy`
- 实现关联方维度的具体策略类
- 更新`RiskChangeRelatedEsSource`以使用这些策略

### 第四阶段：实现负面新闻和经营异常策略

- 实现负面新闻策略`NegativeNewsStrategy`
- 实现经营异常策略`BusinessAbnormalStrategy`
- 更新主类以集成这两个策略

### 第五阶段：实现司法案例和行政处罚策略

- 实现司法案例策略`JudicialCaseStrategy`
- 实现行政处罚策略`AdministrativePenaltyStrategy`
- 更新主类以集成这两个策略

### 第六阶段：实现股权变更和财务指标策略

- 实现股权变更策略`EquityChangeStrategy`
- 实现财务指标策略`FinancialIndicatorStrategy`
- 更新主类以集成这两个策略

### 第七阶段：模块集成和单元测试

- 创建模块配置文件`RiskChangeV2Module`
- 实现服务提供者`RiskChangeV2Provider`
- 编写单元测试
- 完成最终集成

## 优化后的架构

```
risk-change-v2/
├── interfaces/
│   └── risk-change-strategy.interface.ts  // 策略接口定义
├── strategies/
│   ├── base-risk-change.strategy.ts       // 基础策略类
│   ├── base-related-risk-change.strategy.ts // 关联方基础策略类
│   ├── legal-change.strategy.ts           // 法定代表人变更策略
│   ├── capital-change.strategy.ts         // 资本变更策略
│   ├── negative-news.strategy.ts          // 负面新闻策略
│   ├── business-abnormal.strategy.ts      // 经营异常策略
│   ├── judicial-case.strategy.ts          // 司法案例策略
│   ├── administrative-penalty.strategy.ts // 行政处罚策略
│   ├── equity-change.strategy.ts          // 股权变更策略
│   └── financial-indicator.strategy.ts    // 财务指标策略
├── test/                                  // 单元测试
├── risk-change-es.source.v2.ts            // 处理普通维度的主类
├── risk-change-related-es.source.ts       // 处理关联方维度的主类
├── risk-change-v2.provider.ts             // 服务提供者
└── risk-change-v2.module.ts               // 模块配置
```

## 预期成果

完成本优化计划后，风险动态模块将具有以下特点：

1. **松耦合**：通过策略模式，将不同类型的风险处理逻辑解耦
2. **可扩展**：添加新的风险类型只需创建新的策略类，无需修改现有代码
3. **可测试**：每个策略类可以独立测试，提高代码质量
4. **可维护**：代码结构清晰，逻辑分明，易于理解和维护
5. **类型安全**：全面使用 TypeScript 类型，减少运行时错误

## 后续工作

1. **平滑迁移**：在测试环境验证新架构的稳定性和功能正确性
2. **性能优化**：对重构后的代码进行性能分析和优化
3. **文档完善**：编写详细的架构和使用文档
4. **逐步淘汰**：成熟后逐步淘汰旧版本代码

## 附录

- 详细实现步骤请参考各阶段规则文档：
  - rules_step_1.md
  - rules_step_2.md
  - rules_step_3.md
  - rules.step_4.md
  - rules.step_5.md
  - rules.step_6.md
  - rules.step_7.md
