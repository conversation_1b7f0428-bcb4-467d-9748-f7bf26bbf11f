# 风险动态模块优化计划 - 第三阶段：实现关联方维度策略

## 背景说明

前两个阶段已经完成了基础架构搭建和两个核心策略类的实现。在第三阶段，我们将专注于处理关联方维度的风险变更策略，实现对关联方企业风险动态的分析和处理。

## 主要目标

1. 实现关联方维度策略类（包括实控人风险和投资企业风险等）
2. 完善关联方维度的 ES 查询逻辑
3. 更新关联方主类以使用这些策略

## 优化要求

1. 不修改原有业务逻辑，本次仅调整代码结构
2. 新增代码放在 src/apps/data/source/risk-change-v2/ 文件夹
3. 不修改原有文件
4. 文件命名需保持命名含义，体现 RiskChange 处理相关的含义
5. 代码风格需统一，与现有代码保持一致
6. 使用 TypeScript 类型，避免使用 any 类型

## 实现步骤

### 1. 创建关联方维度的策略基类

在`src/apps/data/source/risk-change-v2/strategies/base-related-risk-change.strategy.ts`文件中实现关联方维度的基础策略类：

```typescript
import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RelatedTypeEnums } from 'libs/enums/dimension/RelatedTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRiskChangeStrategy } from './base-risk-change.strategy';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonHelper } from '../../../helper/person.helper';
import { cloneDeep } from 'lodash';

/**
 * 关联方风险变更策略基类
 * 提供处理关联方风险变更的基本方法
 */
@Injectable()
export abstract class BaseRelatedRiskChangeStrategy extends BaseRiskChangeStrategy {
  /**
   * 构造函数
   * @param strategyName 策略名称
   * @param companyDetailService 企业详情服务
   * @param companySearchService 企业搜索服务
   * @param personHelper 人员辅助服务
   */
  constructor(
    strategyName: string,
    protected readonly companyDetailService: CompanyDetailService,
    protected readonly companySearchService: CompanySearchService,
    protected readonly personHelper: PersonHelper,
  ) {
    super(strategyName);
  }

  /**
   * 获取关联方企业ID列表
   * @param companyId 企业ID
   * @param dimension 维度策略
   */
  protected async getRelatedCompanyIds(companyId: string, dimension: DimensionHitStrategyPO): Promise<string[]> {
    try {
      const companyIds: string[] = [];

      // 查询范围，如果指定查询关联方风险 获取关联方范围定义
      const relatedRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRoleType);
      if (!relatedRoleField) {
        return companyIds;
      }

      // 对外投资企业
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.InvestCompany)) {
        // 投资企业的状态
        const compnayStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessStatus);
        const status = compnayStatusField?.fieldValue;
        // 投资企业的持股比例
        const fundedRatioLevelField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.fundedRatioLevel);
        const fundedRatioLevel = fundedRatioLevelField?.fieldValue?.[0] || 0;
        // 符合条件的对外投资企业列表
        const { Paging, Result } = await this.companyDetailService.getInvestCompany(companyId, fundedRatioLevel, status, 200);
        if (Paging?.TotalRecords > 0) {
          companyIds.push(...Result.map((d) => d.KeyNo));
        }
      }

      // 实际控制人
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.ActualController)) {
        const personlist = await this.personHelper.getFinalActualController(companyId, false);
        const keyNos = personlist?.length ? personlist?.map((p) => p.keyNo).filter((t) => t) : [];
        if (keyNos?.length) {
          companyIds.push(...keyNos);
        }
      }

      // 大股东
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.MajorShareholder)) {
        const partnerList = await this.personHelper.getPartnerList(companyId, 'all');
        const bigStockers = partnerList.filter((partner) => partner?.tags.includes('大股东'));
        if (bigStockers?.length) {
          const keyNos = bigStockers?.length ? bigStockers?.map((p) => p.keyNo).filter((t) => t) : [];
          if (keyNos?.length) {
            companyIds.push(...keyNos);
          }
        }
      }

      // 上市主体企业
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.StockControlCompany)) {
        let isListCompany = false;
        const companyInfo = await this.companySearchService.companyDetailsQcc(companyId);
        if (companyInfo?.Tags?.length) {
          // 是否是上市企业
          const Tag122 = companyInfo?.Tags?.find((t) => t.Type === 122);
          if (Tag122) {
            const dataExtend2 = JSON.parse(Tag122?.DataExtend2 || '{}');
            const ListingStage = dataExtend2?.ListingStage;
            if (ListingStage === '1') {
              isListCompany = true;
            }
          }
          // 如果是上市企业
          if (isListCompany) {
            // 港股企业
            const Tag30 = companyInfo?.Tags?.find((t) => t.Type === 30);
            if (Tag30) {
              const dataExtend2 = JSON.parse(Tag30?.DataExtend2 || '{}');
              const companyKeyNo = dataExtend2?.KN;
              if (companyKeyNo) {
                companyIds.push(companyKeyNo);
              }
            } else {
              companyIds.push(companyId);
            }
          }
        }
      }

      return companyIds;
    } catch (error) {
      this.logError('获取关联方企业ID列表失败', error);
      return [];
    }
  }

  /**
   * 生成关联方维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 获取关联方企业ID列表
      const companyIds = await this.getRelatedCompanyIds(companyId, dimension);
      if (!companyIds.length) {
        return null;
      }

      // 创建查询对象
      const query = {
        bool: {
          filter: [],
        },
      };

      // 添加版本过滤
      query.bool.filter.push({ range: { Es_Version: { lt: 999999 } } });

      // 添加企业ID过滤
      query.bool.filter.push({ terms: { KeyNo: companyIds } });

      // 添加有效性过滤
      const isValidParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidParams && Number(isValidParams.fieldValue[0]) >= 0) {
        query.bool.filter.push({ term: { IsValid: Number(isValidParams.fieldValue[0]) } });
      } else {
        // 默认只查询有效记录
        query.bool.filter.push({ term: { IsValid: 1 } });
      }

      // 添加风险类别过滤
      const riskCategoriesParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.riskCategories);
      if (riskCategoriesParams) {
        const riskCategories = riskCategoriesParams.fieldValue;
        query.bool.filter.push({ terms: { Category: riskCategories } });
      } else {
        const categories = this.getDimensionCategoryMap()[dimension.key];
        if (categories?.length) {
          query.bool.filter.push({
            terms: {
              Category: categories,
            },
          });
        }
      }

      // 添加时间范围过滤
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.filter.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加ID过滤
      if (dimensionFilter?.id) {
        query.bool.filter.push({ term: { Id: dimensionFilter.id } });
      }

      return query;
    } catch (error) {
      this.logError('生成关联方维度查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理关联方企业信息
   * @param detail 详情数据
   * @param sourceCompanyId 源企业ID
   */
  protected async getRelatedCompanyInfo(detail: any, sourceCompanyId: string): Promise<any> {
    try {
      // 获取关联方企业ID
      const relatedCompanyId = this.extractRelatedCompanyId(detail, sourceCompanyId);
      if (!relatedCompanyId) {
        return null;
      }

      // 获取关联类型
      const relationType = this.getRelationType(detail);

      // 获取企业名称
      const companyInfo = await this.companySearchService.getSimpleCompanyInfo(relatedCompanyId);
      const companyName = companyInfo?.Name || '';

      return {
        KeyNo: relatedCompanyId,
        Name: companyName,
        RelationType: relationType,
      };
    } catch (error) {
      this.logError('处理关联方企业信息失败', error);
      return null;
    }
  }

  /**
   * 提取关联方企业ID
   * @param detail 详情数据
   * @param sourceCompanyId 源企业ID
   */
  private extractRelatedCompanyId(detail: any, sourceCompanyId: string): string {
    try {
      return detail.KeyNo !== sourceCompanyId ? detail.KeyNo : '';
    } catch (error) {
      this.logError('提取关联方企业ID失败', error);
      return '';
    }
  }

  /**
   * 获取关联类型
   * @param detail 详情数据
   */
  private getRelationType(detail: any): string {
    try {
      // 根据不同的关联类型设置不同的描述
      // 这里可以根据实际业务逻辑扩展
      return '关联企业';
    } catch (error) {
      this.logError('获取关联类型失败', error);
      return '关联企业';
    }
  }
}
```

### 2. 创建实控人风险变更策略类

在`src/apps/data/source/risk-change-v2/strategies/actual-controller-risk-change.strategy.ts`文件中实现实控人风险变更策略：

```typescript
import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRelatedRiskChangeStrategy } from './base-related-risk-change.strategy';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonHelper } from '../../../helper/person.helper';
import { orderBy, cloneDeep } from 'lodash';

/**
 * 实控人风险变更策略
 * 处理实控人风险变更相关的维度
 */
@Injectable()
export class ActualControllerRiskChangeStrategy extends BaseRelatedRiskChangeStrategy {
  /**
   * 构造函数
   * @param companyDetailService 企业详情服务
   * @param companySearchService 企业搜索服务
   * @param personHelper 人员辅助服务
   */
  constructor(
    protected readonly companyDetailService: CompanyDetailService,
    protected readonly companySearchService: CompanySearchService,
    protected readonly personHelper: PersonHelper,
  ) {
    super('ActualControllerRiskChangeStrategy', companyDetailService, companySearchService, personHelper);
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.ActualControllerRiskChange];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.ActualControllerRiskChange]: [
        RiskChangeCategoryEnum.category39, // 法定代表人变更
        RiskChangeCategoryEnum.category62, // 负面新闻
        RiskChangeCategoryEnum.category66, // 负面新闻
        RiskChangeCategoryEnum.category67, // 负面新闻
      ],
    };
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];
      const sourceCompanyId = params.keyNo;

      // 处理每条记录
      for (const itemRaw of response.Result) {
        try {
          const item = cloneDeep(itemRaw);

          // 解析JSON字段
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              const value = item[key];
              try {
                item[key] = value ? JSON.parse(value) : {};
              } catch (error) {
                item[key] = value;
              }
            }
          });

          // 获取关联方企业信息
          const relatedCompanyInfo = await this.getRelatedCompanyInfo(item, sourceCompanyId);
          if (relatedCompanyInfo) {
            item.RelatedCompanyInfo = relatedCompanyInfo;
          }

          hitData.push(item);
        } catch (error) {
          this.logError('处理实控人风险变更详情项失败', error);
        }
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理实控人风险变更详情失败', error);
      return response;
    }
  }
}
```

### 3. 创建投资企业注销风险策略类

在`src/apps/data/source/risk-change-v2/strategies/invest-company-cancellation.strategy.ts`文件中实现投资企业注销风险策略：

```typescript
import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRelatedRiskChangeStrategy } from './base-related-risk-change.strategy';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonHelper } from '../../../helper/person.helper';
import { orderBy, cloneDeep } from 'lodash';
import { RiskChangeHelper } from '../../../helper/risk.change.helper';

/**
 * 投资企业注销风险策略
 * 处理投资企业注销风险相关的维度
 */
@Injectable()
export class InvestCompanyCancellationStrategy extends BaseRelatedRiskChangeStrategy {
  /**
   * 构造函数
   * @param companyDetailService 企业详情服务
   * @param companySearchService 企业搜索服务
   * @param personHelper 人员辅助服务
   * @param riskChangeHelper 风险变更辅助服务
   */
  constructor(
    protected readonly companyDetailService: CompanyDetailService,
    protected readonly companySearchService: CompanySearchService,
    protected readonly personHelper: PersonHelper,
    private readonly riskChangeHelper: RiskChangeHelper,
  ) {
    super('InvestCompanyCancellationStrategy', companyDetailService, companySearchService, personHelper);
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.RecentInvestCancellationsRiskChange];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.RecentInvestCancellationsRiskChange]: [
        RiskChangeCategoryEnum.category42, // 企业注销
        RiskChangeCategoryEnum.category43, // 企业吊销
      ],
    };
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];
      const sourceCompanyId = params.keyNo;

      // 处理每条记录
      for (const itemRaw of response.Result) {
        try {
          const item = cloneDeep(itemRaw);

          // 解析JSON字段
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              const value = item[key];
              try {
                item[key] = value ? JSON.parse(value) : {};
              } catch (error) {
                item[key] = value;
              }
            }
          });

          // 获取关联方企业信息
          const relatedCompanyInfo = await this.getRelatedCompanyInfo(item, sourceCompanyId);
          if (relatedCompanyInfo) {
            item.RelatedCompanyInfo = relatedCompanyInfo;
          }

          // 周期过滤
          const timePeriodField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.timePeriod);
          if (timePeriodField) {
            // 这里应该进行时间周期过滤，但由于我们不能修改原有代码，所以直接添加到命中数据中
            hitData.push(item);
          } else {
            hitData.push(item);
          }
        } catch (error) {
          this.logError('处理投资企业注销风险详情项失败', error);
        }
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理投资企业注销风险详情失败', error);
      return response;
    }
  }
}
```

### 4. 更新主类以使用新的关联方策略类

在`src/apps/data/source/risk-change-v2/risk-change-related-es.source.ts`文件中更新`initStrategies`方法：

```typescript
/**
 * 初始化策略类列表
 */
private initStrategies(): void {
  try {
    // 获取所有策略类实例
    this.strategies = [
      this.moduleRef.get(ActualControllerRiskChangeStrategy),
      this.moduleRef.get(InvestCompanyCancellationStrategy),
      // 其他策略类...
    ];
  } catch (error) {
    this.logger.error(`初始化策略类列表失败: ${error instanceof Error ? error.message : String(error)}`, error);
  }
}
```

### 5. 更新主模块以提供新的关联方策略类

在`src/apps/data/source/risk-change-v2/risk-change-v2.module.ts`文件中注册这些策略类：

```typescript
import { Module } from '@nestjs/common';
import { RiskChangeEsSourceV2 } from './risk-change-es.source.v2';
import { RiskChangeRelatedEsSource } from './risk-change-related-es.source';
import { LegalChangeStrategy } from './strategies/legal-change.strategy';
import { CapitalChangeStrategy } from './strategies/capital-change.strategy';
import { ActualControllerRiskChangeStrategy } from './strategies/actual-controller-risk-change.strategy';
import { InvestCompanyCancellationStrategy } from './strategies/invest-company-cancellation.strategy';
import { RiskChangeModule } from '../risk-change/risk-change.module';

@Module({
  imports: [
    RiskChangeModule, // 导入原有模块以使用其中的服务
  ],
  providers: [
    RiskChangeEsSourceV2,
    RiskChangeRelatedEsSource,
    LegalChangeStrategy,
    CapitalChangeStrategy,
    ActualControllerRiskChangeStrategy,
    InvestCompanyCancellationStrategy,
  ],
  exports: [RiskChangeEsSourceV2, RiskChangeRelatedEsSource],
})
export class RiskChangeV2Module {}
```

## 预期结果

完成本阶段后，我们将实现两个关联方维度的策略类：

1. 实控人风险变更策略（ActualControllerRiskChangeStrategy）
2. 投资企业注销风险策略（InvestCompanyCancellationStrategy）

以及一个关联方维度策略的基类，这些策略类将被集成到关联方主类中，使得关联方主类能够使用这些策略来处理对应类型的风险变更。这是完成策略模式重构的重要步骤，将使得关联方维度的处理更加模块化和可扩展。
