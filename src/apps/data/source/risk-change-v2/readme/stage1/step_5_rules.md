# 风险动态模块优化计划 - 第五阶段：实现司法案例和行政处罚策略

## 背景说明

前四个阶段已完成了基础架构搭建、法定代表人变更、资本变更、关联方维度、负面新闻和经营异常策略的实现。在第五阶段，我们将专注于实现司法案例和行政处罚策略，这两类风险是企业风险动态中的重要法律风险指标。

## 主要目标

1. 实现司法案例策略（JudicialCaseStrategy）
2. 实现行政处罚策略（AdministrativePenaltyStrategy）
3. 更新主类以集成这两个策略

## 优化要求

1. 不修改原有业务逻辑，本次仅调整代码结构
2. 新增代码放在 src/apps/data/source/risk-change-v2/ 文件夹
3. 不修改原有文件
4. 文件命名需保持命名含义，体现 RiskChange 处理相关的含义
5. 代码风格需统一，与现有代码保持一致
6. 使用 TypeScript 类型，避免使用 any 类型

## 实现步骤

### 1. 创建司法案例策略类

在`src/apps/data/source/risk-change-v2/strategies/judicial-case.strategy.ts`文件中实现司法案例策略：

```typescript
import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRiskChangeStrategy } from './base-risk-change.strategy';
import { RiskChangeHelper } from '../../../helper/risk.change.helper';
import { JudgementHelper } from '../../../helper/judgement.helper';
import { CaseReasonHelper } from '../../../helper/case-reason.helper';
import { cloneDeep, orderBy } from 'lodash';

/**
 * 司法案例策略
 * 处理司法案例相关的风险类型
 */
@Injectable()
export class JudicialCaseStrategy extends BaseRiskChangeStrategy {
  /**
   * 构造函数
   * @param riskChangeHelper 风险变更辅助服务
   * @param judgementHelper 判决辅助服务
   * @param caseReasonHelper 案由辅助服务
   */
  constructor(
    private readonly riskChangeHelper: RiskChangeHelper,
    private readonly judgementHelper: JudgementHelper,
    private readonly caseReasonHelper: CaseReasonHelper,
  ) {
    super('JudicialCaseStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.JudgementDocuments, DimensionTypeEnums.CourtAnnouncement, DimensionTypeEnums.IntellectualPropertyJudgement];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.JudgementDocuments]: [RiskChangeCategoryEnum.category7],
      [DimensionTypeEnums.CourtAnnouncement]: [RiskChangeCategoryEnum.category16],
      [DimensionTypeEnums.IntellectualPropertyJudgement]: [RiskChangeCategoryEnum.category7],
    };
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 创建基础查询对象
      const query = this.createBaseQuery(companyId);

      // 添加风险类别过滤
      const categories = this.getDimensionCategoryMap()[dimension.key];
      if (categories?.length) {
        query.bool.must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加维度过滤条件
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.must.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加有效性过滤
      const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidField && Number(isValidField.fieldValue[0]) >= 0) {
        query.bool.must.push({
          term: {
            IsValid: Number(isValidField.fieldValue[0]),
          },
        });
      } else {
        // 默认只查询有效记录
        query.bool.must.push({
          term: {
            IsValid: 1,
          },
        });
      }

      // 根据维度类型添加特定条件
      if (dimension.key === DimensionTypeEnums.IntellectualPropertyJudgement) {
        // 知识产权判决特定条件
        const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.caseType);
        if (caseTypeField && caseTypeField.fieldValue?.length) {
          query.bool.must.push({
            terms: {
              'ChangeExtend.CaseType': caseTypeField.fieldValue,
            },
          });
        }
      }

      return query;
    } catch (error) {
      this.logError('生成司法案例查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];

      // 处理每条记录
      for (const itemRaw of response.Result) {
        try {
          const item = cloneDeep(itemRaw);

          // 解析JSON字段
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              const value = item[key];
              try {
                item[key] = value ? JSON.parse(value) : {};
              } catch (error) {
                item[key] = value;
              }
            }
          });

          let isHit = true;

          // 判决文书处理
          if (dimension.key === DimensionTypeEnums.JudgementDocuments || dimension.key === DimensionTypeEnums.IntellectualPropertyJudgement) {
            // 当事人角色处理
            const litigantRolesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.litigantRoles);
            if (litigantRolesField && isHit) {
              isHit = this.judgementHelper.hitLitigantRoles(litigantRolesField, item);
            }

            // 案件类型处理
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.caseType);
            if (caseTypeField && isHit) {
              isHit = this.judgementHelper.hitCaseType(caseTypeField, item);
            }

            // 案由处理
            const caseReasonsField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.caseReasons);
            if (caseReasonsField && isHit) {
              isHit = this.caseReasonHelper.hitCaseReasons(caseReasonsField, item);
            }

            // 知识产权特殊处理
            if (dimension.key === DimensionTypeEnums.IntellectualPropertyJudgement) {
              // 过滤出知识产权相关案件
              isHit = isHit && this.judgementHelper.isIntellectualPropertyCase(item);
            }
          }

          // 法院公告处理
          else if (dimension.key === DimensionTypeEnums.CourtAnnouncement) {
            // 公告类型处理
            const announcementTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.announcementTypes);
            if (announcementTypesField && isHit) {
              isHit = this.judgementHelper.hitAnnouncementTypes(announcementTypesField, item);
            }
          }

          if (isHit) {
            hitData.push(item);
          }
        } catch (error) {
          this.logError('处理司法案例详情项失败', error);
        }
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理司法案例详情失败', error);
      return response;
    }
  }
}
```

### 2. 创建行政处罚策略类

在`src/apps/data/source/risk-change-v2/strategies/administrative-penalty.strategy.ts`文件中实现行政处罚策略：

```typescript
import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRiskChangeStrategy } from './base-risk-change.strategy';
import { RiskChangeHelper } from '../../../helper/risk.change.helper';
import { PenaltyHelper } from '../../../helper/penalty.helper';
import { cloneDeep, orderBy } from 'lodash';

/**
 * 行政处罚策略
 * 处理行政处罚相关的风险类型
 */
@Injectable()
export class AdministrativePenaltyStrategy extends BaseRiskChangeStrategy {
  /**
   * 构造函数
   * @param riskChangeHelper 风险变更辅助服务
   * @param penaltyHelper 处罚辅助服务
   */
  constructor(private readonly riskChangeHelper: RiskChangeHelper, private readonly penaltyHelper: PenaltyHelper) {
    super('AdministrativePenaltyStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.AdministrativePenalty, DimensionTypeEnums.EnvironmentalPenalty, DimensionTypeEnums.TaxPenalty];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.AdministrativePenalty]: [RiskChangeCategoryEnum.category1],
      [DimensionTypeEnums.EnvironmentalPenalty]: [RiskChangeCategoryEnum.category1],
      [DimensionTypeEnums.TaxPenalty]: [RiskChangeCategoryEnum.category1],
    };
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 创建基础查询对象
      const query = this.createBaseQuery(companyId);

      // 添加风险类别过滤
      const categories = this.getDimensionCategoryMap()[dimension.key];
      if (categories?.length) {
        query.bool.must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加维度过滤条件
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.must.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加有效性过滤
      const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidField && Number(isValidField.fieldValue[0]) >= 0) {
        query.bool.must.push({
          term: {
            IsValid: Number(isValidField.fieldValue[0]),
          },
        });
      } else {
        // 默认只查询有效记录
        query.bool.must.push({
          term: {
            IsValid: 1,
          },
        });
      }

      // 环境处罚特定条件
      if (dimension.key === DimensionTypeEnums.EnvironmentalPenalty) {
        query.bool.must.push({
          terms: {
            'ChangeExtend.PunishmentType': ['环保处罚'],
          },
        });
      }

      // 税务处罚特定条件
      else if (dimension.key === DimensionTypeEnums.TaxPenalty) {
        query.bool.must.push({
          terms: {
            'ChangeExtend.PunishmentType': ['税务处罚'],
          },
        });
      }

      return query;
    } catch (error) {
      this.logError('生成行政处罚查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];

      // 处理每条记录
      for (const itemRaw of response.Result) {
        try {
          const item = cloneDeep(itemRaw);

          // 解析JSON字段
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              const value = item[key];
              try {
                item[key] = value ? JSON.parse(value) : {};
              } catch (error) {
                item[key] = value;
              }
            }
          });

          let isHit = true;

          // 处理行政处罚
          if (item.Category === RiskChangeCategoryEnum.category1) {
            // 处罚类型处理
            const penaltyTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyTypes);
            if (penaltyTypesField && isHit) {
              isHit = this.penaltyHelper.hitPenaltyTypes(penaltyTypesField, item);
            }

            // 处罚机构处理
            const penaltyDepartmentsField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyDepartments);
            if (penaltyDepartmentsField && isHit) {
              isHit = this.penaltyHelper.hitPenaltyDepartments(penaltyDepartmentsField, item);
            }

            // 处罚金额处理
            const penaltyAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyAmount);
            if (penaltyAmountField && isHit) {
              isHit = this.penaltyHelper.hitPenaltyAmount(penaltyAmountField, item);
            }

            // 环保处罚特殊处理
            if (dimension.key === DimensionTypeEnums.EnvironmentalPenalty) {
              isHit = isHit && this.penaltyHelper.isEnvironmentalPenalty(item);
            }
            // 税务处罚特殊处理
            else if (dimension.key === DimensionTypeEnums.TaxPenalty) {
              isHit = isHit && this.penaltyHelper.isTaxPenalty(item);
            }
          }

          if (isHit) {
            hitData.push(item);
          }
        } catch (error) {
          this.logError('处理行政处罚详情项失败', error);
        }
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理行政处罚详情失败', error);
      return response;
    }
  }
}
```

### 3. 更新主类以集成新策略

更新`src/apps/data/source/risk-change-v2/risk-change-es.source.v2.ts`文件，增加对新策略的依赖注入和使用：

```typescript
// 在构造函数中增加依赖注入
constructor(
  readonly configService: ConfigService,
  private readonly moduleRef: ModuleRef,
  private readonly legalChangeStrategy: LegalChangeStrategy,
  private readonly capitalChangeStrategy: CapitalChangeStrategy,
  private readonly negativeNewsStrategy: NegativeNewsStrategy,
  private readonly businessAbnormalStrategy: BusinessAbnormalStrategy,
  private readonly judicialCaseStrategy: JudicialCaseStrategy,
  private readonly administrativePenaltyStrategy: AdministrativePenaltyStrategy,
  // 其他策略依赖...
) {
  super(
    'RiskChangeEsSourceV2',
    new Client({
      nodes: configService.esConfig.riskChangeList.nodes,
      ssl: { rejectUnauthorized: false },
    }),
    configService.esConfig.riskChangeList.indexName,
  );

  // 初始化策略类列表
  this.initStrategies();
}

/**
 * 初始化策略列表
 */
private initStrategies(): void {
  this.strategies = [
    this.legalChangeStrategy,
    this.capitalChangeStrategy,
    this.negativeNewsStrategy,
    this.businessAbnormalStrategy,
    this.judicialCaseStrategy,
    this.administrativePenaltyStrategy,
    // 添加其他策略...
  ];
}
```

## 预期成果

完成第五阶段后，我们将实现两个与法律风险相关的重要策略类：

1. **司法案例策略**：处理企业的判决文书、法院公告和知识产权判决相关风险
2. **行政处罚策略**：处理企业的行政处罚、环境处罚和税务处罚相关风险

这两个策略将与之前实现的策略一起，通过策略模式进行统一管理和调用，进一步完善风险动态模块的重构，使代码结构更加清晰，易于维护和扩展。
