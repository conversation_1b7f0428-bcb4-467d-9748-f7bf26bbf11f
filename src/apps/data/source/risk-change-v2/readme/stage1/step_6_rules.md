# 风险动态模块优化计划 - 第六阶段：实现股权变更和财务指标策略

## 背景说明

前五个阶段已完成了基础架构搭建、法定代表人变更、资本变更、关联方维度、负面新闻、经营异常、司法案例和行政处罚策略的实现。在第六阶段，我们将专注于实现股权变更和财务指标策略，这两类风险反映了企业股权结构和财务状况的变化。

## 主要目标

1. 实现股权变更策略（EquityChangeStrategy）
2. 实现财务指标策略（FinancialIndicatorStrategy）
3. 更新主类以集成这两个策略

## 优化要求

1. 不修改原有业务逻辑，本次仅调整代码结构
2. 新增代码放在 src/apps/data/source/risk-change-v2/ 文件夹
3. 不修改原有文件
4. 文件命名需保持命名含义，体现 RiskChange 处理相关的含义
5. 代码风格需统一，与现有代码保持一致
6. 使用 TypeScript 类型，避免使用 any 类型

## 实现步骤

### 1. 创建股权变更策略类

在`src/apps/data/source/risk-change-v2/strategies/equity-change.strategy.ts`文件中实现股权变更策略：

```typescript
import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRiskChangeStrategy } from './base-risk-change.strategy';
import { RiskChangeHelper } from '../../../helper/risk.change.helper';
import { CompanyShareHelper } from '../../../helper/company-share.helper';
import { CompanyStockHelper } from '../../../helper/company-stock.helper';
import { cloneDeep, orderBy } from 'lodash';

/**
 * 股权变更策略
 * 处理股权变更相关的风险类型
 */
@Injectable()
export class EquityChangeStrategy extends BaseRiskChangeStrategy {
  /**
   * 构造函数
   * @param riskChangeHelper 风险变更辅助服务
   * @param companyShareHelper 企业股份辅助服务
   * @param companyStockHelper 企业股票辅助服务
   */
  constructor(
    private readonly riskChangeHelper: RiskChangeHelper,
    private readonly companyShareHelper: CompanyShareHelper,
    private readonly companyStockHelper: CompanyStockHelper,
  ) {
    super('EquityChangeStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.MainInfoUpdateHolder, DimensionTypeEnums.EquityPledge, DimensionTypeEnums.FreezeEquity, DimensionTypeEnums.StockPledge];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.MainInfoUpdateHolder]: [RiskChangeCategoryEnum.category12],
      [DimensionTypeEnums.EquityPledge]: [RiskChangeCategoryEnum.category12],
      [DimensionTypeEnums.FreezeEquity]: [RiskChangeCategoryEnum.category12],
      [DimensionTypeEnums.StockPledge]: [RiskChangeCategoryEnum.category25],
    };
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 创建基础查询对象
      const query = this.createBaseQuery(companyId);

      // 添加风险类别过滤
      const categories = this.getDimensionCategoryMap()[dimension.key];
      if (categories?.length) {
        query.bool.must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加维度过滤条件
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.must.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加有效性过滤
      const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidField && Number(isValidField.fieldValue[0]) >= 0) {
        query.bool.must.push({
          term: {
            IsValid: Number(isValidField.fieldValue[0]),
          },
        });
      } else {
        // 默认只查询有效记录
        query.bool.must.push({
          term: {
            IsValid: 1,
          },
        });
      }

      // 根据维度类型添加特定条件
      if (dimension.key === DimensionTypeEnums.MainInfoUpdateHolder) {
        // 股东变更特定条件
        const holderChangeTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderChangeType);
        if (holderChangeTypeField && holderChangeTypeField.fieldValue?.length) {
          query.bool.must.push({
            terms: {
              'ChangeExtend.ChangeType': holderChangeTypeField.fieldValue,
            },
          });
        }
      } else if (dimension.key === DimensionTypeEnums.EquityPledge) {
        // 股权质押特定条件
        const equityPledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeStatus);
        if (equityPledgeStatusField && equityPledgeStatusField.fieldValue?.length) {
          query.bool.must.push({
            terms: {
              'ChangeExtend.Status': equityPledgeStatusField.fieldValue,
            },
          });
        }
      } else if (dimension.key === DimensionTypeEnums.FreezeEquity) {
        // 股权冻结特定条件
        query.bool.must.push({
          term: {
            'ChangeExtend.Type': '股权冻结',
          },
        });
      } else if (dimension.key === DimensionTypeEnums.StockPledge) {
        // 股票质押特定条件
        const stockPledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.stockPledgeStatus);
        if (stockPledgeStatusField && stockPledgeStatusField.fieldValue?.length) {
          query.bool.must.push({
            terms: {
              'ChangeExtend.Status': stockPledgeStatusField.fieldValue,
            },
          });
        }
      }

      return query;
    } catch (error) {
      this.logError('生成股权变更查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];

      // 处理每条记录
      for (const itemRaw of response.Result) {
        try {
          const item = cloneDeep(itemRaw);

          // 解析JSON字段
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              const value = item[key];
              try {
                item[key] = value ? JSON.parse(value) : {};
              } catch (error) {
                item[key] = value;
              }
            }
          });

          let isHit = true;

          // 处理股权变更
          if (item.Category === RiskChangeCategoryEnum.category12) {
            // 处理股东变更
            if (dimension.key === DimensionTypeEnums.MainInfoUpdateHolder) {
              // 股东角色处理
              const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
              if (holderRoleField && isHit) {
                isHit = this.companyShareHelper.holderRoleFieldCategory12(holderRoleField, item);
              }

              // 股东变更类型处理
              const holderChangeTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderChangeType);
              if (holderChangeTypeField && isHit) {
                isHit = this.companyShareHelper.hitShareChangeStatusField(holderChangeTypeField, item);
              }

              // 股东变更比例处理
              const shareChangeRateField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeRate);
              if (shareChangeRateField && isHit) {
                isHit = this.companyShareHelper.hitShareChangeRateField(shareChangeRateField, item);
              }
            }
            // 处理股权质押
            else if (dimension.key === DimensionTypeEnums.EquityPledge) {
              // 质押状态处理
              const equityPledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeStatus);
              if (equityPledgeStatusField && isHit) {
                isHit = this.companyShareHelper.equityPledgeStatusFieldCategory12(equityPledgeStatusField, item);
              }

              // 质押比例处理
              const equityPledgeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeRatio);
              if (equityPledgeRatioField && isHit) {
                isHit = this.companyShareHelper.equityPledgeRatioFieldCategory12(equityPledgeRatioField, item);
              }
            }
            // 处理股权冻结
            else if (dimension.key === DimensionTypeEnums.FreezeEquity) {
              // 确认是股权冻结类型
              isHit = item.ChangeExtend?.Type === '股权冻结';
            }
          }
          // 处理股票质押
          else if (item.Category === RiskChangeCategoryEnum.category25 && dimension.key === DimensionTypeEnums.StockPledge) {
            // 股票质押状态处理
            const stockPledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.stockPledgeStatus);
            if (stockPledgeStatusField && isHit) {
              isHit = this.companyStockHelper.stockPledgeStatusFieldCategory25(stockPledgeStatusField, item);
            }

            // 股票质押比例处理
            const stockPledgeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.stockPledgeRatio);
            if (stockPledgeRatioField && isHit) {
              isHit = this.companyStockHelper.stockPledgeRatioFieldCategory25(stockPledgeRatioField, item);
            }
          }

          if (isHit) {
            hitData.push(item);
          }
        } catch (error) {
          this.logError('处理股权变更详情项失败', error);
        }
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理股权变更详情失败', error);
      return response;
    }
  }
}
```

### 2. 创建财务指标策略类

在`src/apps/data/source/risk-change-v2/strategies/financial-indicator.strategy.ts`文件中实现财务指标策略：

```typescript
import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRiskChangeStrategy } from './base-risk-change.strategy';
import { RiskChangeHelper } from '../../../helper/risk.change.helper';
import { CompanyFinaceHelper } from '../../../helper/company-finace.helper';
import { cloneDeep, orderBy } from 'lodash';

/**
 * 财务指标策略
 * 处理财务指标相关的风险类型
 */
@Injectable()
export class FinancialIndicatorStrategy extends BaseRiskChangeStrategy {
  /**
   * 构造函数
   * @param riskChangeHelper 风险变更辅助服务
   * @param companyFinaceHelper 企业财务辅助服务
   */
  constructor(private readonly riskChangeHelper: RiskChangeHelper, private readonly companyFinaceHelper: CompanyFinaceHelper) {
    super('FinancialIndicatorStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [
      DimensionTypeEnums.FinancialHealth,
      DimensionTypeEnums.RetainedProfit,
      DimensionTypeEnums.NetProfitRatio,
      DimensionTypeEnums.RevenueRatio,
      DimensionTypeEnums.AccountsReceivableRatio,
    ];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.FinancialHealth]: [RiskChangeCategoryEnum.category49],
      [DimensionTypeEnums.RetainedProfit]: [RiskChangeCategoryEnum.category49],
      [DimensionTypeEnums.NetProfitRatio]: [RiskChangeCategoryEnum.category49],
      [DimensionTypeEnums.RevenueRatio]: [RiskChangeCategoryEnum.category49],
      [DimensionTypeEnums.AccountsReceivableRatio]: [RiskChangeCategoryEnum.category49],
    };
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 创建基础查询对象
      const query = this.createBaseQuery(companyId);

      // 添加风险类别过滤
      const categories = this.getDimensionCategoryMap()[dimension.key];
      if (categories?.length) {
        query.bool.must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加维度过滤条件
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.must.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加有效性过滤
      const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidField && Number(isValidField.fieldValue[0]) >= 0) {
        query.bool.must.push({
          term: {
            IsValid: Number(isValidField.fieldValue[0]),
          },
        });
      } else {
        // 默认只查询有效记录
        query.bool.must.push({
          term: {
            IsValid: 1,
          },
        });
      }

      return query;
    } catch (error) {
      this.logError('生成财务指标查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];

      // 处理每条记录
      for (const itemRaw of response.Result) {
        try {
          const item = cloneDeep(itemRaw);

          // 解析JSON字段
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              const value = item[key];
              try {
                item[key] = value ? JSON.parse(value) : {};
              } catch (error) {
                item[key] = value;
              }
            }
          });

          let isHit = true;

          // 处理财务指标
          if (item.Category === RiskChangeCategoryEnum.category49) {
            // 根据不同维度类型处理
            switch (dimension.key) {
              case DimensionTypeEnums.RetainedProfit:
                // 留存收益处理
                const retainedProfitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.retainedProfit);
                if (retainedProfitField && isHit) {
                  isHit = this.companyFinaceHelper.categoryRetainedProfitField(retainedProfitField, item);
                }
                break;

              case DimensionTypeEnums.NetProfitRatio:
                // 净利润率处理
                const netProfitRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.netProfitRatio);
                if (netProfitRatioField && isHit) {
                  isHit = this.companyFinaceHelper.categoryNetProfitRatioField(netProfitRatioField, item);
                }
                break;

              case DimensionTypeEnums.RevenueRatio:
                // 营收增长率处理
                const revenueRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.revenueRatio);
                if (revenueRatioField && isHit) {
                  isHit = this.companyFinaceHelper.categoryRevenueRatioField(revenueRatioField, item);
                }
                break;

              case DimensionTypeEnums.AccountsReceivableRatio:
                // 应收账款增长率处理
                const accountsReceivableRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.accountsReceivableRatio);
                if (accountsReceivableRatioField && isHit) {
                  isHit = this.companyFinaceHelper.categoryAccountsReceivableRatioField(accountsReceivableRatioField, item);
                }
                break;

              case DimensionTypeEnums.FinancialHealth:
                // 财务健康度处理
                const financialHealthField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.financialHealth);
                if (financialHealthField && isHit) {
                  isHit = this.companyFinaceHelper.categoryFinancialHealthField(financialHealthField, item);
                }
                break;
            }
          }

          if (isHit) {
            hitData.push(item);
          }
        } catch (error) {
          this.logError('处理财务指标详情项失败', error);
        }
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理财务指标详情失败', error);
      return response;
    }
  }
}
```

### 3. 更新主类以集成新策略

更新`src/apps/data/source/risk-change-v2/risk-change-es.source.v2.ts`文件，增加对新策略的依赖注入和使用：

```typescript
// 在构造函数中增加依赖注入
constructor(
  readonly configService: ConfigService,
  private readonly moduleRef: ModuleRef,
  private readonly legalChangeStrategy: LegalChangeStrategy,
  private readonly capitalChangeStrategy: CapitalChangeStrategy,
  private readonly negativeNewsStrategy: NegativeNewsStrategy,
  private readonly businessAbnormalStrategy: BusinessAbnormalStrategy,
  private readonly judicialCaseStrategy: JudicialCaseStrategy,
  private readonly administrativePenaltyStrategy: AdministrativePenaltyStrategy,
  private readonly equityChangeStrategy: EquityChangeStrategy,
  private readonly financialIndicatorStrategy: FinancialIndicatorStrategy,
  // 其他策略依赖...
) {
  super(
    'RiskChangeEsSourceV2',
    new Client({
      nodes: configService.esConfig.riskChangeList.nodes,
      ssl: { rejectUnauthorized: false },
    }),
    configService.esConfig.riskChangeList.indexName,
  );

  // 初始化策略类列表
  this.initStrategies();
}

/**
 * 初始化策略列表
 */
private initStrategies(): void {
  this.strategies = [
    this.legalChangeStrategy,
    this.capitalChangeStrategy,
    this.negativeNewsStrategy,
    this.businessAbnormalStrategy,
    this.judicialCaseStrategy,
    this.administrativePenaltyStrategy,
    this.equityChangeStrategy,
    this.financialIndicatorStrategy,
    // 添加其他策略...
  ];
}
```

## 预期成果

完成第六阶段后，我们将实现两个与企业股权和财务相关的重要策略类：

1. **股权变更策略**：处理企业的股东变更、股权质押、股权冻结和股票质押相关风险
2. **财务指标策略**：处理企业的财务健康度、留存收益、净利润率、营收增长率和应收账款增长率相关风险

这两个策略将与之前实现的策略一起，通过策略模式进行统一管理和调用，进一步完善风险动态模块的重构，使代码结构更加清晰，易于维护和扩展。
