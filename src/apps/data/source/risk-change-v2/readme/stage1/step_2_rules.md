# 风险动态模块优化计划 - 第二阶段：实现法定代表人变更和资本变更策略

## 背景说明

第一阶段已经完成了策略模式的基础架构搭建，包括接口定义和基类实现。在第二阶段，我们将开始实现具体的策略类，先从法定代表人变更和资本变更这两个重要的风险变更类型开始。

## 主要目标

1. 实现法定代表人变更策略（LegalChangeStrategy）
2. 实现资本变更策略（CapitalChangeStrategy）
3. 更新主类以使用这些策略

## 优化要求

1. 不修改原有业务逻辑，本次仅调整代码结构
2. 新增代码放在 src/apps/data/source/risk-change-v2/ 文件夹
3. 不修改原有文件
4. 文件命名需保持命名含义，体现 RiskChange 处理相关的含义
5. 代码风格需统一，与现有代码保持一致
6. 使用 TypeScript 类型，避免使用 any 类型

## 实现步骤

### 1. 创建法定代表人变更策略类

在`src/apps/data/source/risk-change-v2/strategies/legal-change.strategy.ts`文件中实现法定代表人变更策略：

```typescript
import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRiskChangeStrategy } from './base-risk-change.strategy';
import { RiskChangeHelper } from '../../../helper/risk.change.helper';
import { cloneDeep, orderBy } from 'lodash';

/**
 * 法定代表人变更策略
 * 处理法定代表人变更相关的风险类型
 */
@Injectable()
export class LegalChangeStrategy extends BaseRiskChangeStrategy {
  /**
   * 构造函数
   * @param riskChangeHelper 风险变更辅助服务
   */
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super('LegalChangeStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.LegalPersonChange, DimensionTypeEnums.FrequentLegalPersonChange];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.LegalPersonChange]: [RiskChangeCategoryEnum.category39],
      [DimensionTypeEnums.FrequentLegalPersonChange]: [RiskChangeCategoryEnum.category39],
    };
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 创建基础查询对象
      const query = this.createBaseQuery(companyId);

      // 添加风险类别过滤
      const categories = this.getDimensionCategoryMap()[dimension.key];
      if (categories?.length) {
        query.bool.must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加维度过滤条件
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.must.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加有效性过滤
      const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidField && Number(isValidField.fieldValue[0]) >= 0) {
        query.bool.must.push({
          term: {
            IsValid: Number(isValidField.fieldValue[0]),
          },
        });
      } else {
        // 默认只查询有效记录
        query.bool.must.push({
          term: {
            IsValid: 1,
          },
        });
      }

      return query;
    } catch (error) {
      this.logError('生成法定代表人变更查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];

      // 处理每条记录
      for (const itemRaw of response.Result) {
        try {
          const item = cloneDeep(itemRaw);

          // 解析JSON字段
          Object.keys(item).forEach((key) => {
            if (['Extend1', 'ChangeExtend'].includes(key)) {
              const value = item[key];
              try {
                item[key] = value ? JSON.parse(value) : {};
              } catch (error) {
                item[key] = value;
              }
            }
          });

          let isHit = true;

          // 处理法定代表人变更
          if (item.Category === RiskChangeCategoryEnum.category39) {
            const layTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.layTypes);
            if (layTypesField && isHit) {
              isHit = this.riskChangeHelper.hitLayTypesField(layTypesField, item);
            }
          }

          if (isHit) {
            hitData.push(item);
          }
        } catch (error) {
          this.logError('处理法定代表人变更详情项失败', error);
        }
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理法定代表人变更详情失败', error);
      return response;
    }
  }
}
```

### 2. 创建资本变更策略类

在`src/apps/data/source/risk-change-v2/strategies/capital-change.strategy.ts`文件中实现资本变更策略：

```typescript
import { Injectable } from '@nestjs/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { BaseRiskChangeStrategy } from './base-risk-change.strategy';
import { RiskChangeHelper } from '../../../helper/risk.change.helper';
import { cloneDeep, orderBy } from 'lodash';
import * as Bluebird from 'bluebird';

/**
 * 资本变更策略
 * 处理资本变更相关的风险类型
 */
@Injectable()
export class CapitalChangeStrategy extends BaseRiskChangeStrategy {
  /**
   * 构造函数
   * @param riskChangeHelper 风险变更辅助服务
   */
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super('CapitalChangeStrategy');
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.CapitalReduction, DimensionTypeEnums.MainInfoUpdateCapitalChange];
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): { [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[] } {
    return {
      [DimensionTypeEnums.CapitalReduction]: [RiskChangeCategoryEnum.category123],
      [DimensionTypeEnums.MainInfoUpdateCapitalChange]: [RiskChangeCategoryEnum.category37],
    };
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 创建基础查询对象
      const query = this.createBaseQuery(companyId);

      // 添加风险类别过滤
      const categories = this.getDimensionCategoryMap()[dimension.key];
      if (categories?.length) {
        query.bool.must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加维度过滤条件
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.must.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加有效性过滤
      const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidField && Number(isValidField.fieldValue[0]) >= 0) {
        query.bool.must.push({
          term: {
            IsValid: Number(isValidField.fieldValue[0]),
          },
        });
      } else {
        // 默认只查询有效记录
        query.bool.must.push({
          term: {
            IsValid: 1,
          },
        });
      }

      return query;
    } catch (error) {
      this.logError('生成资本变更查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];

      // 根据维度类型进行不同的处理
      if (dimension.key === DimensionTypeEnums.CapitalReduction) {
        await this.processCapitalReduction(hitData, response, dimension, params);
      } else if (dimension.key === DimensionTypeEnums.MainInfoUpdateCapitalChange) {
        await this.processMainInfoUpdateCapitalChange(hitData, response, dimension, params);
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理资本变更详情失败', error);
      return response;
    }
  }

  /**
   * 处理减资公告
   * @param hitData 命中数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   */
  private async processCapitalReduction(
    hitData: any[],
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
  ): Promise<void> {
    await Bluebird.map(response.Result, async (itemRaw) => {
      try {
        const item = cloneDeep(itemRaw);

        // 解析JSON字段
        Object.keys(item).forEach((key) => {
          if (['Extend1', 'ChangeExtend'].includes(key)) {
            const value = item[key];
            try {
              item[key] = value ? JSON.parse(value) : {};
            } catch (error) {
              item[key] = value;
            }
          }
        });

        let isHit = true;

        // 处理减资公告
        if (item.Category === RiskChangeCategoryEnum.category123) {
          // 币种变更
          const currencyChangeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.currencyChange);
          if (currencyChangeField && isHit) {
            isHit = this.riskChangeHelper.hitCategory123CurrencyChangeField(currencyChangeField, item);
          }

          // 资本减少比率
          const regisCapitalChangeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.capitalReductionRate);
          if (regisCapitalChangeRatioField && isHit) {
            isHit = this.riskChangeHelper.capitalReduceSelectCompareResult(regisCapitalChangeRatioField, item);
          }

          // 注册资本变更周期
          const changeRangeRegisCapitalCycle = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodRegisCapital);
          if (changeRangeRegisCapitalCycle && isHit) {
            const valuePeriodBaseLine = changeRangeRegisCapitalCycle.fieldValue[0]?.valuePeriodBaseLine;
            if (valuePeriodBaseLine) {
              const periodRes = await this.getCommonCivilRiskChange(params.keyNo, [RiskChangeCategoryEnum.category37], valuePeriodBaseLine, 'year', 10000);

              if (periodRes?.Result?.length) {
                isHit = this.riskChangeHelper.hitPeriodRegisCapitalField123(changeRangeRegisCapitalCycle, periodRes.Result, item);
              } else {
                isHit = false;
              }
            }
          }
        }

        if (isHit) {
          hitData.push(item);
        }
      } catch (error) {
        this.logError('处理减资公告详情项失败', error);
      }
    });
  }

  /**
   * 处理注册资本变更
   * @param hitData 命中数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   */
  private async processMainInfoUpdateCapitalChange(
    hitData: any[],
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
  ): Promise<void> {
    if (response?.Result?.length) {
      const strategyFieldByKey = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodRegisCapital);
      if (strategyFieldByKey) {
        const hit = this.riskChangeHelper.hitMainInfoUpdateCapitalChange(strategyFieldByKey, response?.Result);
        if (hit) {
          hitData.push(response?.Result[0]);
        }
      }
    }
  }

  /**
   * 获取通用风险动态
   * @param companyId 企业ID
   * @param categories 风险类别
   * @param periodTime 周期时间
   * @param periodUnit 周期单位
   * @param pageSize 页大小
   */
  private async getCommonCivilRiskChange(
    companyId: string,
    categories: RiskChangeCategoryEnum[],
    periodTime: number,
    periodUnit: string,
    pageSize = 1,
  ): Promise<HitDetailsBaseResponse> {
    try {
      // 这里应该调用ES查询，但由于我们不能修改原有代码，先返回空结果
      return {
        Paging: {
          PageSize: pageSize,
          PageIndex: 1,
          TotalRecords: 0,
        },
        Result: [],
      };
    } catch (error) {
      this.logError('获取通用风险动态失败', error);
      return {
        Paging: {
          PageSize: pageSize,
          PageIndex: 1,
          TotalRecords: 0,
        },
        Result: [],
      };
    }
  }
}
```

### 3. 更新主类以使用新的策略类

在`src/apps/data/source/risk-change-v2/risk-change-es.source.v2.ts`文件中的`initStrategies`方法中添加新的策略类：

```typescript
/**
 * 初始化策略类列表
 */
private initStrategies(): void {
  try {
    // 获取所有策略类实例
    this.strategies = [
      this.moduleRef.get(LegalChangeStrategy),
      this.moduleRef.get(CapitalChangeStrategy),
      // 后续会添加更多策略类
    ];
  } catch (error) {
    this.logger.error(`初始化策略类列表失败: ${error instanceof Error ? error.message : String(error)}`, error);
  }
}
```

同样，在`src/apps/data/source/risk-change-v2/risk-change-related-es.source.ts`文件中的`initStrategies`方法中也添加新的策略类：

```typescript
/**
 * 初始化策略类列表
 */
private initStrategies(): void {
  try {
    // 获取所有策略类实例
    this.strategies = [
      this.moduleRef.get(LegalChangeStrategy),
      this.moduleRef.get(CapitalChangeStrategy),
      // 后续会添加更多策略类
    ];
  } catch (error) {
    this.logger.error(`初始化策略类列表失败: ${error instanceof Error ? error.message : String(error)}`, error);
  }
}
```

### 4. 更新主模块以提供新的策略类

在 `src/apps/data/source/risk-change-v2/risk-change-v2.module.ts` 文件中注册这些策略类：

```typescript
import { Module } from '@nestjs/common';
import { RiskChangeEsSourceV2 } from './risk-change-es.source.v2';
import { RiskChangeRelatedEsSource } from './risk-change-related-es.source';
import { LegalChangeStrategy } from './strategies/legal-change.strategy';
import { CapitalChangeStrategy } from './strategies/capital-change.strategy';
import { RiskChangeModule } from '../risk-change/risk-change.module';

@Module({
  imports: [
    RiskChangeModule, // 导入原有模块以使用其中的服务
  ],
  providers: [RiskChangeEsSourceV2, RiskChangeRelatedEsSource, LegalChangeStrategy, CapitalChangeStrategy],
  exports: [RiskChangeEsSourceV2, RiskChangeRelatedEsSource],
})
export class RiskChangeV2Module {}
```

## 预期结果

完成本阶段后，我们将实现两个具体的策略类：

1. 法定代表人变更策略（LegalChangeStrategy）
2. 资本变更策略（CapitalChangeStrategy）

并将它们集成到主类中，使得主类能够使用这些策略来处理对应类型的风险变更。这是策略模式重构的关键步骤，后续阶段将继续实现更多的策略类，逐步完成整个重构工作。
