# 风险动态模块优化计划 - 第一阶段：基础架构搭建

## 背景说明

当前的风险动态模块存在代码耦合度高、文件过大、逻辑分散和扩展性差等问题。本次优化旨在采用策略模式重构代码，提高可维护性、可扩展性和可测试性。按照分阶段重构计划，第一阶段主要完成基础架构搭建。

## 主要目标

1. 创建策略模式的基础结构，包括接口和基类
2. 创建主要的策略类框架
3. 初步实现主类的框架代码

## 优化要求

1. 不修改原有业务逻辑，本次仅调整代码结构
2. 新增代码放在 src/apps/data/source/risk-change-v2/ 文件夹
3. 不修改原有文件
4. 文件命名需保持命名含义，体现 RiskChange 处理相关的含义
5. 代码风格需统一，与现有代码保持一致
6. 使用 TypeScript 类型，避免使用 any 类型

## 实现步骤

### 1. 创建风险变更策略接口

在`src/apps/data/source/risk-change-v2/interfaces/risk-change-strategy.interface.ts`文件中定义策略接口：

```typescript
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';

/**
 * 维度类型与风险变更类别映射类型
 */
export type DimensionCategoryMap = {
  [key in DimensionTypeEnums]?: RiskChangeCategoryEnum[];
};

/**
 * 风险变更策略接口
 * 定义处理风险变更的核心方法
 */
export interface RiskChangeStrategy {
  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[];

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): DimensionCategoryMap;

  /**
   * 检查维度类型是否由该策略处理
   * @param dimension 维度策略
   */
  supportsDimension(dimension: DimensionHitStrategyPO): boolean;

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object>;

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse>;
}
```

### 2. 创建风险变更策略基类

在`src/apps/data/source/risk-change-v2/strategies/base-risk-change.strategy.ts`文件中实现策略基类：

```typescript
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeStrategy, DimensionCategoryMap } from '../interfaces/risk-change-strategy.interface';

/**
 * 风险变更策略基类
 * 实现RiskChangeStrategy接口的抽象基类
 */
export abstract class BaseRiskChangeStrategy implements RiskChangeStrategy {
  protected readonly logger: Logger;

  /**
   * 构造函数
   * @param strategyName 策略名称
   */
  constructor(private readonly strategyName: string) {
    this.logger = QccLogger.getLogger(strategyName);
  }

  /**
   * 获取支持的维度类型列表
   * 子类应该重写此方法以提供其支持的维度类型列表
   */
  abstract getSupportedDimensions(): DimensionTypeEnums[];

  /**
   * 获取维度类型与风险变更类别的映射
   * 子类应该重写此方法以提供其维度类型与类别的映射关系
   */
  abstract getDimensionCategoryMap(): DimensionCategoryMap;

  /**
   * 检查维度类型是否由该策略处理
   * @param dimension 维度策略
   */
  supportsDimension(dimension: DimensionHitStrategyPO): boolean {
    return this.getSupportedDimensions().includes(dimension.key);
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  abstract generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object>;

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  abstract processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse>;

  /**
   * 创建基础查询对象
   * @param companyId 企业ID
   */
  protected createBaseQuery(companyId: string): object {
    return {
      bool: {
        must: [
          {
            term: {
              KeyNo: companyId,
            },
          },
        ],
      },
    };
  }

  /**
   * 记录错误日志
   * @param message 错误信息
   * @param error 错误对象
   */
  protected logError(message: string, error: unknown): void {
    const errorMessage = error instanceof Error ? error.message : String(error);
    this.logger.error(`${message}: ${errorMessage}`, error);
  }
}
```

### 3. 创建普通维度的风险变更服务框架

在`src/apps/data/source/risk-change-v2/risk-change-es.source.v2.ts`文件中实现处理普通维度的主类框架：

```typescript
import { Injectable } from '@nestjs/common';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from 'libs/config/config.service';
import { BaseEsAnalyzeService } from '../../base-es-analyze.service';
import { RiskChangeStrategy } from './interfaces/risk-change-strategy.interface';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { ModuleRef } from '@nestjs/core';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';

/**
 * 风险动态ES数据源服务（处理普通维度）
 * 使用策略模式处理不同维度的风险变更数据
 */
@Injectable()
export class RiskChangeEsSourceV2 extends BaseEsAnalyzeService {
  private readonly logger: Logger = QccLogger.getLogger(RiskChangeEsSourceV2.name);
  private strategies: RiskChangeStrategy[] = [];

  /**
   * 关联方风险变更维度列表
   */
  private readonly relatedCompanyDimensions = [
    DimensionTypeEnums.RecentInvestCancellationsRiskChange,
    DimensionTypeEnums.ActualControllerRiskChange,
    DimensionTypeEnums.ListedEntityRiskChange,
  ];

  constructor(readonly configService: ConfigService, private readonly moduleRef: ModuleRef) {
    super(
      'RiskChangeEsSourceV2',
      new Client({
        nodes: configService.esConfig.riskChangeList.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.riskChangeList.indexName,
    );
    // 初始化策略类列表
    this.initStrategies();
  }

  /**
   * 初始化策略类列表
   */
  private initStrategies(): void {
    try {
      // 获取所有策略类实例
      // 在策略类实现后需要在这里添加
      this.strategies = [];
    } catch (error) {
      this.logger.error(`初始化策略类列表失败: ${error instanceof Error ? error.message : String(error)}`, error);
    }
  }

  /**
   * 分析当前数据源所有维度命中情况
   * @param companyId 企业ID
   * @param dimensionHitStrategyPOs 维度策略列表
   */
  async analyze(companyId: string, dimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    // 过滤出普通维度（非关联方维度）
    const normalDimensions = dimensionHitStrategyPOs.filter((d) => !this.isRelatedDimension(d));

    if (!normalDimensions.length) {
      return [];
    }

    // 调用父类方法进行基础分析
    const dimHitRes = await super.analyze(companyId, normalDimensions);

    // 待完善：使用策略模式处理命中的维度

    return dimHitRes;
  }

  /**
   * 获取风险动态维度详情
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    // 如果是关联方维度，不在此处理
    if (this.isRelatedDimension(dimension)) {
      return {
        Paging: {
          PageSize: params.pageSize || 10,
          PageIndex: params.pageIndex || 1,
          TotalRecords: 0,
        },
        Result: [],
      };
    }

    // 查找对应的策略
    const strategy = this.findStrategyForDimension(dimension);
    if (strategy) {
      // 待完善：使用策略处理详情
      // 调用父类方法获取基础数据，然后交由策略处理
      const response = await super.getDimensionDetail(dimension, params, analyzeParams);
      return strategy.processDimensionDetail(response, dimension, params, analyzeParams);
    }

    // 没有找到对应策略，使用默认处理
    return super.getDimensionDetail(dimension, params, analyzeParams);
  }

  /**
   * 查找维度对应的策略
   * @param dimension 维度策略
   */
  private findStrategyForDimension(dimension: DimensionHitStrategyPO): RiskChangeStrategy | null {
    return this.strategies.find((strategy) => strategy.supportsDimension(dimension)) || null;
  }

  /**
   * 判断是否是关联方维度
   * @param dimension 维度策略
   */
  private isRelatedDimension(dimension: DimensionHitStrategyPO): boolean {
    return this.relatedCompanyDimensions.includes(dimension.key);
  }
}
```

### 4. 创建关联方维度的风险变更服务框架（如果不存在）

如果不存在`src/apps/data/source/risk-change-v2/risk-change-related-es.source.ts`文件，创建它：

```typescript
import { Injectable } from '@nestjs/common';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from 'libs/config/config.service';
import { BaseEsAnalyzeService } from '../../base-es-analyze.service';
import { RiskChangeStrategy } from './interfaces/risk-change-strategy.interface';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { ModuleRef } from '@nestjs/core';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { CompanySearchService } from 'apps/company/company-search.service';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { PersonHelper } from '../../helper/person.helper';

/**
 * 关联方风险变更服务
 * 处理关联方维度的风险变更数据
 */
@Injectable()
export class RiskChangeRelatedEsSource extends BaseEsAnalyzeService {
  private readonly logger: Logger = QccLogger.getLogger(RiskChangeRelatedEsSource.name);
  private strategies: RiskChangeStrategy[] = [];

  /**
   * 关联方风险变更维度列表
   */
  private readonly relatedCompanyDimensions = [
    DimensionTypeEnums.RecentInvestCancellationsRiskChange,
    DimensionTypeEnums.ActualControllerRiskChange,
    DimensionTypeEnums.ListedEntityRiskChange,
  ];

  constructor(
    readonly configService: ConfigService,
    private readonly companySearchService: CompanySearchService,
    private readonly companyDetailService: CompanyDetailService,
    private readonly personHelper: PersonHelper,
    private readonly moduleRef: ModuleRef,
  ) {
    super(
      'RiskChangeRelatedEsSource',
      new Client({
        nodes: configService.esConfig.riskChangeList.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.riskChangeList.indexName,
    );
    // 初始化策略类列表
    this.initStrategies();
  }

  /**
   * 初始化策略类列表
   */
  private initStrategies(): void {
    try {
      // 获取所有策略类实例
      // 在策略类实现后需要在这里添加
      this.strategies = [];
    } catch (error) {
      this.logger.error(`初始化策略类列表失败: ${error instanceof Error ? error.message : String(error)}`, error);
    }
  }

  /**
   * 判断是否是关联方维度
   * @param dimension 维度策略
   */
  public isRelatedDimension(dimension: DimensionHitStrategyPO): boolean {
    return this.relatedCompanyDimensions.includes(dimension?.key);
  }

  /**
   * 分析关联方企业的风险变更
   * @param companyId 企业ID
   * @param dimensionHitStrategyPOs 维度策略列表
   * @param params 分析参数
   */
  async analyze(companyId: string, dimensionHitStrategyPOs: DimensionHitStrategyPO[], params?: DimensionAnalyzeParamsPO): Promise<DimensionHitResultPO[]> {
    // 过滤出关联方维度
    const relatedDimensions = dimensionHitStrategyPOs.filter((po) => this.isRelatedDimension(po));

    if (!relatedDimensions.length) {
      return [];
    }

    // 待完善：使用策略模式处理关联方维度

    // 调用父类方法分析关联方风险变更
    return super.analyze(companyId, relatedDimensions, params);
  }

  /**
   * 获取关联方风险动态维度详情
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    // 如果不是关联方维度，不在此处理
    if (!this.isRelatedDimension(dimension)) {
      return {
        Paging: {
          PageSize: params.pageSize || 10,
          PageIndex: params.pageIndex || 1,
          TotalRecords: 0,
        },
        Result: [],
      };
    }

    // 查找对应的策略
    const strategy = this.findStrategyForDimension(dimension);
    if (strategy) {
      // 待完善：使用策略处理详情
      const query = await strategy.generateDimensionQuery(params.keyNo, dimension, params, analyzeParams);
      if (!query) {
        return {
          Paging: {
            PageSize: params.pageSize || 10,
            PageIndex: params.pageIndex || 1,
            TotalRecords: 0,
          },
          Result: [],
        };
      }

      // 调用父类方法获取基础数据，然后交由策略处理
      const response = await super.getDimensionDetail(dimension, params, analyzeParams);
      return strategy.processDimensionDetail(response, dimension, params, analyzeParams);
    }

    // 没有找到对应策略，使用默认处理
    return super.getDimensionDetail(dimension, params, analyzeParams);
  }

  /**
   * 查找维度对应的策略
   * @param dimension 维度策略
   */
  private findStrategyForDimension(dimension: DimensionHitStrategyPO): RiskChangeStrategy | null {
    return this.strategies.find((strategy) => strategy.supportsDimension(dimension)) || null;
  }
}
```

## 预期结果

完成本阶段后，我们将建立起完整的策略模式基础架构，包括：

1. 定义完整的策略接口
2. 实现基础的策略抽象类
3. 搭建处理普通维度的主类框架
4. 搭建处理关联方维度的主类框架

这些基础架构将为后续各个具体策略类的实现提供基础，使得后续的重构工作能够循序渐进地进行。
