# 风险动态模块优化计划 - 第七阶段：模块集成和单元测试

## 背景说明

前六个阶段已完成了风险动态模块的所有核心策略类的实现，包括基础架构搭建、法定代表人变更、资本变更、关联方维度、负面新闻、经营异常、司法案例、行政处罚、股权变更和财务指标策略的实现。在第七阶段，我们将专注于模块集成和单元测试，确保整个系统能够正常运行并保持高质量。

## 主要目标

1. 创建模块配置文件
2. 实现服务提供者
3. 编写单元测试
4. 完成最终集成

## 优化要求

1. 不修改原有业务逻辑，本次仅调整代码结构
2. 新增代码放在 src/apps/data/source/risk-change-v2/ 文件夹
3. 不修改原有文件
4. 文件命名需保持命名含义，体现 RiskChange 处理相关的含义
5. 代码风格需统一，与现有代码保持一致
6. 使用 TypeScript 类型，避免使用 any 类型

## 实现步骤

### 1. 创建模块配置文件

在`src/apps/data/source/risk-change-v2/risk-change-v2.module.ts`文件中实现模块配置：

```typescript
import { Module } from '@nestjs/common';
import { RiskChangeEsSourceV2 } from './risk-change-es.source.v2';
import { RiskChangeRelatedEsSource } from './risk-change-related-es.source';
import { LegalChangeStrategy } from './strategies/legal-change.strategy';
import { CapitalChangeStrategy } from './strategies/capital-change.strategy';
import { NegativeNewsStrategy } from './strategies/negative-news.strategy';
import { BusinessAbnormalStrategy } from './strategies/business-abnormal.strategy';
import { JudicialCaseStrategy } from './strategies/judicial-case.strategy';
import { AdministrativePenaltyStrategy } from './strategies/administrative-penalty.strategy';
import { EquityChangeStrategy } from './strategies/equity-change.strategy';
import { FinancialIndicatorStrategy } from './strategies/financial-indicator.strategy';
import { RiskChangeV2Provider } from './risk-change-v2.provider';

/**
 * 风险动态V2模块
 * 提供风险动态相关的服务
 */
@Module({
  imports: [],
  providers: [
    // 策略类
    LegalChangeStrategy,
    CapitalChangeStrategy,
    NegativeNewsStrategy,
    BusinessAbnormalStrategy,
    JudicialCaseStrategy,
    AdministrativePenaltyStrategy,
    EquityChangeStrategy,
    FinancialIndicatorStrategy,

    // 主服务类
    RiskChangeEsSourceV2,
    RiskChangeRelatedEsSource,
    RiskChangeV2Provider,
  ],
  exports: [RiskChangeEsSourceV2, RiskChangeRelatedEsSource, RiskChangeV2Provider],
})
export class RiskChangeV2Module {}
```

### 2. 实现服务提供者

在`src/apps/data/source/risk-change-v2/risk-change-v2.provider.ts`文件中实现服务提供者，作为旧版和新版之间的桥梁：

```typescript
import { Injectable } from '@nestjs/common';
import { RiskChangeEsSourceV2 } from './risk-change-es.source.v2';
import { RiskChangeRelatedEsSource } from './risk-change-related-es.source';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';

/**
 * 风险动态V2服务提供者
 * 作为新旧版本风险动态服务的桥梁
 */
@Injectable()
export class RiskChangeV2Provider {
  private readonly logger: Logger = QccLogger.getLogger(RiskChangeV2Provider.name);

  /**
   * 构造函数
   * @param riskChangeEsSourceV2 风险动态ES数据源V2
   * @param riskChangeRelatedEsSource 关联方风险动态ES数据源
   */
  constructor(private readonly riskChangeEsSourceV2: RiskChangeEsSourceV2, private readonly riskChangeRelatedEsSource: RiskChangeRelatedEsSource) {}

  /**
   * 分析企业风险动态维度命中情况
   * @param companyId 企业ID
   * @param dimensions 维度列表
   * @param params 分析参数
   */
  async analyze(companyId: string, dimensions: DimensionHitStrategyPO[], params?: DimensionAnalyzeParamsPO): Promise<DimensionHitResultPO[]> {
    try {
      return await this.riskChangeEsSourceV2.analyze(companyId, dimensions, params);
    } catch (error) {
      this.logger.error(`分析企业风险动态维度命中情况失败: ${error.message}`, error);
      return [];
    }
  }

  /**
   * 获取风险动态维度详情
   * @param companyId 企业ID
   * @param dimension 维度
   * @param params 查询参数
   */
  async getDimensionDetail(companyId: string, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<HitDetailsBaseResponse> {
    try {
      return await this.riskChangeEsSourceV2.getDimensionDetail(companyId, dimension, params);
    } catch (error) {
      this.logger.error(`获取风险动态维度详情失败: ${error.message}`, error);
      return new HitDetailsBaseResponse();
    }
  }
}
```

### 3. 编写单元测试

#### 3.1 基础策略测试

在`src/apps/data/source/risk-change-v2/test/strategies/base-risk-change.strategy.unittest.spec.ts`文件中实现基础策略测试：

```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { BaseRiskChangeStrategy } from '../../strategies/base-risk-change.strategy';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';

// 创建测试用的具体策略类
class TestStrategy extends BaseRiskChangeStrategy {
  constructor() {
    super('TestStrategy');
  }

  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.LegalPersonChange, DimensionTypeEnums.FrequentLegalPersonChange];
  }

  getDimensionCategoryMap() {
    return {
      [DimensionTypeEnums.LegalPersonChange]: [1],
      [DimensionTypeEnums.FrequentLegalPersonChange]: [1],
    };
  }

  async generateDimensionQuery(companyId: string, dimension: DimensionHitStrategyPO) {
    return this.createBaseQuery(companyId);
  }

  async processDimensionDetail(response: any, dimension: DimensionHitStrategyPO, params: any) {
    return response;
  }
}

describe('BaseRiskChangeStrategy', () => {
  let strategy: TestStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TestStrategy],
    }).compile();

    strategy = module.get<TestStrategy>(TestStrategy);
  });

  it('应该正确支持特定维度', () => {
    // 创建测试维度
    const dimension = new DimensionHitStrategyPO();
    dimension.key = DimensionTypeEnums.LegalPersonChange;

    // 测试维度支持
    expect(strategy.supportsDimension(dimension)).toBe(true);

    // 测试不支持的维度
    dimension.key = DimensionTypeEnums.FinancialHealth;
    expect(strategy.supportsDimension(dimension)).toBe(false);
  });

  it('应该正确创建基础查询对象', async () => {
    const companyId = 'testCompanyId';
    const dimension = new DimensionHitStrategyPO();
    dimension.key = DimensionTypeEnums.LegalPersonChange;

    const query = await strategy.generateDimensionQuery(companyId, dimension);
    expect(query).toHaveProperty('bool');
    expect(query).toHaveProperty('bool.must');
    expect(query['bool']['must'][0]).toEqual({ term: { KeyNo: companyId } });
  });
});
```

#### 3.2 具体策略测试

在`src/apps/data/source/risk-change-v2/test/strategies/legal-change.strategy.unittest.spec.ts`文件中实现法定代表人变更策略测试：

```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { LegalChangeStrategy } from '../../strategies/legal-change.strategy';
import { RiskChangeHelper } from '../../../../helper/risk.change.helper';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldTypeParamsPO } from 'libs/model/dimension/params/DimensionFieldTypeParamsPO';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';

describe('LegalChangeStrategy', () => {
  let strategy: LegalChangeStrategy;
  let riskChangeHelper: RiskChangeHelper;

  // 模拟 RiskChangeHelper
  const mockRiskChangeHelper = {
    hitLayTypesField: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LegalChangeStrategy,
        {
          provide: RiskChangeHelper,
          useValue: mockRiskChangeHelper,
        },
      ],
    }).compile();

    strategy = module.get<LegalChangeStrategy>(LegalChangeStrategy);
    riskChangeHelper = module.get<RiskChangeHelper>(RiskChangeHelper);
  });

  it('应该正确支持法定代表人变更维度', () => {
    // 确认支持的维度类型
    const supportedDimensions = strategy.getSupportedDimensions();
    expect(supportedDimensions).toContain(DimensionTypeEnums.LegalPersonChange);
    expect(supportedDimensions).toContain(DimensionTypeEnums.FrequentLegalPersonChange);
  });

  it('应该正确生成法定代表人变更查询条件', async () => {
    // 准备测试数据
    const companyId = 'testCompanyId';
    const dimension = new DimensionHitStrategyPO();
    dimension.key = DimensionTypeEnums.LegalPersonChange;
    dimension.dimensionFilter = {
      startTime: 1600000000000,
      endTime: 1610000000000,
    };

    // 添加 isValid 字段
    const isValidField = new DimensionFieldTypeParamsPO();
    isValidField.fieldKey = DimensionFieldKeyEnums.isValid;
    isValidField.fieldValue = ['1'];
    dimension.strategyFieldList = [isValidField];

    // 执行测试
    const query = await strategy.generateDimensionQuery(companyId, dimension);

    // 验证结果
    expect(query).toHaveProperty('bool');
    expect(query).toHaveProperty('bool.must');

    // 验证公司ID条件
    expect(query['bool']['must']).toContainEqual({ term: { KeyNo: companyId } });

    // 验证时间范围条件
    expect(query['bool']['must']).toContainEqual({
      range: {
        CreateDate: {
          gte: Math.ceil(dimension.dimensionFilter.startTime),
          lte: Math.ceil(dimension.dimensionFilter.endTime),
        },
      },
    });

    // 验证有效性条件
    expect(query['bool']['must']).toContainEqual({ term: { IsValid: 1 } });
  });

  it('应该正确处理法定代表人变更详情数据', async () => {
    // 准备测试数据
    const dimension = new DimensionHitStrategyPO();
    dimension.key = DimensionTypeEnums.LegalPersonChange;

    // 添加 layTypes 字段
    const layTypesField = new DimensionFieldTypeParamsPO();
    layTypesField.fieldKey = DimensionFieldKeyEnums.layTypes;
    layTypesField.fieldValue = ['某类型'];
    dimension.strategyFieldList = [layTypesField];

    // 模拟响应数据
    const response = new HitDetailsBaseResponse();
    response.Result = [
      {
        Id: '1',
        Category: 39, // 法定代表人变更类别
        CreateDate: '2021-01-01',
        Extend1: '{}',
        ChangeExtend: '{}',
      },
    ];

    // 设置 hitLayTypesField 的返回值
    mockRiskChangeHelper.hitLayTypesField.mockReturnValue(true);

    // 执行测试
    const params = { pageSize: 10, pageIndex: 1 };
    const result = await strategy.processDimensionDetail(response, dimension, params);

    // 验证结果
    expect(result.Result.length).toBe(1);
    expect(result.Paging.TotalRecords).toBe(1);

    // 验证 hitLayTypesField 被调用
    expect(mockRiskChangeHelper.hitLayTypesField).toHaveBeenCalledWith(layTypesField, expect.any(Object));
  });
});
```

#### 3.3 主服务测试

在`src/apps/data/source/risk-change-v2/test/risk-change-es.source.v2.unittest.spec.ts`文件中实现主服务测试：

```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { RiskChangeEsSourceV2 } from '../risk-change-es.source.v2';
import { ConfigService } from 'libs/config/config.service';
import { ModuleRef } from '@nestjs/core';
import { LegalChangeStrategy } from '../strategies/legal-change.strategy';
import { CapitalChangeStrategy } from '../strategies/capital-change.strategy';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { Client } from '@elastic/elasticsearch';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';

jest.mock('@elastic/elasticsearch');

describe('RiskChangeEsSourceV2', () => {
  let service: RiskChangeEsSourceV2;
  let legalChangeStrategy: LegalChangeStrategy;
  let capitalChangeStrategy: CapitalChangeStrategy;

  // 模拟策略
  const mockLegalChangeStrategy = {
    supportsDimension: jest.fn(),
    generateDimensionQuery: jest.fn(),
    processDimensionDetail: jest.fn(),
    getSupportedDimensions: jest.fn(),
    getDimensionCategoryMap: jest.fn(),
  };

  const mockCapitalChangeStrategy = {
    supportsDimension: jest.fn(),
    generateDimensionQuery: jest.fn(),
    processDimensionDetail: jest.fn(),
    getSupportedDimensions: jest.fn(),
    getDimensionCategoryMap: jest.fn(),
  };

  // 模拟ES客户端
  const mockEsClient = {
    search: jest.fn(),
  };

  // 模拟配置服务
  const mockConfigService = {
    esConfig: {
      riskChangeList: {
        nodes: ['http://localhost:9200'],
        indexName: 'test_index',
      },
    },
  };

  beforeEach(async () => {
    // 重置所有模拟
    jest.clearAllMocks();

    // 模拟ES客户端
    (Client as jest.Mock).mockImplementation(() => mockEsClient);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RiskChangeEsSourceV2,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: ModuleRef,
          useValue: { get: jest.fn() },
        },
        {
          provide: LegalChangeStrategy,
          useValue: mockLegalChangeStrategy,
        },
        {
          provide: CapitalChangeStrategy,
          useValue: mockCapitalChangeStrategy,
        },
      ],
    }).compile();

    service = module.get<RiskChangeEsSourceV2>(RiskChangeEsSourceV2);
    legalChangeStrategy = module.get<LegalChangeStrategy>(LegalChangeStrategy);
    capitalChangeStrategy = module.get<CapitalChangeStrategy>(CapitalChangeStrategy);

    // 初始化服务的策略列表
    service['strategies'] = [legalChangeStrategy, capitalChangeStrategy];
  });

  it('应该正确分析维度命中情况', async () => {
    // 准备测试数据
    const companyId = 'testCompanyId';
    const dimension = new DimensionHitStrategyPO();
    dimension.key = DimensionTypeEnums.LegalPersonChange;

    // 设置策略模拟返回值
    mockLegalChangeStrategy.supportsDimension.mockReturnValue(true);
    mockLegalChangeStrategy.generateDimensionQuery.mockResolvedValue({
      bool: { must: [{ term: { KeyNo: companyId } }] },
    });

    // 模拟ES搜索结果
    mockEsClient.search.mockResolvedValue({
      body: {
        hits: {
          total: { value: 5 },
          hits: [{ _source: { id: '1' } }],
        },
      },
    });

    // 执行测试
    const result = await service.analyze(companyId, [dimension]);

    // 验证结果
    expect(result.length).toBe(1);
    expect(result[0].id).toBe(dimension.id);
    expect(result[0].key).toBe(dimension.key);
    expect(result[0].isHit).toBe(true);

    // 验证策略方法被调用
    expect(mockLegalChangeStrategy.supportsDimension).toHaveBeenCalledWith(dimension);
    expect(mockLegalChangeStrategy.generateDimensionQuery).toHaveBeenCalledWith(companyId, dimension, undefined, undefined);

    // 验证ES搜索被调用
    expect(mockEsClient.search).toHaveBeenCalled();
  });

  it('应该正确获取维度详情', async () => {
    // 准备测试数据
    const companyId = 'testCompanyId';
    const dimension = new DimensionHitStrategyPO();
    dimension.key = DimensionTypeEnums.LegalPersonChange;
    const params = { pageSize: 10, pageIndex: 1 };

    // 设置策略模拟返回值
    mockLegalChangeStrategy.supportsDimension.mockReturnValue(true);
    mockLegalChangeStrategy.generateDimensionQuery.mockResolvedValue({
      bool: { must: [{ term: { KeyNo: companyId } }] },
    });

    // 模拟ES搜索结果
    mockEsClient.search.mockResolvedValue({
      body: {
        hits: {
          total: { value: 5 },
          hits: [
            {
              _source: {
                id: '1',
                Category: 39,
                CreateDate: '2021-01-01',
                IsValid: 1,
              },
            },
          ],
        },
      },
    });

    // 模拟处理详情结果
    const expectedResponse = new HitDetailsBaseResponse();
    expectedResponse.Result = [{ id: '1' }];
    expectedResponse.Paging = { TotalRecords: 1, PageSize: 10, PageIndex: 1 };
    mockLegalChangeStrategy.processDimensionDetail.mockResolvedValue(expectedResponse);

    // 执行测试
    const result = await service.getDimensionDetail(companyId, dimension, params);

    // 验证结果
    expect(result).toBe(expectedResponse);

    // 验证策略方法被调用
    expect(mockLegalChangeStrategy.supportsDimension).toHaveBeenCalledWith(dimension);
    expect(mockLegalChangeStrategy.generateDimensionQuery).toHaveBeenCalledWith(companyId, dimension, undefined, undefined);
    expect(mockLegalChangeStrategy.processDimensionDetail).toHaveBeenCalled();

    // 验证ES搜索被调用
    expect(mockEsClient.search).toHaveBeenCalled();
  });
});
```

### 4. 创建 Jest 配置文件

在`src/apps/data/source/risk-change-v2/jest.config.js`文件中配置 Jest 测试：

```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/test/**/*.unittest.spec.ts'],
  collectCoverage: true,
  collectCoverageFrom: [
    'src/apps/data/source/risk-change-v2/**/*.ts',
    '!src/apps/data/source/risk-change-v2/test/**/*.ts',
    '!src/apps/data/source/risk-change-v2/**/index.ts',
    '!src/apps/data/source/risk-change-v2/readme/**/*.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

## 预期成果

完成第七阶段后，我们将实现风险动态模块的完整集成和单元测试：

1. **模块配置**：通过模块配置文件，实现了所有策略类和服务的注册和导出
2. **服务提供者**：实现了新旧版本的兼容性，确保平滑迁移
3. **单元测试**：为核心组件编写了单元测试，确保代码质量和功能正确性

这一阶段完成后，风险动态模块的重构工作基本完成，新的架构将使代码更加清晰、可维护和可测试。后续可以考虑逐步淘汰旧版本的代码，完全迁移到新的架构上。

## 注意事项

1. 确保所有单元测试能够通过，并达到预期的覆盖率
2. 在实际部署前，先在测试环境进行充分测试
3. 考虑增加集成测试，验证整个系统的运行情况
4. 为新架构编写详细的文档，便于后续开发者了解和维护
