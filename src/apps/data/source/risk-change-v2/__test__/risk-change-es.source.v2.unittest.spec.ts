import { Test, TestingModule } from '@nestjs/testing';
import { RiskChangeEsSourceV2 } from '../risk-change-es.source.v2';
import { ConfigService } from 'libs/config/config.service';
import { ModuleRef } from '@nestjs/core';
import { LegalChangeStrategy } from '../strategies/legal-change.strategy';
import { CapitalChangeStrategy } from '../strategies/capital-change.strategy';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { Client } from '@elastic/elasticsearch';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';

jest.mock('@elastic/elasticsearch');

describe('RiskChangeEsSourceV2', () => {
  let service: RiskChangeEsSourceV2;
  let legalChangeStrategy: LegalChangeStrategy;
  let capitalChangeStrategy: CapitalChangeStrategy;

  // 模拟策略
  const mockLegalChangeStrategy = {
    supportsDimension: jest.fn(),
    generateDimensionQuery: jest.fn(),
    processDimensionDetail: jest.fn(),
    getSupportedDimensions: jest.fn(),
    getDimensionCategoryMap: jest.fn(),
  };

  const mockCapitalChangeStrategy = {
    supportsDimension: jest.fn(),
    generateDimensionQuery: jest.fn(),
    processDimensionDetail: jest.fn(),
    getSupportedDimensions: jest.fn(),
    getDimensionCategoryMap: jest.fn(),
  };

  // 模拟ES客户端
  const mockEsClient = {
    search: jest.fn(),
  };

  // 模拟配置服务
  const mockConfigService = {
    esConfig: {
      riskChangeList: {
        nodes: ['http://localhost:9200'],
        indexName: 'test_index',
      },
    },
  };

  beforeEach(async () => {
    // 重置所有模拟
    jest.clearAllMocks();

    // 模拟ES客户端
    (Client as jest.Mock).mockImplementation(() => mockEsClient);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RiskChangeEsSourceV2,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: ModuleRef,
          useValue: { get: jest.fn() },
        },
        {
          provide: LegalChangeStrategy,
          useValue: mockLegalChangeStrategy,
        },
        {
          provide: CapitalChangeStrategy,
          useValue: mockCapitalChangeStrategy,
        },
      ],
    }).compile();

    service = module.get<RiskChangeEsSourceV2>(RiskChangeEsSourceV2);
    legalChangeStrategy = module.get<LegalChangeStrategy>(LegalChangeStrategy);
    capitalChangeStrategy = module.get<CapitalChangeStrategy>(CapitalChangeStrategy);

    // 初始化服务的策略列表
    service['strategies'] = [legalChangeStrategy, capitalChangeStrategy];
  });

  it('应该正确分析维度命中情况', async () => {
    // 准备测试数据
    const companyId = 'testCompanyId';

    // 创建维度定义实体
    const dimensionDef = new DimensionDefinitionEntity();
    dimensionDef.key = DimensionTypeEnums.MainInfoUpdateLegalPerson;
    dimensionDef.name = '法定代表人变更';

    // 使用维度定义创建维度策略
    const dimension = new DimensionHitStrategyPO(dimensionDef);
    dimension.strategyId = 123; // 添加测试需要的ID

    // 设置策略模拟返回值
    mockLegalChangeStrategy.supportsDimension.mockReturnValue(true);
    mockLegalChangeStrategy.generateDimensionQuery.mockResolvedValue({
      bool: { must: [{ term: { KeyNo: companyId } }] },
    });

    // 模拟ES搜索结果
    mockEsClient.search.mockResolvedValue({
      body: {
        hits: {
          total: { value: 5 },
          hits: [{ _source: { id: '1' } }],
        },
      },
    });

    // 执行测试
    const result = await service.analyze(companyId, [dimension]);

    // 验证结果
    expect(result.length).toBe(1);
    expect(result[0].strategyId).toBe(dimension.strategyId);
    expect(result[0].dimensionKey).toBe(dimension.key);
    expect(result[0].totalHits).toBeGreaterThan(0);

    // 验证策略方法被调用
    expect(mockLegalChangeStrategy.supportsDimension).toHaveBeenCalledWith(dimension);
    expect(mockLegalChangeStrategy.generateDimensionQuery).toHaveBeenCalledWith(companyId, dimension, undefined, undefined);

    // 验证ES搜索被调用
    expect(mockEsClient.search).toHaveBeenCalled();
  });

  it('应该正确获取维度详情', async () => {
    // 准备测试数据
    const companyId = 'testCompanyId';

    // 创建维度定义实体
    const dimensionDef = new DimensionDefinitionEntity();
    dimensionDef.key = DimensionTypeEnums.MainInfoUpdateLegalPerson;
    dimensionDef.name = '法定代表人变更';

    // 使用维度定义创建维度策略
    const dimension = new DimensionHitStrategyPO(dimensionDef);
    const params = { pageSize: 10, pageIndex: 1, keyNo: companyId };

    // 设置策略模拟返回值
    mockLegalChangeStrategy.supportsDimension.mockReturnValue(true);
    mockLegalChangeStrategy.generateDimensionQuery.mockResolvedValue({
      bool: { must: [{ term: { KeyNo: companyId } }] },
    });

    // 模拟ES搜索结果
    mockEsClient.search.mockResolvedValue({
      body: {
        hits: {
          total: { value: 5 },
          hits: [
            {
              _source: {
                id: '1',
                Category: 39,
                CreateDate: '2021-01-01',
                IsValid: 1,
              },
            },
          ],
        },
      },
    });

    // 模拟处理详情结果
    const expectedResponse = new HitDetailsBaseResponse();
    expectedResponse.Result = [{ id: '1' }];
    expectedResponse.Paging = { TotalRecords: 1, PageSize: 10, PageIndex: 1 };
    mockLegalChangeStrategy.processDimensionDetail.mockResolvedValue(expectedResponse);

    // 执行测试
    const result = await service.getDimensionDetail(dimension, params, null);

    // 验证结果
    expect(result).toBe(expectedResponse);

    // 验证策略方法被调用
    expect(mockLegalChangeStrategy.supportsDimension).toHaveBeenCalledWith(dimension);
    expect(mockLegalChangeStrategy.generateDimensionQuery).toHaveBeenCalledWith(companyId, dimension, undefined, undefined);
    expect(mockLegalChangeStrategy.processDimensionDetail).toHaveBeenCalled();

    // 验证ES搜索被调用
    expect(mockEsClient.search).toHaveBeenCalled();
  });
});
