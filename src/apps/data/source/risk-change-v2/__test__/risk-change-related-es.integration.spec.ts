import { Client } from '@elastic/elasticsearch';
import { ModuleRef } from '@nestjs/core';
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from 'libs/config/config.service';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RelatedTypeEnums } from 'libs/enums/dimension/RelatedTypeEnums';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { CompanyDetailService } from '../../../../company/company-detail.service';
import { CompanySearchService } from '../../../../company/company-search.service';
import { PersonHelper } from '../../../helper/person.helper';
import { RiskChangeRelatedEsSource } from '../risk-change-related-es.source';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';

describe('RiskChangeRelatedEsSource 集成测试', () => {
  let service: RiskChangeRelatedEsSource;
  let mockModuleRef: jest.Mocked<ModuleRef>;
  let mockEsClient: jest.Mocked<Client>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockCompanyDetailService: jest.Mocked<CompanyDetailService>;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockConfigService: jest.Mocked<ConfigService>;

  // 生成测试用户
  const [testOrgId, testUserId] = generateUniqueTestIds('risk-change-related-es.integration.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  // 模拟ES响应数据
  const mockEsResponse = {
    body: {
      hits: {
        total: { value: 2 },
        hits: [
          {
            _source: {
              Id: '1',
              KeyNo: 'related1',
              Category: 17, // 对外投资变更
              ChangeExtend: JSON.stringify({ F: { KeyNo: 'related1', Name: '关联企业1' } }),
              CreateDate: '2021-06-01',
            },
          },
          {
            _source: {
              Id: '2',
              KeyNo: 'related2',
              Category: 25, // 实际控制人变更
              ChangeExtend: JSON.stringify({ F: { KeyNo: 'related2', Name: '关联企业2' } }),
              CreateDate: '2021-05-01',
            },
          },
        ],
      },
    },
  };

  beforeEach(async () => {
    // 创建模拟实现
    mockModuleRef = {
      get: jest.fn(),
    } as any;

    mockEsClient = {
      search: jest.fn().mockResolvedValue(mockEsResponse),
    } as any;

    mockCompanySearchService = {
      companyDetailsQcc: jest.fn().mockImplementation((keyNo) => {
        if (keyNo === 'related1') {
          return {
            KeyNo: 'related1',
            Name: '关联企业1',
            CreditCode: 'credit1',
            Oper: '张三',
            ShortStatus: '存续',
          };
        }
        if (keyNo === 'related2') {
          return {
            KeyNo: 'related2',
            Name: '关联企业2',
            CreditCode: 'credit2',
            Oper: '李四',
            ShortStatus: '存续',
          };
        }
        return null;
      }),
    } as any;

    mockCompanyDetailService = {
      getInvestCompany: jest.fn().mockResolvedValue({
        Paging: { TotalRecords: 2 },
        Result: [
          { KeyNo: 'related1', Name: '关联企业1' },
          { KeyNo: 'related2', Name: '关联企业2' },
        ],
      }),
    } as any;

    mockPersonHelper = {
      getFinalActualController: jest.fn().mockResolvedValue([
        { keyNo: 'person1', name: '实控人1' },
        { keyNo: 'person2', name: '实控人2' },
      ]),
      getPartnerList: jest.fn().mockResolvedValue([
        { keyNo: 'partner1', name: '股东1', tags: ['大股东'] },
        { keyNo: 'partner2', name: '股东2', tags: [] },
      ]),
    } as any;

    mockConfigService = {
      esConfig: {
        riskChange: {
          nodes: ['http://localhost:9200'],
          indexName: 'test_index',
        },
      },
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RiskChangeRelatedEsSource,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: ModuleRef,
          useValue: mockModuleRef,
        },
        {
          provide: CompanySearchService,
          useValue: mockCompanySearchService,
        },
        {
          provide: CompanyDetailService,
          useValue: mockCompanyDetailService,
        },
        {
          provide: PersonHelper,
          useValue: mockPersonHelper,
        },
      ],
    })
      .overrideProvider(RiskChangeRelatedEsSource)
      .useFactory({
        factory: () => {
          const svc = new RiskChangeRelatedEsSource(mockConfigService, mockCompanySearchService, mockCompanyDetailService, mockPersonHelper, mockModuleRef);
          // 替换私有ES客户端
          Object.defineProperty(svc, 'esClient', { value: mockEsClient });
          return svc;
        },
      })
      .compile();

    service = module.get<RiskChangeRelatedEsSource>(RiskChangeRelatedEsSource);
  });

  afterEach(async () => {
    // 清理测试数据
    jest.clearAllMocks();
  });

  it('应该成功创建实例', () => {
    expect(service).toBeDefined();
  });

  describe('isRelatedDimension', () => {
    it('应该正确识别关联方维度', () => {
      const relatedDimension = {
        key: DimensionTypeEnums.RecentInvestCancellationsRiskChange,
      } as DimensionHitStrategyPO;

      const normalDimension = {
        key: DimensionTypeEnums.MainInfoUpdateCapital,
      } as DimensionHitStrategyPO;

      expect(service.isRelatedDimension(relatedDimension)).toBe(true);
      expect(service.isRelatedDimension(normalDimension)).toBe(false);
    });
  });

  describe('analyze', () => {
    it('应该只分析关联方维度', async () => {
      // 准备测试数据
      const companyId = 'company1';
      const dimensionHitStrategyPOs: DimensionHitStrategyPO[] = [
        {
          strategyId: 1,
          key: DimensionTypeEnums.RecentInvestCancellationsRiskChange, // 关联方维度
          dimensionFilter: { startTime: 1622505600, endTime: 1625097599 },
          getStrategyFieldByKey: jest.fn().mockImplementation((key) => {
            if (key === DimensionFieldKeyEnums.relatedRoleType) {
              return { fieldValue: [RelatedTypeEnums.InvestCompany] };
            }
            return null;
          }),
        } as any,
        {
          strategyId: 2,
          key: DimensionTypeEnums.MainInfoUpdateCapital, // 非关联方维度
          dimensionFilter: { startTime: 1622505600, endTime: 1625097599 },
          getStrategyFieldByKey: jest.fn(),
        } as any,
      ];

      // 执行测试方法
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // 验证结果
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      // 验证ES查询
      expect(mockEsClient.search).toHaveBeenCalled();
      // 验证查询参数中使用了关联企业ID
      const searchCallArgs = mockEsClient.search.mock.calls[0][0];
      //@ts-ignore
      expect(searchCallArgs.body.query).toBeDefined();
    });
  });

  describe('getDimensionDetail', () => {
    it('应该处理关联方维度详情', async () => {
      // 准备测试数据
      const dimension: DimensionHitStrategyPO = {
        strategyId: 1,
        key: DimensionTypeEnums.RecentInvestCancellationsRiskChange, // 关联方维度
        dimensionFilter: { startTime: 1622505600, endTime: 1625097599 },
        getStrategyFieldByKey: jest.fn().mockImplementation((key) => {
          if (key === DimensionFieldKeyEnums.relatedRoleType) {
            return { fieldValue: [RelatedTypeEnums.InvestCompany] };
          }
          if (key === DimensionFieldKeyEnums.riskCategories) {
            return { fieldValue: [17, 25] }; // 对外投资变更, 实际控制人变更
          }
          return null;
        }),
      } as any;

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'company1',
        pageIndex: 1,
        pageSize: 10,
      };

      // 执行测试方法
      const result = await service.getDimensionDetail(dimension, params);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Result).toBeDefined();
      // 验证查询和处理
      expect(mockEsClient.search).toHaveBeenCalled();
      expect(mockCompanySearchService.companyDetailsQcc).toHaveBeenCalled();
    });

    it('应该返回空结果对于非关联方维度', async () => {
      // 准备测试数据
      const dimension: DimensionHitStrategyPO = {
        strategyId: 1,
        key: DimensionTypeEnums.MainInfoUpdateCapital, // 非关联方维度
        dimensionFilter: { startTime: 1622505600, endTime: 1625097599 },
        getStrategyFieldByKey: jest.fn(),
      } as any;

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'company1',
        pageIndex: 1,
        pageSize: 10,
      };

      // 执行测试方法
      const result = await service.getDimensionDetail(dimension, params);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Result).toEqual([]);
      // 验证不会进行查询
      expect(mockEsClient.search).not.toHaveBeenCalled();
    });
  });

  describe('detailAnalyzeForRelated', () => {
    it('应该为结果添加关联企业信息', async () => {
      // 准备测试数据
      const dimension: DimensionHitStrategyPO = {
        key: DimensionTypeEnums.RecentInvestCancellationsRiskChange,
        getStrategyFieldByKey: jest.fn(),
      } as any;

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'company1',
      };

      const esHitDetails = [
        {
          Id: '1',
          KeyNo: 'related1',
          Category: 17, // 对外投资变更
          ChangeExtend: { F: { KeyNo: 'related1', Name: '关联企业1' } },
        },
      ];

      // 执行测试方法
      const result = await service.detailAnalyzeForRelated(esHitDetails, dimension, params);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.length).toBe(1);
      expect(result[0].RelatedCompanyInfo).toBeDefined();
      expect(result[0].RelatedCompanyInfo.KeyNo).toBe('related1');
      expect(result[0].RelatedCompanyInfo.Name).toBe('关联企业1');
      expect(result[0].RelatedCompanyInfo.RelationType).toBe('对外投资');
    });

    it('应该处理空结果', async () => {
      // 准备测试数据
      const dimension = {} as DimensionHitStrategyPO;
      const params = {} as HitDetailsBaseQueryParams;
      const emptyDetails = [];

      // 执行测试方法
      const result = await service.detailAnalyzeForRelated(emptyDetails, dimension, params);

      // 验证结果
      expect(result).toEqual([]);
    });
  });
});
