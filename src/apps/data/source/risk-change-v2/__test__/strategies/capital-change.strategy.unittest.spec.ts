import { Test, TestingModule } from '@nestjs/testing';
import { DimensionTypeEnums } from '../../../../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../../../../libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from '../../../../../../libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from '../../../../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../../../../libs/model/diligence/details/response';
import { DimensionHitStrategyPO } from '../../../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { RiskChangeHelper } from '../../../../helper/risk.change.helper';
import { CapitalChangeStrategy } from '../../strategies/capital-change.strategy';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';

describe('CapitalChangeStrategy', () => {
  let strategy: CapitalChangeStrategy;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;

  beforeEach(async () => {
    // 创建RiskChangeHelper的模拟实现
    mockRiskChangeHelper = {
      hitCategory123CurrencyChangeField: jest.fn(),
      capitalReduceSelectCompareResult: jest.fn(),
      hitPeriodRegisCapitalField123: jest.fn(),
      hitMainInfoUpdateCapitalChange: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CapitalChangeStrategy,
        {
          provide: RiskChangeHelper,
          useValue: mockRiskChangeHelper,
        },
      ],
    }).compile();

    strategy = module.get<CapitalChangeStrategy>(CapitalChangeStrategy);
  });

  it('应该正确创建实例', () => {
    expect(strategy).toBeDefined();
  });

  it('应该返回正确的支持维度列表', () => {
    const dimensions = strategy.getSupportedDimensions();
    expect(dimensions).toContain(DimensionTypeEnums.MainInfoUpdateCapital);
    expect(dimensions).toContain(DimensionTypeEnums.MainInfoUpdateCapitalChange);
  });

  it('应该返回正确的维度类型与风险变更类别映射', () => {
    const mapping = strategy.getDimensionCategoryMap();
    expect(mapping[DimensionTypeEnums.MainInfoUpdateCapital]).toContain(RiskChangeCategoryEnum.category37);
    expect(mapping[DimensionTypeEnums.MainInfoUpdateCapital]).toContain(RiskChangeCategoryEnum.category123);
    expect(mapping[DimensionTypeEnums.MainInfoUpdateCapitalChange]).toContain(RiskChangeCategoryEnum.category37);
  });

  describe('generateDimensionQuery', () => {
    it('应该生成包含正确过滤条件的查询对象', async () => {
      // 准备测试数据
      const companyId = 'test123';
      const dimensionDef = new DimensionDefinitionEntity();
      dimensionDef.key = DimensionTypeEnums.MainInfoUpdateHolder;
      const dimension = new DimensionHitStrategyPO(dimensionDef);
      dimension.key = DimensionTypeEnums.MainInfoUpdateCapital;
      dimension.dimensionFilter = {
        startTime: 1609459200, // 2021-01-01
        endTime: 1640995199, // 2021-12-31
      };

      // 模拟dimension.getStrategyFieldByKey方法
      dimension.getStrategyFieldByKey = jest.fn().mockReturnValue(null);

      // 执行测试方法
      const query = await strategy.generateDimensionQuery(companyId, dimension);

      // 验证结果
      expect(query).toBeDefined();
      const anyQuery = query as any;
      expect(anyQuery.bool.must).toBeDefined();

      // 检查企业ID过滤条件
      const companyIdFilter = anyQuery.bool.must.find((item: any) => item.term && item.term.KeyNo === companyId);
      expect(companyIdFilter).toBeDefined();

      // 检查风险类别过滤条件
      const categoryFilter = anyQuery.bool.must.find((item: any) => item.terms && item.terms.Category);
      expect(categoryFilter).toBeDefined();
      expect(categoryFilter.terms.Category).toContain(RiskChangeCategoryEnum.category37);
      expect(categoryFilter.terms.Category).toContain(RiskChangeCategoryEnum.category123);

      // 检查时间范围过滤条件
      const dateRangeFilter = anyQuery.bool.must.find((item: any) => item.range && item.range.CreateDate);
      expect(dateRangeFilter).toBeDefined();
      expect(dateRangeFilter.range.CreateDate.gte).toBe(1609459200);
      expect(dateRangeFilter.range.CreateDate.lte).toBe(1640995199);

      // 检查有效性过滤条件
      const isValidFilter = anyQuery.bool.must.find((item: any) => item.term && item.term.IsValid === 1);
      expect(isValidFilter).toBeDefined();
    });

    it('应该在发生错误时返回null', async () => {
      // 准备测试数据
      const companyId = 'test123';
      const dimension = {
        key: DimensionTypeEnums.MainInfoUpdateCapital,
        getStrategyFieldByKey: () => {
          throw new Error('模拟错误');
        },
      } as unknown as DimensionHitStrategyPO;

      // 执行测试方法
      const query = await strategy.generateDimensionQuery(companyId, dimension);

      // 验证结果
      expect(query).toBeNull();
    });
  });

  describe('processDimensionDetail', () => {
    it('应该正确处理减资公告(CapitalReduction)维度详情数据', async () => {
      // 准备测试数据
      const dimensionDef = new DimensionDefinitionEntity();
      dimensionDef.key = DimensionTypeEnums.CapitalReduction;
      const dimension = new DimensionHitStrategyPO(dimensionDef);

      // 模拟dimension.getStrategyFieldByKey方法返回不同字段
      const mockFields = {
        [DimensionFieldKeyEnums.currencyChange]: { id: 1 },
        [DimensionFieldKeyEnums.capitalReductionRate]: { id: 2 },
        [DimensionFieldKeyEnums.periodRegisCapital]: {
          id: 3,
          fieldValue: [{ valuePeriodBaseLine: 12 }],
        },
      };

      dimension.getStrategyFieldByKey = jest.fn().mockImplementation((key) => {
        return mockFields[key];
      });

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test123',
        pageIndex: 1,
        pageSize: 10,
      };

      const response = new HitDetailsBaseResponse();
      response.Result = [
        {
          Id: '1',
          Category: RiskChangeCategoryEnum.category123,
          ChangeExtend: JSON.stringify({ A: '1000万', B: '500万', T: 1 }),
          CreateDate: '2021-06-01',
        },
        {
          Id: '2',
          Category: RiskChangeCategoryEnum.category123,
          ChangeExtend: JSON.stringify({ A: '500万', B: '300万', T: 1 }),
          CreateDate: '2021-05-01',
        },
      ];

      // 模拟依赖方法的返回值
      mockRiskChangeHelper.hitCategory123CurrencyChangeField.mockReturnValue(true);
      mockRiskChangeHelper.capitalReduceSelectCompareResult.mockReturnValue(true);
      mockRiskChangeHelper.hitPeriodRegisCapitalField123.mockReturnValue(false); // 只有第一个条件命中

      // 模拟getCommonCivilRiskChange方法的返回值
      jest.spyOn(strategy as any, 'getCommonCivilRiskChange').mockResolvedValue({
        Result: [{ id: 'period1' }],
      });

      // 执行测试方法
      const result = await strategy.processDimensionDetail(response, dimension, params);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Paging.TotalRecords).toBe(0); // 因为hitPeriodRegisCapitalField123返回false，所以没有命中
      expect(result.Result.length).toBe(0);

      // 验证模拟方法被正确调用
      expect(mockRiskChangeHelper.hitCategory123CurrencyChangeField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.capitalReduceSelectCompareResult).toHaveBeenCalled();
      expect(mockRiskChangeHelper.hitPeriodRegisCapitalField123).toHaveBeenCalled();
    });

    it('应该正确处理注册资本变更(MainInfoUpdateCapitalChange)维度详情数据', async () => {
      // 准备测试数据
      const dimensionDef = new DimensionDefinitionEntity();
      dimensionDef.key = DimensionTypeEnums.MainInfoUpdateCapitalChange;
      const dimension = new DimensionHitStrategyPO(dimensionDef);

      // 添加periodRegisCapital策略字段
      const periodField = {
        id: 1,
        fieldValue: [{ period: 12 }],
      };

      // 模拟dimension.getStrategyFieldByKey方法
      dimension.getStrategyFieldByKey = jest.fn().mockImplementation((key) => {
        if (key === DimensionFieldKeyEnums.periodRegisCapital) {
          return periodField;
        }
        return null;
      });

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test123',
        pageIndex: 1,
        pageSize: 10,
      };

      const response = new HitDetailsBaseResponse();
      response.Result = [
        {
          Id: '1',
          Category: RiskChangeCategoryEnum.category37,
          ChangeExtend: JSON.stringify({ A: '1000万', B: '2000万', T: 2 }),
          CreateDate: '2021-06-01',
        },
      ];

      // 模拟hitMainInfoUpdateCapitalChange方法的返回值
      mockRiskChangeHelper.hitMainInfoUpdateCapitalChange.mockReturnValue(true);

      // 执行测试方法
      const result = await strategy.processDimensionDetail(response, dimension, params);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Paging.TotalRecords).toBe(1);
      expect(result.Result.length).toBe(1);
      expect(result.Result[0].Id).toBe('1');

      // 验证模拟方法被正确调用
      expect(mockRiskChangeHelper.hitMainInfoUpdateCapitalChange).toHaveBeenCalledWith(periodField, response.Result);
    });

    it('应该在响应为空时返回原始响应', async () => {
      // 准备测试数据
      const dimension = {} as DimensionHitStrategyPO;
      const params = {} as HitDetailsBaseQueryParams;
      const emptyResponse = new HitDetailsBaseResponse();

      // 执行测试方法
      const result = await strategy.processDimensionDetail(emptyResponse, dimension, params);

      // 验证结果
      expect(result).toBe(emptyResponse);
    });
  });

  describe('parseCapital', () => {
    it('应该正确解析资本字符串', () => {
      const testCases = [
        { input: '1000万元', expected: 1000 },
        { input: '2000万人民币', expected: 2000 },
        { input: '500万美元', expected: 500 },
        { input: '不是数字', expected: 0 },
        { input: '', expected: 0 },
        { input: null, expected: 0 },
      ];

      const parseCapital = (strategy as any).parseCapital.bind(strategy);

      testCases.forEach(({ input, expected }) => {
        const result = parseCapital(input);
        expect(result).toBe(expected);
      });
    });
  });
});
