import { Test, TestingModule } from '@nestjs/testing';
import { BaseRiskChangeStrategy } from '../../strategies/base-risk-change.strategy';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';

// 创建测试用的具体策略类
class TestStrategy extends BaseRiskChangeStrategy {
  constructor() {
    super('TestStrategy');
  }

  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.MainInfoUpdateHolder, DimensionTypeEnums.MainInfoUpdateName];
  }

  getDimensionCategoryMap() {
    return {
      [DimensionTypeEnums.MainInfoUpdateHolder]: [1],
      [DimensionTypeEnums.MainInfoUpdateName]: [1],
    };
  }

  async generateDimensionQuery(companyId: string, dimension: DimensionHitStrategyPO) {
    return this.createBaseQuery(companyId);
  }

  async processDimensionDetail(response: any, dimension: DimensionHitStrategyPO, params: any) {
    return response;
  }
}

describe('BaseRiskChangeStrategy', () => {
  let strategy: TestStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TestStrategy],
    }).compile();

    strategy = module.get<TestStrategy>(TestStrategy);
  });

  it('应该正确支持特定维度', () => {
    // 创建测试维度
    const dimensionDef = new DimensionDefinitionEntity();
    dimensionDef.key = DimensionTypeEnums.MainInfoUpdateHolder;
    const dimension = new DimensionHitStrategyPO(dimensionDef);

    // 测试维度支持
    expect(strategy.supportsDimension(dimension)).toBe(true);

    // 测试不支持的维度
    dimensionDef.key = DimensionTypeEnums.FinancialHealth;
    const dimension2 = new DimensionHitStrategyPO(dimensionDef);
    expect(strategy.supportsDimension(dimension2)).toBe(false);
  });

  it('应该正确创建基础查询对象', async () => {
    const companyId = 'testCompanyId';
    const dimensionDef = new DimensionDefinitionEntity();
    dimensionDef.key = DimensionTypeEnums.MainInfoUpdateHolder;
    const dimension = new DimensionHitStrategyPO(dimensionDef);

    const query = await strategy.generateDimensionQuery(companyId, dimension);
    expect(query).toHaveProperty('bool');
    expect(query).toHaveProperty('bool.must');
    expect(query['bool']['must'][0]).toEqual({ term: { KeyNo: companyId } });
  });
});
