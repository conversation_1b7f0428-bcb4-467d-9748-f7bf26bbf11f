import { Test, TestingModule } from '@nestjs/testing';
import { LegalChangeStrategy } from '../../strategies/legal-change.strategy';
import { RiskChangeHelper } from '../../../../helper/risk.change.helper';
import { DimensionHitStrategyPO } from '../../../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionTypeEnums } from '../../../../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../../../../libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from '../../../../../../libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from '../../../../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../../../../libs/model/diligence/details/response';
import { DimensionHitStrategyFieldsEntity } from '../../../../../../libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';

describe('LegalChangeStrategy', () => {
  let strategy: LegalChangeStrategy;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;

  beforeEach(async () => {
    // 创建RiskChangeHelper的模拟实现
    mockRiskChangeHelper = {
      hitLayTypesField: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LegalChangeStrategy,
        {
          provide: RiskChangeHelper,
          useValue: mockRiskChangeHelper,
        },
      ],
    }).compile();

    strategy = module.get<LegalChangeStrategy>(LegalChangeStrategy);
  });

  it('应该正确创建实例', () => {
    expect(strategy).toBeDefined();
  });

  it('应该返回正确的支持维度列表', () => {
    const dimensions = strategy.getSupportedDimensions();
    expect(dimensions).toEqual([DimensionTypeEnums.MainInfoUpdateLegalPerson, DimensionTypeEnums.MainInfoUpdateCapitalChange]);
  });

  it('应该返回正确的维度类型与风险变更类别映射', () => {
    const mapping = strategy.getDimensionCategoryMap();
    expect(mapping).toEqual({
      [DimensionTypeEnums.MainInfoUpdateLegalPerson]: [RiskChangeCategoryEnum.category39],
      [DimensionTypeEnums.MainInfoUpdateCapitalChange]: [RiskChangeCategoryEnum.category39],
    });
  });

  it('应该正确判断是否支持指定维度', () => {
    const supportedDimension = {
      key: DimensionTypeEnums.MainInfoUpdateLegalPerson,
    } as DimensionHitStrategyPO;

    const unsupportedDimension = {
      key: DimensionTypeEnums.BusinessAbnormal,
    } as DimensionHitStrategyPO;

    expect(strategy.supportsDimension(supportedDimension)).toBe(true);
    expect(strategy.supportsDimension(unsupportedDimension)).toBe(false);
  });

  describe('generateDimensionQuery', () => {
    it('应该生成包含正确过滤条件的查询对象', async () => {
      // 准备测试数据
      const companyId = 'test123';
      const dimensionDef = new DimensionDefinitionEntity();
      dimensionDef.key = DimensionTypeEnums.MainInfoUpdateLegalPerson;
      const dimension = new DimensionHitStrategyPO(dimensionDef);
      dimension.dimensionFilter = {
        startTime: 1609459200, // 2021-01-01
        endTime: 1640995199, // 2021-12-31
      };

      // 添加isValid策略字段
      const isValidField = new DimensionHitStrategyFieldsEntity();
      isValidField.dimensionFieldKey = DimensionFieldKeyEnums.isValid;
      isValidField.fieldValue = [1];
      isValidField.compareType = DimensionFieldCompareTypeEnums.Equal;

      // 模拟dimension.getStrategyFieldByKey方法
      dimension.getStrategyFieldByKey = jest.fn().mockImplementation((key) => {
        if (key === DimensionFieldKeyEnums.isValid) {
          return isValidField;
        }
        return null;
      });

      // 执行测试方法
      const query = await strategy.generateDimensionQuery(companyId, dimension);

      // 验证结果
      expect(query).toBeDefined();
      const anyQuery = query as any;
      expect(anyQuery.bool.must).toBeDefined();

      // 检查企业ID过滤条件
      const companyIdFilter = anyQuery.bool.must.find((item: any) => item.term && item.term.KeyNo === companyId);
      expect(companyIdFilter).toBeDefined();

      // 检查风险类别过滤条件
      const categoryFilter = anyQuery.bool.must.find((item: any) => item.terms && item.terms.Category);
      expect(categoryFilter).toBeDefined();
      expect(categoryFilter.terms.Category).toEqual([RiskChangeCategoryEnum.category39]);

      // 检查时间范围过滤条件
      const dateRangeFilter = anyQuery.bool.must.find((item: any) => item.range && item.range.CreateDate);
      expect(dateRangeFilter).toBeDefined();
      expect(dateRangeFilter.range.CreateDate.gte).toBe(1609459200);
      expect(dateRangeFilter.range.CreateDate.lte).toBe(1640995199);

      // 检查有效性过滤条件
      const isValidFilter = anyQuery.bool.must.find((item: any) => item.term && item.term.IsValid === 1);
      expect(isValidFilter).toBeDefined();
    });

    it('应该在发生错误时返回null', async () => {
      // 准备测试数据
      const companyId = 'test123';
      const dimension = {
        key: DimensionTypeEnums.MainInfoUpdateLegalPerson,
        getStrategyFieldByKey: () => {
          throw new Error('模拟错误');
        },
      } as unknown as DimensionHitStrategyPO;

      // 执行测试方法
      const query = await strategy.generateDimensionQuery(companyId, dimension);

      // 验证结果
      expect(query).toBeNull();
    });
  });

  describe('processDimensionDetail', () => {
    it('应该正确处理法定代表人变更详情数据', async () => {
      // 准备测试数据
      const dimensionDef = new DimensionDefinitionEntity();
      dimensionDef.key = DimensionTypeEnums.MainInfoUpdateLegalPerson;
      const dimension = new DimensionHitStrategyPO(dimensionDef);

      // 添加layTypes策略字段
      const layTypesField = new DimensionHitStrategyFieldsEntity();
      layTypesField.dimensionFieldKey = DimensionFieldKeyEnums.layTypes;
      layTypesField.fieldValue = [1];
      layTypesField.compareType = DimensionFieldCompareTypeEnums.Equal;

      // 模拟dimension.getStrategyFieldByKey方法
      dimension.getStrategyFieldByKey = jest.fn().mockImplementation((key) => {
        if (key === DimensionFieldKeyEnums.layTypes) {
          return layTypesField;
        }
        return null;
      });

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test123',
        pageIndex: 1,
        pageSize: 10,
      };

      const response = new HitDetailsBaseResponse();
      response.Result = [
        {
          Id: '1',
          Category: RiskChangeCategoryEnum.category39,
          ChangeExtend: JSON.stringify({ A: '张三', B: '李四', C: 1 }),
          CreateDate: '2021-06-01',
        },
        {
          Id: '2',
          Category: RiskChangeCategoryEnum.category39,
          ChangeExtend: JSON.stringify({ A: '王五', B: '赵六', C: 2 }),
          CreateDate: '2021-05-01',
        },
      ];

      // 模拟hitLayTypesField方法的返回值
      mockRiskChangeHelper.hitLayTypesField.mockImplementation((field, item) => {
        return item.ChangeExtend.C === 1; // 只有第一条记录命中
      });

      // 执行测试方法
      const result = await strategy.processDimensionDetail(response, dimension, params);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Paging.TotalRecords).toBe(1); // 只有一条记录命中
      expect(result.Result.length).toBe(1);
      expect(result.Result[0].Id).toBe('1');

      // 验证模拟方法被正确调用
      expect(mockRiskChangeHelper.hitLayTypesField).toHaveBeenCalledTimes(2);
    });

    it('应该在响应为空时返回原始响应', async () => {
      // 准备测试数据
      const dimension = {} as DimensionHitStrategyPO;
      const params = {} as HitDetailsBaseQueryParams;
      const emptyResponse = new HitDetailsBaseResponse();

      // 执行测试方法
      const result = await strategy.processDimensionDetail(emptyResponse, dimension, params);

      // 验证结果
      expect(result).toBe(emptyResponse);
    });

    it('应该在发生错误时返回原始响应', async () => {
      // 准备测试数据
      const dimension = {
        getStrategyFieldByKey: () => {
          throw new Error('模拟错误');
        },
      } as unknown as DimensionHitStrategyPO;

      const params = {} as HitDetailsBaseQueryParams;
      const response = new HitDetailsBaseResponse();
      response.Result = [{ Id: '1' }];

      // 执行测试方法
      const result = await strategy.processDimensionDetail(response, dimension, params);

      // 验证结果
      expect(result).toBe(response);
    });
  });
});
