import { Client } from '@elastic/elasticsearch';
import { ModuleRef } from '@nestjs/core';
import { Test, TestingModule } from '@nestjs/testing';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { ConfigService } from '../../../../../libs/config/config.service';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import { HitDetailsBaseQueryParams } from '../../../../../libs/model/diligence/details/request';
import { DimensionHitStrategyPO } from '../../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { RiskChangeHelper } from '../../../helper/risk.change.helper';
import { RiskChangeEsSourceV2 } from '../risk-change-es.source.v2';
import { CapitalChangeStrategy } from '../strategies/capital-change.strategy';
import { LegalChangeStrategy } from '../strategies/legal-change.strategy';

describe('RiskChangeEsSourceV2 集成测试', () => {
  let service: RiskChangeEsSourceV2;
  let mockModuleRef: jest.Mocked<ModuleRef>;
  let mockEsClient: jest.Mocked<Client>;
  let mockLegalChangeStrategy: jest.Mocked<LegalChangeStrategy>;
  let mockCapitalChangeStrategy: jest.Mocked<CapitalChangeStrategy>;
  let mockConfigService: jest.Mocked<ConfigService>;

  // 生成测试用户
  const [testOrgId, testUserId] = generateUniqueTestIds('risk-change-es.integration.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  // 模拟ES响应数据
  const mockEsResponse = {
    body: {
      hits: {
        total: { value: 2 },
        hits: [
          {
            _source: {
              Id: '1',
              KeyNo: 'company1',
              Category: 39,
              ChangeExtend: JSON.stringify({ A: '张三', B: '李四' }),
              CreateDate: '2021-06-01',
            },
          },
          {
            _source: {
              Id: '2',
              KeyNo: 'company1',
              Category: 37,
              ChangeExtend: JSON.stringify({ A: '1000万', B: '2000万', T: 2 }),
              CreateDate: '2021-05-01',
            },
          },
        ],
      },
    },
  };

  beforeEach(async () => {
    // 创建模拟实现
    mockModuleRef = {
      get: jest.fn(),
    } as any;

    mockEsClient = {
      search: jest.fn().mockResolvedValue(mockEsResponse),
    } as any;

    mockLegalChangeStrategy = {
      supportsDimension: jest.fn(),
      getSupportedDimensions: jest.fn().mockReturnValue([DimensionTypeEnums.MainInfoUpdateLegalPerson]),
      getDimensionCategoryMap: jest.fn(),
      generateDimensionQuery: jest.fn(),
      processDimensionDetail: jest.fn(),
    } as any;

    mockCapitalChangeStrategy = {
      supportsDimension: jest.fn(),
      getSupportedDimensions: jest.fn().mockReturnValue([DimensionTypeEnums.MainInfoUpdateCapital]),
      getDimensionCategoryMap: jest.fn(),
      generateDimensionQuery: jest.fn(),
      processDimensionDetail: jest.fn(),
    } as any;

    mockConfigService = {
      esConfig: {
        riskChangeList: {
          nodes: ['http://localhost:9200'],
          indexName: 'test_index',
        },
      },
    } as any;

    // 模拟ModuleRef.get方法的实现
    mockModuleRef.get.mockImplementation((token) => {
      if (token === LegalChangeStrategy) {
        return mockLegalChangeStrategy;
      }
      if (token === CapitalChangeStrategy) {
        return mockCapitalChangeStrategy;
      }
      return null;
    });

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RiskChangeEsSourceV2,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: ModuleRef,
          useValue: mockModuleRef,
        },
        {
          provide: RiskChangeHelper,
          useValue: {},
        },
      ],
    })
      .overrideProvider(RiskChangeEsSourceV2)
      .useFactory({
        factory: () => {
          const svc = new RiskChangeEsSourceV2(mockConfigService, mockModuleRef);
          // 替换私有ES客户端
          Object.defineProperty(svc, 'esClient', { value: mockEsClient });
          return svc;
        },
      })
      .compile();

    service = module.get<RiskChangeEsSourceV2>(RiskChangeEsSourceV2);
  });

  afterEach(async () => {
    // 清理测试数据
    jest.clearAllMocks();
  });

  it('应该成功创建实例', () => {
    expect(service).toBeDefined();
  });

  describe('analyze', () => {
    it('应该使用正确的策略类分析不同维度', async () => {
      // 准备测试数据
      const companyId = 'company1';
      const dimensionHitStrategyPOs: DimensionHitStrategyPO[] = [
        {
          strategyId: 1,
          key: DimensionTypeEnums.MainInfoUpdateLegalPerson,
          dimensionFilter: { startTime: **********, endTime: ********** },
          getStrategyFieldByKey: jest.fn(),
        } as any,
        {
          strategyId: 2,
          key: DimensionTypeEnums.MainInfoUpdateCapital,
          dimensionFilter: { startTime: **********, endTime: ********** },
          getStrategyFieldByKey: jest.fn(),
        } as any,
      ];

      // 模拟策略类行为
      mockLegalChangeStrategy.supportsDimension.mockImplementation((dim) => dim.key === DimensionTypeEnums.MainInfoUpdateLegalPerson);
      mockCapitalChangeStrategy.supportsDimension.mockImplementation((dim) => dim.key === DimensionTypeEnums.MainInfoUpdateCapital);

      // 模拟ES查询返回命中结果
      //@ts-ignore
      mockEsClient.search.mockResolvedValueOnce({
        body: {
          hits: {
            total: { value: 1 },
            hits: [{ _source: { Category: 39 } }],
          },
        },
      });

      // 执行测试方法
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // 验证结果
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);

      // 验证策略类方法调用
      expect(mockLegalChangeStrategy.supportsDimension).toHaveBeenCalled();
      expect(mockCapitalChangeStrategy.supportsDimension).toHaveBeenCalled();
    });
  });

  describe('getDimensionDetail', () => {
    it('应该使用正确的策略类处理维度详情', async () => {
      // 准备测试数据
      const dimension: DimensionHitStrategyPO = {
        strategyId: 1,
        key: DimensionTypeEnums.MainInfoUpdateLegalPerson,
        dimensionFilter: { startTime: **********, endTime: ********** },
        getStrategyFieldByKey: jest.fn(),
      } as any;

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'company1',
        pageIndex: 1,
        pageSize: 10,
      };

      // 模拟策略类行为
      mockLegalChangeStrategy.supportsDimension.mockReturnValue(true);
      mockLegalChangeStrategy.generateDimensionQuery.mockResolvedValue({
        bool: {
          must: [{ term: { KeyNo: 'company1' } }],
        },
      });

      //@ts-ignore
      mockLegalChangeStrategy.processDimensionDetail.mockImplementation((response) => {
        return {
          ...response,
          Result: response.Result.map((item) => ({
            ...item,
            BeforeLegalPerson: '张三',
            AfterLegalPerson: '李四',
            ChangeDescription: '法定代表人由"张三"变更为"李四"',
          })),
        };
      });

      // 执行测试方法
      const result = await service.getDimensionDetail(dimension, params);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Result).toBeDefined();
      expect(mockLegalChangeStrategy.generateDimensionQuery).toHaveBeenCalledWith(params.keyNo, dimension, params, undefined);
      expect(mockLegalChangeStrategy.processDimensionDetail).toHaveBeenCalled();
    });

    it('应该处理关联方维度', async () => {
      // 准备测试数据
      const dimension: DimensionHitStrategyPO = {
        strategyId: 1,
        key: DimensionTypeEnums.RecentInvestCancellationsRiskChange, // 关联方维度
        dimensionFilter: { startTime: **********, endTime: ********** },
        getStrategyFieldByKey: jest.fn(),
      } as any;

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'company1',
        pageIndex: 1,
        pageSize: 10,
      };

      // 执行测试方法
      const result = await service.getDimensionDetail(dimension, params);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Result).toEqual([]);
    });
  });
});
