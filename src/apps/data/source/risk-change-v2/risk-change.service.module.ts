import { Module } from '@nestjs/common';
import { RiskChangeRelatedEsSource } from './risk-change-related-es.source';
import { RiskChangeEsSourceV2 } from './risk-change-es.source.v2';
import { LegalChangeStrategy } from './strategies/legal-change.strategy';
import { CapitalChangeStrategy } from './strategies/capital-change.strategy';
import { NegativeNewsStrategy } from './strategies/negative-news.strategy';
import { BusinessAbnormalStrategy } from './strategies/business-abnormal.strategy';
import { JudicialCaseStrategy } from './strategies/judicial-case.strategy';
import { AdministrativePenaltyStrategy } from './strategies/administrative-penalty.strategy';
import { ActualControllerRiskChangeStrategy } from './strategies/actual-controller-risk-change.strategy';
import { InvestCompanyCancellationStrategy } from './strategies/invest-company-cancellation.strategy';
import { EquityChangeStrategy } from './strategies/equity-change.strategy';
import { FinancialIndicatorStrategy } from './strategies/financial-indicator.strategy';

/**
 * 风险变更服务模块
 * 提供风险变更相关的服务和策略
 */
@Module({
  providers: [
    RiskChangeRelatedEsSource,
    RiskChangeEsSourceV2,
    // 注册策略类
    LegalChangeStrategy,
    CapitalChangeStrategy,
    NegativeNewsStrategy,
    BusinessAbnormalStrategy,
    JudicialCaseStrategy,
    AdministrativePenaltyStrategy,
    // 新增关联方策略类
    ActualControllerRiskChangeStrategy,
    InvestCompanyCancellationStrategy,
    // 新增股权变更和财务指标策略类
    EquityChangeStrategy,
    FinancialIndicatorStrategy,
  ],
  exports: [
    RiskChangeRelatedEsSource,
    RiskChangeEsSourceV2,
    // 导出策略类
    LegalChangeStrategy,
    CapitalChangeStrategy,
    NegativeNewsStrategy,
    BusinessAbnormalStrategy,
    JudicialCaseStrategy,
    AdministrativePenaltyStrategy,
    // 导出关联方策略类
    ActualControllerRiskChangeStrategy,
    InvestCompanyCancellationStrategy,
    // 导出股权变更和财务指标策略类
    EquityChangeStrategy,
    FinancialIndicatorStrategy,
  ],
})
export class RiskChangeServiceModule {}
