import { Injectable } from '@nestjs/common';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from 'libs/config/config.service';
import { BaseEsAnalyzeService } from '../base-es-analyze.service';
import { RiskChangeStrategy } from './interfaces/risk-change-strategy.interface';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { ModuleRef } from '@nestjs/core';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { orderBy } from 'lodash';
import { LegalChangeStrategy } from './strategies/legal-change.strategy';
import { CapitalChangeStrategy } from './strategies/capital-change.strategy';
import { NegativeNewsStrategy } from './strategies/negative-news.strategy';
import { BusinessAbnormalStrategy } from './strategies/business-abnormal.strategy';
import { JudicialCaseStrategy } from './strategies/judicial-case.strategy';
import { AdministrativePenaltyStrategy } from './strategies/administrative-penalty.strategy';
import { EquityChangeStrategy } from './strategies/equity-change.strategy';
import { FinancialIndicatorStrategy } from './strategies/financial-indicator.strategy';

/**
 * 风险动态ES数据源服务（处理普通维度）
 * 使用策略模式处理不同维度的风险变更数据
 */
@Injectable()
export class RiskChangeEsSourceV2 extends BaseEsAnalyzeService {
  protected readonly riskChangeLogger: Logger = QccLogger.getLogger(RiskChangeEsSourceV2.name);
  private strategies: RiskChangeStrategy[] = [];

  /**
   * 关联方风险变更维度列表
   */
  private readonly relatedCompanyDimensions = [
    DimensionTypeEnums.RecentInvestCancellationsRiskChange,
    DimensionTypeEnums.ActualControllerRiskChange,
    DimensionTypeEnums.ListedEntityRiskChange,
  ];

  constructor(readonly configService: ConfigService, private readonly moduleRef: ModuleRef) {
    super(
      'RiskChangeEsSourceV2',
      new Client({
        nodes: configService.esConfig.riskChangeList.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.riskChangeList.indexName,
    );
    // 初始化策略类列表
    this.initStrategies();
  }

  /**
   * 初始化策略类列表
   */
  private initStrategies(): void {
    try {
      // 获取所有策略类实例
      this.strategies = [
        this.moduleRef.get(LegalChangeStrategy),
        this.moduleRef.get(CapitalChangeStrategy),
        this.moduleRef.get(NegativeNewsStrategy),
        this.moduleRef.get(BusinessAbnormalStrategy),
        this.moduleRef.get(JudicialCaseStrategy),
        this.moduleRef.get(AdministrativePenaltyStrategy),
        this.moduleRef.get(EquityChangeStrategy),
        this.moduleRef.get(FinancialIndicatorStrategy),
        // 后续会添加更多策略类
      ];
    } catch (error) {
      this.riskChangeLogger.error(`初始化策略类列表失败: ${error instanceof Error ? error.message : String(error)}`, error);
    }
  }

  /**
   * 分析当前数据源所有维度命中情况
   * @param companyId 企业ID
   * @param dimensionHitStrategyPOs 维度策略列表
   * @param params 分析参数
   */
  async analyze(companyId: string, dimensionHitStrategyPOs: DimensionHitStrategyPO[], params?: DimensionAnalyzeParamsPO): Promise<DimensionHitResultPO[]> {
    // 过滤出普通维度（非关联方维度）
    const normalDimensions = dimensionHitStrategyPOs.filter((d) => !this.isRelatedDimension(d));

    if (!normalDimensions.length) {
      return [];
    }

    // 调用父类方法进行基础分析
    const dimHitRes = await super.analyze(companyId, normalDimensions, params);

    // 使用策略模式处理命中的维度
    for (const hitResult of dimHitRes) {
      const dimension = normalDimensions.find((d) => d.strategyId === hitResult.strategyId);
      if (dimension) {
        const strategy = this.findStrategyForDimension(dimension);
        if (strategy) {
          // 如果找到对应的策略，可以在这里进行额外的处理
          // 例如，获取额外的数据或者修改命中结果
        }
      }
    }

    return dimHitRes;
  }

  /**
   * 获取风险动态维度详情
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    // 如果是关联方维度，不在此处理
    if (this.isRelatedDimension(dimension)) {
      return new HitDetailsBaseResponse();
    }

    // 查找对应的策略
    const strategy = this.findStrategyForDimension(dimension);
    if (strategy) {
      // 生成查询条件
      const query = await strategy.generateDimensionQuery(params.keyNo, dimension, params, analyzeParams);
      if (!query) {
        return new HitDetailsBaseResponse();
      }

      // 获取基础数据，然后交由策略处理
      const response = await super.getDimensionDetail(dimension, params, analyzeParams);
      return strategy.processDimensionDetail(response, dimension, params, analyzeParams);
    }

    // 没有找到对应策略，使用默认处理
    return super.getDimensionDetail(dimension, params, analyzeParams);
  }

  /**
   * 查找维度对应的策略
   * @param dimension 维度策略
   */
  private findStrategyForDimension(dimension: DimensionHitStrategyPO): RiskChangeStrategy | null {
    return this.strategies.find((strategy) => strategy.supportsDimension(dimension)) || null;
  }

  /**
   * 判断是否是关联方维度
   * @param dimension 维度策略
   */
  private isRelatedDimension(dimension: DimensionHitStrategyPO): boolean {
    return this.relatedCompanyDimensions.includes(dimension.key);
  }

  /**
   * 实现抽象方法 - 获取维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  protected async getDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    // 查找对应的策略
    const strategy = this.findStrategyForDimension(dimension);
    if (strategy) {
      return strategy.generateDimensionQuery(companyId, dimension, params, analyzeParams);
    }

    // 默认查询
    return {
      bool: {
        must: [
          {
            term: {
              KeyNo: companyId,
            },
          },
        ],
      },
    };
  }

  /**
   * 实现抽象方法 - 处理维度详情数据
   * @param res 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  protected async getDimensionDetailItemData(
    res: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    // 查找对应的策略
    const strategy = this.findStrategyForDimension(dimension);
    if (strategy) {
      return strategy.processDimensionDetail(res, dimension, params, analyzeParams);
    }

    // 默认处理
    return res;
  }
}
