import { Module } from '@nestjs/common';
import { RiskChangeEsSourceV2 } from './risk-change-es.source.v2';
import { RiskChangeRelatedEsSource } from './risk-change-related-es.source';
import { LegalChangeStrategy } from './strategies/legal-change.strategy';
import { CapitalChangeStrategy } from './strategies/capital-change.strategy';
import { NegativeNewsStrategy } from './strategies/negative-news.strategy';
import { BusinessAbnormalStrategy } from './strategies/business-abnormal.strategy';
import { JudicialCaseStrategy } from './strategies/judicial-case.strategy';
import { AdministrativePenaltyStrategy } from './strategies/administrative-penalty.strategy';
import { ActualControllerRiskChangeStrategy } from './strategies/actual-controller-risk-change.strategy';
import { InvestCompanyCancellationStrategy } from './strategies/invest-company-cancellation.strategy';
import { EquityChangeStrategy } from './strategies/equity-change.strategy';
import { FinancialIndicatorStrategy } from './strategies/financial-indicator.strategy';

/**
 * 风险动态V2模块
 * 提供风险动态相关的服务
 */
@Module({
  imports: [],
  providers: [
    // 策略类
    LegalChangeStrategy,
    CapitalChangeStrategy,
    NegativeNewsStrategy,
    BusinessAbnormalStrategy,
    JudicialCaseStrategy,
    AdministrativePenaltyStrategy,
    ActualControllerRiskChangeStrategy,
    InvestCompanyCancellationStrategy,
    EquityChangeStrategy,
    FinancialIndicatorStrategy,

    // 主服务类
    RiskChangeEsSourceV2,
    RiskChangeRelatedEsSource,
  ],
  exports: [RiskChangeEsSourceV2, RiskChangeRelatedEsSource],
})
export class RiskChangeV2Module {}
