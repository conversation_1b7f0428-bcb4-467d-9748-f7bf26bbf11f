import { Injectable, Inject } from '@nestjs/common';
import { ConfigService } from '../../../../libs/config/config.service';
import { Client } from '@elastic/elasticsearch';
import { DimensionHitStrategyPO } from '../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionAnalyzeParamsPO } from '../../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from '../../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../../libs/model/diligence/details/response';
import * as Bluebird from 'bluebird';
import { find, pick } from 'lodash';
import { CompanySearchService } from '../../../company/company-search.service';
import { DimensionHitResultPO } from '../../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { DimensionTypeEnums } from '../../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../../libs/enums/dimension/dimension.filter.params';
import { RelatedTypeEnums } from '../../../../libs/enums/dimension/RelatedTypeEnums';
import { CompanyDetailService } from '../../../company/company-detail.service';
import { PersonHelper } from '../../helper/person.helper';
import * as moment from 'moment';
import { DATE_TIME_FORMAT } from '../../../../libs/constants/common';
import { RiskChangeStrategy } from './interfaces/risk-change-strategy.interface';
import { ModuleRef } from '@nestjs/core';
import { LegalChangeStrategy } from './strategies/legal-change.strategy';
import { CapitalChangeStrategy } from './strategies/capital-change.strategy';
import { NegativeNewsStrategy } from './strategies/negative-news.strategy';
import { BusinessAbnormalStrategy } from './strategies/business-abnormal.strategy';
import { JudicialCaseStrategy } from './strategies/judicial-case.strategy';
import { AdministrativePenaltyStrategy } from './strategies/administrative-penalty.strategy';
import { ActualControllerRiskChangeStrategy } from './strategies/actual-controller-risk-change.strategy';
import { InvestCompanyCancellationStrategy } from './strategies/invest-company-cancellation.strategy';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BaseEsAnalyzeService } from '../base-es-analyze.service';

/**
 * 关联方风险变更服务
 * 继承自AbstractEsAnalyzeService，专门处理关联方维度的风险变更
 */
@Injectable()
export class RiskChangeRelatedEsSource extends BaseEsAnalyzeService {
  /**
   * 关联方风险变更维度列表
   */
  private readonly relatedCompanyDimensions = [
    DimensionTypeEnums.RecentInvestCancellationsRiskChange,
    DimensionTypeEnums.ActualControllerRiskChange,
    DimensionTypeEnums.ListedEntityRiskChange,
  ];

  /**
   * 风险变更策略类列表
   */
  private strategies: RiskChangeStrategy[] = [];

  /**
   * 构造函数
   * @param configService 配置服务
   * @param companySearchService 企业搜索服务
   * @param companyDetailService 企业详情服务
   * @param personHelper 人员辅助服务
   * @param moduleRef 模块引用，用于获取策略类实例
   */
  constructor(
    readonly configService: ConfigService,
    private readonly companySearchService: CompanySearchService,
    private readonly companyDetailService: CompanyDetailService,
    private readonly personHelper: PersonHelper,
    private readonly moduleRef: ModuleRef,
  ) {
    super(
      RiskChangeRelatedEsSource.name,
      new Client({
        nodes: configService.esConfig.riskChange.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.riskChange.indexName,
    );
    // 初始化策略类列表
    this.initStrategies();
  }

  /**
   * 初始化策略类列表
   */
  private initStrategies(): void {
    try {
      // 获取所有策略类实例，已经在构造函数中被初始化
      this.strategies = [
        this.moduleRef.get(LegalChangeStrategy),
        this.moduleRef.get(CapitalChangeStrategy),
        this.moduleRef.get(NegativeNewsStrategy),
        this.moduleRef.get(BusinessAbnormalStrategy),
        this.moduleRef.get(JudicialCaseStrategy),
        this.moduleRef.get(AdministrativePenaltyStrategy),
        // 关联方策略类
        this.moduleRef.get(ActualControllerRiskChangeStrategy),
        this.moduleRef.get(InvestCompanyCancellationStrategy),
      ];
    } catch (error) {
      this.logger.error(`初始化策略类列表失败: ${error instanceof Error ? error.message : String(error)}`, error);
    }
  }

  /**
   * 获取所有策略类
   */
  private getStrategies(): RiskChangeStrategy[] {
    return this.strategies;
  }

  /**
   * 判断是否是关联方维度
   * @param dimension 维度策略
   */
  public isRelatedDimension(dimension: DimensionHitStrategyPO): boolean {
    return this.relatedCompanyDimensions.includes(dimension?.key);
  }

  /**
   * 分析关联方企业的风险变更
   * @param companyId 企业ID
   * @param dimensionHitStrategyPOs 维度策略列表
   * @param params 分析参数
   */
  async analyze(companyId: string, dimensionHitStrategyPOs: DimensionHitStrategyPO[], params?: DimensionAnalyzeParamsPO): Promise<DimensionHitResultPO[]> {
    // 过滤出关联方维度
    const relatedDimensions = dimensionHitStrategyPOs.filter((po) => this.isRelatedDimension(po));

    if (!relatedDimensions.length) {
      return [];
    }

    // 调用父类方法分析关联方风险变更
    return super.analyze(companyId, relatedDimensions, params);
  }

  /**
   * 处理关联方维度详情数据
   * @param esHitDetails ES命中详情
   * @param dimension 维度策略
   * @param params 查询参数
   */
  async detailAnalyzeForRelated(esHitDetails: any[], dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<any[]> {
    if (!esHitDetails?.length) {
      return [];
    }

    try {
      // 查找匹配的策略
      const strategies = this.getStrategies();
      const strategy = strategies.find((s) => s.supportsDimension(dimension));

      if (strategy) {
        // 如果找到匹配的策略，使用策略类处理详情数据
        const tempResponse = new HitDetailsBaseResponse();
        tempResponse.Result = esHitDetails;
        tempResponse.Paging = { TotalRecords: esHitDetails.length, PageIndex: 1, PageSize: esHitDetails.length };

        const result = await strategy.processDimensionDetail(tempResponse, dimension, params);
        return result.Result || [];
      } else {
        this.logger.warn(`未找到处理维度类型 ${dimension.key} 的策略，使用基础处理逻辑`);

        // 尝试从moduleRef获取BaseRelatedRiskChangeStrategy的实例，但这实际上是抽象类
        // 这里我们需要找一个具体的关联方策略来处理
        const fallbackStrategy = strategies.find((s) => s instanceof InvestCompanyCancellationStrategy || s instanceof ActualControllerRiskChangeStrategy);

        if (fallbackStrategy) {
          const tempResponse = new HitDetailsBaseResponse();
          tempResponse.Result = esHitDetails;
          tempResponse.Paging = { TotalRecords: esHitDetails.length, PageIndex: 1, PageSize: esHitDetails.length };

          const result = await fallbackStrategy.processDimensionDetail(tempResponse, dimension, params);
          return result.Result || [];
        }

        // 如果没有找到任何策略，回退到简单的关联方企业信息处理
        return Bluebird.map(esHitDetails, async (detail) => {
          try {
            // 获取关联方企业信息
            const relatedCompanyInfo = await this.getRelatedCompanyInfo(detail, params.keyNo);

            if (relatedCompanyInfo) {
              detail.RelatedCompanyInfo = relatedCompanyInfo;
            }

            return detail;
          } catch (error) {
            this.logger.error(`处理关联方维度详情数据失败: ${error?.message || error}`, error);
            return detail;
          }
        });
      }
    } catch (error) {
      this.logger.error(`处理关联方维度详情数据失败: ${error?.message || error}`, error);
      return esHitDetails;
    }
  }

  /**
   * 处理关联方维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  protected async getDimensionDetailItemData(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk || !response?.Result?.length) {
      return response;
    }

    try {
      // 1. 查找匹配的策略
      const strategies = this.getStrategies();
      const strategy = strategies.find((s) => s.supportsDimension(dimension));

      if (strategy) {
        // 2. 如果找到匹配的策略，使用策略处理详情数据
        return await strategy.processDimensionDetail(response, dimension, params, analyzeParams);
      } else {
        // 3. 如果没有找到匹配的策略，使用通用处理方法
        // 处理关联方维度详情数据
        const processedDetails = await this.detailAnalyzeForRelated(response.Result, dimension, params);

        // 处理返回字段，保持与原始服务一致
        response.Result = processedDetails.map((item) => {
          // 根据维度类型选择性地保留字段
          return pick(item, [
            'GroupId',
            'Id',
            'KeyNo',
            'Name',
            'RiskLevel',
            'DataType',
            'Category',
            'BeforeContent',
            'AfterContent',
            'ChangeExtend',
            'ObjectId',
            'ChangeStatus',
            'ChangeDate',
            'CreateDate',
            'DetailCount',
            'DisplayList',
            'Extend1',
            'Extend4',
            'MaxLevel',
            'Extend3',
            'Extend2',
            'RKDetailCount',
            'IsImportant',
            'ImportantCount',
            'RelatedCompanyInfo',
            'RelatedInfo',
          ]);
        });

        return response;
      }
    } catch (error) {
      this.logger.error(`处理关联方维度详情数据失败: ${error?.message || String(error)}`, error);
      return response;
    }
  }

  /**
   * 获取关联方企业ID列表
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @private
   */
  private async getRelatedCompanyIds(companyId: string, dimension: DimensionHitStrategyPO): Promise<string[]> {
    const companyIds = [];

    // 查询范围，获取关联方范围定义
    const relatedRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRoleType);
    if (!relatedRoleField) {
      return companyIds;
    }

    // 对外投资企业
    if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.InvestCompany)) {
      // 投资企业的状态
      const compnayStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessStatus);
      const status = compnayStatusField?.fieldValue;

      // 投资企业的持股比例
      const fundedRatioLevelField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.fundedRatioLevel);
      const fundedRatioLevel = fundedRatioLevelField?.fieldValue?.[0] || 0;

      // 符合条件的对外投资企业列表
      const { Paging, Result } = await this.companyDetailService.getInvestCompany(companyId, fundedRatioLevel, status, 200);
      if (Paging?.TotalRecords > 0) {
        companyIds.push(...Result.map((d) => d.KeyNo));
      }
    }

    // 实际控制人
    if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.ActualController)) {
      const personlist = await this.personHelper.getFinalActualController(companyId, false);
      const keyNos = personlist?.length ? personlist?.map((p) => p.keyNo).filter((t) => t) : [];
      if (keyNos?.length) {
        companyIds.push(...keyNos);
      }
    }

    // 大股东
    if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.MajorShareholder)) {
      const partnerList = await this.personHelper.getPartnerList(companyId, 'all');
      const bigStockers = partnerList.filter((partner) => partner?.tags.includes('大股东'));
      if (bigStockers?.length) {
        const keyNos = bigStockers?.length ? bigStockers?.map((p) => p.keyNo).filter((t) => t) : [];
        if (keyNos?.length) {
          companyIds.push(...keyNos);
        }
      }
    }

    // 上市主体企业
    if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.StockControlCompany)) {
      // A股的上市主体是企业本身，港股的上市主体在企业详情的Tag中获取
      let isListCompany = false;
      const companyInfo = await this.companySearchService.companyDetailsQcc(companyId);
      if (companyInfo?.Tags?.length) {
        // 是否是上市企业
        const Tag122 = companyInfo?.Tags?.find((t) => t.Type === 122);
        if (Tag122) {
          const dataExtend2 = JSON.parse(Tag122?.DataExtend2 || '{}');
          const ListingStage = dataExtend2?.ListingStage;
          if (ListingStage === '1') {
            isListCompany = true;
          }
        }

        // 如果是上市企业
        if (isListCompany) {
          // 港股企业
          const Tag30 = companyInfo?.Tags?.find((t) => t.Type === 30);
          if (Tag30) {
            const dataExtend2 = JSON.parse(Tag30?.DataExtend2 || '{}');
            const companyKeyNo = dataExtend2?.KN;
            if (companyKeyNo) {
              companyIds.push(companyKeyNo);
            }
          } else {
            companyIds.push(companyId);
          }
        }
      }
    }

    return companyIds;
  }

  /**
   * 获取时间周期日期
   * @param periodTime 周期时间
   * @param periodUnit 周期单位
   * @private
   */
  private getTimePeriodDate(periodTime: number, periodUnit: string): string {
    switch (periodUnit) {
      case '年':
      case 'year': {
        return moment()
          .subtract(periodTime - 1, 'year')
          .startOf('year')
          .format(DATE_TIME_FORMAT);
      }
      case '月':
      case 'month': {
        return moment().subtract(periodTime, 'month').format(DATE_TIME_FORMAT);
      }
      default:
        return moment().subtract(periodTime, 'month').format(DATE_TIME_FORMAT);
    }
  }

  /**
   * 创建基础查询对象
   * @param companyId 企业ID
   * @private
   */
  private createBaseQueryObject(companyId: string): any {
    return {
      bool: {
        must: [
          {
            term: {
              KeyNo: companyId,
            },
          },
        ],
      },
    };
  }

  /**
   * 获取关联方企业信息
   * @param detail 详情数据
   * @param sourceCompanyId 源企业ID
   * @private
   */
  private async getRelatedCompanyInfo(detail: any, sourceCompanyId: string): Promise<any> {
    try {
      // 从详情中提取关联方企业ID
      const relatedCompanyId = this.extractRelatedCompanyId(detail, sourceCompanyId);

      if (!relatedCompanyId) {
        return null;
      }

      // 查询关联方企业基本信息
      const companyInfo = await this.companySearchService.companyDetailsQcc(relatedCompanyId);

      if (!companyInfo) {
        return null;
      }

      return {
        KeyNo: companyInfo.KeyNo,
        Name: companyInfo.Name,
        CreditCode: companyInfo.CreditCode,
        OperName: companyInfo.Oper,
        Status: companyInfo.ShortStatus,
        RelationType: this.getRelationType(detail),
      };
    } catch (error) {
      this.logger.error(`获取关联方企业信息失败: ${error?.message || error}`, error);
      return null;
    }
  }

  /**
   * 提取关联方企业ID
   * @param detail 详情数据
   * @param sourceCompanyId 源企业ID
   * @private
   */
  private extractRelatedCompanyId(detail: any, sourceCompanyId: string): string {
    // 根据不同的风险变更类别提取关联方企业ID
    const category = detail.Category;

    try {
      switch (category) {
        case 17: // 对外投资变更
          return detail.ChangeExtend?.F?.KeyNo;

        case 24: // 大股东变更
        case 25: // 实际控制人变更
          const changeExtend = typeof detail.ChangeExtend === 'string' ? JSON.parse(detail.ChangeExtend) : detail.ChangeExtend;

          if (changeExtend?.F?.KeyNo && changeExtend.F.KeyNo !== sourceCompanyId) {
            return changeExtend.F.KeyNo;
          }
          break;
      }
    } catch (error) {
      this.logger.error(`提取关联方企业ID失败: ${error?.message || error}`, error);
    }

    return null;
  }

  /**
   * 获取关联关系类型
   * @param detail 详情数据
   * @private
   */
  private getRelationType(detail: any): string {
    // 根据不同的风险变更类别获取关联关系类型
    const category = detail.Category;

    switch (category) {
      case 17:
        return '对外投资';
      case 24:
        return '大股东';
      case 25:
        return '实际控制人';
      default:
        return '关联方';
    }
  }

  /**
   * 生成关联方维度查询
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  protected async getDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    if (!this.isRelatedDimension(dimension)) {
      return null;
    }

    // 查找匹配的策略
    const strategies = this.getStrategies();
    const strategy = strategies.find((s) => s.supportsDimension(dimension));

    if (strategy) {
      // 如果找到匹配的策略，使用策略生成查询条件
      return strategy.generateDimensionQuery(companyId, dimension, params, analyzeParams);
    } else {
      // 如果没有找到匹配的策略，使用默认查询生成逻辑
      // 获取关联方企业ID列表
      const companyIds = await this.getRelatedCompanyIds(companyId, dimension);

      // 如果没有关联方企业，返回空查询
      if (!companyIds.length) {
        return null;
      }

      // 创建基础查询对象
      const query = { bool: { filter: [] } };

      // 添加版本过滤
      query.bool.filter.push({ range: { Es_Version: { lt: 999999 } } });

      // 添加企业ID过滤
      query.bool.filter.push({ terms: { KeyNo: companyIds } });

      // 添加有效性过滤
      const isValidParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidParams && Number(isValidParams.fieldValue[0]) >= 0) {
        query.bool.filter.push({ term: { IsValid: Number(isValidParams.fieldValue[0]) } });
      } else {
        // 默认只查询有效记录
        query.bool.filter.push({ term: { IsValid: 1 } });
      }

      // 添加IsRK字段过滤（风险快讯标识）
      query.bool.filter.push({ term: { IsRK: 1 } });

      // 添加风险类别过滤
      const riskCategoriesParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.riskCategories);
      if (riskCategoriesParams) {
        const riskCategories = riskCategoriesParams.fieldValue;
        query.bool.filter.push({ terms: { Category: riskCategories } });
      }

      // 添加时间范围过滤
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        // 关联方或者实际控制人也需要满足当前监控单位时间内产生动态
        const range = {
          CreateDate: {
            gte: Math.ceil(dimensionFilter?.startTime),
            lte: Math.ceil(dimensionFilter?.endTime),
          },
        };
        query.bool.filter.push({ range });
      }

      // 添加ID过滤
      if (dimensionFilter?.id) {
        query.bool.filter.push({ term: { Id: dimensionFilter.id } });
      }

      // 添加周期过滤
      const timePeriodField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.timePeriod);
      if (timePeriodField) {
        const periodTime = timePeriodField.fieldValue[0];
        const periodQuery = {
          range: {
            CreateDate: {
              time_zone: '+08:00',
              gte: this.getTimePeriodDate(periodTime, 'month'),
            },
          },
        };
        query.bool.filter.push(periodQuery);
      }

      return query;
    }
  }
}
