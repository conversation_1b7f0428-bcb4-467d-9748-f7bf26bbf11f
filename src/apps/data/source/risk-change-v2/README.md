# 风险变更模块

## 概述

风险变更模块负责处理企业的各类风险变更数据，包括法定代表人变更、资本变更、经营异常等。该模块采用策略模式设计，将不同类型的风险变更处理逻辑分离到各自的策略类中，提高了代码的可维护性和可扩展性。

## 架构设计

### 核心组件

1. **RiskChangeEsSourceV2**：主要服务类，处理常规风险变更维度。
2. **RiskChangeRelatedEsSource**：处理关联方风险变更维度。
3. **策略接口和基类**：
   - `RiskChangeStrategy`：策略接口，定义所有策略类必须实现的方法。
   - `BaseRiskChangeStrategy`：基础策略类，提供通用实现。
4. **具体策略类**：
   - `LegalChangeStrategy`：处理法定代表人变更相关维度。
   - `CapitalChangeStrategy`：处理资本变更相关维度。
   - 其他策略类（如 NegativeNewsStrategy 等）。
5. **工具类**：
   - `RiskChangeUtils`：提供风险变更处理相关的工具方法。

### 类图

```
┌───────────────────────┐    ┌───────────────────────┐
│  RiskChangeEsSourceV2 │    │RiskChangeRelatedEsSource│
└───────────┬───────────┘    └───────────┬───────────┘
            │                            │
            │uses                        │uses
            ▼                            ▼
┌──────────────────────────────────────────────────┐
│               <<interface>>                      │
│             RiskChangeStrategy                   │
└──────────────────────┬───────────────────────────┘
                       │
                       │implements
                       ▼
┌──────────────────────────────────────────────────┐
│             BaseRiskChangeStrategy               │
└──────────────────────┬───────────────────────────┘
                       │
           ┌───────────┴───────────┐
           │                       │
           │extends                │extends
           ▼                       ▼
┌────────────────────┐    ┌─────────────────────┐
│ LegalChangeStrategy│    │CapitalChangeStrategy│
└────────────────────┘    └─────────────────────┘
```

### 数据流

1. 客户端请求分析某企业的风险变更维度。
2. `RiskChangeEsSourceV2`接收请求并根据维度类型找到对应的策略类。
3. 策略类生成查询条件并从 ES 获取原始数据。
4. 策略类处理原始数据，应用业务规则，返回处理后的结果。
5. `RiskChangeEsSourceV2`将处理后的结果返回给客户端。

## 使用方法

### 添加新的风险变更类型

1. 创建新的策略类，继承`BaseRiskChangeStrategy`：

```typescript
@Injectable()
export class NewRiskChangeStrategy extends BaseRiskChangeStrategy {
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super('NewRiskChangeStrategy');
  }

  // 实现必要的方法
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.NewDimensionType];
  }

  getDimensionCategoryMap(): DimensionCategoryMap {
    return {
      [DimensionTypeEnums.NewDimensionType]: [RiskChangeCategoryEnum.newCategory],
    };
  }

  // 实现其他必要的方法
  // ...
}
```

2. 在`RiskChangeEsSourceV2`和`RiskChangeRelatedEsSource`的`initStrategies`方法中注册新策略类：

```typescript
private initStrategies(): void {
  try {
    this.strategies = [
      // 现有策略
      this.moduleRef.get(LegalChangeStrategy),
      this.moduleRef.get(CapitalChangeStrategy),
      // 添加新策略
      this.moduleRef.get(NewRiskChangeStrategy),
    ];
  } catch (error) {
    this.logger.error(`初始化策略类列表失败: ${error.message}`, error);
  }
}
```

### 查询风险变更数据

```typescript
// 分析维度命中情况
const dimHitRes = await riskChangeEsSourceV2.analyze(companyId, dimensionHitStrategyPOs);

// 获取维度详情
const detailRes = await riskChangeEsSourceV2.getDimensionDetail(dimension, params);
```

## 测试

### 单元测试

为每个策略类编写单元测试，确保其逻辑正确：

```typescript
describe('NewRiskChangeStrategy', () => {
  let strategy: NewRiskChangeStrategy;
  let mockDependencies: any;

  beforeEach(async () => {
    // 设置测试模块和模拟依赖
  });

  it('应该正确处理新风险类型', async () => {
    // 测试逻辑
  });
});
```

### 集成测试

编写集成测试验证策略类与主服务的协作：

```typescript
describe('RiskChangeEsSourceV2 集成测试', () => {
  // 设置测试环境

  it('应该使用正确的策略类处理新风险类型', async () => {
    // 测试逻辑
  });
});
```

## 最佳实践

1. **策略隔离**：每个策略类只处理特定类型的风险变更，不要在策略类中混合处理不同类型。
2. **工具类复用**：尽量使用`RiskChangeUtils`提供的工具方法，避免重复代码。
3. **错误处理**：所有方法都应该包含适当的错误处理，避免异常传播。
4. **日志记录**：使用统一的日志记录方式，便于问题定位。
5. **测试覆盖**：为新增的策略类编写全面的单元测试和集成测试。

## 常见问题

### 1. 如何判断一个维度应该由哪个策略类处理？

使用`supportsDimension`方法判断，该方法检查维度类型是否在策略类支持的维度列表中。

### 2. 如何添加新的维度类型？

1. 在`DimensionTypeEnums`中添加新的枚举值。
2. 创建或修改对应的策略类，在`getSupportedDimensions`方法中返回新的维度类型。
3. 在`getDimensionCategoryMap`方法中定义新维度类型与风险变更类别的映射关系。

### 3. 如何优化性能？

1. 使用缓存减少重复查询。
2. 针对大量数据进行分批处理。
3. 优化 ES 查询条件，减少不必要的字段返回。
4. 使用并发处理提高吞吐量。
