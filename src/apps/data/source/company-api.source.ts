import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { CompanySearchService } from '../../company/company-search.service';
import * as moment from 'moment/moment';
import { DATE_FORMAT } from 'libs/constants/common';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import * as Bluebird from 'bluebird';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { isOrganism } from '../../company/utils';
import { CompanyDetailService } from '../../company/company-detail.service';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums, OperatorName } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { convertByUnit, excludeAmountUnits, getCompareResult, getCompareResultForArray } from '../../../libs/utils/diligence/diligence.utils';
import { QfkRiskItemConstants } from '../../../libs/constants/qfk.risk.item.constants';
import { CompanyDetails } from '../../company/model/CompanyDetails';
import {
  CompFlagType,
  CompRealCapitalSelectType,
  CompWealthRank,
  EconTypeList,
  ListingIndustry,
  ListingMarkType,
  NetProfitSelectType,
} from '../../../libs/constants/company.constants';
import { CommonListItem } from '@kezhaozhao/company-search-api/src/company/model/common';
import { KysCompanyResponseDetails } from '@kezhaozhao/company-search-api';
import { compact, intersection, uniq } from 'lodash';
import { UnitEnums } from '../../../libs/enums/dimension/UnitEnums';
import { DimensionHitStrategyFieldsEntity } from '../../../libs/entities/DimensionHitStrategyFieldsEntity';
import { getEconTypeListNamesByValueList } from '../../../libs/utils/utils';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { registrationRatioType } from '../../../libs/constants/risk.change.constants';
import { IAnalyzeService } from './analyze.interface';

/**
 * 企业详情数据源接口
 */
@Injectable()
export class CompanyApiSource implements IAnalyzeService<HitDetailsBaseQueryParams, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  private readonly logger: Logger = QccLogger.getLogger(CompanyApiSource.name);

  constructor(private readonly companySearchService: CompanySearchService, private readonly companyDetailService: CompanyDetailService) {}

  async analyze(companyId: string, dimensionHitStrategyPOS: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const [companyDetail, companyKzzDetail] = await Bluebird.all([
      this.companySearchService.companyDetailsQcc(companyId),
      this.companySearchService.companyDetailsKys(companyId),
    ]);
    // this.logger.info(`scanByCompanyEs():` + targetTypes.map((t) => t.key));
    return Bluebird.map(dimensionHitStrategyPOS, async (d: DimensionHitStrategyPO) => {
      /**
       * 处理命中描述信息需要的参数
       */
      const desData = {
        isHidden: '',
        isHiddenY: '',
      };
      let hitCount = 0;
      switch (d.key) {
        // case DimensionTypeEnums.QfkRisk6610:
        // case DimensionTypeEnums.QfkRisk6907:
        // case DimensionTypeEnums.QfkRisk6611:
        // case DimensionTypeEnums.QfkRisk2210:
        // case DimensionTypeEnums.QfkRisk6709:
        // case DimensionTypeEnums.QfkRisk2010:
        // case DimensionTypeEnums.QfkRisk2310:
        // case DimensionTypeEnums.BeneficialOwnersControlNumerousEnterprises:
        // case DimensionTypeEnums.ReviewAndInvestigation: {
        //   // isadd 用来表示qfktag的有效性，-1表示无效，0是更新，1是新增
        //   if (companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail?.result?.qfktag) {
        //     const qfktag = JSON.parse(companyKzzDetail.result.qfktag).tags?.find((t) => t.code == d.dimensionDef.typeCode);
        //     if (qfktag?.count > 0) {
        //       hitCount = qfktag?.count;
        //     }
        //   }
        //   break;
        // }
        // 经营状态非存续
        case DimensionTypeEnums.BusinessAbnormal1: {
          if (this.isBusinessAbnormal1(companyId, companyDetail?.ShortStatus)) {
            hitCount = 1;
          }
          break;
        }
        // 经营期限已过有效期
        case DimensionTypeEnums.BusinessAbnormal6: {
          // == ' 253392422400' ? '无固定期限' : company.TeamEnd | dateformat({default:'无固定期限',isS:true})
          if (companyDetail?.TeamEnd && moment.unix(companyDetail?.TeamEnd).isSameOrBefore(moment())) {
            hitCount = 1;
            Object.assign(desData, {
              start: moment.unix(companyDetail?.TermStart).format(DATE_FORMAT),
              end: moment.unix(companyDetail?.TeamEnd).format(DATE_FORMAT),
            });
          }
          break;
        }
        case DimensionTypeEnums.BusinessAbnormal7: {
          // 无统一社会信用代码
          if (!companyDetail?.CreditCode) {
            hitCount = 1;
          }
          break;
        }
        // 被列为非正常户
        case DimensionTypeEnums.BusinessAbnormal4: {
          const { isHit } = await this.isBusinessAbnormal4(companyDetail?.CountInfo?.['TaxUnnormalCount'], { keyNo: companyId });
          if (isHit) {
            hitCount = 1;
          }
          break;
        }
        case DimensionTypeEnums.BusinessAbnormal8: {
          // 临近经营期限
          if (this.isBusinessAbnormal8(companyDetail?.TeamEnd)) {
            hitCount = 1;
            Object.assign(desData, {
              start: moment.unix(companyDetail?.TermStart).format(DATE_FORMAT),
              end: moment.unix(companyDetail?.TeamEnd).format(DATE_FORMAT),
            });
          }
          break;
        }
        case DimensionTypeEnums.EstablishedTime: {
          const { durationHit, baseMonthCount, operator } = this.durationCompareResult(d, companyDetail?.StartDate);
          if (durationHit) {
            hitCount = 1;
            Object.assign(desData, { amountMonth: baseMonthCount, operator: OperatorName[operator] });
          }
          break;
        }
        case DimensionTypeEnums.LowCapital: {
          // 注册资本
          const registerCapital = this.getRegisterCapital(companyDetail);
          if (registerCapital) {
            const { capitalHit, baseAmount, operator2 } = this.capitalCompareResult(d, registerCapital);
            if (capitalHit) {
              hitCount = 1;
              Object.assign(desData, { amountW: parseInt(baseAmount), operator: OperatorName[operator2] });
            }
          }
          break;
        }
        case DimensionTypeEnums.QfkRisk6612:
        case DimensionTypeEnums.QfkRisk6803:
        case DimensionTypeEnums.QfkRisk6610:
        case DimensionTypeEnums.QfkRisk6907:
        case DimensionTypeEnums.QfkRisk6611:
        case DimensionTypeEnums.QfkRisk2210:
        // case DimensionTypeEnums.QfkRisk6709:
        case DimensionTypeEnums.QfkRisk6710:
        case DimensionTypeEnums.QfkRisk2010:
        // case DimensionTypeEnums.QfkRisk2310:
        case DimensionTypeEnums.QfkRisk6609:
        case DimensionTypeEnums.BeneficialOwnersControlNumerousEnterprises:
        case DimensionTypeEnums.ReviewAndInvestigation:
        case DimensionTypeEnums.QfkRisk1410:
        case DimensionTypeEnums.QfkRisk1411:
        case DimensionTypeEnums.QfkRisk1312:
        case DimensionTypeEnums.BusinessAbnormal2:
        case DimensionTypeEnums.QfkRisk6802:
        case DimensionTypeEnums.NoCapital:
        case DimensionTypeEnums.FakeSOES:
        case DimensionTypeEnums.FraudList:
        case DimensionTypeEnums.CompanyShell: {
          // isadd 用来表示qfktag的有效性，-1表示无效，0是更新，1是新增
          if (companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail?.result?.qfktag) {
            const qfktag = JSON.parse(companyKzzDetail.result.qfktag).tags?.find((t) => t.code == d.dimensionDef.typeCode);
            if (qfktag) {
              hitCount = qfktag.count > 0 ? qfktag.count : 1;
            }
          }
          break;
        }
        // case DimensionTypeEnums.FakeSOES:
        // case DimensionTypeEnums.FraudList:
        // case DimensionTypeEnums.CompanyShell: {
        //   // isadd 用来表示qfktag的有效性，-1表示无效，0是更新，1是新增
        //   if (companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail?.result?.qfktag) {
        //     const qfktag = JSON.parse(companyKzzDetail.result.qfktag).tags?.find((t) => t.code == d.dimensionDef.typeCode);
        //     if (qfktag?.count > 0) {
        //       hitCount = qfktag?.count;
        //     }
        //   }
        //   break;
        // }
        case DimensionTypeEnums.QfkRisk: {
          const dimFieldCode = d.getStrategyFieldByKey(DimensionFieldKeyEnums.QfkRiskItem)?.fieldValue[0];
          if (companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail.result.qfktag && dimFieldCode) {
            const qfktag = JSON.parse(companyKzzDetail.result.qfktag)?.tags?.find((t) => t.code == dimFieldCode);
            if (qfktag) {
              hitCount = qfktag.count > 0 ? qfktag.count : 1;
              if (dimFieldCode == '6801') {
                // 股权结构复杂 qfktag.count里存放的是 股东层级数 不是命中数量
                hitCount = 1;
              }
            }
          }
          break;
        }
        case DimensionTypeEnums.CompanyDetail: {
          const allHitStrategyFieldIds: number[] = [];
          await Bluebird.map(d.strategyFields, async (strategyField: DimensionHitStrategyFieldsEntity) => {
            switch (strategyField.dimensionFieldKey) {
              // 企查查行业
              case DimensionFieldKeyEnums.qccIndustry: {
                const { qccIndustryHit } = this.qccIndustryCompareResult(d, companyDetail?.QccIndustry);
                if (qccIndustryHit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
              // 国标行业
              case DimensionFieldKeyEnums.companyIndustry: {
                const { companyIndustryHit } = this.companyIndustryCompareResult(d, companyDetail?.IndustryV3);
                if (companyIndustryHit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
              // 企业成立日期
              case DimensionFieldKeyEnums.duration: {
                const { durationHit } = this.durationCompareResult(d, companyDetail?.StartDate);
                if (durationHit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
              // 企业注册资本
              case DimensionFieldKeyEnums.registrationAmount: {
                const { capitalHit } = this.capitalCompareResult(d, this.getRegisterCapital(companyDetail));
                if (capitalHit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
              // 企业实缴资本
              case DimensionFieldKeyEnums.realRegistrationAmount: {
                const { realCapitalHit } = this.realCapitalCompareResult(d, companyDetail?.['RecCap']);
                if (realCapitalHit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
              // 企业实缴资本筛选
              case DimensionFieldKeyEnums.realCapitalSelect: {
                const { recCapSelectHit } = this.realCapitalSelectCompareResult(d, companyDetail?.['RecCap']);
                if (recCapSelectHit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
              // 企业状态
              case DimensionFieldKeyEnums.companyStatus: {
                const companyStatusCode = companyKzzDetail.result?.['statuscode'];
                if (companyStatusCode) {
                  const { statusHit } = this.statusCompareResult(d, [companyStatusCode]);
                  if (statusHit) {
                    allHitStrategyFieldIds.push(strategyField.id);
                  }
                }
                break;
              }
              // 分支机构
              case DimensionFieldKeyEnums.companyIsBranch: {
                const { isBranchHit } = this.isBranchCompareResult(d, companyDetail?.['IsBranch'] ?? -1);
                if (isBranchHit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
              // 企业性质
              case DimensionFieldKeyEnums.companyEconType: {
                const { econTypeHit } = this.econTypeCompareResult(d, companyDetail?.['standardCode']);
                if (econTypeHit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
              // 上市企业
              case DimensionFieldKeyEnums.companyListed: {
                const { comListedHit } = this.comListedCompareResult(
                  d,
                  this.getListStatus(companyKzzDetail.result, companyKzzDetail.result?.['stockinfo']),
                  companyKzzDetail.result?.['listingstatuskw'],
                );
                if (comListedHit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
              //上市板块
              case DimensionFieldKeyEnums.listedIndustry: {
                const { listedIndustryHit } = this.comListedIndustryResult(d, companyKzzDetail.result?.['listingstatuskw']);
                if (listedIndustryHit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
              // 企业标识
              case DimensionFieldKeyEnums.companyFlag: {
                const { comFlagHit } = this.companyFlagCompareResult(d, companyKzzDetail?.result?.flag);
                if (comFlagHit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
              // 法人代表持股比例
              case DimensionFieldKeyEnums.legalRepresentHoldingRatio: {
                const company = companyKzzDetail.result as any;
                if (company?.operkeyno && company?.mongoemployees) {
                  const mongoemployees = JSON.parse(company.mongoemployees);
                  if (!(mongoemployees instanceof Array) || !mongoemployees?.length) {
                    break;
                  }
                  const operEmployee = mongoemployees.find((t) => t.KeyNo == company?.operkeyno);
                  const numberStock = operEmployee?.StockPercent ? parseFloat(operEmployee?.StockPercent.match(/[\d.]+/)[0]) : 0; // 匹配字符串中的数字部分（包括小数点）
                  const { holdingRatioHit } = this.legalRepresentHoldingRatioCompareResult(d, numberStock);
                  if (holdingRatioHit) {
                    allHitStrategyFieldIds.push(strategyField.id);
                  }
                }
                break;
              }
              case DimensionFieldKeyEnums.wealthRank: {
                const company = companyKzzDetail.result as KysCompanyResponseDetails;
                const isWealthRankHit = this.companyWealthRank(d, company);
                if (isWealthRankHit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
              // 净利润
              case DimensionFieldKeyEnums.netProfit: {
                const companyFinance = await this.companySearchService.doGetCompanyFinance(companyId);
                if (companyFinance?.netProfit && companyFinance.netProfit !== '-') {
                  //接口参数问题返回-
                  const { netProfitHit } = this.companyNetProfitCompareResult(d, companyFinance?.netProfit);
                  if (netProfitHit) {
                    allHitStrategyFieldIds.push(strategyField.id);
                  }
                }
                break;
              }
              case DimensionFieldKeyEnums.registrationRatio: {
                //判断实缴比例
                const rationField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.registrationRatio);
                const { hit } = this.companyRegisterCapitalRatio(rationField, companyDetail);
                if (hit) {
                  allHitStrategyFieldIds.push(strategyField.id);
                }
                break;
              }
            }
          });
          if (allHitStrategyFieldIds?.length) {
            const strategy = d?.fieldHitStrategy;
            if (strategy?.must?.length || strategy?.must_not?.length || strategy?.should?.length) {
              let hit = true;
              // 检查must 必须全命中
              if (strategy.must?.length) {
                const hitMetrics = allHitStrategyFieldIds.filter((id: number) => strategy.must.includes(id));
                hit = hitMetrics.length === strategy.must.length;
              }
              // 检查must 必须全不命中
              if (hit && strategy.must_not?.length) {
                const hitMetrics = allHitStrategyFieldIds.filter((id: number) => strategy.must_not.includes(id));
                hit = hitMetrics.length === 0;
              }
              // 检查should 需要满足 minimum_should_match(至少1个)
              if (hit && strategy.should?.length) {
                const hitMetrics = allHitStrategyFieldIds.filter((strategyId: number) => strategy.should.includes(strategyId));
                hit = hitMetrics.length >= (strategy.minimum_should_match || 1);
              }
              if (hit) {
                hitCount = 1;
              }
            } else {
              // 如果fieldHitStrategy 没有配置默认是must
              if (allHitStrategyFieldIds.length === d.strategyFields.length) {
                hitCount = 1;
              }
            }
          }
          break;
        }
        case DimensionTypeEnums.RealCapitalException: {
          //实缴资本异常
          //成立时间判断，大于等于 5 年
          const { durationHit } = this.durationCompareResult(d, companyDetail?.StartDate);
          //判断实缴比例
          const rationField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.registrationRatio);
          const ration = rationField?.fieldValue?.[0];
          const compareType = rationField?.compareType || DimensionFieldCompareTypeEnums.LessThan;
          //如果注册资本/实缴资本 compareType ration 则命中
          const registerCapitalAmount = this.getRegisterCapital(companyDetail);
          //实缴资本
          const recCap = companyDetail?.['RecCap'];
          let realRegistrationAmount = 0;
          if (recCap && recCap.length > 0 && recCap.includes('万元')) {
            realRegistrationAmount = parseInt(recCap.split('万元')[0]);
          }
          if (
            (durationHit &&
              registerCapitalAmount &&
              realRegistrationAmount &&
              getCompareResult(realRegistrationAmount / registerCapitalAmount, ration / 100, compareType)) ||
            !realRegistrationAmount
          ) {
            hitCount = 1;
          }
          break;
        }
        case DimensionTypeEnums.TaxpayerCertificationChange: {
          if (companyDetail.TaxpayerType) {
            hitCount = 1;
          }
          break;
        }
      }
      if (hitCount > 0) {
        return processDimHitResPO(d, hitCount, desData);
      }
      return null;
    }).then((item) => item.filter((t) => t));
  }

  async getDimensionDetail(dimension: DimensionHitStrategyPO, data: HitDetailsBaseQueryParams): Promise<HitDetailsBaseResponse> {
    const { keyNo, pageIndex: PageIndex, pageSize: PageSize } = data;
    const [companyDetail, companyKzzDetail] = await Bluebird.all([
      this.companySearchService.companyDetailsQcc(keyNo),
      this.companySearchService.companyDetailsKys(keyNo),
    ]);
    const typeCodeTag = companyKzzDetail?.result?.qfktag
      ? JSON.parse(companyKzzDetail.result.qfktag)?.tags?.find((t) => t.code == dimension.dimensionDef.typeCode)
      : undefined;

    const dimensionDetails = HitDetailsBaseResponse.ok();
    switch (dimension?.key) {
      case DimensionTypeEnums.CompanyDetail: {
        let hitCount = 0;
        const allHitStrategyFieldMaps = new Map<number, any>();
        await Bluebird.map(dimension.strategyFields, async (t: DimensionHitStrategyFieldsEntity) => {
          // dimension.strategyFields.forEach(async (t) => {
          switch (t.dimensionFieldKey) {
            // 企查查行业
            case DimensionFieldKeyEnums.qccIndustry: {
              const { qccIndustryHit, QccIndustry, industryName } = this.qccIndustryCompareResult(dimension, companyDetail?.QccIndustry);
              if (qccIndustryHit) {
                QccIndustry;
                allHitStrategyFieldMaps.set(t.id, {
                  desc: `该企业命中企查查行业: ${industryName}`,
                  accessScope: t.accessScope,
                  QccIndustry,
                });
              }
              break;
            }
            // 国标行业
            case DimensionFieldKeyEnums.companyIndustry: {
              const { companyIndustryHit, IndustryV3, industryName } = this.companyIndustryCompareResult(dimension, companyDetail?.IndustryV3);
              if (companyIndustryHit) {
                allHitStrategyFieldMaps.set(t.id, {
                  desc: `该企业命中国标行业: ${industryName}`,
                  accessScope: t.accessScope,
                  IndustryV3,
                });
              }
              break;
            }
            // 企业成立日期
            case DimensionFieldKeyEnums.duration: {
              const { durationHit, baseMonthCount, operator } = this.durationCompareResult(dimension, companyDetail?.StartDate);
              if (durationHit) {
                const Month = moment().diff(moment.unix(companyDetail?.StartDate), 'months');
                allHitStrategyFieldMaps.set(t.id, {
                  desc: `该企业成立时间${OperatorName[operator]}${baseMonthCount}个月`,
                  accessScope: t.accessScope,
                  StartDate: companyDetail?.StartDate,
                  Month,
                });
              }
              break;
            }
            // 企业注册资本
            case DimensionFieldKeyEnums.registrationAmount: {
              const { capitalHit, baseAmount, operator2, registerCapitalAmount } = this.capitalCompareResult(dimension, this.getRegisterCapital(companyDetail));
              if (capitalHit) {
                allHitStrategyFieldMaps.set(t.id, {
                  registerCapitalAmount,
                  desc: `该企业注册资本为${registerCapitalAmount},${OperatorName[operator2]}${baseAmount}`,
                  accessScope: t.accessScope,
                });
              }
              break;
            }
            // 企业实缴资本
            case DimensionFieldKeyEnums.realRegistrationAmount: {
              const { realCapitalHit, realCapitalOperator, realRegistrationAmount, realCapitalBaseAmount } = this.realCapitalCompareResult(
                dimension,
                companyDetail?.['RecCap'],
              );
              if (realCapitalHit) {
                allHitStrategyFieldMaps.set(t.id, {
                  realRegistrationAmount,
                  desc: `该企业实缴资本为${realRegistrationAmount},${OperatorName[realCapitalOperator]}${realCapitalBaseAmount}`,
                  accessScope: t.accessScope,
                });
              }
              break;
            }
            // 企业实缴资本筛选
            case DimensionFieldKeyEnums.realCapitalSelect: {
              const { recCapSelectHit, realRegistrationAmount, amountUnit } = this.realCapitalSelectCompareResult(dimension, companyDetail?.['RecCap']);
              if (recCapSelectHit) {
                allHitStrategyFieldMaps.set(t.id, {
                  realRegistrationAmount,
                  amountUnit,
                  desc: `该企业实缴资本为${this.addThousandsSeparatorByNumber(realRegistrationAmount)}${amountUnit}满足设置条件要求`,
                  accessScope: t.accessScope,
                });
              }
              break;
            }
            // 企业状态
            case DimensionFieldKeyEnums.companyStatus: {
              const companyStatusCode = companyKzzDetail.result?.['statuscode'];
              if (companyStatusCode) {
                const { statusHit } = this.statusCompareResult(dimension, [companyStatusCode]);
                if (statusHit) {
                  const status = companyKzzDetail.result?.['status'];
                  allHitStrategyFieldMaps.set(t.id, { desc: `该企业状态满足设置条件要求`, accessScope: t.accessScope, status });
                }
              }
              break;
            }
            // 分支机构
            case DimensionFieldKeyEnums.companyIsBranch: {
              const { isBranchHit, branchName } = this.isBranchCompareResult(dimension, companyDetail?.['IsBranch'] ?? -1);
              if (isBranchHit) {
                allHitStrategyFieldMaps.set(t.id, {
                  desc: `该企业分支机构类型: ${branchName}`,
                  accessScope: t.accessScope,
                });
              }
              break;
            }
            // 企业性质
            case DimensionFieldKeyEnums.companyEconType: {
              const { econTypeHit, hitNameList } = this.econTypeCompareResult(dimension, companyDetail?.['standardCode']);
              if (econTypeHit) {
                allHitStrategyFieldMaps.set(t.id, {
                  name: hitNameList.join(','),
                  desc: `该企业性质为${hitNameList.join(',')}满足设置条件要求`,
                  accessScope: t.accessScope,
                });
              }
              break;
            }
            // 上市企业
            case DimensionFieldKeyEnums.companyListed: {
              const { comListedHit, hitName, listedIndustryName } = this.comListedCompareResult(
                dimension,
                this.getListStatus(companyKzzDetail.result, companyKzzDetail.result?.['stockinfo']),
                companyKzzDetail.result?.['listingstatuskw'],
                companyKzzDetail.result?.['stockinfo'],
              );
              if (comListedHit) {
                allHitStrategyFieldMaps.set(t.id, {
                  desc: `该企业为${hitName}企业${listedIndustryName}满足设置条件要求`,
                  accessScope: t.accessScope,
                });
              }
              break;
            }
            //上市板块
            case DimensionFieldKeyEnums.listedIndustry: {
              const { listedIndustryHit, hitNameList } = this.comListedIndustryResult(dimension, companyKzzDetail.result?.['listingstatuskw']);
              if (listedIndustryHit) {
                allHitStrategyFieldMaps.set(t.id, {
                  desc: `该企业为${hitNameList.join(',')}企业满足设置条件要求`,
                  accessScope: t.accessScope,
                });
              }
              break;
            }
            // 企业标识
            case DimensionFieldKeyEnums.companyFlag: {
              const { comFlagHit, hitNameList } = this.companyFlagCompareResult(dimension, companyKzzDetail?.result?.flag);
              if (comFlagHit) {
                allHitStrategyFieldMaps.set(t.id, {
                  name: hitNameList.join(','),
                  desc: `该企业为${hitNameList.join(',')}满足设置条件要求`,
                  accessScope: t.accessScope,
                });
              }
              break;
            }
            // 法人代表持股比例
            case DimensionFieldKeyEnums.legalRepresentHoldingRatio: {
              const company = companyKzzDetail.result as any;
              if (company?.operkeyno && company?.mongoemployees) {
                const mongoemployees = JSON.parse(company.mongoemployees);
                if (!(mongoemployees instanceof Array) || !mongoemployees.length) {
                  break;
                }
                const operEmployee = mongoemployees.find((t) => t.KeyNo == company?.operkeyno);
                const numberStock = operEmployee?.StockPercent ? parseFloat(operEmployee?.StockPercent.match(/[\d.]+/)[0]) : 0; // 匹配字符串中的数字部分（包括小数点）
                if (operEmployee?.Name) {
                  const { holdingRatioHit } = this.legalRepresentHoldingRatioCompareResult(dimension, numberStock);
                  if (holdingRatioHit) {
                    allHitStrategyFieldMaps.set(t.id, {
                      dimensionFieldKey: DimensionFieldKeyEnums.legalRepresentHoldingRatio, // 前端要求加的，区分唯一性
                      name: operEmployee?.Name,
                      personKeyNo: operEmployee?.KeyNo,
                      stockPercent: operEmployee?.StockPercent,
                      desc: `该企业法人代表${operEmployee?.Name}持股比例${numberStock}满足设置条件要求`,
                      accessScope: t.accessScope,
                    });
                  }
                }
              }
              break;
            }
            //财富榜单
            case DimensionFieldKeyEnums.wealthRank: {
              const dimFieldCode = dimension.getStrategyFieldByKey(t.dimensionFieldKey)?.fieldValue[0];
              const company = companyKzzDetail.result as KysCompanyResponseDetails;
              const isWealthRankHit = this.companyWealthRank(dimension, company);
              if (isWealthRankHit) {
                const compFlagType = CompWealthRank.find((t) => t.value == dimFieldCode);
                allHitStrategyFieldMaps.set(t.id, {
                  compFlagType,
                  desc: `该企业为${compFlagType.label}满足设置条件要求`,
                  accessScope: t.accessScope,
                });
              }
              break;
            }
            //企业净利润
            case DimensionFieldKeyEnums.netProfit: {
              const companyFinance = await this.companySearchService.doGetCompanyFinance(keyNo);
              if (companyFinance?.netProfit && companyFinance.netProfit !== '-') {
                const { netProfitHit, netProfitValues, amountUnit } = this.companyNetProfitCompareResult(dimension, companyFinance?.netProfit);
                if (netProfitHit) {
                  allHitStrategyFieldMaps.set(t.id, {
                    netProfitValues: netProfitValues.join('-'),
                    desc: `该企业净利润为${this.addThousandsSeparatorByArray(netProfitValues).join('-')}${amountUnit}满足设置条件要求`,
                    accessScope: t.accessScope,
                  });
                }
              }
              break;
            }
            //实缴资本/注册资本
            case DimensionFieldKeyEnums.registrationRatio: {
              //判断实缴比例
              const rationField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.registrationRatio);
              const { hit, sourceRatio } = this.companyRegisterCapitalRatio(rationField, companyDetail);
              if (hit) {
                allHitStrategyFieldMaps.set(t.id, {
                  sourceRatio,
                  desc: `该企业注册资本实缴比例为${sourceRatio}满足设置条件要求`,
                  accessScope: t.accessScope,
                });
              }
              break;
            }
          }
        });
        if (allHitStrategyFieldMaps.size > 0) {
          const strategy = dimension?.fieldHitStrategy;
          const allHitStrategyFieldIds = [...allHitStrategyFieldMaps.keys()];
          if (strategy?.must?.length || strategy?.must_not?.length || strategy?.should?.length) {
            let hit = true;
            // 检查must 必须全命中
            if (strategy.must?.length) {
              const hitRecords = allHitStrategyFieldIds.filter((id: number) => strategy.must.includes(id));
              hit = hitRecords.length === strategy.must.length;
            }
            // 检查must 必须全不命中
            if (hit && strategy.must_not?.length) {
              const hitRecords = allHitStrategyFieldIds.filter((id: number) => strategy.must_not.includes(id));
              hit = hitRecords.length === 0;
            }
            // 检查should 需要满足 minimum_should_match(至少1个)
            if (hit && strategy.should?.length) {
              const hitRecords = allHitStrategyFieldIds.filter((strategyId: number) => strategy.should.includes(strategyId));
              hit = hitRecords.length >= (strategy.minimum_should_match || 1);
            }
            if (hit) {
              hitCount = 1;
            }
          } else {
            // 如果fieldHitStrategy 没有配置默认是must
            // if (allHitStrategyFieldIds.length === dimension.strategyFields.length) {
            hitCount = 1;
            // }
          }
        }
        if (hitCount > 0) {
          // const description = allHitStrategyFieldMaps.values().e;
          return Object.assign(dimensionDetails, {
            Result: [{ description: [...allHitStrategyFieldMaps.values()].filter((d) => d?.accessScope != 4) }],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      }
      // 企风控风险指标
      case DimensionTypeEnums.QfkRisk: {
        const dimFieldCode = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.QfkRiskItem)?.fieldValue[0];
        if (companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail.result.qfktag && dimFieldCode) {
          const hitTag = JSON.parse(companyKzzDetail.result.qfktag)?.tags?.find((t) => t.code == dimFieldCode);
          if (hitTag) {
            let description = QfkRiskItemConstants.find((i) => i.value == dimFieldCode).name;
            switch (dimFieldCode) {
              case '6313': {
                return Object.assign(dimensionDetails, {
                  Result: [
                    {
                      scope: companyKzzDetail.result.scope,
                      // description: '该企业涉及到废品收购、旧货、艺术品收藏、博彩、典当、拍卖、贵金属等高风险行业',
                    },
                  ],
                  Paging: { TotalRecords: 1 },
                });
              }
              case '6504': {
                description = `该企业工商登记的注册地址：${companyKzzDetail.result.address}，疑似采用托管、代办、秘书公司注册`;
                break;
              }
              case '6502': {
                description = `该企业工商登记的注册地址：${companyKzzDetail.result.address}，位于自由贸易试验区内`;
                break;
              }
              case '6809': {
                // 股权结构疑似隐藏控制方
                description = await this.getHiddenControlDescription(keyNo);
                break;
              }
              case '6505':
                description = `该企业工商登记的注册地址：${companyKzzDetail.result.address}，采用自主申报、住所申报方式注册`;
                break;
              case '6801':
                description = '直接股权结构层数超过（含）5层，交叉持股结构层数超过（含）2层。';
                break;
              case '6401':
                description = '该企业注册的联系电话为无效的电话';
                break;
              case '6302':
                description = '该企业年报信息中披露员工数据=0';
                break;
              case '6803':
                description = '该企业暂无实际控制人或者持股比例平均无法认定单一实际控制人';
                break;
              case '2410':
                description = '该企业注册地址与企业通讯地址不符';
                return Object.assign(dimensionDetails, {
                  Result: [
                    {
                      address: companyKzzDetail.result.address,
                      address2: companyKzzDetail.result.address2 || companyKzzDetail.result['operatingaddress'],
                      description,
                    },
                  ],
                  Paging: { TotalRecords: 1 },
                });
              default:
                break;
            }
            return Object.assign(dimensionDetails, {
              Result: [{ ...hitTag, description }],
              Paging: { TotalRecords: 1 },
            });
          }
        }
        // TODO Flag
        break;
      }
      // 无统一社会信用代码
      case DimensionTypeEnums.BusinessAbnormal7:
        if (!companyDetail?.CreditCode) {
          return Object.assign(dimensionDetails, {
            Result: [{ description: '该企业未登记统一社会信用代码' }],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      // 经营状态非存续
      case DimensionTypeEnums.BusinessAbnormal1: {
        const shortStatus = companyDetail?.ShortStatus || companyKzzDetail.result.status;
        if (this.isBusinessAbnormal1(keyNo, shortStatus)) {
          return Object.assign(dimensionDetails, {
            Result: [
              {
                label: '登记状态',
                value: companyDetail.ShortStatus,
                ShortStatus: companyDetail.ShortStatus,
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      }
      // 被列为非正常户
      case DimensionTypeEnums.BusinessAbnormal4: {
        const { isHit, taxUnormals, joinTime } = await this.isBusinessAbnormal4(companyDetail?.CountInfo?.['TaxUnnormalCount'], data);

        if (isHit) {
          const resultDetail = {
            // 纳税人识别号
            taxUnormalsCaseNo: taxUnormals?.CaseNo,
            // 列入机关
            taxUnormalsExecuteGov: taxUnormals?.ExecuteGov,
          };

          // const resultDetail = [
          //   { label: '纳税人识别号', value: taxUnormals?.CaseNo },
          //   { label: '列入机关', value: taxUnormals?.ExecuteGov },
          // ];
          if (joinTime !== 0) {
            // resultDetail.push({ label: '列入日期', value: moment(joinTime * 1000).format(DATE_FORMAT) });
            // 列入日期
            resultDetail['joinTime'] = moment(joinTime * 1000).format(DATE_FORMAT);
          }

          return Object.assign(dimensionDetails, {
            Result: [resultDetail],
            Paging: { TotalRecords: 1 },
          });
        }
        break;
      }
      case DimensionTypeEnums.BusinessAbnormal8: //临近经营期限
        if (this.isBusinessAbnormal8(companyDetail?.TeamEnd)) {
          return Object.assign(dimensionDetails, {
            Result: [
              {
                label: '营业期限',
                value: moment.unix(companyDetail?.TermStart).format(DATE_FORMAT) + ' 至 ' + moment.unix(companyDetail.TeamEnd).format(DATE_FORMAT),
                TermStart: companyDetail?.TermStart,
                TeamEnd: companyDetail.TeamEnd,
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      case DimensionTypeEnums.BusinessAbnormal6: //经营期限已过有效期
        if (companyDetail?.TeamEnd && moment.unix(companyDetail?.TeamEnd).isSameOrBefore(moment())) {
          const start = companyDetail?.TermStart ? moment.unix(companyDetail?.TermStart).format(DATE_FORMAT) : '-';
          const end = companyDetail?.TeamEnd ? moment.unix(companyDetail.TeamEnd).format(DATE_FORMAT) : '-';
          return Object.assign(dimensionDetails, {
            Result: [
              {
                label: '营业期限',
                value: start + ' 至 ' + end,
                TermStart: companyDetail?.TermStart,
                TeamEnd: companyDetail.TeamEnd,
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      case DimensionTypeEnums.EstablishedTime: {
        //新成立企业
        const { durationHit } = this.durationCompareResult(dimension, companyDetail?.StartDate);
        if (durationHit) {
          return Object.assign(dimensionDetails, {
            Result: [
              {
                label: '成立日期',
                value: moment.unix(companyDetail.StartDate).format(DATE_FORMAT),
                StartDate: companyDetail.StartDate,
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      }
      case DimensionTypeEnums.LowCapital: {
        const registerCapital = this.getRegisterCapital(companyDetail);
        if (registerCapital) {
          const { capitalHit, registerCapitalAmount } = this.capitalCompareResult(dimension, registerCapital);
          if (registerCapitalAmount) {
            if (capitalHit) {
              return Object.assign(dimensionDetails, {
                Result: [
                  {
                    label: '注册资本',
                    value: companyDetail?.RegistCapi ?? null,
                    RegistCapi: companyDetail?.RegistCapi ?? null,
                  },
                ],
                Paging: { PageSize, PageIndex, TotalRecords: 1 },
              });
            }
          }
        } else {
          return Object.assign(dimensionDetails, {
            Result: [
              {
                label: '注册资本',
                value: null,
                RegistCapi: null,
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      }
      case DimensionTypeEnums.FakeSOES: {
        // isadd 用来表示qfktag的有效性，-1表示无效，0是更新，1是新增
        if (companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail.result.qfktag && typeCodeTag) {
          return Object.assign(dimensionDetails, {
            Result: [{ description: '该企业被列入假冒国企名单' }],
            Paging: { TotalRecords: 1 },
          });
        }
        break;
      }
      case DimensionTypeEnums.NoCapital: {
        if (companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail.result.qfktag && typeCodeTag) {
          //无实缴资本（实缴资本比例过低）
          return Object.assign(dimensionDetails, {
            Result: [
              {
                RegistCapiName: '注册资本',
                RegistCapiValue: companyDetail.RegistCapi,
                RecCapName: '实缴资本',
                RecCapValue: companyDetail.RecCap,
              },
            ],
            Paging: { TotalRecords: 1 },
          });
        }
        break;
      }
      case DimensionTypeEnums.RealCapitalException: {
        //实缴资本异常
        //成立时间判断，大于等于 5 年
        const { durationHit } = this.durationCompareResult(dimension, companyDetail?.StartDate);
        //判断实缴比例
        const rationField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.registrationRatio);
        const ration = rationField?.fieldValue?.[0];
        const compareType = rationField?.compareType || DimensionFieldCompareTypeEnums.LessThan;
        //如果注册资本/实缴资本 compareType ration 则命中
        const registerCapitalAmount = this.getRegisterCapital(companyDetail);
        //实缴资本
        const recCap = companyDetail?.['RecCap'];
        let realRegistrationAmount = 0;
        if (recCap && recCap.length > 0 && recCap.includes('万元')) {
          realRegistrationAmount = parseInt(recCap.split('万元')[0]);
        }
        if (
          (durationHit &&
            registerCapitalAmount &&
            realRegistrationAmount &&
            getCompareResult(realRegistrationAmount / registerCapitalAmount, ration / 100, compareType)) ||
          !realRegistrationAmount
        ) {
          //成立以来无实缴或（成立日期超过5年的 且 未完成全部实缴(注册资本>实缴资本)）
          return Object.assign(dimensionDetails, {
            Result: [
              {
                RegistCapiName: '注册资本',
                RegistCapiValue: companyDetail.RegistCapi,
                RecCapName: '实缴资本',
                RecCapValue: companyDetail.RecCap,
                Ratio: !registerCapitalAmount ? null : realRegistrationAmount / registerCapitalAmount,
              },
            ],
            Paging: { TotalRecords: 1 },
          });
        }
        break;
      }
      // 涉诈高风险名单
      case DimensionTypeEnums.FraudList: {
        if (companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail.result.qfktag && typeCodeTag) {
          return Object.assign(dimensionDetails, {
            Result: [{ description: '该企业因涉诈被列入高风险名单，请注意核实企业信息' }],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      }
      //疑似空壳企业
      case DimensionTypeEnums.CompanyShell: {
        if (companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail?.result?.qfktag && typeCodeTag) {
          return Object.assign(dimensionDetails, {
            Result: [{ title: '', description: typeCodeTag?.reason, hasDetail: '1' }],
            Paging: { PageSize, PageIndex, TotalRecords: typeCodeTag?.count },
          });
        }
        break;
      }
      case DimensionTypeEnums.TaxpayerCertificationChange: {
        if (companyDetail.TaxpayerType) {
          return Object.assign(dimensionDetails, {
            Result: [{ description: companyDetail.TaxpayerType }],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      }
      default:
        break;
    }
    return dimensionDetails;
  }

  /**
   * 是否经营状态非存续
   */
  private isBusinessAbnormal1(keyNo: string, shortStatus: string) {
    if (isOrganism(keyNo)) {
      if (['撤销', '吊销', '注销', '注销中', '名称核准不通过', '清算', '除名'].includes(shortStatus)) {
        return true;
      }
    } else if (['吊销', '注销', '撤销', '停业', '歇业', '责令关闭', '清算', '除名'].includes(shortStatus)) {
      return true;
    }
    return false;
  }

  /**
   * 是否临近经营期限
   */
  private isBusinessAbnormal8(teamEnd) {
    if (teamEnd) {
      const baseDays = 90;
      //  企业经营到期时间 >今天 || <= 90天时命中
      if (moment.unix(teamEnd).isAfter(moment()) && moment.unix(teamEnd).isSameOrBefore(moment().add(baseDays, 'days'))) {
        return true;
      }
    }
    return false;
  }

  /**
   * 企业QCC行业判断
   * @param d
   * @param QccIndustry
   * @private
   * {
   *     "Ac": "28",
   *     "An": "房地产",
   *     "Bc": "2801",
   *     "Bn": "房地产开发",
   *     "Cc": "280101",
   *     "Cn": "商品房开发",
   *     "Dc": "",
   *     "Dn": ""
   * }
   */
  private qccIndustryCompareResult(dimension: DimensionHitStrategyPO, QccIndustry: any) {
    const companyIndustryParam = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.qccIndustry);
    const targetCompanyIndustry = companyIndustryParam?.fieldValue[0];
    const levels = targetCompanyIndustry.split('-').length;
    const industryLevels = ['Ac', 'Bc', 'Cc'] as const;
    const industryNameLevels = ['An', 'Bn', 'Cn'] as const;
    const sourceIndustryCode = industryLevels
      .map((level) => QccIndustry[level])
      .filter((value) => value !== undefined && value !== null && value !== '')
      .join('-');
    const sourceIndustryName = industryNameLevels
      .map((level) => QccIndustry[level])
      .filter((value) => value !== undefined && value !== null && value !== '')
      .join('-');
    // sourceIndustryCode 只取levels 的长度
    const sourceIndustryCodeString = sourceIndustryCode.split('-').slice(0, levels).join('-');
    const industryName = sourceIndustryName.split('-').slice(0, levels).join('-');
    if (sourceIndustryCodeString && targetCompanyIndustry) {
      if (getCompareResultForArray(companyIndustryParam.compareType, [sourceIndustryCodeString], [targetCompanyIndustry])) {
        return { qccIndustryHit: true, QccIndustry, industryName };
      }
    }
    return { qccIndustryHit: false, QccIndustry, industryName };
  }

  /**
   * 国标行业判断
   * @param d
   * @param QccIndustry
   * @private
   * {
   *     "IndustryCode": "K",
   *     "Industry": "房地产业",
   *     "SubIndustryCode": "70",
   *     "SubIndustry": "房地产业",
   *     "MiddleCategoryCode": "701",
   *     "MiddleCategory": "房地产开发经营",
   *     "SmallCategoryCode": "7010",
   *     "SmallCategory": "房地产开发经营"
   * }
   *
   */
  private companyIndustryCompareResult(dimension: DimensionHitStrategyPO, IndustryV3: any) {
    const companyIndustryParam = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyIndustry);
    const targetCompanyIndustry = companyIndustryParam?.fieldValue[0];
    const levels = targetCompanyIndustry.split('-').length;
    const industryLevels = ['IndustryCode', 'SubIndustryCode', 'MiddleCategoryCode', 'SmallCategoryCode'] as const;
    const industryNameLevels = ['Industry', 'SubIndustry', 'MiddleCategory', 'SmallCategory'] as const;
    const sourceIndustryCode = industryLevels
      .map((level) => IndustryV3[level])
      .filter((value) => value !== undefined && value !== null && value !== '')
      .join('-');
    const sourceIndustryName = industryNameLevels
      .map((level) => IndustryV3[level])
      .filter((value) => value !== undefined && value !== null && value !== '')
      .join('-');
    // sourceIndustryCode 只取levels 的长度
    const sourceIndustryCodeString = sourceIndustryCode.split('-').slice(0, levels).join('-');
    const industryName = sourceIndustryName.split('-').slice(0, levels).join('-');
    if (sourceIndustryCodeString && targetCompanyIndustry) {
      if (getCompareResultForArray(companyIndustryParam.compareType, [sourceIndustryCodeString], [targetCompanyIndustry])) {
        return { companyIndustryHit: true, IndustryV3, industryName };
      }
    }
    return { companyIndustryHit: false, IndustryV3, industryName };
  }

  /**
   * 企业成立日期判断 默认 近24个月
   */
  private durationCompareResult(dimension: DimensionHitStrategyPO, companyStartDate: number, dateUnit = 'months') {
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.duration);
    const baseMonthCount = detailsParams?.fieldValue[0] || 24;
    const operator = detailsParams?.compareType || DimensionFieldCompareTypeEnums.LessThanOrEqual;
    if (companyStartDate) {
      if (getCompareResult(moment().subtract(baseMonthCount, dateUnit).startOf('day').unix(), companyStartDate, operator)) {
        return { durationHit: true, baseMonthCount, operator };
      }
    }
    return { durationHit: false, baseMonthCount, operator };
  }

  private companyNetProfitCompareResult(dimension: DimensionHitStrategyPO, netProfit: string) {
    let netProfitValues: number[] = [];
    const amountUnit = UnitEnums.ten_thousand_yuan;
    try {
      netProfitValues = convertByUnit(netProfit, amountUnit);
      if (netProfitValues?.length === 2) {
        //小米科技有限责任公司 净利润是一个区间值，需要特殊处理
        netProfitValues = [Math.abs(netProfitValues[0]), Math.abs(netProfitValues[1])];
      }
    } catch (e) {
      this.logger.error(`convertByUnit error: ${JSON.stringify(e)}`);
    }
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.netProfit);
    const defaultStatus = detailsParams?.fieldValue[0] || [200000, 500000];
    const operator2 = detailsParams?.compareType || DimensionFieldCompareTypeEnums.ContainsAny;
    const netProfitLabels: string[] = [];
    const min = defaultStatus[0] || undefined;
    const max = defaultStatus[1] || undefined;
    netProfitValues.forEach((netProfitValue) => {
      const isMatch = (min === undefined || netProfitValue >= min) && (max === undefined || netProfitValue < max);
      if (isMatch) {
        const type = NetProfitSelectType.find((t) => {
          const min = t.value[0] || undefined;
          const max = t.value[1] || undefined;
          return (min === undefined || netProfitValue >= min) && (max === undefined || netProfitValue < max);
        });
        if (type) {
          netProfitLabels.push(type.label);
        }
      }
    });
    const defaultStatusType = NetProfitSelectType.find((t) => {
      const compmin = t.value[0] || undefined;
      const compmax = t.value[1] || undefined;
      return (min === undefined || compmin === min) && (max === undefined || compmax === max);
    });

    if (netProfitLabels?.length && defaultStatusType && getCompareResultForArray(operator2, netProfitLabels, [defaultStatusType.label])) {
      return { netProfitHit: true, netProfitValues, amountUnit };
    }
    return { netProfitHit: false, netProfitValues, amountUnit };
  }

  private companyRegisterCapitalRatio(rationField: DimensionHitStrategyFieldsEntity, companyDetail: any) {
    let hit = false;
    const targetRatio = rationField?.fieldValue?.[0];
    const valueCompareType = rationField?.compareType;
    //如果实缴资本/注册资本 compareType ration 则命中
    const registerCapitalAmount = this.getRegisterCapital(companyDetail);
    //实缴资本
    const recCap = companyDetail?.['RecCap'];
    const realRegistrationAmount = excludeAmountUnits(recCap);

    const sourceRatio = (realRegistrationAmount / registerCapitalAmount) * 100;

    const recCapLabels: string[] = [];
    const min = targetRatio[0] ?? undefined;
    const max = targetRatio[1] ?? undefined;

    if (sourceRatio === 0) {
      recCapLabels.push(registrationRatioType.find((t) => t.value[0] === 0 && t.value[1] === 0)?.label);
      const defaultStatusType = registrationRatioType.find((t) => {
        const compmin = t.value[0] ?? undefined;
        const compmax = t.value[1] ?? undefined;
        return (min === undefined || compmin === min) && (max === undefined || compmax === max);
      });
      if (getCompareResultForArray(valueCompareType, recCapLabels, [defaultStatusType.label])) {
        hit = true;
      }
      return {
        hit,
        sourceRatio,
      };
    }
    const isMatch = (min === undefined || sourceRatio >= min) && (max === undefined || sourceRatio < max);
    if (isMatch) {
      const type = registrationRatioType.find((t) => {
        const min = t.value[0] ?? undefined;
        const max = t.value[1] ?? undefined;
        return (min === undefined || sourceRatio >= min) && (max === undefined || sourceRatio < max);
      });
      recCapLabels.push(type.label);
    }
    const defaultStatusType = registrationRatioType.find((t) => {
      const compmin = t.value[0] ?? undefined;
      const compmax = t.value[1] ?? undefined;
      return (min === undefined || compmin === min) && (max === undefined || compmax === max);
    });

    if (getCompareResultForArray(valueCompareType, recCapLabels, [defaultStatusType.label])) {
      hit = true;
    }
    return {
      hit,
      sourceRatio,
    };
  }

  /**
   * 企业实缴资本筛选
   * @param dimension
   * @param companyDetailElement
   * @private
   */
  private realCapitalSelectCompareResult(dimension: DimensionHitStrategyPO, recCap: string) {
    let realRegistrationAmount = 0;
    const amountUnit = UnitEnums.ten_thousand_yuan;
    if (recCap && recCap.length > 0 && recCap.includes(amountUnit)) {
      realRegistrationAmount = parseInt(recCap.split(amountUnit)[0]);
    }
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.realCapitalSelect);
    const defaultStatus = detailsParams?.fieldValue[0] || [10000, 50000];
    const operator2 = detailsParams?.compareType || DimensionFieldCompareTypeEnums.ContainsAny;
    const recCapLabels: string[] = [];
    const min = defaultStatus[0] || undefined;
    const max = defaultStatus[1] || undefined;
    const isMatch = (min === undefined || realRegistrationAmount >= min) && (max === undefined || realRegistrationAmount < max);
    if (isMatch) {
      const type = CompRealCapitalSelectType.find((t) => {
        const min = t.value[0] || undefined;
        const max = t.value[1] || undefined;
        return (min === undefined || realRegistrationAmount >= min) && (max === undefined || realRegistrationAmount < max);
      });
      recCapLabels.push(type.label);
    }
    const dfaultStatusType = CompRealCapitalSelectType.find((t) => {
      const compmin = t.value[0] || undefined;
      const compmax = t.value[1] || undefined;
      return (min === undefined || compmin === min) && (max === undefined || compmax === max);
    });

    if (getCompareResultForArray(operator2, recCapLabels, [dfaultStatusType.label])) {
      return { recCapSelectHit: true, realRegistrationAmount, amountUnit };
    }
    return { recCapSelectHit: false, realRegistrationAmount, amountUnit };
  }

  private addThousandsSeparatorByNumber(num: number) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  private addThousandsSeparatorByArray(array: number[]) {
    return array.map((num) => num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','));
  }

  /**
   * 实缴资本判断
   * @param dimension
   * @param recCap
   * @private
   */
  private realCapitalCompareResult(dimension: DimensionHitStrategyPO, recCap: string) {
    let realRegistrationAmount = 0;
    if (recCap && recCap.length > 0 && recCap.includes('万元')) {
      realRegistrationAmount = parseInt(recCap.split('万元')[0]);
    }
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.realRegistrationAmount);
    const realCapitalBaseAmount = detailsParams?.fieldValue[0] || 100;
    const realCapitalOperator = detailsParams?.compareType || DimensionFieldCompareTypeEnums.LessThanOrEqual;
    if (getCompareResult(realRegistrationAmount, realCapitalBaseAmount, realCapitalOperator)) {
      return { realCapitalHit: true, realCapitalBaseAmount, realCapitalOperator, realRegistrationAmount };
    }
    return { realCapitalHit: false, realCapitalBaseAmount, realCapitalOperator, realRegistrationAmount };
  }

  /**
   * 注册资本判断 默认 是否注册资本小于100万
   */
  private capitalCompareResult(dimension: DimensionHitStrategyPO, registerCapitalAmount: number) {
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.registrationAmount);
    const baseAmount = detailsParams?.fieldValue[0] || 100;
    const operator2 = detailsParams?.compareType || DimensionFieldCompareTypeEnums.LessThanOrEqual;
    if (getCompareResult(registerCapitalAmount, baseAmount, operator2)) {
      return { capitalHit: true, baseAmount, operator2, registerCapitalAmount };
    }
    return { capitalHit: false, baseAmount, operator2, registerCapitalAmount };
  }

  /**
   * 是否被列为非正常户
   */
  private async isBusinessAbnormal4(taxUnnormalCount: number, data: HitDetailsBaseQueryParams) {
    if (!taxUnnormalCount) {
      return { isHit: false };
    }
    const taxResponse = await this.companyDetailService.getTaxUnnormals(data);
    if (!taxResponse) {
      return { isHit: false };
    }
    const taxUnormals = taxResponse?.Result[0];
    const joinTime = taxUnormals.JoinDate || 0;

    return { isHit: true, taxUnormals, joinTime };
  }

  private getListStatus(company: any, stockinfo: string[]) {
    const listingStatusKw = company?.listingstatuskw;
    if (listingStatusKw?.length > 0 && listingStatusKw.includes('F_4')) {
      if (!stockinfo?.join('')?.includes('ST')) {
        // 上市(非ST、*ST)
        return 3;
      }
      // 已上市
      return 1;
    }
    // 未上市
    return 2;
  }

  private getRegisterCapital(companyDetail: CompanyDetails) {
    // 第一个注册资本取不到，取第二个
    const RegistCapi = companyDetail?.['RegistCapi'];
    if (RegistCapi && RegistCapi.length > 0 && RegistCapi.includes('万元')) {
      return parseInt(RegistCapi.split('万元')[0]) || 0;
    }
    return 0;
  }

  /**
   * 企业状态判断
   * @param dimension
   * @param companyStatus
   * @private
   */
  private statusCompareResult(dimension: DimensionHitStrategyPO, companyStatus: string[]) {
    if (!compact(companyStatus)?.length) {
      return { statusHit: false };
    }
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyStatus);
    const defaultStatus = detailsParams?.fieldValue || ['99', '90'];
    const operator2 = detailsParams?.compareType || DimensionFieldCompareTypeEnums.ContainsAny;
    if (
      getCompareResultForArray(
        operator2,
        companyStatus.map((s) => s.toString()),
        defaultStatus,
      )
    ) {
      return { statusHit: true };
    }
    return { statusHit: false };
  }

  /**
   * 企业标识判断
   * @param dimension
   * @param fagList
   * @private
   */
  private companyFlagCompareResult(dimension: DimensionHitStrategyPO, fagList: string[]) {
    const hitNameList: string[] = [];
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyFlag);
    const defaultStatus = detailsParams?.fieldValue || ['SXFFS'];
    const operator2 = detailsParams?.compareType || DimensionFieldCompareTypeEnums.ContainsAny;
    if (getCompareResultForArray(operator2, fagList, defaultStatus)) {
      defaultStatus.forEach((fieldValue) => {
        const compFlagType = CompFlagType.find((t) => t.value == fieldValue);
        hitNameList.push(compFlagType.label);
      });
      return { comFlagHit: true, hitNameList };
    }
    return { comFlagHit: false, hitNameList };
  }

  /**
   * 企业是否为分支机构
   * @param dimension
   * @param isBranch
   */
  public isBranchCompareResult(dimension: DimensionHitStrategyPO, isBranch: number) {
    const branchName = isBranch === 1 ? '是分支机构' : '不限';
    const params = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyIsBranch);
    const defaultStatus = params?.fieldValue || [-1];
    const operator2 = params?.compareType || DimensionFieldCompareTypeEnums.ExceptAny;
    if (getCompareResultForArray(operator2, [isBranch], defaultStatus)) {
      return { isBranchHit: true, branchName };
    }
    return { isBranchHit: false, branchName };
  }

  /**
   * 企业性质判断
   * @param dimension
   * @param companyKzzDetailElement
   * @private
   */
  public econTypeCompareResult(dimension: DimensionHitStrategyPO, companyKzzDetailElement: string[]) {
    let hitNameList: string[] = [];
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyEconType);
    const defaultStatus = detailsParams?.fieldValue || ['*********'];
    const operator2 = detailsParams?.compareType || DimensionFieldCompareTypeEnums.ContainsAny;
    if (getCompareResultForArray(operator2, companyKzzDetailElement, defaultStatus)) {
      //获取 companyKzzDetailElement 和 defaultStatus的交集
      const intersect = intersection(companyKzzDetailElement, defaultStatus);
      if (intersect?.length) {
        hitNameList = getEconTypeListNamesByValueList(intersect, EconTypeList);
      }
      return { econTypeHit: true, hitNameList };
    }
    return { econTypeHit: false, hitNameList };
  }

  /**
   * 上市企业
   * @param dimension
   * @param comListed
   * @param listedIndustryStatus
   * @param stockinfo
   * @private
   */
  private comListedCompareResult(dimension: DimensionHitStrategyPO, comListed: number, listedIndustryStatus: string[], stockinfo?: any[]) {
    let hitName = '';
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyListed);
    const defaultStatus = detailsParams?.fieldValue[0] || 1; // 非上市企业默认传的是2，不能给默认值
    const operator2 = detailsParams?.compareType || DimensionFieldCompareTypeEnums.Equal;
    if (getCompareResult(comListed, defaultStatus, operator2)) {
      const listingMark = ListingMarkType.find((t) => t.value == defaultStatus);
      const listedIndustryName = ListingIndustry.filter((t) => listedIndustryStatus.includes(t.value))
        ?.map((t) => t.label)
        ?.join(',');
      const stockInfo = stockinfo?.length ? `【${stockinfo.join('，')}】` : '';
      hitName = listingMark.label;
      return {
        comListedHit: true,
        hitName,
        listedIndustryName: listedIndustryName ? `（${listedIndustryName}${stockInfo}）` : '',
      };
    }
    return { comListedHit: false, hitName };
  }

  /**
   * 上市板块
   * @param dimension
   * @param listedIndustryStatus
   * @private
   */
  private comListedIndustryResult(dimension: DimensionHitStrategyPO, listedIndustryStatus: string[]) {
    let hitNameList: string[] = [];
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.listedIndustry);
    const defaultStatus = detailsParams?.fieldValue;
    const operator2 = detailsParams?.compareType || DimensionFieldCompareTypeEnums.Equal;
    if (!listedIndustryStatus?.length) {
      return { listedIndustryHit: false, hitNameList };
    }
    if (getCompareResultForArray(operator2, listedIndustryStatus, defaultStatus)) {
      //获取 companyKzzDetailElement 和 defaultStatus的交集
      const intersect = intersection(listedIndustryStatus, defaultStatus);
      if (intersect?.length) {
        hitNameList = ListingIndustry.filter((t) => intersect.includes(t.value)).map((t) => t.label);
      }
      return { listedIndustryHit: true, hitNameList };
    }
    return { listedIndustryHit: false, hitNameList };
  }

  /**
   * 企业法人持股比例
   * @param dimension
   * @param numberStock
   * @private
   */
  private legalRepresentHoldingRatioCompareResult(dimension: DimensionHitStrategyPO, numberStock: number) {
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.legalRepresentHoldingRatio);
    const defaultStatus = detailsParams?.fieldValue[0] || 5;
    const operator2 = detailsParams?.compareType || DimensionFieldCompareTypeEnums.GreaterThanOrEqual;
    if (getCompareResult(numberStock, defaultStatus, operator2)) {
      return { holdingRatioHit: true };
    }
    return { holdingRatioHit: false };
  }

  private companyWealthRank(dimension: DimensionHitStrategyPO, company: KysCompanyResponseDetails) {
    const detailsParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.wealthRank);
    const compareType = detailsParams.compareType || DimensionFieldCompareTypeEnums.Equal;
    const dimFieldCode = detailsParams?.fieldValue[0];
    const commonList: CommonListItem[] = company?.commonlist;
    try {
      const commonListItem = commonList?.find((f) => f.k == '60');
      if (!commonListItem) {
        return false;
      }
      const wealthRanks = JSON.parse(commonListItem.v);
      if (wealthRanks) {
        const codes = uniq(
          wealthRanks
            .map((w) => w.c)
            .join(',')
            .split(','),
        );
        if (compareType === DimensionFieldCompareTypeEnums.Equal || compareType === DimensionFieldCompareTypeEnums.ContainsAny) {
          if (codes.includes(dimFieldCode)) {
            return true;
          }
        } else if (compareType === DimensionFieldCompareTypeEnums.NotEqual) {
          if (codes.includes(dimFieldCode)) {
            return false;
          } else {
            return true;
          }
        }
      }
    } catch (e) {
      this.logger.error(`wealth rank commonlist ${JSON.stringify(commonList)}`);
      this.logger.error(`get wealth rank error: ${JSON.stringify(e)}`);
    }
    return false;
  }

  private async getHiddenControlDescription(keyNo: string) {
    const params = {
      keyNo,
      isBenefit: true,
    };
    const benefitResult = await this.companyDetailService.getBenefitDetail(params);
    let befitDescription = '';
    if (benefitResult.Status === 200 && benefitResult.Result) {
      const benefitNames = benefitResult.Result.Names;
      if (benefitNames && benefitNames.length > 0) {
        const befitDescriptionMap = benefitNames.map((b) => {
          if (!b.PercentTotal) {
            return `${b.Name}(持股比例：-)`;
          }
          return `${b.Name}(持股比例：${b.PercentTotal})`;
        });
        befitDescription = befitDescriptionMap.join('、');
      }
    }
    const actualController = await this.companyDetailService.GetSuspectedActualControllerNoPathV2(keyNo);
    let actualControllerDescription = '';
    if (actualController.Status === 200 && actualController.Result) {
      if (actualController.Result.ControllerData?.Name) {
        actualControllerDescription = `${actualController.Result.ControllerData?.Name}`;
      }
      if (actualController.Result.ControllerData?.PercentTotal) {
        actualControllerDescription += `(持股比例：${actualController.Result.ControllerData?.PercentTotal})`;
      } else {
        actualControllerDescription += `(持股比例：-)`;
      }
    }

    return `该企业实际控制人与受益所有人不一致，实际控制人：${actualControllerDescription}，受益所有人：${befitDescription}`;
  }
}
