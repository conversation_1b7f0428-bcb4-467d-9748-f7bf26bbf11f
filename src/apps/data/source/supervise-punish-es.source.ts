import { Injectable } from '@nestjs/common';
import { BaseEsAnalyzeService } from './base-es-analyze.service';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { ConfigService } from '../../../libs/config/config.service';
import { Client } from '@elastic/elasticsearch';
import { containsKeywords, toRoundFixed, transferToNumber } from '../../../libs/utils/utils';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { find, isNumber, orderBy } from 'lodash';
import { HitDetailsBaseQueryParams, HitDetailsCreditParam } from '../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import * as moment from 'moment/moment';
import { DATE_FORMAT, NoLimitValidNumbers } from '../../../libs/constants/common';
import * as Bluebird from 'bluebird';
import { CreditAggBucketItemPO } from '../../../libs/model/data/source/credit.analyze/CreditAggBucketItemPO';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { DimensionHitStrategyFieldsEntity } from '../../../libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums, EsOperator } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { getCompareResult, getIsValidValue, getStartTimeByCycle } from '../../../libs/utils/diligence/diligence.utils';
import { PenaltiesType } from '../../../libs/constants/punish.constants';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { supervisePunishHelper } from '../helper/supervise.punish.helper';

/**
 * 行政处罚ES数据源
 */
@Injectable()
export class SupervisePunishEsSource extends BaseEsAnalyzeService {
  constructor(private readonly configService: ConfigService, private readonly supervisePunishHelper: supervisePunishHelper) {
    super(
      SupervisePunishEsSource.name,
      new Client({
        nodes: configService.esConfig.supervisePunish.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.supervisePunish.indexName,
    );
  }

  async analyze(companyId: string, dimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const result: DimensionHitResultPO[] = [];
    await Bluebird.map(dimensionHitStrategyPOs, async (po) => {
      const hitStrategy = await super.analyze(companyId, [po]);
      result.push(...hitStrategy);
    });
    return result;
  }

  /**
   * 查询维度命中详情
   * @param dimension
   * @param params
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsCreditParam,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const resp: HitDetailsBaseResponse = await super.getDimensionDetail(dimension, params, analyzeParams);
    return resp;
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    const hitData = await this.fetchHits(resp, dimension, params);

    // 内存分页
    const response = new HitDetailsBaseResponse();
    const pageSize = params?.pageSize || 10;
    const pageIndex = params?.pageIndex || 1;
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;
    response.Paging = {
      PageSize: pageSize,
      PageIndex: pageIndex,
      TotalRecords: hitData.length,
    };
    const sortedData = orderBy(hitData, 'CreateDate', 'desc');
    response.Result = sortedData.slice(start, end);
    // 数据处理
    if (response?.Result?.length) {
      response.Result.forEach((source) => this.doSourceFormat(source));
    }
    return response;
  }

  /**
   * 组装 ES Query
   * @param companyId
   * @param dimension
   * @protected
   */
  protected async getDimensionQuery(companyId: string, dimension: DimensionHitStrategyPO): Promise<object> {
    // const cycle = dimension.getCycle();
    const key = dimension.key;
    const subBool = {
      bool: {
        filter: [],
        must_not: [],
        should: [],
      },
    };
    subBool.bool.filter.push({ term: { punishedkeyno: companyId } });

    const cycleField = dimension.getCycleField();
    const cycle = cycleField ? (cycleField.fieldValue?.[0] as number) : 0;
    const operator = cycleField ? EsOperator[cycleField.compareType] : 'gt';

    if (cycle > 0) {
      const timestamp = getStartTimeByCycle(cycle);
      subBool.bool.filter.push({ range: { punishdate: { [operator]: Math.ceil(timestamp / 1000) } } });
    }

    switch (key) {
      case DimensionTypeEnums.AdministrativePenalties: {
        // 行政处罚
        if (dimension.strategyFields.length) {
          this.processAdministrativePenalties(dimension, subBool);
          if (subBool.bool.should.length > 0) {
            subBool.bool['minimum_should_match'] = 1;
          }
        }
        break;
      }
      case DimensionTypeEnums.TaxPenalties: {
        // 税务处罚
        this.processTaxPenalties(dimension, subBool);
        break;
      }
      default:
    }
    return subBool;
  }

  /**
   * 聚合 Query
   * @param companyId
   * @param DimensionHitStrategyPOs
   * @protected
   */
  protected async createAggs(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]) {
    const aggs: any = {};
    await Bluebird.map(DimensionHitStrategyPOs, async (po) => {
      const dimQuery = await this.getDimensionQuery(companyId, po);
      if (dimQuery) {
        const aggsName = `${this.bucketNamePrefix}${po.strategyId}`;
        aggs[aggsName] = {
          filter: dimQuery,
          aggs: {
            amount: {
              sum: {
                field: 'penalsum',
              },
            },
          },
        };
      }
    });
    return aggs;
  }

  /**
   * 聚合数据处理
   * @param aggObj
   * @protected
   */
  protected processAggs(aggObj: any): CreditAggBucketItemPO[] {
    const bucketData: CreditAggBucketItemPO[] = [];
    Object.keys(aggObj).forEach((bucketName) => {
      const dimensionType = bucketName.replace(this.bucketNamePrefix, '');
      const bucket = aggObj[bucketName];
      const hitCount = bucket['doc_count'];
      if (hitCount > 0) {
        const res: CreditAggBucketItemPO = {
          dimensionType,
          hitCount,
        };
        if (bucket.amount) {
          res.amount = transferToNumber(bucket.amount.value);
        }
        bucketData.push(res);
      }
    });
    return bucketData;
  }

  protected processBucketData(bucketData: CreditAggBucketItemPO[], DimensionHitStrategyPOs: DimensionHitStrategyPO[]): DimensionHitResultPO[] {
    return bucketData
      .map((item) => {
        const d: DimensionHitStrategyPO = find(DimensionHitStrategyPOs, { strategyId: +item.dimensionType });
        const desData = {
          isHidden: '',
          isHiddenY: '',
        };
        const { hitCount, amount } = item;
        switch (d.key) {
          case DimensionTypeEnums.BidAdministrativePenalties:
          case DimensionTypeEnums.AdministrativePenalties: // '行政处罚'
          case DimensionTypeEnums.TaxPenalties: {
            // 税务处罚
            if (amount) {
              const amountW = toRoundFixed(amount / 10000, 4);
              Object.assign(desData, { amountW });
            }
            break;
          }
        }

        let hit = true;
        // 命中记录条数 规则设置
        const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
        if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
          // 不满足 命中记录条数规则 标记未命中
          hit = false;
        }

        if (hit) {
          return processDimHitResPO(d, hitCount, desData);
        }
        return null;
      })
      .filter((t) => t);
  }

  /**
   * 行政处罚
   * @param detailsParams
   * @param subBool
   * @private
   */
  private processAdministrativePenalties(dimension: DimensionHitStrategyPO, subBool: any) {
    let dataStatus = NoLimitValidNumbers;
    dimension.strategyFields.forEach((field: DimensionHitStrategyFieldsEntity) => {
      switch (field.dimensionFieldKey) {
        // 数据状态
        case DimensionFieldKeyEnums.isValid:
          dataStatus = getIsValidValue(field?.fieldValue[0]);
          break;
        // 处罚金额
        case DimensionFieldKeyEnums.penaltiesAmount: //处罚金额
          if (isNumber(field?.fieldValue[0])) {
            const fieldVal = Number(field?.fieldValue[0]);

            switch (field.compareType) {
              case DimensionFieldCompareTypeEnums.Equal: {
                subBool.bool.should.push({
                  term: {
                    penalsum: fieldVal,
                  },
                });
                break;
              }
              case DimensionFieldCompareTypeEnums.GreaterThan:
              case DimensionFieldCompareTypeEnums.LessThan:
              case DimensionFieldCompareTypeEnums.LessThanOrEqual:
              case DimensionFieldCompareTypeEnums.GreaterThanOrEqual: {
                subBool.bool.should.push({
                  range: {
                    penalsum: {
                      [EsOperator[field.compareType]]: fieldVal,
                    },
                  },
                });
                break;
              }
            }
          }
          break;
        // 处罚事由
        case DimensionFieldKeyEnums.punishReasonType: {
          const mustNotItems = ['101'];
          if (field.fieldValue?.length > 0) {
            // ["201", "202", "203", "301", "0"]
            if (!field.fieldValue.includes('0')) {
              // 不包含其他
              subBool.bool.should.push({
                terms: {
                  punishreasontype: field.fieldValue,
                },
              });
            } else {
              // 含有其他
              const defaultItems = ['201', '202', '203', '301', '0'];
              const searchItems = defaultItems.filter((x) => !field.fieldValue.includes(x));
              if (searchItems?.length > 0) {
                mustNotItems.push(...searchItems);
              }
            }
          }
          subBool.bool.must_not.push({ terms: { punishreasontype: mustNotItems } });
          break;
        }
        // 处罚类型
        case DimensionFieldKeyEnums.penaltiesType: {
          let publishTypeItems = [];
          //处罚类型
          if (field?.fieldValue?.length > 0) {
            publishTypeItems = field?.fieldValue?.map((v) => PenaltiesType.find((t) => t.value == v)?.esCode || '').filter((t) => t);
          } else {
            // 不选时默认候选值的全部
            publishTypeItems = PenaltiesType.map((t) => t?.esCode).filter((t) => t);
          }
          if (publishTypeItems?.length > 0) {
            subBool.bool.should.push({
              terms: {
                // 'A001', 'A002'
                punishresultgrouplabel: publishTypeItems,
              },
            });
          }

          break;
        }
        //处理机构1级
        case DimensionFieldKeyEnums.ProcessingAgencyLevelOne: {
          if (field?.fieldValue?.length) {
            if (field?.compareType == DimensionFieldCompareTypeEnums.ExceptAny) {
              subBool.bool.must_not.push({ terms: { punishofficetag1: field.fieldValue } });
            } else {
              subBool.bool.filter.push({ terms: { punishofficetag1: field.fieldValue } });
            }
          }
          break;
        }
        //处理机构2级
        case DimensionFieldKeyEnums.ProcessingAgencyLevelTwo: {
          if (field?.fieldValue?.length) {
            if (field?.compareType == DimensionFieldCompareTypeEnums.ExceptAny) {
              subBool.bool.must_not.push({ terms: { punishofficetag1: field.fieldValue } });
            } else {
              subBool.bool.filter.push({ terms: { punishofficetag1: field.fieldValue } });
            }
          }
          break;
        }
      }
    });
    subBool.bool.filter.push({ terms: { datastatus: dataStatus.split(',') } });
    // 行政处罚不包含税务处罚
    subBool.bool.must_not.push({
      terms: {
        punishreasontype: [101],
      },
    });
    return subBool;
  }

  /**
   * 税务处罚
   * @param detailsParams
   * @param subBool
   * @private
   */
  private processTaxPenalties(dimension: DimensionHitStrategyPO, subBool: any) {
    let dataStatus = [0, 1];
    dimension.strategyFields.forEach((field: DimensionHitStrategyFieldsEntity) => {
      switch (field.dimensionFieldKey) {
        // 数据状态
        case DimensionFieldKeyEnums.isValid: {
          if (Number(field?.fieldValue[0]) >= 0) {
            dataStatus = [Number(field.fieldValue[0])];
          }
          break;
        }
        // 处罚金额
        case DimensionFieldKeyEnums.penaltiesAmount: {
          const fieldVal = field?.fieldValue[0];
          if (isNumber(fieldVal)) {
            switch (field.compareType) {
              case DimensionFieldCompareTypeEnums.Equal: {
                subBool.bool.filter.push({
                  term: {
                    penalsum: fieldVal,
                  },
                });
                break;
              }
              case DimensionFieldCompareTypeEnums.GreaterThan:
              case DimensionFieldCompareTypeEnums.LessThan:
              case DimensionFieldCompareTypeEnums.LessThanOrEqual:
              case DimensionFieldCompareTypeEnums.GreaterThanOrEqual: {
                subBool.bool.filter.push({
                  range: {
                    penalsum: {
                      [EsOperator[field.compareType]]: fieldVal,
                    },
                  },
                });
                break;
              }
            }
          }
          break;
        }
        // 处罚类型
        case DimensionFieldKeyEnums.penaltiesType: {
          let publishTypeItems = [];
          //处罚类型
          if (field?.fieldValue?.length > 0) {
            publishTypeItems = field?.fieldValue?.map((v) => PenaltiesType.find((t) => t.value == v)?.esCode || '').filter((t) => t);
          } else {
            // 不选时默认候选值的全部
            publishTypeItems = PenaltiesType.map((t) => t?.esCode).filter((t) => t);
          }
          if (publishTypeItems?.length > 0) {
            subBool.bool.filter.push({
              terms: {
                // 'A001', 'A002'
                punishresultgrouplabel: publishTypeItems,
              },
            });
          }

          // if (field?.fieldValue?.length > 0) {
          //   const publishTypeItems: string[] = field?.fieldValue?.map((v) => PenaltiesType.find((t) => t.value == v)?.esCode || '').filter((t) => t);
          //   if (publishTypeItems?.length > 0)
          //     subBool.bool.filter.push({
          //       terms: {
          //         // 'A001', 'A002'
          //         punishresultgrouplabel: publishTypeItems,
          //       },
          //     });
          // }
          break;
        }
      }
    });
    subBool.bool.filter.push({ terms: { datastatus: dataStatus } });
    // 税务处罚过滤属性 '处罚事由类型',1-税务处罚 101-税务处罚
    subBool.bool.filter.push({
      terms: {
        punishreasontype: [101],
      },
    });
  }

  private doSourceFormat(source: any) {
    Object.assign(source, {
      Id: source.id,
      KeyNo: source.punishedkeyno,
      Title: source.punishresult,
      title: source.punishresult,
      Name: source.punishedname,
      name: source.punishedname,
      // Type: source.type,
      RiskId: source.id,
      DataStatus: source.datastatus,
      // HasImage: source.hasimage,
      // StockInfo: source.stockinfo,
      // Product: source.product,
      // PunishGovCode: source.punishgovcode,
      // OssId: source.ossid,
      Province: source.punishprovince,
      AreaCode: source.punisharea,
      // ProvinceDesc: source.provincedesc,
      TypeDesc: '行政处罚',
      // IdNum: source.idnum,
      // CreditCode: source.creditcode,
      // CompOrg: source.comporg,
      OssKey: source.osskey,
      // Applicant,
      // Pledgor: source.pledgor,
      TagJsonArray: source.tagjsonarray ? JSON.parse(source.tagjsonarray) : undefined,
      nameandkeyno: source.punishedkeynoarray ? JSON.parse(source.punishedkeynoarray) : undefined,
      CaseReasonType: source.punishbasis,
      // PublishTypeDesc: source.publishtypedesc,
      // CaseReason: source.casereason,
      PublishDate: source?.publishdate,
      // CurrenceDate: source.occurrencedate,
      // OccurrenceDate: source.occurrencedate,
      Court: source.punishoffice,
      court: source.punishoffice,
      // OrgNo: source.orgno,
      RemoveReason: source.punishbasisclean,
      // OriginalName: source.originalname,
      // CourtCode: source.courtcode,
      // CourtProvince: source.courtprovince,
      CaseNo: source.docno,
      caseno: source.docno,
      Amount: source?.penalsum === 0.0 ? '-' : source?.penalsum, // 罚没款总金额：penalsum；罚款金额：punishamount；没收金额：forfeitedamount；
      amount: source?.penalsum, // 罚没款总金额：penalsum；罚款金额：punishamount；没收金额：forfeitedamount；
      // ActionRemark: source.actionremark || source.removereason,
      FileUrl: source?.osskey?.length > 0 ? `https://qccdata.qichacha.com/CaseSupervisePunish/${source.osskey}` : '',
      // ExecutionApplicant: source.executionapplicant,
      // LianDate: source.liandate,
      // PPledgor: source.p_pledgor,
      // Specs,
      // ApplicantInfo,
      // AmountDesc: source.amountdesc,
      // ExecuteStatus: dimension.key === DimensionTypeEnums.ProductQualityProblem9 ? '不合格' : source.executestatus,
      // Address: source.address,
      docNo: source.docno,
      punishReason: source.punishbasis,
      punishResult: source.punishresult,
      punishOffice: source.punishoffice,
      punishDate: moment(source?.punishdate * 1000).format(DATE_FORMAT),
      // Amount2: source?.amount2,
      CaseReason: source.casefacts,
      casereason: source.casefacts,
      PunishDate: source?.punishdate === 0 ? '-' : moment(source?.punishdate * 1000).format(DATE_FORMAT),
    });
  }

  async fetchHits(detailResp: HitDetailsBaseResponse, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<any[]> {
    const hitData = [];
    await Bluebird.map(detailResp.Result, async (itemRaw) => {
      try {
        let isHit = true;
        const redCardField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishRedCard);
        if (redCardField && isHit) {
          isHit = this.supervisePunishHelper.penaltyRedCardField(redCardField, itemRaw);
        }
        if (isHit) {
          hitData.push(itemRaw);
        }
      } catch (e) {
        this.logger.error(`supervisePunish getDimensionDetail request: ${JSON.stringify(itemRaw)}`, e);
      }
    });
    return hitData;
  }
}
