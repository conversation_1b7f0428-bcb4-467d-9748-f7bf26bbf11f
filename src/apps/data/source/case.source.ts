/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { ConfigService } from 'libs/config/config.service';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { Client } from '@elastic/elasticsearch';
import { BaseEsAnalyzeService } from './base-es-analyze.service';
import { CaseReasonCode, LawsuitResultNewMap } from '../../../libs/constants/case.constants';
import * as moment from 'moment';
import { HitDetailsBaseQueryParams, HitDetailsCreditParam } from '../../../libs/model/diligence/details/request';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { getStartTimeByCycle } from '../../../libs/utils/diligence/diligence.utils';
import { PersonHelper } from '../helper/person.helper';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';

/**
 * 司法案件数据源接口
 */
@Injectable()
export class CaseSource extends BaseEsAnalyzeService {
  constructor(private readonly configService: ConfigService, private readonly personHelper: PersonHelper) {
    super(
      CaseSource.name,
      new Client({
        nodes: configService.esConfig.case.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.case.indexName,
    );
  }

  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsCreditParam,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const resp: HitDetailsBaseResponse = await super.getDimensionDetail(dimension, params, analyzeParams);
    return resp;
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const { keyNo } = params;
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    if (resp?.Result?.length) {
      switch (dimension?.key) {
        case DimensionTypeEnums.CompanyOrMainMembersCriminalOffence:
        case DimensionTypeEnums.CompanyOrMainMembersCriminalOffenceHistory: {
          const { personJobSet } = await this.personHelper.getCompanyExecutivesKeyNosV2(keyNo, 'all');
          resp.Result.forEach((r) => {
            const AmtInfo = JSON.parse(r?.AmtInfo);
            const CaseRole = JSON.parse(r?.CaseRoleSearch);
            r['RoleAmt'] = CaseRole?.map((c) => {
              c['AmtInfo'] = AmtInfo[c?.N];
              c['Job'] = personJobSet[c?.N];
              return c;
            })?.filter(Boolean);
            return r;
          });
          break;
        }
        case DimensionTypeEnums.LaborContractDispute: {
          resp.Result.forEach((r) => {
            r['Amt'] = r?.AmtInfo ? JSON.parse(r?.AmtInfo)?.[keyNo]?.Amt || '-' : '-';
            r['CaseRoleIdentity'] = r?.CaseRole
              ? JSON.parse(r?.CaseRole)
                  ?.find((f) => f.N === keyNo)
                  ?.RL?.map((rl) => `${rl.T}${rl.R}${rl.LR ? `[${LawsuitResultNewMap[rl.LR]}]` : ''}`)
                  ?.join('\n')
              : '-';
            r['LatestDateTrialRound'] = moment(r?.LastestDate * 1000).format('YYYY-MM-DD') + '\n' + r?.LatestTrialRound;
          });
          break;
        }
        default:
          break;
      }
    }
    return resp;
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionHitStrategyPO) {
    const ids = [companyId];
    const CaseReasonListObscure = ['资产保全', '交通肇事', '其他刑事'];
    const CaseReasonList = [
      '危险驾驶罪',
      '故意伤害罪',
      '故意杀人罪',
      '遗弃罪',
      '职务侵占',
      '诽谤罪',
      '盗窃罪',
      '过失致人死亡罪',
      '侵占罪',
      '强奸罪',
      '抢劫罪',
      '滥用职权罪',
      '侮辱罪',
      '聚众斗殴罪',
      '过失致人重伤罪',
      '诈骗罪',
      '妨害传染病防治罪',
      '聚众扰乱社会秩序罪',
    ];

    const needPersonKeyTypes: DimensionTypeEnums[] = [
      DimensionTypeEnums.CompanyOrMainMembersCriminalOffence,
      DimensionTypeEnums.CompanyOrMainMembersCriminalOffenceHistory,
    ];
    let personKeys: string[] = [];
    if (needPersonKeyTypes.includes(dimension.key)) {
      //TODO  可能存在人员列表很多导致es查询条件超出限制， 具体参考任务 RA-4865
      const { personNos } = await this.personHelper.getCompanyExecutivesKeyNosV2(companyId, 'all');
      personKeys = personNos;
    }
    const subBool = {
      filter: [],
    };
    switch (dimension.key) {
      case DimensionTypeEnums.CompanyOrMainMembersCriminalOffenceHistory: {
        if (personKeys?.length > 0) {
          Array.prototype.push.apply(ids, personKeys);
        }
        // 公司、法定代表人/股东/董监高存在涉贿、不正当竞争等刑事犯罪行为（3年以上）
        // 默认条件 最新变更时间 小于 是3年
        const timestamp = getStartTimeByCycle(3);
        subBool.filter.push({ range: { LastestDate: { lte: Math.ceil(timestamp / 1000) } } });
        subBool.filter.push({ term: { IsValid: 1 } }, { term: { CaseTypeCode2: '2' } });
        subBool.filter.push({ terms: { 'DefendantNames.keyword': ids } });
        // subBool['must_not'] = [
        //   {
        //     terms: {
        //       CaseReason: CaseReasonListObscure,
        //     },
        //   },
        // ];
        subBool['must_not'] = [
          {
            terms: { CaseReason: CaseReasonList },
          },
          {
            bool: {
              should: CaseReasonListObscure.map((reason) => {
                return { match_phrase: { ['CaseReason.aliws']: { query: reason, slop: 1 } } };
              }),
              minimum_should_match: 1,
            },
          },
        ];
        return { bool: subBool };
      }
      case DimensionTypeEnums.CompanyOrMainMembersCriminalOffence: {
        if (personKeys?.length > 0) {
          Array.prototype.push.apply(ids, personKeys);
        }
        const timestamp = getStartTimeByCycle(3);
        // 最新变更时间 大于 是3年
        subBool.filter.push({ range: { LastestDate: { gt: Math.ceil(timestamp / 1000) } } });
        subBool.filter.push({ term: { IsValid: 1 } }, { term: { CaseTypeCode2: '2' } });
        subBool.filter.push({ terms: { 'DefendantNames.keyword': ids } });
        subBool['must_not'] = [
          {
            terms: { CaseReason: CaseReasonList },
          },
          {
            bool: {
              should: CaseReasonListObscure.map((reason) => {
                return { match_phrase: { ['CaseReason.aliws']: { query: reason, slop: 1 } } };
              }),
              minimum_should_match: 1,
            },
          },
        ];
        return { bool: subBool };
      }
      case DimensionTypeEnums.LaborContractDispute: {
        // 劳动合同纠纷
        const cycle = dimension.getCycle();
        if (cycle > 0) {
          const timestamp = getStartTimeByCycle(cycle);
          // 最新变更时间 大于 是3年
          subBool.filter.push({ range: { LastestDate: { gt: Math.ceil(timestamp / 1000) } } });
        }
        let datastatus = [0, 1];
        const queryPo = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
        // const queryPo = dimension.strategyModel.detailsParams.find((p) => p.field === DimensionFieldKeyEnums.isValid);
        if (queryPo && Number(queryPo?.fieldValue[0]) >= 0) {
          datastatus = [Number(queryPo.fieldValue[0])];
        }
        subBool.filter.push({ terms: { IsValid: datastatus } });
        subBool.filter.push({ terms: { 'DefendantNames.keyword': ids } }, { terms: { CaseReasonCode } });
        return { bool: subBool };
      }
    }
    return { bool: subBool };
  }
}
