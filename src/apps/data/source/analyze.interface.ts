import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';

export interface IAnalyzeService<T extends HitDetailsBaseQueryParams, A extends DimensionAnalyzeParamsPO, R extends HitDetailsBaseResponse> {
  analyze(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[], params?: A): Promise<DimensionHitResultPO[]>;

  getDimensionDetail(dimension: DimensionHitStrategyPO, params: T, analyzeParams?: A): Promise<R>;
}
