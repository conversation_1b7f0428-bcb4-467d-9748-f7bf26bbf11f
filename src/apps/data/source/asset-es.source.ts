import { Injectable } from '@nestjs/common';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from 'libs/config/config.service';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { BaseEsAnalyzeService } from './base-es-analyze.service';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import * as Bluebird from 'bluebird';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { getCompareResult, getStartTimeByCycle } from '../../../libs/utils/diligence/diligence.utils';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';

@Injectable()
export class AssertESSource extends BaseEsAnalyzeService {
  constructor(private readonly configService: ConfigService, private readonly redisService: RedisService) {
    super(
      AssertESSource.name,
      new Client({
        nodes: configService.esConfig.asset.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.asset.indexName,
    );
  }

  async analyze(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    return Bluebird.map(DimensionHitStrategyPOs, async (d: DimensionHitStrategyPO) => {
      /**
       * 处理命中描述信息需要的参数
       */
      const desData = {
        isHidden: '',
        isHiddenY: '',
      };
      let hitCount = 0;
      switch (d.key) {
        case DimensionTypeEnums.AssetInvestigationAndFreezing: {
          const params = new HitDetailsBaseQueryParams();
          params.keyNo = companyId;
          const res = await this.getDimensionDetail(
            d,
            params,
            Object.assign(new DimensionAnalyzeParamsPO(), {
              isScanRisk: true,
              keyNo: companyId,
            }),
          );
          if (res) {
            hitCount = res?.Paging?.TotalRecords || 0;
          }
          break;
        }
        default:
          break;
      }
      // 命中记录条数 规则设置
      const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
      if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
        // 不满足 命中记录条数规则 标记未命中
        hitCount = 0;
      }
      if (hitCount > 0) {
        return processDimHitResPO(d, hitCount, desData);
      }
      return null;
    }).then((item) => item.filter((t) => t));
  }

  /**
   * 获取黑名维度详情
   * @param dimension
   * @param params
   * @returns
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const blacklistResp: HitDetailsBaseResponse = await super.getDimensionDetail(dimension, params, analyzeParams);
    return blacklistResp;
  }

  protected async getDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ) {
    const subBool = {
      filter: [],
    };
    subBool.filter.push({ term: { subjectnames: companyId } });
    // 查封公告时间
    const cycle = dimension.getCycle();
    if (cycle && cycle > 0) {
      const timestamp = getStartTimeByCycle(cycle);
      subBool.filter.push({ range: { publishdate: { gte: Math.ceil(timestamp / 1000) } } });
    }
    // datastatus
    const isValidParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
    if (isValidParams && Number(isValidParams.fieldValue[0]) >= 0) {
      subBool.filter.push({ term: { datastatus: Number(isValidParams.fieldValue[0]) } });
    }
    // 监控单位时间内的数据
    const dimensionFilter = dimension?.dimensionFilter;
    if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
      const range = {
        publishdate: {
          gte: Math.ceil(dimensionFilter?.startTime),
          lte: Math.ceil(dimensionFilter?.endTime),
        },
      };
      subBool.filter.push({ range });
    }
    switch (dimension.key) {
      // 资产查冻
      case DimensionTypeEnums.AssetInvestigationAndFreezing: {
        break;
      }
    }
    return { bool: subBool };
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    // 数据处理
    if (resp?.Result?.length) {
      resp.Result.forEach((source) => {
        Object.assign(source, {
          Id: source.id,
          Title: source.noticetitle,
          NameAndKeyNo: source?.subjectnameandkeyno ? JSON.parse(source.subjectnameandkeyno) : [],
          Name: source.subjectnameandkeyno ? JSON.parse(source.subjectnameandkeyno)[0].Name : '',
          KeyNo: source.subjectnameandkeyno ? JSON.parse(source.subjectnameandkeyno)[0].KeyNo : '',
          DataStatus: source.datastatus,
          Publishdate: source?.publishdate,
          AttachMentInfo: source?.attachmentinfo ? JSON.parse(source.attachmentinfo) : [],
        });
      });
    }
    return resp;
  }
}
