import { EnterpriseLibApiSource } from './source/enterprise-lib-api.source';
import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../app/app.test.module';
import { DataModule } from './data.module';
import { getDimensionHitStrategyPO } from '../test_utils_module/dimension.test.utils';
import { DimensionTypeEnums } from '../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { HitDetailsBaseQueryParams } from '../../libs/model/diligence/details/request';
import { YearPeriodType } from '../../libs/constants/recruitment.constants';
import {
  hasCertificationRevoked,
  HasCompanyCircularShareholder,
  hasEmployeeStockPlatform,
  IndustrialChainCoreCompanyContant,
  InternationPatentStatusConstant,
  IsHistoryPatentConstant,
  IsInstitutionalInvestorConstant,
  PatentStableConstant,
  PatentStatisticsConstant,
  PatentTypeConstant,
  RecruitmentStatisticsConstant,
  SourcesInvestInstiteRankConstant,
} from '../../libs/constants/company.constants';
import { RiskChangeEsSource } from './source/risk-change/risk-change-es.source';
import { BaseLineDateSelect, RegisCapitalTrendMap, registrationRatioType } from '../../libs/constants/risk.change.constants';
import { CompanyApiSource } from './source/company-api.source';
import { CompanySearchService } from '../company/company-search.service';
import * as Bluebird from 'bluebird';
import { CreditEsSource } from './source/credit-es.source';

jest.setTimeout(60 * 10000);
describe('企业科创健康性尽调指标集成测试', () => {
  let enterpriseLibService: EnterpriseLibApiSource;
  let riskChangeESService: RiskChangeEsSource;
  let companyService: CompanyApiSource;
  let companySearchService: CompanySearchService;
  let creditESService: CreditEsSource;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    enterpriseLibService = module.get<EnterpriseLibApiSource>(EnterpriseLibApiSource);
    riskChangeESService = module.get<RiskChangeEsSource>(RiskChangeEsSource);
    companyService = module.get<CompanyApiSource>(CompanyApiSource);
    companySearchService = module.get<CompanySearchService>(CompanySearchService);
    creditESService = module.get<CreditEsSource>(CreditEsSource);
  });

  it('should be defined', async () => {
    const [companyDetail, companyKzzDetail] = await Bluebird.all([
      companySearchService.companyDetailsQcc('2735e76f62558ddeb5478fb8a26cfc54'),
      companySearchService.companyDetailsKys('2735e76f62558ddeb5478fb8a26cfc54'),
    ]);
    console.log('companyDetail', companyDetail);
  });

  /**
   * 企业创新性
   */
  it('【本科以上招聘占比】-X=0', async () => {
    const companyId = 'f51294e83da764648ec587b60328a370';
    const companyName = '上海逐风信息技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RecruitmentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.recruitmentStatistics,
        fieldValue: [1],
        options: RecruitmentStatisticsConstant,
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'publishtime', order: 'DESC', fieldSnapshot: 'PublishTime' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [3],
        options: [-1, 1, 2, 3, 4, 5],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.avgXn,
        fieldValue: [0],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.yearPeriod,
        fieldValue: [1, 2, 3],
        options: YearPeriodType,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【发明专利占比】-<10%', async () => {
    const companyId = 'f51294e83da764648ec587b60328a370';
    const companyName = '上海逐风信息技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [1],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [6],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.rightRatio,
        fieldValue: [10],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【发明专利申请平均占比】-X=0', async () => {
    const companyId = 'f51294e83da764648ec587b60328a370';
    const companyName = '上海逐风信息技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [2],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [4],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.yearPeriod,
        fieldValue: [2, 3, 4],
        options: YearPeriodType,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.avgXn,
        fieldValue: [0],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【发明专利授权率】-0', async () => {
    const companyId = '09f59c1d2a035696131a8a322b15d594';
    const companyName = '深圳市星光达珠宝首饰实业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [3],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [6],
        options: [-1, 1, 2, 3, 4, 5, 6],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.rightRatio,
        fieldValue: [0],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【发明专利平均授权率】-X=0', async () => {
    const companyId = 'f51294e83da764648ec587b60328a370';
    const companyName = '上海逐风信息技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [4],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [4],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.yearPeriod,
        fieldValue: [2, 3, 4],
        options: YearPeriodType,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.avgXn,
        fieldValue: [0],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【发明专利发明人集中度】-0', async () => {
    const companyId = 'f51294e83da764648ec587b60328a370';
    const companyName = '上海逐风信息技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [5],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [6],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.rightRatio,
        fieldValue: [0],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【以转让方式获取的发明专利占比】-0', async () => {
    const companyId = 'f51294e83da764648ec587b60328a370';
    const companyName = '上海逐风信息技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [6],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [6],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.leftRatio,
        fieldValue: [0],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【发明专利申请稳定性】-最近3年1期，任意3个年度有专利申请', async () => {
    const companyId = '09f59c1d2a035696131a8a322b15d594';
    const companyName = '深圳市星光达珠宝首饰实业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [7],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [4],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.yearPeriod,
        fieldValue: [1, 2, 3, 4],
        options: YearPeriodType,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentStable,
        fieldValue: [3],
        options: PatentStableConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【发明专利申请稳定性】-最近3年1期，无专利申请', async () => {
    const companyId = 'f51294e83da764648ec587b60328a370';
    const companyName = '上海逐风信息技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentInfo, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [7],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [4],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.yearPeriod,
        fieldValue: [1, 2, 3, 4],
        options: YearPeriodType,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentStable,
        fieldValue: [0],
        options: PatentStableConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【本科以上招聘占比】-高X (≥80%)+ 低CV(＜20%)', async () => {
    const companyId = '6822a96d0b8c48ea09b0089993d973ab';
    const companyName = '深圳市芯存科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RecruitmentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.recruitmentStatistics,
        fieldValue: [1],
        options: RecruitmentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'publishtime', order: 'DESC', fieldSnapshot: 'PublishTime' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [3],
        options: [-1, 1, 2, 3, 4, 5],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.avgXn,
        fieldValue: [80],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cvXn,
        fieldValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.yearPeriod,
        fieldValue: [1, 2, 3],
        options: YearPeriodType,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail.length).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【本科以上招聘占比】-高X (≥80%)+ 高CV(>20%)', async () => {
    const companyId = '98a0b767e40c503ca158670d08bcf55e';
    const companyName = '深圳中微电科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RecruitmentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.recruitmentStatistics,
        fieldValue: [1],
        options: RecruitmentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'publishtime', order: 'DESC', fieldSnapshot: 'PublishTime' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [3],
        options: [-1, 1, 2, 3, 4, 5],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.avgXn,
        fieldValue: [80],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cvXn,
        fieldValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.yearPeriod,
        fieldValue: [1, 2, 3],
        options: YearPeriodType,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail.length).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【近三年新增股权融资情况】-A类投资者，投资主体包含“中国PE/VC行业评选”榜单机构、清科榜单机构或市级以上国资机构', async () => {
    const companyId = 'b939f96520e99c0388ef7d89e408c57f';
    const companyName = '深圳市海柔创新科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.EquityFinancing, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'financedate', order: 'DESC', fieldSnapshot: '"FinanceDate"' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [4],
        options: [-1, 1, 2, 3, 4, 5],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isInstitutionalInvestor,
        fieldValue: [1],
        options: IsInstitutionalInvestorConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sourcesInvestInstiteRank,
        fieldValue: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
        options: SourcesInvestInstiteRankConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail.length).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【近三年新增股权融资情况】-B类投资者，投资主体是具有产业链核心企业(上市公司、国央企、世界/中国500强企业，包含上述公司的关联公司)背景的投资机构', async () => {
    const companyId = '683d13b0c4985c4b06b3b07ac4d499ba';
    const companyName = '深圳第七大道科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.EquityFinancing, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'financedate', order: 'DESC', fieldSnapshot: '"FinanceDate"' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [4],
        options: [-1, 1, 2, 3, 4, 5],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isInstitutionalInvestor,
        fieldValue: [1],
        options: IsInstitutionalInvestorConstant,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isIndustrialChainCoreCompany,
        fieldValue: [1],
        options: IndustrialChainCoreCompanyContant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail.length).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【近三年新增股权融资情况】-C类投资者，无投资信息', async () => {
    const companyId = '272cfc15a4c50689cee16f8d643df3bb';
    const companyName = '百信信息技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.EquityFinancing, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'financedate', order: 'DESC', fieldSnapshot: '"FinanceDate"' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [4],
        options: [-1, 1, 2, 3, 4, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [0],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail.length).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【近三年新增股权融资情况】-C类投资者，其他投资机构', async () => {
    const companyId = '272cfc15a4c50689cee16f8d643df3bb';
    const companyName = '百信信息技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.EquityFinancing, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'financedate', order: 'DESC', fieldSnapshot: '"FinanceDate"' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [4],
        options: [-1, 1, 2, 3, 4, 5],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isInstitutionalInvestor,
        fieldValue: [2],
        options: IsInstitutionalInvestorConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【发明专利占比】[40%，60%)', async () => {
    const companyId = '3d4ee9d5361ff8e1de8ace51744a1a3e';
    const companyName = '深圳市深汇通能源科技发展有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [1],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [6],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.leftRatio,
        fieldValue: [40],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.rightRatio,
        fieldValue: [60],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【发明专利平均占比】高X(≥80%) + 低CV(≤20%)', async () => {
    const companyId = '98a0b767e40c503ca158670d08bcf55e';
    const companyName = '深圳中微电科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [2],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [4],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.yearPeriod,
        fieldValue: [2, 3, 4],
        options: YearPeriodType,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.avgXn,
        fieldValue: [80],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cvXn,
        fieldValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(1);
  });

  it('【发明专利发明人集中度】-[80%，100%]', async () => {
    const companyId = '04a6c4b8e26f2ad45fac5f80b2ac8aba';
    const companyName = '深圳市嘉和余庆科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [5],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [6],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.leftRatio,
        fieldValue: [80],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.rightRatio,
        fieldValue: [100],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【发明专利发明人集中度】-<20%', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [5],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [6],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.rightRatio,
        fieldValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【发明专利发明人集中度】-[60%，80%)', async () => {
    const companyId = '350d0c9177cc32936b630ef77446c4fd';
    const companyName = '深圳市中顺半导体照明有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        fieldValue: [5],
        options: PatentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [6],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.leftRatio,
        fieldValue: [60],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.rightRatio,
        fieldValue: [80],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【发明专利流出】-最近1年1期，有发明专利流出企业', async () => {
    const companyId = 'f4eced8c43e98548367c17095a4f4537';
    const companyName = '深圳微步信息股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentInfo, [
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [2],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isHistoryPatent,
        fieldValue: [1],
        options: IsHistoryPatentConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【发明专利流出】-最近1年1期，无发明专利流出企业', async () => {
    const companyId = 'f4eced8c43e98548367c17095a4f4537';
    const companyName = '深圳微步信息股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentInfo, [
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [2],
        options: [-1, 1, 2, 3, 4, 5, 6],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isHistoryPatent,
        fieldValue: [1],
        options: IsHistoryPatentConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [0],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail.length).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【有效PCT国际专利】-存在有效的PCT国际专利', async () => {
    const companyId = '6b242b475738f45a4dd180564d029aa9';
    const companyName = '华为技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.InternationPatent, [
      {
        fieldKey: DimensionFieldKeyEnums.internationPatentStatus,
        fieldValue: ['ZT005002', 'ZT006002'],
        options: InternationPatentStatusConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicatioDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail.length).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【有效PCT国际专利】-不存在有效的PCT国际专利', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.InternationPatent, [
      {
        fieldKey: DimensionFieldKeyEnums.internationPatentStatus,
        fieldValue: ['ZT005002', 'ZT006002'],
        options: InternationPatentStatusConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'applicationdate',
            order: 'DESC',
            fieldSnapshot: 'ApplicationDate',
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [0],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【减资】-75', async () => {
    const companyId = '02eb1742dcd2b4268628a95e786d4cab';
    const companyName = '东莞市益商文化发展有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateCapitalChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [37],
        options: [{ value: 37, label: '注册资本' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
        fieldValue: [
          {
            valuePeriodTrend: 1,
            valuePeriodThreShold: [[75]],
            valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
            valuePeriodBaseLine: 1,
          },
        ],
        options: [
          {
            valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
            valuePeriodThreShold: {
              label: '占比',
              value: { unit: '%', min: 0, max: 100 },
            },
            valuePeriodThreSholdCompareType: {
              label: '占比比较',
              value: DimensionFieldCompareTypeEnums.ContainsAny,
            },
            valuePeriodBaseLine: {
              label: '时间基准',
              value: BaseLineDateSelect,
            },
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【减资】-50-75', async () => {
    const companyId = '766e589464dcb1ea10d640b6108e03ab';
    const companyName = '江西赣水建设集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateCapitalChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [37],
        options: [{ value: 37, label: '注册资本' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
        fieldValue: [
          {
            valuePeriodTrend: 1,
            valuePeriodThreShold: [[50, 75]],
            valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
            valuePeriodBaseLine: 1,
          },
        ],
        options: [
          {
            valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
            valuePeriodThreShold: {
              label: '占比',
              value: { unit: '%', min: 0, max: 100 },
            },
            valuePeriodThreSholdCompareType: {
              label: '占比比较',
              value: DimensionFieldCompareTypeEnums.ContainsAny,
            },
            valuePeriodBaseLine: {
              label: '时间基准',
              value: BaseLineDateSelect,
            },
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【减资】-0-5', async () => {
    const companyId = '885b1ae500703fb5ffec926d33d9fc67';
    const companyName = '上海科众恒盛云计算科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateCapitalChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [37],
        options: [{ value: 37, label: '注册资本' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
        fieldValue: [
          {
            valuePeriodTrend: 1,
            valuePeriodThreShold: [[0, 5]],
            valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
            valuePeriodBaseLine: 1,
          },
        ],
        options: [
          {
            valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
            valuePeriodThreShold: {
              label: '占比',
              value: { unit: '%', min: 0, max: 100 },
            },
            valuePeriodThreSholdCompareType: {
              label: '占比比较',
              value: DimensionFieldCompareTypeEnums.ContainsAny,
            },
            valuePeriodBaseLine: {
              label: '时间基准',
              value: BaseLineDateSelect,
            },
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(0);
    const result = await riskChangeESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(0);
  });

  it('【被列入经营异常名录】-被列入经营异常名录', async () => {
    const companyId = 'eaa333ddcac0f17ac59ad2ea5ee85c09';
    const companyName = '新疆红杏生态农业（集团）有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAbnormal3, [
      {
        // 经营异常类型
        fieldKey: DimensionFieldKeyEnums.businessAbnormalType,
        fieldValue: ['0801', '0802', '0803', '0804', '0805', '0806', '0807'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        // 周期不限
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【注册资本实缴比例】-0-40', async () => {
    const companyId = '84e4f1d23d1350e1d5b08f6ea27bf624';
    const companyName = '苏州数琨创享信息技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.registrationRatio,
        accessScope: 2,
        options: registrationRatioType,
        fieldValue: [[0, 40]],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【注册资本实缴比例】-100', async () => {
    const companyId = '885b1ae500703fb5ffec926d33d9fc67';
    const companyName = '上海科众恒盛云计算科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.registrationRatio,
        accessScope: 2,
        options: registrationRatioType,
        fieldValue: [[100]],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【注册资本实缴比例】-0', async () => {
    const companyId = 'e464355ad73892937dc196e947e942e6';
    const companyName = '浙江耀厦控股集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.registrationRatio,
        accessScope: 2,
        options: registrationRatioType,
        fieldValue: [[0, 0]],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【有员工持股平台】-', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.EmployeeStockPlatform, [
      {
        fieldKey: DimensionFieldKeyEnums.HasEmployeeStockPlatform,
        accessScope: 2,
        options: hasEmployeeStockPlatform,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【股权结构异常】-', async () => {
    const companyId = '8cfdd3bec69c485c450cc3236fa4c83a';
    const companyName = '深圳市大恒数据安全科技有限责任公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.EquityStructureAbnormal, [
      {
        fieldKey: DimensionFieldKeyEnums.primaryShareholderAbnormality,
        accessScope: 2,
        options: [{}],
        fieldValue: [],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.secondaryShareholderAbnormality,
        accessScope: 2,
        options: [{}],
        fieldValue: [],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【股权结构异常】-关联方循环持股', async () => {
    const companyId = 'be56cbeed8f136f25545ea8b55d0f1e9';
    const companyName = '广东信基产业投资控股集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.EquityStructureAbnormal, [
      {
        fieldKey: DimensionFieldKeyEnums.CircularShareholdingBetweenRelatedParties,
        accessScope: 2,
        options: HasCompanyCircularShareholder,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【股权变更频率】>=3', async () => {
    const companyId = '766e589464dcb1ea10d640b6108e03ab';
    const companyName = '江西赣水建设集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainMembersChangeFrequency, [
      {
        fieldKey: DimensionFieldKeyEnums.equityChangeFrequency,
        accessScope: 2,
        fieldValue: [3],
        options: [{ unit: '个', min: 1, max: 50 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [6],
        options: [-1, 1, 2, 3, 4, 5],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(3);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(3);
  });

  it('【董监高法变更】=2', async () => {
    const companyId = 'bd41df27a893354c79b21f755f56506f';
    const companyName = '中建城开集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainMembersChangeFrequency, [
      {
        fieldKey: DimensionFieldKeyEnums.MainMembersChangeFrequency,
        accessScope: 2,
        fieldValue: [2],
        options: [{ unit: '个', min: 1, max: 50 }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [4],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(2);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(2);
  });

  it('【董监高法变更】<=1', async () => {
    const companyId = '885b1ae500703fb5ffec926d33d9fc67';
    const companyName = '上海科众恒盛云计算科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainMembersChangeFrequency, [
      {
        fieldKey: DimensionFieldKeyEnums.MainMembersChangeFrequency,
        accessScope: 2,
        fieldValue: [1],
        options: [{ unit: '个', min: 1, max: 50 }],
        compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [4],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isShowTip,
        fieldValue: [1],
        options: [
          { value: 1, label: '提示' },
          { value: 2, label: '不提示' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeLessThanOrEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeLessThanOrEqual(1);
  });

  it('【关联方企业集中注册或注销】>=3', async () => {
    const companyId = '8cfdd3bec69c485c450cc3236fa4c83a';
    const companyName = '深圳市大恒数据安全科技有限责任公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanyMassRegistrationCancellation, [
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        accessScope: 2,
        fieldValue: [3],
        options: [{ unit: '个', min: 1, max: 50 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [3],
        options: [-1, 1, 2, 3, 4, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(3);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【省级荣誉】>=5', async () => {
    const companyId = '9aad525a8212c14b74295827995a8e70';
    const companyName = '中车齐齐哈尔车辆有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ProvincialHonor, [
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        accessScope: 2,
        fieldValue: [5],
        options: [{ unit: '个', min: 1, max: 50 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(4);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
  it('【省级以上荣誉被取消】', async () => {
    const companyId = 'e42cb07961734830ee2b3bf238293ad2';
    const companyName = '山东中晟医疗器械科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ProvincialHonor, [
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [3],
        options: [-1, 1, 2, 3, 4, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.HasCertificationRevoked,
        accessScope: 2,
        options: hasCertificationRevoked,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
  it('【省级以上荣誉未被取消】', async () => {
    const companyId = '9fd5a931a6a5787fe79f826390f5a824';
    const companyName = '深圳爱递医药科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ProvincialHonor, [
      {
        fieldKey: DimensionFieldKeyEnums.naturalCycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.HasCertificationRevoked,
        accessScope: 2,
        options: hasCertificationRevoked,
        fieldValue: [0],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
});
