/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { HttpModuleOptions } from '@nestjs/axios';
import { PulsarMQSettings } from '@kezhaozhao/message-queue/dist/components/PulsarMQSettings';
import { AuthenticationToken } from 'pulsar-client';
import * as path from 'path';
import * as process from 'process';
import * as dotenvx from '@dotenvx/dotenvx';
import { KafkaConfig } from 'kafkajs';

@Injectable()
export class ConfigService {
  server: any;
  kzzServer: any;
  redis: any;
  typeorm: any;
  saasDB: any;
  jwt: any;
  jwtBO: any;
  accessCheck: {
    includes: string[];
  };
  proxyServer: any;
  dataServer: any;
  // domain: string;
  nodeEnv: string;
  stage: string;
  axiosConfig: HttpModuleOptions;
  searchDomain: string;
  pulsarMQ: PulsarMQSettings;
  mongodb: any;
  esConfig: any;
  roverGraphServer: any;
  kafkaClientConfig: KafkaConfig;
  kafkaTopic: any;

  constructor() {
    this.nodeEnv = process.env.NODE_ENV || 'local';
    // 加载主配置文件
    dotenvx.config({ path: '.env' });
    if (this.nodeEnv == 'local' && process.env.JEST_WORKER_ID) {
      // 加载本地配置文件，覆盖主配置中相同的变量
      dotenvx.config({ path: '.env.local', override: true });
    }
    // this.domain = process.env.DOMAIN || 'rover.dev.greatld.com';
    this.searchDomain = 'http://search.test.greatld.com';
    this.stage = process.env.STAGE || 'test';
    this.proxyServer = {
      // AI数据地址
      dataService: process.env.QCCDATASERVICE || 'http://nodejs-qcc-backend-data.sit.office.qichacha.com',
      roverService: process.env.QCCROVERSERVICE || 'http://rover-rover-backend.sit.office.qichacha.com',
      riskService: process.env.QCCRISKSERVICE || 'http://qcc-risk-app-risk-api.sit.office.qichacha.com',
      bigDataService: process.env.BigDataSERVICE || 'http://bigdataapi.ld-hadoop.com',
      graphService: process.env.GRAPHSERVICE || 'http://nodejs-qcc-app-graph.sit.office.greatld.com',
      // userService: process.env.QCCUSERSERVICE || 'http://qcc-user-app-user-api.sit.office.qichacha.com',
    };
    this.dataServer = {
      // GET 公司详情接口
      companyDetail: `${this.proxyServer.dataService}/api/ECILocal/GetDetail`,
      companyInfo: `${this.proxyServer.dataService}/api/QccSearch/List/KeyNosByNames`,
      searchAdvance: `${this.proxyServer.dataService}/api/ECILocal/SearchMultiSelection`,
      // 获取公司各维度数据当前count
      getCountInfo: `${this.proxyServer.dataService}/api/ECILocal/getCountInfo`,
      // 获取公司各维度数据历史count
      getHistoryCountInfo: `${this.proxyServer.dataService}/api/History/GetCoyHistoryCountInfo`,
      // 信用评价列表 （主体信用评级 + 债券评级）
      getCreditRating: `${this.proxyServer.roverService}/api/dimension/enterprise-qualification/get-credit-rating-view-list`,
      // 纳税信用等级
      getTaxCredit: `${this.proxyServer.roverService}/api/dimension/enterprise-qualification/get-tax-credit-list`,
      // 海关评级 进出口信用详情
      getImportExport: `${this.proxyServer.roverService}/api/dimension/enterprise-qualification/get-import-export-detail`,
      // 查询信用大数据
      searchCredit: `${this.proxyServer.roverService}/api/search/search-credit`,
      // 任意文本解析公司名列表
      getCoyList: `${this.proxyServer.dataService}/api/ECILocal/GetCoyListWithFreeText`,
      // 根据keynos获取logo
      getLogoByKeyNos: `${this.proxyServer.dataService}/api/ECILocal/GetShortNameByKeyNos`,
      //企业信用评分详情
      getCreditRate: `${this.proxyServer.dataService}/api/ECILocal/GetCreditRate`,
      //人企核验
      getPersonAccReport: `${this.proxyServer.bigDataService}/api/personCompany/getPersonAccReport`,
    };
    this.server = {
      mailerService: {
        host: process.env.MAIL_SERVICE_HOST,
        port: process.env.MAIL_SERVICE_PORT,
        secure: true,
        auth: {
          user: process.env.MAIL_SERVICE_USER,
          pass: process.env.MAIL_SERVICE_PASS,
        },
      },
      smsService: {
        msgService: process.env.MSGSERVICE || 'http://nodejs-qcc-msg-server.sit.office.qichacha.com',
        riskMonitorTplId: 'SMS_478465017',
      },
      oss: {
        region: 'oss-cn-hangzhou',
        accessKeyId: process.env.OSS_ACCESS_KEY_ID,
        endpoint: ['prod', 'release'].includes(this.nodeEnv) ? 'https://oss-cn-hangzhou-internal.aliyuncs.com/' : undefined,
        internal: ['prod', 'release'].includes(this.nodeEnv),
        accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
        bucket: 'kezhaozhao-data',
        urlExpires: 24 * 3600,
        secure: true,
        timeout: 120 * 1000, // 120s
        prefix: 'rover',
      },
      tender: {
        baseUrl: process.env.TENDER_SERVICE,
        token: process.env.TENDER_SERVICE_TOKEN,
      },
      // 调用专业版接口配置
      qccPro: {
        loginName: process.env.QCC_PRO_LOGIN_NAME,
        baseUrl: process.env.QCC_PRO_BASE_URL,
        key: process.env.QCC_PRO_SERVICE_KEY,
        secretKey: process.env.QCC_PRO_SERVICE_SECRET_KEY,
      },
      comDomainService: process.env.COMDOMAIN || 'http://172.16.183.131:8800/index/',
      extDomainService: process.env.EXTDOMAIN || 'http://172.16.183.131:9999/index/',
      ssoService: process.env.SSOSERVICE || 'http://172.16.183.131:8800/index/',
      appService: process.env.APPSERVICE || 'http://172.16.183.131:9900/app/',
      wxQccDomainService: process.env.WXQCCDOMAIM || 'http://wxapi.qichacha.com/owx/api/wechat/',
      wxAdminService: process.env.WXADMINSERVICE || 'http://wxapi.qichacha.com/owx/v1/admin',
      saasService: process.env.SAASSERVICE || 'http://qcc-user-app-user-api.sit.office.qichacha.com',
      bossService: process.env.BOSSSERVICE || 'http://boss.greatld.com:81',
    };
    this.kzzServer = {
      enterpriseService: process.env.ENTERPRISE_SERVICE_V2 || 'http://e.test.greatld.com/qcc/e',
      bundleService: process.env.SAAS_BUNDLE_SERVICE || 'http://api.test.greatld.com/api/bundle',
      authService: process.env.AUTH_SERVICE || 'http://api.test.greatld.com/qcc/auth',
      roverGraphService: process.env.ROVER_GRAPH_SERVICE || 'http://api.test.greatld.com/graph/rover',
      pdfService: process.env.PDF_SERVICE || 'http://api.test.greatld.com/qcc/pdf',
      companySearchApi: process.env.COMPANY_SEARCH_API || this.searchDomain,
    };
    this.roverGraphServer = {
      // 初始化组织
      initOrg: `${this.kzzServer.roverGraphService}/init`,
      partner: `${this.kzzServer.roverGraphService}/partner`,
      blacklist: `${this.kzzServer.roverGraphService}/blacklist`,
      blacklistBatch: `${this.kzzServer.roverGraphService}/blacklist/batch`,
      invest: `${this.kzzServer.roverGraphService}/invest`,
      direct: `${this.kzzServer.roverGraphService}/direct`,
      syncManually: `${this.kzzServer.roverGraphService}/sync/manually`,
      relations: `${this.kzzServer.roverGraphService}/relations`,
      customerFinalBenefit: `${this.kzzServer.roverGraphService}/finalBenefit/customer`,
      blacklistFinalBenefit: `${this.kzzServer.roverGraphService}/finalBenefit/blacklist`,
      partnerBranch: `${this.kzzServer.roverGraphService}/branch/partner`,
      blacklistBranch: `${this.kzzServer.roverGraphService}/branch/blacklist`,
      blacklistBranchBatch: `${this.kzzServer.roverGraphService}/branch/blacklist/batch`,
      customerInvestigation: `${this.kzzServer.roverGraphService}/customer/investigation`,
      blacklistInvestigation: `${this.kzzServer.roverGraphService}/blacklist/investigation`,
      customerPotentiallyInvestigation: `${this.kzzServer.roverGraphService}/customer/potentially/interested`,
      blacklistPotentiallyInvestigation: `${this.kzzServer.roverGraphService}/blacklist/potentially/interested`,
      deepRelationsWithEnd: `${this.kzzServer.roverGraphService}/deep/relations/withEnd`,
    };
    this.redis = {
      host: process.env.REDIS_HOST,
      port: process.env.REDIS_PORT,
      db: process.env.REDIS_DB,
      password: process.env.REDIS_PASSWD,
      keyPrefix: 'dd-platform:',
      enableReadyCheck: true,
      // retryStrategy: (times) => {
      //   return Math.min(times * 200, 5000) || 500;
      // },
      onClientReady: (client) => {
        // client.on('close', () => {
        //   configLogger.error('redis connection is closed');
        // });
        client.on('reconnecting', async (error) => {
          // const message.handler = `redis is reconnecting with delay: ${error}`;
          // configLogger.error(message.handler);
        });

        client.on('connect', () => {
          // configLogger.info('redis client is connected');
        });
        client.on('error', (err) => {
          // configLogger.error(`redis error: ${err}`);
        });
      },
      reconnectOnError: function (err) {
        // configLogger.error('redis reconnectOnError', err);
        // const targetError = 'READONLY';
        // if (err.message.handler.includes(targetError)) {
        //   // Only reconnect when the error contains "READONLY"
        //   return true; // or `return 1;`
        // }
        // return 2;
      },
    };
    this.typeorm = {
      type: 'mysql',
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      username: process.env.DB_USER,
      password: process.env.DB_PASSWD,
      database: process.env.DATABASE,
      saasDB: process.env.DATABASE_BUNDLE,
      charset: 'utf8mb4',
      synchronize: false,
      logging: false,
      multipleStatements: true,
      entities: [path.join(__dirname, '../entities/**/*.{js,ts}')],
      cache: {
        duration: 30000, // 默认缓存30s(当明确开启缓存的时候)
        type: 'ioredis',
        options: this.redis,
      },
    };
    /* this.saasDB = {
      type: 'mysql',
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      username: process.env.DB_USER,
      password: process.env.DB_PASSWD,
      database: process.env.DATABASE_BUNDLE,
      charset: 'utf8mb4',
      synchronize: false,
      logging: false,
      entities: [path.join(__dirname, '../saasentities/!**!/!*.{js,ts}')],
      cache: {
        duration: 600000,
        type: 'ioredis',
        options: this.redis,
      },
    };*/
    this.kafkaClientConfig = {
      clientId: `${process.env.NODE_ENV || 'test'}_dd_platform`,
      ssl: false,
      sasl: {
        mechanism: 'scram-sha-256', // scram-sha-256 or scram-sha-512
        username: process.env.KAFKA_SASL_USER,
        password: process.env.KAFKA_SASL_PWD,
      },
      brokers: process.env.KAFKA_BROKERS_GROUP ? process.env.KAFKA_BROKERS_GROUP.split(',') : [],
    };
    this.kafkaTopic = {
      strategicCustomerMonitor: {
        name: 'kezz_strategic_customer_monitor',
        producerConfig: {},
      },
    };
    this.jwt = {
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '604800s' },
    }; // expire after 1 week
    this.jwtBO = {
      secret: process.env.JWT_SECRET_BO || 'qcc-sub-module-xzfslkjfskl;dkgksa-test',
      signOptions: { expiresIn: '604800s' },
    }; // expire after 1 week
    this.accessCheck = {
      includes: ['**/(diligence|batch|settings|company)/**'],
    };
    this.axiosConfig = {
      timeout: 28000, // 5s
      headers: {
        'x-kzz-request-from-server': 'dd-platform-service',
        'x-request-from-app-name': 'dd-platform-service',
        'x-request-from-head-app-name': 'dd-platform-service',
      },
    };
    this.esConfig = {
      violation: {
        nodes: process.env.ES_CREDIT_READ,
        indexName: 'financial_sk_violation_disp_query',
        indexType: '_doc',
      },
      bundleUsage: {
        nodesQuery: process.env.KZZ_ES_NODES_READ,
        nodesWrite: process.env.KZZ_ES_NODES_WRITE,
        indexName: process.env.BUNDLE_ES_INDEX || 'saas_bundle_usage_daily-test',
        indexType: '_doc',
      },
      credit: {
        nodes: process.env.ES_CREDIT_READ,
        indexName: 'rover_credit_check_query',
        indexType: '_doc',
      },
      finance: {
        nodes: process.env.ES_CREDIT_READ,
        indexName: 'qfk_finc_corp_stat_query',
        indexType: '_doc',
      },
      blacklist: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'kys_blacklist_query',
        indexType: '_doc',
      },
      asset: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'risk_movable_property_seizure_query',
        indexType: '_doc',
      },
      pledgeMerger: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'kzz_comp_busi_chattle_query',
        indexType: '_doc',
      },
      case: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'case_search_query',
        indexType: '_doc',
      },
      supervisePunish: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'kys_risk_supervise_punish_administrative_merge_query',
        indexType: '_doc',
      },
      bidCollusive: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'kys_risk_collusive_comps_query',
        indexType: '_doc',
      },
      negativeNews: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'new_merchants_negative_news_query',
        indexType: '_doc',
      },
      judgement: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'mongo_judgements_query',
        indexType: '_doc',
      },
      snapshot: {
        nodesQuery: process.env.ES_NODES_SNAPSHOT_READ,
        nodesWrite: process.env.ES_NODES_SNAPSHOT_WRITE,
        indexName: process.env.ES_INDEX_SNAPSHOT || 'kys_snapshot_test',
        indexType: '_doc',
      },
      rickChangeCopy: {
        nodes: 'http://172.16.61.21:10000',
        indexName: 'risk_changelist_copy',
        indexType: '_doc',
      },
      metricDynamics: {
        nodesQuery: process.env.ES_NODES_SNAPSHOT_READ,
        nodesWrite: process.env.ES_NODES_SNAPSHOT_WRITE,
        indexName: process.env.ES_INDEX_METRIC_DYNAMICS || 'kys_metric_dynamics_test',
        indexType: '_doc',
      },
      diligenceAnalyze: {
        nodesQuery: process.env.ES_NODES_SNAPSHOT_READ,
        nodesWrite: process.env.ES_NODES_SNAPSHOT_WRITE,
        indexName: process.env.ES_INDEX_ANALYZE || 'kys_diligence_analyze_test',
        indexType: '_doc',
      },
      kysCompany: {
        nodes: process.env.ES_K8S_KZZ_READ,
        indexName: 'kys_company_query',
        indexType: '_doc',
      },
      tax: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'list_search_query',
        indexType: '_doc',
      },
      riskChangeList: {
        nodes: process.env.ES_RISK_CHANGE_READ,
        indexName: 'risk_changelist_query',
        indexType: '_doc',
      },
      pledge: {
        nodes: process.env.ES_K8S_KZZ_READ,
        indexName: 'companyrisk_pledge_new_query', // 股权出质
        indexType: '_doc',
      },
      ovsSanctions: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'kys_comp_ovs_sanctions_query',
        indexType: '_doc',
      },
    };
    this.pulsarMQ = {
      tenant: 'kezz',
      namespace: process.env.PULSAR_NS,
      clientConfig: {
        messageListenerThreads: 10,
        ioThreads: 2,
        serviceUrl: process.env.PULSAR_URL,
        authentication: new AuthenticationToken({ token: process.env.PULSAR_TOKEN }),
      },
    };

    this.mongodb = {
      uri: process.env.MONGODB_URL,
      dbName: process.env.MONGODB_DBNAME,
      options: {
        useUnifiedTopology: true,
        auth: { user: process.env.MONGODB_USER, password: process.env.MONGODB_PASSWORD },
      },
    };
  }

  getOssObject(folderName: string, filename?: string, ossStage?: string) {
    if (filename) {
      return `rover/${ossStage || this.nodeEnv}/${folderName}/${filename}`;
    }
    return `rover/${ossStage || this.nodeEnv}/${folderName}`;
  }
}
