# include:
#   - project: 'kezhaozhao/tools/qcc-deployment'
#     ref: static
#     file: '/gitlab/template.nodejs.v3.yml'

include:
  local: 'gitlab-template-docker.yml'

variables:
  SENTRY_DSN: http://<EMAIL>/34
  DEPENDENCIES_IMAGE: harbor-in.greatld.com/kezhaozhao/kzz-node:18-bullseye-slim
  SERVICE_IMAGE_BASE: $HARBOR_REPO/$CI_PROJECT_NAME:base-1.3.3
  NS_PROD: 'rover'
  NS_RELEASE: 'release'
  CLUSTER_PROD: 'rover'
  SONAR_TOKEN: 'sqp_e159b34d112ebca7a044b5943f4ba3b6a387cdf3'
  MOBILE_NUMBERS: '18626272086'
  SKIP_UNITTEST: 'false'
  MYSQL_ROOT_PASSWORD: 'kezhaozhao_dev'
  MYSQL_USER: 'kezhaozhao_dev'
  MYSQL_PASSWORD: 'kezhaozhao_dev'
  MYSQL_DATABASE: 'qcc_scorecard_test'
  REDIS_PASSWORD: 'yourpassword'
  REPORT_BASE_PATH: '/reports'
  REPORT_BASE_URL: 'http://localhost:8000'
  # 服务连接配置
  DB_HOST: mysql
  DB_PORT: 3306
  REDIS_HOST: redis
  REDIS_PORT: 6379
  ES_HOST: elasticsearch
  ES_PORT: 9200

.tags_job:
  tags:
    - kezhaozhao_idc

.unittest_reporter_tag:
  tags:
    - runner_backend_group1

# 项目特定的单元测试配置
.project_unittest_base:
  extends: .unittest_reporter
  services:
    - name: mysql:8.0
      alias: mysql
      command:
        ['--default-authentication-plugin=mysql_native_password', '--character-set-server=utf8mb4', '--collation-server=utf8mb4_unicode_ci', "--sql-mode=''"]
      entrypoint: ['docker-entrypoint.sh', '--mount', 'type=bind,source=$CI_PROJECT_DIR/scripts/test/init_sql,target=/docker-entrypoint-initdb.d']
      environment:
        MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
        MYSQL_USER: ${MYSQL_USER}
        MYSQL_PASSWORD: ${MYSQL_PASSWORD}
        MYSQL_DATABASE: ${MYSQL_DATABASE}
        MYSQL_SQL_MODE: ''

    - name: redis:7.2
      alias: redis
      command: ['--requirepass', '${REDIS_PASSWORD}', '--appendonly', 'no']

    - name: elasticsearch:7.17.11
      alias: elasticsearch
      environment:
        discovery.type: single-node
        ES_JAVA_OPTS: '-Xms512m -Xmx512m'

  tags:
    - runner_backend_group1
  before_script:
    # 基础准备（来自模板）
    - echo "开始单元测试报告处理准备(.unittest_reporter before-script)"
    - PROJECT_NAME=${CI_PROJECT_NAME}
    - >
      current_time=$(date +"%Y-%m-%d %H:%M:%S");
      echo "current_time: $current_time";
      echo "export START_TIME='${current_time}';" >> sonarqube_vars.sh;
    - apt-get update && apt-get install -y git curl bc
    # 项目特定的环境准备
    - echo "开始项目特定的环境准备"

    # 安装依赖
    - yarn install --ignore-optional

    # 等待并准备 ES 模板
    - >
      echo "等待 Elasticsearch 启动..."
      until curl -s "http://$ES_HOST:$ES_PORT/_cluster/health?wait_for_status=yellow&timeout=30s" > /dev/null; do
        sleep 2
      done
      echo "Elasticsearch 已就绪"

    # 加载 ES 模板
    - >
      echo "加载 ES 模板..."
      curl -XPUT "http://$ES_HOST:$ES_PORT/_template/kys_metric_dynamics-template" \
        -H "Content-Type: application/json" \
        -d "@$CI_PROJECT_DIR/scripts/test/es_template_init/metrics.dynamic.index.template.json"
      curl -XPUT "http://$ES_HOST:$ES_PORT/_template/kys_snapshot_-template" \
        -H "Content-Type: application/json" \
        -d "@$CI_PROJECT_DIR/scripts/test/es_template_init/snapshot.index.template.v5.json"

    # 创建 ES 索引
    - >
      echo "创建 ES 索引..."
      curl -XPUT "http://$ES_HOST:$ES_PORT/kys_metric_dynamics_test"
      curl -XPUT "http://$ES_HOST:$ES_PORT/kys_snapshot_test"

    # 创建 ES 别名
    - >
      echo "创建 ES 别名..."
      curl -XPOST "http://$ES_HOST:$ES_PORT/_aliases" -H "Content-Type: application/json" -d'
        {
        "actions": [
            { "add": { "index": "kys_metric_dynamics_test", "alias": "kys_metric_dynamics_test_write" } },
            { "add": { "index": "kys_metric_dynamics_test", "alias": "kys_metric_dynamics_test_query" } }
        ]
        }'
      curl -XPOST "http://$ES_HOST:$ES_PORT/_aliases" -H "Content-Type: application/json" -d'
        {
        "actions": [
            { "add": { "index": "kys_snapshot_test", "alias": "kys_snapshot_test_write" } },
            { "add": { "index": "kys_snapshot_test", "alias": "kys_snapshot_test_query" } }
        ]
        }'

    # 验证服务是否可用
    - echo "检查 MySQL 连接..."
    - apt-get install -y netcat-openbsd
    - nc -z $DB_HOST $DB_PORT || (echo "MySQL 不可用" && exit 1)
    - echo "检查 Redis 连接..."
    - nc -z $REDIS_HOST $REDIS_PORT || (echo "Redis 不可用" && exit 1)

# 重写模板中的测试任务，使用项目特定配置
unittest_commit:
  extends:
    - .project_unittest_base
  rules:
    - if: $SKIP_UNITTEST == "true" || $CI_PIPELINE_SOURCE  == "schedule" || $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "gitlab/debug"
      allow_failure: true
  script:
    - changed_files=$(git diff --name-only HEAD~${NUM_COMMITS} HEAD)
    - echo ${changed_files}
    - >
      echo "export GitLab_Job_Type=2;" >> sonarqube_vars.sh;
      if echo "$changed_files" | grep -q '\.sql$\'; then
         echo "发现有新的SQL变更文件:";
      fi

      if  echo "$changed_files" | grep -q '\.ts$\|\.tsx$'; then
        echo "发现有新的变更文件:";
        echo "-----------";
        echo "-----------";
        echo "$changed_files";
        echo "-----------";
        echo "-----------";
        echo "开始执行单元测试...";
        MOCK_MESSAGE_QUEUE='true'
        if [ "$USE_VITEST" == "true" ]; then
          ./node_modules/.bin/vitest related --passWithNoTests --run $changed_files || status=$?;
        else
          echo "生成.env.key 加载本地环境变量...";
          echo "DOTENV_PRIVATE_KEY=f6e1a0408c7ede370726aafa55c036b61738146709c995f91522f1fed170669e;" >> .env.keys;
          ./node_modules/.bin/jest --maxWorkers 7 --maxConcurrency 1 --findRelatedTests $changed_files --passWithNoTests || status=$?;
        fi
      else
        echo "-----------";
        echo "-----------";
        echo "没有发现有效的变更文件，跳过单元测试...";
        echo "-----------";
        echo "-----------";
        echo "export No_Active_Tests=true;" >> sonarqube_vars.sh;
      fi

      if [ -z "$status" ]; then
        status=0;
      fi

      echo "输出 sonarqube_vars.sh:";
      cat sonarqube_vars.sh;
      echo "export TEST_STATUS=${status};" >> sonarqube_vars.sh;
      echo "export DINGTALK_NOTIFY_WHEN_SUCCESS=false;" >> sonarqube_vars.sh;
      echo "export SONAR_REPORT_ENABLE=false;" >> sonarqube_vars.sh;

      echo "写入环境变量...";
      echo "status: ${status};"
      echo "TEST_STATUS: ${status};"

unittest_master:
  extends:
    - .project_unittest_base
  rules:
    - if: $SKIP_UNITTEST == "true" || $CI_PIPELINE_SOURCE  == "schedule"
      when: never
    - if: $CI_COMMIT_REF_NAME == "master"
      when: manual
      allow_failure: true
  script:
    - echo "run unit test(unittest_master)"
    - >
      if [ "$USE_VITEST" == "true" ]; then
        ./node_modules/.bin/vitest run --coverage --coverage.reportOnFailure --passWithNoTests || status=$?;
      else
        echo "生成.env.key 加载本地环境变量...";
        echo "DOTENV_PRIVATE_KEY=f6e1a0408c7ede370726aafa55c036b61738146709c995f91522f1fed170669e;" >> .env.keys;
        echo "开始执行单元测试...";
        ./node_modules/.bin/jest --coverage --passWithNoTests --maxConcurrency 1 --maxWorkers 7 || status=$?;
      fi;

      if [ -z "$status" ]; then
        status=0;
      fi;

      echo "export SONAR_REPORT_ENABLE=true;" >> sonarqube_vars.sh;
      echo "export TEST_STATUS=${status};" >> sonarqube_vars.sh;
      echo "export DINGTALK_NOTIFY_WHEN_SUCCESS=true;" >> sonarqube_vars.sh;
      echo "export GitLab_Job_Type=5;" >> sonarqube_vars.sh;
      echo "写入环境变量...";
      echo "status: ${status};"
      echo "TEST_STATUS: ${status};"

unittest_manually:
  extends:
    - .project_unittest_base
  rules:
    - if: $SKIP_UNITTEST == "true" || $CI_PIPELINE_SOURCE  == "schedule" || $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME == "unittest-debug"
      when: manual
      allow_failure: true
  script:
    - echo "run unit test(unittest_manually)"
    - >
      if [ "$USE_VITEST" == "true" ]; then
        ./node_modules/.bin/vitest run --coverage --coverage.reportOnFailure --passWithNoTests || status=$?;
      else
        echo "生成.env.key 加载本地环境变量...";
        echo "DOTENV_PRIVATE_KEY=f6e1a0408c7ede370726aafa55c036b61738146709c995f91522f1fed170669e;" >> .env.keys;
        echo "开始执行单元测试...";
        ./node_modules/.bin/jest --coverage --passWithNoTests --maxConcurrency 1 --maxWorkers 7 || status=$?;
      fi;

      if [ -z "$status" ]; then
        status=0;
      fi;

      echo "export SONAR_REPORT_ENABLE=true;" >> sonarqube_vars.sh;
      echo "export TEST_STATUS=${status};" >> sonarqube_vars.sh;
      echo "export DINGTALK_NOTIFY_WHEN_SUCCESS=true;" >> sonarqube_vars.sh;
      echo "export GitLab_Job_Type=4;" >> sonarqube_vars.sh;
      echo "写入环境变量...";
      echo "status: ${status};"
      echo "TEST_STATUS: ${status};"

unittest_daily:
  extends:
    - .project_unittest_base
  rules:
    - if: $CI_PIPELINE_SOURCE  == "schedule"
      allow_failure: true
  script:
    - echo "run unit test(unittest_daily)"
    - >
      if [ "$(date +%u)" -eq 5 ]; then
        echo "今天是周五，开启Sonarqube扫描.";
        echo "export SONAR_REPORT_ENABLE=true;" >> sonarqube_vars.sh;
        echo "export DINGTALK_NOTIFY_WHEN_SUCCESS=true;" >> sonarqube_vars.sh;
      else
        echo "今天不是周五，关闭Sonarqube扫描.";
        echo "export SONAR_REPORT_ENABLE=false;" >> sonarqube_vars.sh;
        echo "export DINGTALK_NOTIFY_WHEN_SUCCESS=false;" >> sonarqube_vars.sh;
      fi

      echo "export TEST_STATUS=${status};" >> sonarqube_vars.sh;
      echo "export GitLab_Job_Type=1;" >> sonarqube_vars.sh;

      if [ "$USE_VITEST" == "true" ]; then
        ./node_modules/.bin/vitest run --coverage --coverage.reportOnFailure || status=$?;
      else
        echo "生成.env.key 加载本地环境变量...";
        echo "DOTENV_PRIVATE_KEY=f6e1a0408c7ede370726aafa55c036b61738146709c995f91522f1fed170669e;" >> .env.keys;
        echo "开始执行单元测试...";
        ./node_modules/.bin/jest --coverage --maxConcurrency 1 --maxWorkers 7 || status=$?;
      fi;

      if [ -z "$status" ]; then
        status=0;
      fi;

      echo "写入环境变量...";
      echo "status: ${status};"
      echo "TEST_STATUS: ${status};"
