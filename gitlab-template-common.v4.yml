variables:
  NUM_COMMITS: 5
  DingTalk_Webhook: 'https://oapi.dingtalk.com/robot/send?access_token=e6b3b6fcc902e23e33b68ad6c8e61aa38983a6904963819d1bf3d7fdad0b77b3'
  Contact_Json_Map: '{"<EMAIL>":"17612125306", "<EMAIL>":"18888178160","<EMAIL>":"18626272086","<EMAIL>":"18625000947","<EMAIL>":"13584406778","<EMAIL>":"13451631369","<EMAIL>":"18662525509","<EMAIL>":"13512129425","<EMAIL>":"17612542237","<EMAIL>":"13771867411","<EMAIL>":"13917837146","<EMAIL>":"13917837146","<EMAIL>":"15755382203","<EMAIL>":"18961889165","<EMAIL>":"13151991219","<EMAIL>":"15150268315","<EMAIL>":"15295620612","<EMAIL>":"17612125306", "<EMAIL>":"18888178160","<EMAIL>":"18626272086","<EMAIL>":"18625000947","<EMAIL>":"13584406778","<EMAIL>":"13451631369","<EMAIL>":"18662525509","<EMAIL>":"13512129425","<EMAIL>":"17612542237","<EMAIL>":"13771867411","<EMAIL>":"13917837146","<EMAIL>":"13917837146","<EMAIL>":"15755382203","<EMAIL>":"18961889165","<EMAIL>":"13151991219","<EMAIL>":"15150268315","<EMAIL>":"15295620612","<EMAIL>":"15295620612"}'
  Name_Json_Map: '{"<EMAIL>":"戴文军", "<EMAIL>":"单怡然","<EMAIL>":"许利强","<EMAIL>":"闫东旭","<EMAIL>":"贾理国","<EMAIL>":"陈小飞","<EMAIL>":"刘建伟","<EMAIL>":"葛荣银","<EMAIL>":"吕诗文","<EMAIL>":"李冬","<EMAIL>":"张晨诚","<EMAIL>":"张晨诚","<EMAIL>":"彭猛","<EMAIL>":"季嘉成","<EMAIL>":"程森然","<EMAIL>":"陈楠","<EMAIL>":"丁煜超","<EMAIL>":"戴文军", "<EMAIL>":"单怡然","<EMAIL>":"许利强","<EMAIL>":"闫东旭","<EMAIL>":"贾理国","<EMAIL>":"陈小飞","<EMAIL>":"刘建伟","<EMAIL>":"葛荣银","<EMAIL>":"吕诗文","<EMAIL>":"李冬","<EMAIL>":"张晨诚","<EMAIL>":"张晨诚","<EMAIL>":"彭猛","<EMAIL>":"季嘉成","<EMAIL>":"程森然","<EMAIL>":"陈楠","<EMAIL>":"丁煜超","<EMAIL>":"丁煜超"}'
  NODE_VERSION: 'v18.20.8'
  SKIP_UNITTEST: 'false'

.unittest_reporter_tag:
  tags:
    - idc_runner_ssh

.unittest_reporter:
  stage: sonarqube
  extends: .unittest_reporter_tag
  artifacts:
    name: 'sonarqube_env_artifact'
    expire_in: 3 days
    paths:
      - sonarqube_vars.sh
  before_script:
    - echo "here is before script(.unittest_reporter) ..................................................................................."
    - source ~/.bashrc
    - echo $PATH
    - which node
    - node -v
    - yarn -v
    - echo "run unit test(.unittest_reporter before-script)"
    - PROJECT_NAME=${CI_PROJECT_NAME};
    - cache_dir="/home/<USER>/node_modules_shared/${PROJECT_NAME}";
    - >
      current_time=$(date +"%Y-%m-%d %H:%M:%S");
      echo "current_time: $current_time";
      echo "export START_TIME='${current_time}';" >> sonarqube_vars.sh;
      if [ ! -d "$cache_dir" ]; then
       mkdir -p $cache_dir | true;
      fi
    - cp -rf package.json .npmrc $cache_dir/
    - >
      if [ -f yarn.lock ]; then
        cp -rf yarn.lock $cache_dir/
      fi
    - >
      if [ -f .nvmrc ]; then
        cp -rf .nvmrc $cache_dir/
      fi
    - ls $cache_dir
    - cd $cache_dir
    - >
      if [ "$USE_VITEST" == "true" ]; then
        yarn
      else
        yarn install --ignore-optional
      fi;
    - cd ${CI_PROJECT_DIR};
    - ln -s $cache_dir/node_modules node_modules || true
    # - yarn unittest:prepare || true
  #    - ls -lh
  after_script:
    - echo "here is after script(.unittest_reporter) ..................................................................................."
    - |
      convert_mobile_numbers() {
          local input_numbers="$1"
          local result=""

          IFS=',' read -ra ADDR <<< "$input_numbers"
          for number in "${ADDR[@]}"; do
              result+="@$number,"
          done
          result="${result%,}"
          echo "$result"
      }
    - |
      extract_value_by_email() {
          local email_list="$1"
          local contact_json_map="$2";
          local result=""
          IFS=',' read -r -a email_array <<< "$email_list"
          for email in "${email_array[@]}"; do
              phone_number=$(echo "${contact_json_map}" | grep -o "\"${email}\": *\"[^\"]*\"" | cut -d '"' -f 4)
              if [ -n "${phone_number}" ]; then
                  result+="${phone_number},"
              else
                  result+="${email},"
              fi
          done

          result="${result%,}"
          echo "${result}"
      }
    - |
      is_phone_number() {
          local number=$1
          # 检查是否是11位数字
          if [[ "$number" =~ ^[0-9]{11}$ ]]; then
              return 0
          else
              return 1
          fi
      }

      # 合并两个逗号分隔的字符串，只保留手机号格式的号码，并去重
      merge_and_filter_numbers() {
          local numbers1="$1"
          local numbers2="$2"
          local merged_numbers=""

          # 合并两个逗号分隔的字符串为一个新的字符串
          merged_string="${numbers1},${numbers2}"

          # 分割字符串为数组，以逗号为分隔符
          IFS=',' read -ra merged_list <<< "$merged_string"

          # 声明一个关联数组用于去重
          declare -A seen_numbers

          # 遍历合并后的列表，去重和过滤非手机号格式的号码
          for number in "${merged_list[@]}"; do
          # 去除空格
          number=$(echo "$number" | tr -d ' ')

          # 检查是否是手机号格式且未在已见过的列表中
          if is_phone_number "$number" && [ -z "${seen_numbers[$number]}" ]; then
          # 标记为已见过
          seen_numbers[$number]=1

          # 添加到结果字符串中
          if [ -n "$merged_numbers" ]; then
          merged_numbers+=",${number}"
          else
          merged_numbers="${number}"
          fi
          fi
          done

          echo "$merged_numbers"
      }
    - |
      generate_message() {
          local is_success="$1"
          local suffix1="$2"
          local project_name="$3"
          local repo_url="$4"
          local ci_pipeline_id="$5"
          local ci_job_id="$6"
          local suffix2="$7"
          local ci_commit_ref_slug="$8"
          local at_current_committer_numbers="$9"
          local at_project_owner_mobiles="${10}"
          local related_committers_numbers="${11}"
          local details_text="${12}"
          local start_time="${13}"
          local end_time="${14}"

          local message=""
          if [ "$is_success" == "true" ]; then
              message="## ✅单元测试执行成功(${suffix1})\n\n"
          else
              message="## ❌单元测试执行失败(${suffix1})\n\n"
          fi

          message+="**概要** \n\n"
          message+="- **项目**: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;[${project_name}](${repo_url}) \n\n"
          message+="- **当前任务**:&nbsp;&nbsp;[${ci_pipeline_id}](${repo_url}/-/jobs/${ci_job_id}) \n\n"
          message+="- **代码范围**:&nbsp;&nbsp;${suffix2} \n\n"
          message+="- **当前分支**:&nbsp;&nbsp;${ci_commit_ref_slug} \n\n"
          message+="- **提交人**: &nbsp;&nbsp;&nbsp;&nbsp; ${at_current_committer_numbers}  \n\n"
          message+="- **负责人**: &nbsp;&nbsp;&nbsp;&nbsp; ${at_project_owner_mobiles}  \n\n"
          message+="- **相关人**: &nbsp;&nbsp;&nbsp;&nbsp; ${related_committers_numbers}  \n\n"
          message+="- **开始时间**:&nbsp;&nbsp;&nbsp; ${start_time}    \n\n"
          message+="- **结束时间**:&nbsp;&nbsp;&nbsp; ${end_time} \n\n"

          echo "$message"
      }
    - |

      extract_field_bak() {
          local js_string="$1"
          local field="$2"
          local value=$(echo "$js_string" | grep -o "\"$field\":\s*[^,}]*" | cut -d ":" -f 2- | tr -d '"' | tr -d '[:space:]')
          echo "$value"
      }

      extract_field() {
          local js_string="$1"
          local field="$2"
          local value=$(echo "$js_string" | grep -o "\"$field\":\s*[^,}]*" | cut -d ":" -f 2- | tr -d '"' | tr -d '[:space:]')

          if [[ "$value" =~ ^0+$ ]]; then
            value="0"
          fi
          echo "$value"
      }

      generate_report() {
          local file="$1"
          local link="$2"
          local markdown_content=""
          markdown_content+="**测试报告(执行结果)** [点击查看](${link}) \n"

          if [[ ! -f "$file" ]]; then
            markdown_content+="- <font color='red'>${file} 不存在 </font> \n\n"
          else
            local pos=$(grep -aob ',\"testResults\":' "$file" | head -n 1 | cut -d ":" -f 1)
            local js_string=""
            if [[ -n "$pos" ]]; then
                js_string=$(head -c "$pos" "$file")
            else
                js_string=$(cat "$file")
            fi
            local numPassedTestSuites=$(extract_field "$js_string" "numPassedTestSuites")
            local numPassedTests=$(extract_field "$js_string" "numPassedTests")
            local numFailedTestSuites=$(extract_field "$js_string" "numFailedTestSuites")
            local numFailedTests=$(extract_field "$js_string" "numFailedTests")
            local numPendingTestSuites=$(extract_field "$js_string" "numPendingTestSuites")
            local numPendingTests=$(extract_field "$js_string" "numPendingTests")
            local numRuntimeErrorTestSuites=$(extract_field "$js_string" "numRuntimeErrorTestSuites")
            local numTodoTests=$(extract_field "$js_string" "numTodoTests")
            local numTotalTestSuites=$(extract_field "$js_string" "numTotalTestSuites")
            local numTotalTests=$(extract_field "$js_string" "numTotalTests")

            markdown_content+="- <font color='green'>Total Test Suites</font>: $numTotalTestSuites \n\n"
            markdown_content+="- <font color='green'>Total Tests</font>: $numTotalTests \n\n"
            markdown_content+="- <font color='green'>Passed Test Suites</font>: $numPassedTestSuites \n\n"
            markdown_content+="- <font color='green'>Passed Tests</font>: $numPassedTests \n\n"
            markdown_content+="- <font color='#D2691E'>Pending Test Suites</font>: $numPendingTestSuites \n\n"
            markdown_content+="- <font color='#D2691E'>Pending Tests</font>: $numPendingTests \n\n"
            markdown_content+="- <font color='#D2691E'>Todo Tests</font>: $numTodoTests \n\n"
            markdown_content+="- <font color='red'>Runtime Error Test Suites</font>: $numRuntimeErrorTestSuites \n\n"
            markdown_content+="- <font color='red'>Failed Test Suites</font>: $numFailedTestSuites \n\n"
            markdown_content+="- <font color='red'>Failed Tests</font>: $numFailedTests \n\n"

          fi

          echo "$markdown_content"
      }
    - |
      extract_to_markdown() {
          local label="$1"
          local percent="$2"
          local details="$3"
          local color=""

          # Remove leading and trailing whitespace
          percent=$(echo "$percent" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
          details=$(echo "$details" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

          # Remove '%' sign
          percent=$(echo "$percent" | sed 's/%$//')

          # Determine color based on percentage
          if (( $(echo "$percent < 30" | bc -l) )); then
          color="red"
          elif (( $(echo "$percent >= 30 && $percent <= 50" | bc -l) )); then
          color="#D2691E"
          else
          color="green"
          fi

          # Format as Markdown list item with color
          echo "- **$label:** <font color='$color'>$2</font> ($3)"
      }

      extract_coverage() {
        local html_file="$1"
        local label="$2"
        local percent=$(grep -A 1 -B 1 "<span class=\"quiet\">$label</span>" "$html_file" | grep -v "<span class=\"quiet\">$label</span>" | grep -oE '[0-9]+\.[0-9]+%')
        local details=$(grep -A 1 -B 1 "<span class=\"quiet\">$label</span>" "$html_file" | grep -v "<span class=\"quiet\">$label</span>" | grep -oE '[0-9]+/[0-9]+')

        extract_to_markdown "$label" "$percent" "$details"
      }

      generate_markdown() {
        local html_file="$1"
        local link="$2"
        local markdown_content=""
        markdown_content+="**测试报告(Coverage)** [点击查看]($link)\n"

        if [[ ! -f "$html_file" ]]; then
          markdown_content+="- <font color='red'>${html_file} 不存在 </font>\n\n"
        else
          markdown_content+="$(extract_coverage "$html_file" "Statements") \n\n"
          markdown_content+="$(extract_coverage "$html_file" "Branches") \n\n"
          markdown_content+="$(extract_coverage "$html_file" "Functions") \n\n"
          markdown_content+="$(extract_coverage "$html_file" "Lines") \n\n"
        fi

        echo "$markdown_content"
      }
    - >
      source sonarqube_vars.sh;
      echo "读取当前环境变量...";
      echo "TEST_STATUS: ${TEST_STATUS};";
      echo "DINGTALK_NOTFIY_WHEN_SUCCESS: ${DINGTALK_NOTFIY_WHEN_SUCCESS};";
      echo "SONAR_REPORT_WHEN_SUCCESS: ${SONAR_REPORT_WHEN_SUCCESS};";

      if [ -z "$TEST_STATUS" ]; then
          TEST_STATUS=0
      fi

      echo "TEST_STATUS: ${TEST_STATUS}";

      echo "设置默认值，如果不存在.....";
      DINGTALK_NOTFIY_WHEN_SUCCESS=${DINGTALK_NOTFIY_WHEN_SUCCESS:-"false"};
      SONAR_REPORT_WHEN_SUCCESS=${SONAR_REPORT_WHEN_SUCCESS:-"false"};

      echo "DINGTALK_NOTFIY_WHEN_SUCCESS: ${DINGTALK_NOTFIY_WHEN_SUCCESS}"
      echo "SONAR_REPORT_WHEN_SUCCESS: ${SONAR_REPORT_WHEN_SUCCESS}"

      echo "单元测试执行状态:" $TEST_STATUS;
      echo "Project DIR:" ${CI_PROJECT_DIR};
      path="${CI_PROJECT_DIR}";
      result=$(echo "$path" | sed 's/.*\/builds\///');
      echo "Build path:" $result;
      host_ip=$(hostname -I | awk '{print $1}');
      echo "Host IP address is: $host_ip";

      REPORT_PATH="reports/${CI_PROJECT_NAME}/${CI_PIPELINE_ID}";
      REPORT_DIR="/home/<USER>/builds/${REPORT_PATH}";

      echo "REPORT_DIR: $REPORT_DIR";
      link="http://$host_ip:8000/$REPORT_PATH/coverage";
      PROJECT_NAME=${CI_PROJECT_NAME};
      REPORT_URL="${link}/html-report/";
      COVERAGE_URL="${link}/lcov-report/";
      REPO_URL="${CI_PROJECT_URL}";

      details_text="";
      SUFFIX1="";
      SUFFIX2="";

      case "$GitLab_Job_Type" in
          0)
              SUFFIX1="每周任务"
              SUFFIX2="Master分支"
              ;;
          1)
              SUFFIX1="每日任务"
              SUFFIX2="当前分支所有代码"
              ;;
          2)
              SUFFIX1="实时任务"
              SUFFIX2="当前分支最近${NUM_COMMITS}次的提交.[查看任务详情](${REPO_URL}/-/jobs/${CI_JOB_ID})"
              ;;
          4)
              SUFFIX1="手动任务"
              SUFFIX2="当前分支所有代码"
              ;;
          5)
              SUFFIX1="<font color='#D2691E'>发版</font>"
              SUFFIX2="Master分支最新代码"
              ;;
          *)
              echo "❌ 未知的GitLab_Job_Type, 中断后续任务的执行."
              exit 1
              ;;
      esac

      Message_Report_Display_Type=3;
      if [ "$No_Active_Tests" = "true" ]; then
        details_text="没有单元测试可执行";
        SUFFIX2="没有发现有效的变更文件，跳过单元测试";
        Message_Report_Display_Type=0;
      else
        echo "-----------";
        echo "-----------";
        echo "复制链接到浏览器查看报告: $link";
        echo "-----------";
        echo "-----------";
        if [ -d "${CI_PROJECT_DIR}/coverage" ]; then
            mkdir -p $REPORT_DIR;
            cp -r ${CI_PROJECT_DIR}/coverage $REPORT_DIR;

            case "$GitLab_Job_Type" in
                2)
                    details_text="[执行结果](${REPORT_URL})"
                    Message_Report_Display_Type=1
                    ;;
                *)
                    details_text="[执行结果](${REPORT_URL}) , [覆盖率](${COVERAGE_URL})"
                    Message_Report_Display_Type=3
                    ;;
            esac
        else
            echo "❌ coverage 目录不存在(没有运行单元测试或者jest --coverage命令失败等).";
            details_text="❌ coverage 目录不存在(没有运行单元测试或者jest --coverage 命令失败等).";
            DINGTALK_NOTIFY_WHEN_SUCCESS="false";
            Message_Report_Display_Type=0;
        fi
      fi

      echo "================================";

      Related_Committers=$(git log -${NUM_COMMITS} --pretty=format:"%ae"  --grep="debug" --invert-grep | sort -u | paste -sd "," -);
      Current_Committer=$(git log -1 --pretty=format:"%ae");
      echo "Related_Committers: ${Related_Committers}";

      Related_Committers_Names=$(extract_value_by_email "$Related_Committers" "$Name_Json_Map");
      Current_Committer_Numbers=$(extract_value_by_email "$Current_Committer" "$Contact_Json_Map");
      Project_Owner_Mobiles=${MOBILE_NUMBERS:-"18626272086,18625000947"};

      echo "Related_Committers_Names: ${Related_Committers_Names}";
      echo "Current_Committer_Numbers: ${Current_Committer_Numbers}";

      AT_Project_Owner_Mobiles=$(convert_mobile_numbers "$Project_Owner_Mobiles");
      AT_Current_Committer_Numbers=$(convert_mobile_numbers "$Current_Committer_Numbers");

      echo "AT_Project_Owner_Mobiles: $AT_Project_Owner_Mobiles";
      echo "AT_Current_Committer_Numbers: $AT_Current_Committer_Numbers";

      Notify_Numbers=$(merge_and_filter_numbers "$Project_Owner_Mobiles" "$Current_Committer_Numbers","true");
      echo "Notify_Numbers: $Notify_Numbers";
      echo "================================";:


      END_TIME=$(date +"%Y-%m-%d %H:%M:%S");
      Is_Success=true;
      if [ $TEST_STATUS -ne 0 ]; then
        Is_Success=false;
      fi

      dingtalk_message=$(generate_message "$Is_Success" "$SUFFIX1" "$PROJECT_NAME" "$REPO_URL" "$CI_PIPELINE_ID" "$CI_JOB_ID" "$SUFFIX2" "$CI_COMMIT_REF_SLUG" "$AT_Current_Committer_Numbers" "$AT_Project_Owner_Mobiles" "$Related_Committers_Names" "$details_text" "$START_TIME" "$END_TIME")

      case "$Message_Report_Display_Type" in
          0)
              echo "没有结果输出，Coverage和执行结果都不显示"
              dingtalk_message+="- <font color='red'>错误信息</font>: ${details_text} \n\n"
              ;;
          1)
              echo "只显示执行结果"
              Result_File="./coverage/html-report/jest-html-reporters-attach/index/result.js"
              result_message=$(generate_report "$Result_File" "$REPORT_URL")
              dingtalk_message+="${result_message}"
              ;;
          2)
              echo "只显示覆盖率"
              HTML_FILE="./coverage/lcov-report/index.html"
              coverage_message=$(generate_markdown "$HTML_FILE" "$COVERAGE_URL")
              dingtalk_message+="${coverage_message}"
              ;;
          *)
              echo "执行结果和覆盖率都显示"
              Result_File="./coverage/html-report/jest-html-reporters-attach/index/result.js"
              result_message=$(generate_report "$Result_File" "$REPORT_URL")
              dingtalk_message+="${result_message}"
              HTML_FILE="./coverage/lcov-report/index.html"
              coverage_message=$(generate_markdown "$HTML_FILE" "$COVERAGE_URL")
              dingtalk_message+="${coverage_message}"
              ;;
      esac


      echo "钉钉消息 markdown:";
      echo $dingtalk_message;

      if [ $TEST_STATUS -ne 0 ]; then
        curl ${DingTalk_Webhook} \
            -H 'Content-Type: application/json' \
            -d "{
              \"msgtype\": \"markdown\",
              \"markdown\": {
                \"title\": \"❌${PROJECT_NAME}, 单元测试执行失败(${SUFFIX1}) \",
                \"text\": \"${dingtalk_message}\"
              },
              \"at\": {
                \"atMobiles\": [${Notify_Numbers}],
                \"isAtAll\": false
              }
            }";
        echo "\n"
        echo "发送dingtalk通知: 单元测试执行失败";

        curl -X 'POST' \
          "http://p.test.greatld.com/api/ingest/unittest/report" \
          -H 'accept: */*' \
          -H 'Content-Type: text/plain' \
          -d "\"${dingtalk_message}\"";
        echo "\n"
        echo "推送考核系统通知: 单元测试执行失败";

        echo "单元测试执行失败，退出...";
      else
        if [ "$DINGTALK_NOTIFY_WHEN_SUCCESS" = "true" ]; then
          curl ${DingTalk_Webhook} \
                      -H 'Content-Type: application/json' \
                      -d "{
                        \"msgtype\": \"markdown\",
                        \"markdown\": {
                          \"title\": \"✅ ${PROJECT_NAME}, 单元测试执行成功(${SUFFIX1})。 \",
                          \"text\": \"${dingtalk_message}\"
                        },
                        \"at\": {
                          \"atMobiles\": [${Notify_Numbers}],
                          \"isAtAll\": false
                        }
                      }";
          echo "\n";
          echo "发送钉钉通知: 单元测试执行成功";
          curl -X 'POST' \
          "http://p.test.greatld.com/api/ingest/unittest/report" \
          -H 'accept: */*' \
          -H 'Content-Type: text/plain' \
          -d "\"${dingtalk_message}\"";
          echo "\n";
          echo "推送考核系统通知: 单元测试执行成功";
        else
          echo "DINGTALK_NOTIFY_WHEN_SUCCESS 不是 true，跳过 钉钉 通知";
        fi
      fi

      if [ "$SONAR_REPORT_ENABLE" = "true" ]; then
        echo "Sonarqube 扫描和报告上传...";
        yarn sonar || SONAR_STATUS=$?;
        if [ -z "$SONAR_STATUS" ]; then
          SONAR_STATUS=0;
        fi

        if [ $SONAR_STATUS -ne 0 ]; then
          echo "yarn sonar 执行失败，退出...";
          exit 1;
        else
          echo "报告上传到 SonarQube 完成";
        fi
        echo "发送钉钉通知: 单元测试执行成功";
      else
        echo "SONAR_REPORT_ENABLE 不是 true，跳过 上传sonar报告";
      fi

      if [ $TEST_STATUS -ne 0 ]; then
        echo "单元测试执行失败";
        exit 1;
      else
        echo "单元测试执行成功";
      fi

.unittest_commit:
  extends:
    - .unittest_reporter
  rules:
    - if: $SKIP_UNITTEST == "true" || $CI_PIPELINE_SOURCE  == "schedule" || $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "gitlab/debug"
      allow_failure: true
  script:
    - changed_files=$(git diff --name-only HEAD~${NUM_COMMITS} HEAD)
    - echo ${changed_files}
    - >
      echo "export GitLab_Job_Type=2;" >> sonarqube_vars.sh;
      if echo "$changed_files" | grep -q '\.sql$\'; then
         echo "发现有新的SQL变更文件:";
         yarn unittest:prepare || true;
      fi

      if  echo "$changed_files" | grep -q '\.ts$\|\.tsx$'; then
        echo "发现有新的变更文件:";
        echo "-----------";
        echo "-----------";
        echo "$changed_files";
        echo "-----------";
        echo "-----------";
        echo "开始执行单元测试...";
        MOCK_MESSAGE_QUEUE='true'
        if [ "$USE_VITEST" == "true" ]; then
          ./node_modules/.bin/vitest related --passWithNoTests --run $changed_files || status=$?;
        else
          echo "生成.env.key 加载本地环境变量...";
          echo "DOTENV_PRIVATE_KEY=f6e1a0408c7ede370726aafa55c036b61738146709c995f91522f1fed170669e;" >> .env.keys;
          ./node_modules/.bin/jest --maxWorkers 7 --maxConcurrency 1 --findRelatedTests $changed_files --passWithNoTests || status=$?;
        fi
      else
        echo "-----------";
        echo "-----------";
        echo "没有发现有效的变更文件，跳过单元测试...";
        echo "-----------";
        echo "-----------";
        echo "export No_Active_Tests=true;" >> sonarqube_vars.sh;
      fi

      if [ -z "$status" ]; then
        status=0;
      fi

      echo "输出 sonarqube_vars.sh:";
      cat sonarqube_vars.sh;
      echo "export TEST_STATUS=${status};" >> sonarqube_vars.sh;
      echo "export DINGTALK_NOTIFY_WHEN_SUCCESS=false;" >> sonarqube_vars.sh;
      echo "export SONAR_REPORT_ENABLE=false;" >> sonarqube_vars.sh;

      echo "写入环境变量...";
      echo "status: ${status};"
      echo "TEST_STATUS: ${status};"

unittest_master:
  extends:
    - .unittest_reporter
  rules:
    - if: $SKIP_UNITTEST == "true" || $CI_PIPELINE_SOURCE  == "schedule"
      when: never
    - if: $CI_COMMIT_REF_NAME == "master"
      when: manual
      allow_failure: true
  script:
    - echo "run unit test(unittest_master)"
    - >
      if [ "$USE_VITEST" == "true" ]; then
        ./node_modules/.bin/vitest run --coverage --coverage.reportOnFailure --passWithNoTests || status=$?;
      else
        echo "生成.env.key 加载本地环境变量...";
        echo "DOTENV_PRIVATE_KEY=f6e1a0408c7ede370726aafa55c036b61738146709c995f91522f1fed170669e;" >> .env.keys;
        echo "开始执行单元测试...";
        ./node_modules/.bin/jest --coverage --passWithNoTests --maxConcurrency 1 --maxWorkers 7 || status=$?;
      fi;

      if [ -z "$status" ]; then
        status=0;
      fi;

      echo "export SONAR_REPORT_ENABLE=true;" >> sonarqube_vars.sh;
      echo "export TEST_STATUS=${status};" >> sonarqube_vars.sh;
      echo "export DINGTALK_NOTIFY_WHEN_SUCCESS=true;" >> sonarqube_vars.sh;
      echo "export GitLab_Job_Type=5;" >> sonarqube_vars.sh;
      echo "写入环境变量...";
      echo "status: ${status};"
      echo "TEST_STATUS: ${status};"

unittest_manually:
  extends:
    - .unittest_reporter
  rules:
    - if: $SKIP_UNITTEST == "true" || $CI_PIPELINE_SOURCE  == "schedule" || $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^release-.*$/ || $CI_COMMIT_REF_NAME == "develop" || $CI_COMMIT_REF_NAME == "gitlab/debug"
      when: manual
      allow_failure: true
  script:
    - echo "run unit test(unittest_manually)"
    - >

      if [ "$USE_VITEST" == "true" ]; then
        ./node_modules/.bin/vitest run --coverage --coverage.reportOnFailure --passWithNoTests || status=$?;
      else
        echo "生成.env.key 加载本地环境变量...";
        echo "DOTENV_PRIVATE_KEY=f6e1a0408c7ede370726aafa55c036b61738146709c995f91522f1fed170669e;" >> .env.keys;
        echo "开始执行单元测试...";
        ./node_modules/.bin/jest --coverage --passWithNoTests --maxConcurrency 1 --maxWorkers 7 || status=$?;
      fi;

      if [ -z "$status" ]; then
        status=0;
      fi;

      echo "export SONAR_REPORT_ENABLE=true;" >> sonarqube_vars.sh;
      echo "export TEST_STATUS=${status};" >> sonarqube_vars.sh;
      echo "export DINGTALK_NOTIFY_WHEN_SUCCESS=true;" >> sonarqube_vars.sh;
      echo "export GitLab_Job_Type=4;" >> sonarqube_vars.sh;
      echo "写入环境变量...";
      echo "status: ${status};"
      echo "TEST_STATUS: ${status};"

unittest_daily:
  extends:
    - .unittest_reporter
  rules:
    - if: $CI_PIPELINE_SOURCE  == "schedule"
      allow_failure: true
  script:
    - echo "run unit test(unittest_daily)"
    - >
      if [ "$(date +%u)" -eq 5 ]; then
        echo "今天是周五，开启Sonarqube扫描.";
        echo "export SONAR_REPORT_ENABLE=true;" >> sonarqube_vars.sh;
        echo "export DINGTALK_NOTIFY_WHEN_SUCCESS=true;" >> sonarqube_vars.sh;
      else
        echo "今天不是周五，关闭Sonarqube扫描.";
        echo "export SONAR_REPORT_ENABLE=false;" >> sonarqube_vars.sh;
        echo "export DINGTALK_NOTIFY_WHEN_SUCCESS=false;" >> sonarqube_vars.sh;
      fi

      echo "export TEST_STATUS=${status};" >> sonarqube_vars.sh;
      echo "export GitLab_Job_Type=1;" >> sonarqube_vars.sh;

      if [ "$USE_VITEST" == "true" ]; then
        ./node_modules/.bin/vitest run --coverage --coverage.reportOnFailure || status=$?;
      else
        echo "生成.env.key 加载本地环境变量...";
        echo "DOTENV_PRIVATE_KEY=f6e1a0408c7ede370726aafa55c036b61738146709c995f91522f1fed170669e;" >> .env.keys;
        echo "开始执行单元测试...";
        ./node_modules/.bin/jest --coverage --maxConcurrency 1 --maxWorkers 7 || status=$?;
      fi;

      if [ -z "$status" ]; then
        status=0;
      fi;

      echo "写入环境变量...";
      echo "status: ${status};"
      echo "TEST_STATUS: ${status};"

.gitstat_manually:
  stage: sonarqube
  extends: .unittest_reporter_tag
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - if: '$CI_COMMIT_REF_NAME =~ /^(release-.*|develop|gitlab\/debug)$/'
      when: manual
      allow_failure: true
  script:
    - |
      #!/bin/bash
      # 获取项目名称（优先使用CI环境变量，否则动态解析）
        PROJECT_NAME=${CI_PROJECT_NAME:-$(basename $(git rev-parse --show-toplevel))}
        echo "获取git提交记录";
        echo $PROJECT_NAME;
        git version;
        # 生成Git日志统计
        git log --no-merges --all --since="1 year ago" --date=iso --pretty=format:"%ae|%ad" --numstat -- '*.js'  '*.less' '*.ts' '*.java' '*.tsx' '*.css' '*.scss' '*.vue'  ':!**/__snapshots__/**' ':!*.spec.*'  |
        awk -v project="$PROJECT_NAME" '
        BEGIN {
            OFS=",";
            print "项目名称,开发者邮箱,日期范围,新增行数,删除行数,修改行数";
        }
        # 处理提交头
        /^[^|]+@[^|]+\|[0-9]{4}-[0-9]{2}-[0-9]{2}/ {
            split($0, arr, "|");
            split(arr[1], parts, "@");  # 新增分割操作[1,6](@ref)
            email = parts[1];            # 只保留@前的用户名[1,6](@ref)
            split(arr[2], dt, /[- :]/);
            commit_month = sprintf("%04d-%02d", dt[1], dt[2]);
            next;
        }
        # 处理文件变更行
        $3 ~ /\.(js|ts|java|less|tsx|css|scss|vue)$/i && $3 !~ /(__snapshots__|\.spec)/ {
            split($0, arr, "\t");
            add[email,commit_month] += arr[1];
            del[email,commit_month] += arr[2];
            update[email,commit_month] += (arr[1]>0 && arr[2]>0 ? (arr[1]<arr[2] ? arr[1] : arr[2]) : 0);
            next;
        }
        END {
            for (key in add) {
                split(key, sep, SUBSEP);
                email = sep[1];
                month = sep[2];
                if (add[key] > 0 || del[key] > 0) {
                    printf "%s,%s,%s,%d,%d,%d\n", 
                        project, 
                        email, 
                        month, 
                        add[key], 
                        del[key], 
                        update[key]
                }
            }
        }' |
        sort -t ',' -k3,3n -k2,2
